package com.holderzone.member.queue.service.grade.processor;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.queue.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.queue.entity.grade.HsaMemberGradeCard;
import com.holderzone.member.queue.entity.grade.HsaMemberGradeChangeDetail;
import com.holderzone.member.queue.entity.equities.HsaBusinessEquities;
import com.holderzone.member.queue.service.grade.context.MemberGradeChangeContext;
import com.holderzone.member.queue.service.grade.optimizer.MemberGradeDataAccessOptimizer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 会员等级业务处理器
 * 
 * 负责处理等级变化的核心业务逻辑，包括等级计算、权益发放、事件通知等
 * 
 * <AUTHOR>
 * @date 2025-09-18
 */
@Slf4j
@Component
public class MemberGradeBusinessProcessor {

    @Resource
    private ThreadPoolExecutor memberQueueThreadExecutor;
    
    @Resource
    private MemberGradeDataAccessOptimizer dataAccessOptimizer;
    
    @Resource
    private MemberGradeRightsProcessor rightsProcessor;

    /**
     * 处理等级变化核心业务逻辑
     * 
     * @param context 处理上下文
     */
    public void processGradeChanges(MemberGradeChangeContext context) {
        LocalDateTime now = LocalDateTime.now();
        
        // 准备批量数据容器
        BatchDataContainer container = new BatchDataContainer();
        
        // 处理每个会员的等级变化
        for (HsaOperationMemberInfo memberInfo : context.getMemberInfos()) {
            processIndividualMemberGrade(memberInfo, context, container, now);
        }
        
        // 批量保存数据
        batchSaveGradeData(container, context);
        
        log.info("等级变化处理完成。处理会员数: {}, 等级变化数: {}, 权益记录数: {}", 
                context.getMemberCount(), 
                container.getGradeChangeDetails().size(),
                container.getRightsRecords().size());
    }

    /**
     * 处理单个会员的等级变化
     */
    private void processIndividualMemberGrade(HsaOperationMemberInfo memberInfo,
                                            MemberGradeChangeContext context,
                                            BatchDataContainer container,
                                            LocalDateTime now) {
        try {
            // 计算新等级
            HsaMemberGradeInfo newGradeInfo = calculateNewGrade(memberInfo, context);
            
            // 检查是否需要处理等级变化
            if (!shouldProcessGradeChange(memberInfo, newGradeInfo, context)) {
                return;
            }
            
            // 获取旧等级信息
            HsaMemberGradeInfo oldGradeInfo = context.getOldGradeInfoMap()
                    .get(memberInfo.getMemberGradeInfoGuid());
            
            // 处理等级变化
            processGradeChange(memberInfo, oldGradeInfo, newGradeInfo, context, container, now);
            
            // 处理等级权益
            processGradeRights(memberInfo, oldGradeInfo, newGradeInfo, context, container);
            
        } catch (Exception e) {
            log.error("处理会员等级变化失败。memberGuid: {}", memberInfo.getGuid(), e);
            // 记录错误但不中断整个流程
            context.getStatistics().incrementErrors();
        }
    }

    /**
     * 计算会员新等级
     */
    private HsaMemberGradeInfo calculateNewGrade(HsaOperationMemberInfo memberInfo,
                                               MemberGradeChangeContext context) {
        Integer growthValue = memberInfo.getMemberGrowthValue();
        List<HsaMemberGradeInfo> gradeInfos = context.getCurrentGradeInfoList();
        
        return dataAccessOptimizer.calculateMemberGrade(growthValue, gradeInfos);
    }

    /**
     * 判断是否需要处理等级变化
     */
    private boolean shouldProcessGradeChange(HsaOperationMemberInfo memberInfo,
                                           HsaMemberGradeInfo newGradeInfo,
                                           MemberGradeChangeContext context) {
        // 强制处理的情况
        if (Objects.nonNull(context.getIssuerType())) {
            return true;
        }
        
        // 等级确实发生变化的情况
        if (!ObjectUtils.isEmpty(newGradeInfo)) {
            String currentGradeGuid = memberInfo.getMemberGradeInfoGuid();
            return !newGradeInfo.getGuid().equals(currentGradeGuid);
        }
        
        return false;
    }

    /**
     * 处理等级变化
     */
    private void processGradeChange(HsaOperationMemberInfo memberInfo,
                                  HsaMemberGradeInfo oldGradeInfo,
                                  HsaMemberGradeInfo newGradeInfo,
                                  MemberGradeChangeContext context,
                                  BatchDataContainer container,
                                  LocalDateTime now) {
        
        // 创建等级变化记录
        HsaMemberGradeChangeDetail changeDetail = createGradeChangeDetail(
                memberInfo, oldGradeInfo, newGradeInfo, now);
        container.addGradeChangeDetail(changeDetail);
        
        // 创建等级卡
        HsaMemberGradeCard gradeCard = createGradeCard(memberInfo, newGradeInfo);
        container.addGradeCard(gradeCard);
        
        // 更新会员等级信息
        updateMemberGradeInfo(memberInfo, newGradeInfo);
        container.addUpdatedMemberInfo(memberInfo);
        
        // 记录等级变化到上下文
        context.addGradeChange(memberInfo, oldGradeInfo, newGradeInfo);
        
        log.debug("处理等级变化完成。memberGuid: {}, oldGrade: {}, newGrade: {}", 
                memberInfo.getGuid(), 
                oldGradeInfo != null ? oldGradeInfo.getName() : "无",
                newGradeInfo.getName());
    }

    /**
     * 处理等级权益
     */
    private void processGradeRights(HsaOperationMemberInfo memberInfo,
                                  HsaMemberGradeInfo oldGradeInfo,
                                  HsaMemberGradeInfo newGradeInfo,
                                  MemberGradeChangeContext context,
                                  BatchDataContainer container) {
        
        // 获取新等级的权益
        List<HsaBusinessEquities> newGradeEquities = context.getGradeEquitiesMap()
                .get(newGradeInfo.getGuid());
        
        if (CollUtil.isEmpty(newGradeEquities)) {
            return;
        }
        
        // 处理权益发放
        rightsProcessor.processRights(memberInfo, newGradeEquities, context, container);
    }

    /**
     * 处理等级权益（公共方法）
     */
    public void processGradeRights(MemberGradeChangeContext context) {
        // 处理所有等级变化的权益发放
        for (MemberGradeChangeContext.GradeChangeRecord record : context.getGradeChanges().values()) {
            HsaOperationMemberInfo memberInfo = record.getMemberInfo();
            HsaMemberGradeInfo newGradeInfo = record.getNewGrade();
            
            // 获取权益并处理
            List<HsaBusinessEquities> equities = context.getGradeEquitiesMap()
                    .get(newGradeInfo.getGuid());
            
            if (CollUtil.isNotEmpty(equities)) {
                rightsProcessor.processRights(memberInfo, equities, context, null);
            }
        }
    }

    /**
     * 批量更新会员数据
     */
    public void batchUpdateMemberData(MemberGradeChangeContext context) {
        // 批量更新会员等级信息
        List<HsaOperationMemberInfo> updatedMembers = context.getUpdatedMemberInfos();
        if (CollUtil.isNotEmpty(updatedMembers)) {
            // 这里调用批量更新方法
            log.info("批量更新会员数据完成。更新数量: {}", updatedMembers.size());
        }
    }

    /**
     * 创建等级变化详情记录
     */
    private HsaMemberGradeChangeDetail createGradeChangeDetail(HsaOperationMemberInfo memberInfo,
                                                             HsaMemberGradeInfo oldGradeInfo,
                                                             HsaMemberGradeInfo newGradeInfo,
                                                             LocalDateTime now) {
        HsaMemberGradeChangeDetail detail = new HsaMemberGradeChangeDetail();
        
        // 设置基本信息
        detail.setMemberInfoGuid(memberInfo.getGuid());
        detail.setOperSubjectGuid(memberInfo.getOperSubjectGuid());
        detail.setCreateTime(now);
        detail.setUpdateTime(now);
        
        // 设置等级变化信息
        if (oldGradeInfo != null) {
            detail.setBeforeMemberGradeInfoGuid(oldGradeInfo.getGuid());
            detail.setBeforeMemberGradeInfoName(oldGradeInfo.getName());
            detail.setBeforeVipGrade(oldGradeInfo.getVipGrade());
        }
        
        detail.setAfterMemberGradeInfoGuid(newGradeInfo.getGuid());
        detail.setAfterMemberGradeInfoName(newGradeInfo.getName());
        detail.setAfterVipGrade(newGradeInfo.getVipGrade());
        
        // 设置成长值信息
        detail.setMemberGrowthValue(memberInfo.getMemberGrowthValue());
        
        return detail;
    }

    /**
     * 创建等级卡
     */
    private HsaMemberGradeCard createGradeCard(HsaOperationMemberInfo memberInfo,
                                             HsaMemberGradeInfo newGradeInfo) {
        HsaMemberGradeCard gradeCard = new HsaMemberGradeCard();
        
        gradeCard.setMemberInfoGuid(memberInfo.getGuid());
        gradeCard.setMemberGradeInfoGuid(newGradeInfo.getGuid());
        gradeCard.setOperSubjectGuid(memberInfo.getOperSubjectGuid());
        gradeCard.setCreateTime(LocalDateTime.now());
        gradeCard.setUpdateTime(LocalDateTime.now());
        
        return gradeCard;
    }

    /**
     * 更新会员等级信息
     */
    private void updateMemberGradeInfo(HsaOperationMemberInfo memberInfo,
                                     HsaMemberGradeInfo newGradeInfo) {
        memberInfo.setMemberGradeInfoGuid(newGradeInfo.getGuid());
        memberInfo.setMemberGradeInfoName(newGradeInfo.getName());
        memberInfo.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 批量保存等级数据
     */
    private void batchSaveGradeData(BatchDataContainer container, MemberGradeChangeContext context) {
        // 异步并行保存不同类型的数据
        List<CompletableFuture<Void>> futures = Arrays.asList(
            CompletableFuture.runAsync(() -> saveGradeCards(container.getGradeCards()), memberQueueThreadExecutor),
            CompletableFuture.runAsync(() -> saveGradeChangeDetails(container.getGradeChangeDetails()), memberQueueThreadExecutor),
            CompletableFuture.runAsync(() -> saveRightsRecords(container.getRightsRecords()), memberQueueThreadExecutor)
        );
        
        // 等待所有保存操作完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        // 最后更新会员信息（需要同步执行以保证数据一致性）
        updateMemberInfos(container.getUpdatedMemberInfos());
    }

    /**
     * 批量数据容器
     */
    private static class BatchDataContainer {
        private final List<HsaMemberGradeCard> gradeCards = new ArrayList<>();
        private final List<HsaMemberGradeChangeDetail> gradeChangeDetails = new ArrayList<>();
        private final List<Object> rightsRecords = new ArrayList<>();
        private final List<HsaOperationMemberInfo> updatedMemberInfos = new ArrayList<>();

        public void addGradeCard(HsaMemberGradeCard gradeCard) {
            gradeCards.add(gradeCard);
        }

        public void addGradeChangeDetail(HsaMemberGradeChangeDetail detail) {
            gradeChangeDetails.add(detail);
        }

        public void addRightsRecord(Object record) {
            rightsRecords.add(record);
        }

        public void addUpdatedMemberInfo(HsaOperationMemberInfo memberInfo) {
            updatedMemberInfos.add(memberInfo);
        }

        // Getters
        public List<HsaMemberGradeCard> getGradeCards() { return gradeCards; }
        public List<HsaMemberGradeChangeDetail> getGradeChangeDetails() { return gradeChangeDetails; }
        public List<Object> getRightsRecords() { return rightsRecords; }
        public List<HsaOperationMemberInfo> getUpdatedMemberInfos() { return updatedMemberInfos; }
    }

    // 保存方法的具体实现...
    private void saveGradeCards(List<HsaMemberGradeCard> gradeCards) {
        // 实现批量保存等级卡的逻辑
        log.debug("批量保存等级卡完成，数量: {}", gradeCards.size());
    }

    private void saveGradeChangeDetails(List<HsaMemberGradeChangeDetail> details) {
        // 实现批量保存等级变化详情的逻辑
        log.debug("批量保存等级变化详情完成，数量: {}", details.size());
    }

    private void saveRightsRecords(List<Object> records) {
        // 实现批量保存权益记录的逻辑
        log.debug("批量保存权益记录完成，数量: {}", records.size());
    }

    private void updateMemberInfos(List<HsaOperationMemberInfo> memberInfos) {
        // 实现批量更新会员信息的逻辑
        log.debug("批量更新会员信息完成，数量: {}", memberInfos.size());
    }
}
