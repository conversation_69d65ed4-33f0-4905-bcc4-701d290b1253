package com.holderzone.member.queue.service.grade.optimizer;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.GradeTypeEnum;
import com.holderzone.member.common.enums.DataUnitEnum;
import com.holderzone.member.common.enums.GradeRightsTypeEnum;
import com.holderzone.member.queue.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.queue.entity.equities.HsaBusinessEquities;
import com.holderzone.member.queue.entity.grade.HsaMemberGradeRightsRecord;
import com.holderzone.member.queue.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.queue.mapper.equities.HsaBusinessEquitiesMapper;
import com.holderzone.member.queue.mapper.grade.HsaMemberGradeRightsRecordMapper;
import com.holderzone.member.queue.service.grade.context.MemberGradeChangeContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员等级数据访问优化器
 * 
 * 负责优化数据库查询，减少查询次数，提高查询效率
 * 
 * <AUTHOR>
 * @date 2025-09-18
 */
@Slf4j
@Component
public class MemberGradeDataAccessOptimizer {

    private static final int BATCH_QUERY_SIZE = 500;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;
    
    @Resource
    private HsaBusinessEquitiesMapper hsaBusinessEquitiesMapper;
    
    @Resource
    private HsaMemberGradeRightsRecordMapper hsaMemberGradeRightsRecordMapper;

    /**
     * 批量加载当前生效的等级信息
     * 
     * @param context 处理上下文
     * @return 等级信息列表
     */
    public List<HsaMemberGradeInfo> loadCurrentGradeInfos(MemberGradeChangeContext context) {
        context.getStatistics().incrementDatabaseQueries();
        
        List<HsaMemberGradeInfo> gradeInfos = hsaMemberGradeInfoMapper.selectList(
                new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getOperSubjectGuid, context.getOperSubjectGuid())
                        .eq(HsaMemberGradeInfo::getRoleType, context.getRoleType())
                        .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode())
                        .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                        .orderByAsc(HsaMemberGradeInfo::getVipGrade)
        );

        log.debug("加载当前等级信息完成，数量: {}", gradeInfos.size());
        return gradeInfos;
    }

    /**
     * 批量加载历史等级信息映射
     * 
     * @param context 处理上下文
     * @return 等级信息映射
     */
    public Map<String, HsaMemberGradeInfo> loadOldGradeInfoMap(MemberGradeChangeContext context) {
        List<String> gradeGuids = extractGradeGuids(context.getMemberInfos(), context.getRoleType());
        
        if (CollUtil.isEmpty(gradeGuids)) {
            return Collections.emptyMap();
        }

        // 分批查询避免IN子句过长
        Map<String, HsaMemberGradeInfo> resultMap = new HashMap<>();
        List<List<String>> batches = Lists.partition(gradeGuids, BATCH_QUERY_SIZE);
        
        for (List<String> batch : batches) {
            context.getStatistics().incrementDatabaseQueries();
            
            List<HsaMemberGradeInfo> batchResult = hsaMemberGradeInfoMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberGradeInfo>()
                            .in(HsaMemberGradeInfo::getGuid, batch)
            );
            
            Map<String, HsaMemberGradeInfo> batchMap = batchResult.stream()
                    .collect(Collectors.toMap(
                            HsaMemberGradeInfo::getGuid, 
                            Function.identity(), 
                            (existing, replacement) -> existing
                    ));
            
            resultMap.putAll(batchMap);
        }

        log.debug("加载历史等级信息完成，数量: {}", resultMap.size());
        return resultMap;
    }

    /**
     * 批量加载等级权益映射
     * 
     * @param context 处理上下文
     * @return 权益映射
     */
    public Map<String, List<HsaBusinessEquities>> loadGradeEquitiesMap(MemberGradeChangeContext context) {
        context.getStatistics().incrementDatabaseQueries();
        
        List<HsaBusinessEquities> equitiesList = hsaBusinessEquitiesMapper.selectList(
                new LambdaQueryWrapper<HsaBusinessEquities>()
                        .eq(HsaBusinessEquities::getOperSubjectGuid, context.getOperSubjectGuid())
                        .eq(HsaBusinessEquities::getEffective, NumberConstant.NUMBER_1)
                        .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2)
        );

        Map<String, List<HsaBusinessEquities>> equitiesMap = equitiesList.stream()
                .collect(Collectors.groupingBy(HsaBusinessEquities::getMemberGradeInfoGuid));

        log.debug("加载等级权益映射完成，等级数量: {}, 权益总数: {}", 
                equitiesMap.size(), equitiesList.size());
        
        return equitiesMap;
    }

    /**
     * 批量加载周期性权益
     * 
     * @param context 处理上下文
     * @return 周期性权益列表
     */
    public List<HsaBusinessEquities> loadPeriodicEquities(MemberGradeChangeContext context) {
        context.getStatistics().incrementDatabaseQueries();
        
        return hsaBusinessEquitiesMapper.selectList(
                new LambdaQueryWrapper<HsaBusinessEquities>()
                        .eq(HsaBusinessEquities::getSetPeriod, DataUnitEnum.FOREVER.getCode())
                        .in(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaBusinessEquities::getEffective, BooleanEnum.TRUE.getCode())
                        .eq(HsaBusinessEquities::getOperSubjectGuid, context.getOperSubjectGuid())
        );
    }

    /**
     * 批量加载会员权益接收记录
     * 
     * @param context 处理上下文
     * @return 权益接收记录映射
     */
    public Map<String, List<HsaMemberGradeRightsRecord>> loadMemberRightsRecords(
            MemberGradeChangeContext context) {
        
        List<String> memberGradeGuids = extractMemberGradeGuids(context.getMemberInfos(), context.getRoleType());
        
        if (CollUtil.isEmpty(memberGradeGuids)) {
            return Collections.emptyMap();
        }

        // 分批查询
        Map<String, List<HsaMemberGradeRightsRecord>> resultMap = new HashMap<>();
        List<List<String>> batches = Lists.partition(memberGradeGuids, BATCH_QUERY_SIZE);
        
        for (List<String> batch : batches) {
            context.getStatistics().incrementDatabaseQueries();
            
            List<HsaMemberGradeRightsRecord> batchResult = hsaMemberGradeRightsRecordMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                            .eq(HsaMemberGradeRightsRecord::getOperSubjectGuid, context.getOperSubjectGuid())
                            .in(HsaMemberGradeRightsRecord::getMemberInfoGuid, batch)
                            .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS.getCode())
            );
            
            Map<String, List<HsaMemberGradeRightsRecord>> batchMap = batchResult.stream()
                    .collect(Collectors.groupingBy(HsaMemberGradeRightsRecord::getMemberInfoGuid));
            
            // 合并结果
            batchMap.forEach((key, value) -> 
                    resultMap.merge(key, value, (existing, replacement) -> {
                        existing.addAll(replacement);
                        return existing;
                    })
            );
        }

        log.debug("加载会员权益记录完成，会员数量: {}", resultMap.size());
        return resultMap;
    }

    /**
     * 提取等级GUID列表
     */
    private List<String> extractGradeGuids(List<HsaOperationMemberInfo> memberInfos, String roleType) {
        Set<String> gradeGuids = new HashSet<>();
        
        // 添加会员当前等级GUID
        memberInfos.stream()
                .map(HsaOperationMemberInfo::getMemberGradeInfoGuid)
                .filter(Objects::nonNull)
                .forEach(gradeGuids::add);
        
        // 添加根据角色类型生成的等级GUID
        gradeGuids.addAll(generateGradeGuidsByRole(memberInfos, roleType));
        
        return new ArrayList<>(gradeGuids);
    }

    /**
     * 提取会员等级GUID列表
     */
    private List<String> extractMemberGradeGuids(List<HsaOperationMemberInfo> memberInfos, String roleType) {
        List<String> memberGradeGuids = memberInfos.stream()
                .map(HsaOperationMemberInfo::getMemberGradeInfoGuid)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        memberGradeGuids.addAll(generateGradeGuidsByRole(memberInfos, roleType));
        
        return memberGradeGuids;
    }

    /**
     * 根据角色类型生成等级GUID列表
     */
    private List<String> generateGradeGuidsByRole(List<HsaOperationMemberInfo> memberInfos, String roleType) {
        // 这里应该根据具体的业务逻辑来生成
        // 暂时返回空列表，具体实现需要根据原有的makeGradeGuidList方法逻辑
        return Collections.emptyList();
    }

    /**
     * 优化的等级计算方法
     * 
     * @param growthValue 成长值
     * @param gradeInfos 等级信息列表（已排序）
     * @return 对应的等级信息
     */
    public HsaMemberGradeInfo calculateMemberGrade(Integer growthValue, List<HsaMemberGradeInfo> gradeInfos) {
        if (growthValue == null || growthValue <= 0 || CollUtil.isEmpty(gradeInfos)) {
            return null;
        }

        // 使用二分查找优化性能
        int left = 0, right = gradeInfos.size() - 1;
        HsaMemberGradeInfo result = null;
        
        while (left <= right) {
            int mid = left + (right - left) / 2;
            HsaMemberGradeInfo gradeInfo = gradeInfos.get(mid);
            
            if (gradeInfo.getGrowthValue() <= growthValue) {
                result = gradeInfo;
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }
        
        return result;
    }

    /**
     * 批量保存优化
     * 
     * @param dataList 数据列表
     * @param batchSize 批次大小
     * @param saveFunction 保存函数
     */
    public <T> void batchSaveOptimized(List<T> dataList, int batchSize, 
                                      Function<List<T>, Boolean> saveFunction) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }

        List<List<T>> batches = Lists.partition(dataList, batchSize);
        
        for (List<T> batch : batches) {
            try {
                saveFunction.apply(batch);
            } catch (Exception e) {
                log.error("批量保存失败，批次大小: {}", batch.size(), e);
                throw new RuntimeException("批量保存失败", e);
            }
        }
        
        log.debug("批量保存完成，总数量: {}, 批次数: {}", dataList.size(), batches.size());
    }
}
