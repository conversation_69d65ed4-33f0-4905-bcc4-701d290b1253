package com.holderzone.member.mall.tool.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.integral.DeductCommodityDTO;
import com.holderzone.member.common.exception.MemberToolException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyDiscountBaseReqDTO;
import com.holderzone.member.common.qo.tool.MessagesConfigQO;
import com.holderzone.member.common.qo.tool.MessagesConfigStatusQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.grade.MemberGradeRelationVO;
import com.holderzone.member.common.vo.tool.MessagesConfigVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import com.holderzone.member.common.vo.tool.UserOperSubjectVO;
import com.holderzone.member.mall.tool.entity.HsaMessagesConfig;
import com.holderzone.member.mall.tool.manager.CommonManager;
import com.holderzone.member.mall.tool.mapper.HsaMessagesConfigMapper;
import com.holderzone.member.mall.tool.service.HsaMessagesConfigService;
import com.holderzone.member.mall.tool.service.help.MallMessagesConfigBaseHelp;
import com.holderzone.member.mall.tool.service.help.MessagesConfigBaseHelp;
import com.holderzone.member.mall.tool.utils.WeChatMessagesUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-07-10
 */
@Slf4j
@Service
@AllArgsConstructor
public class HsaMessagesConfigServiceImpl extends ServiceImpl<HsaMessagesConfigMapper, HsaMessagesConfig> implements HsaMessagesConfigService {

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final HsaMessagesConfigMapper hsaMessagesConfigMapper;

    private final WeChatMessagesUtil weChatMessagesUtil;

    private final CommonManager commonManager;

    // TODO 需要改造(已改)
    private final ExternalSupport externalSupport;

    private final SystemRoleHelper systemRoleHelper;

    @Override
    public void saveMessagesConfig(List<MessagesConfigQO> messagesConfigQO) {

        for (MessagesConfigQO configQO : messagesConfigQO) {
            HsaMessagesConfig hsaMessagesConfig;
            hsaMessagesConfig = hsaMessagesConfigMapper.selectOne(new LambdaQueryWrapper<HsaMessagesConfig>()
                    .eq(HsaMessagesConfig::getOperSubjectGuid, configQO.getOperSubjectGuid())
                    .eq(HsaMessagesConfig::getAppletTemplateNo, configQO.getAppletTemplateNo()));

            if (Objects.nonNull(hsaMessagesConfig)) {
                log.info("此模板已经初始化过");
                BeanUtils.copyProperties(configQO, hsaMessagesConfig);
                hsaMessagesConfig.setAppletTemplateNo(configQO.getAppletTemplateNo());
                baseMapper.updateByGuid(hsaMessagesConfig);
            } else {
                hsaMessagesConfig = new HsaMessagesConfig();
                hsaMessagesConfig.setGuid(guidGeneratorUtil.getStringGuid(HsaMessagesConfig.class.getSimpleName()));
                BeanUtils.copyProperties(configQO, hsaMessagesConfig);
                this.save(hsaMessagesConfig);
            }

        }
    }

    @Override
    public SendMessagesConfigVO getMessagesConfigByName(String operSubjectGuid, String name) {
        SendMessagesConfigVO sendMessagesConfigVO = new SendMessagesConfigVO();
        HsaMessagesConfig hsaMessagesConfig = hsaMessagesConfigMapper.selectOne(new LambdaQueryWrapper<HsaMessagesConfig>()
                .and(qw -> qw.eq(HsaMessagesConfig::getMsgTitle, name)
                        .or()
                        .eq(HsaMessagesConfig::getTitle, name))
                .eq(HsaMessagesConfig::getOperSubjectGuid, operSubjectGuid));

        if (Objects.nonNull(hsaMessagesConfig)) {
            BeanUtils.copyProperties(hsaMessagesConfig, sendMessagesConfigVO);
        }
        return sendMessagesConfigVO;
    }

    @Override
    public boolean updateMessagesStatus(MessagesConfigStatusQO messagesConfigVO) {
        HsaMessagesConfig messagesConfig = hsaMessagesConfigMapper.queryByGuid(messagesConfigVO.getGuid());

        if (messagesConfigVO.getStatus() == 1) {
            dealOpenStatus(messagesConfigVO, messagesConfig);
        } else {
            dealDeleteStatus(messagesConfigVO, messagesConfig);
        }
        return hsaMessagesConfigMapper.updateByGuid(messagesConfig);
    }

    @Override
    public List<MessagesConfigVO> getMessagesConfig() {
        List<HsaMessagesConfig> hsaMessagesConfigList = hsaMessagesConfigMapper.selectList(new LambdaQueryWrapper<HsaMessagesConfig>()
                .eq(HsaMessagesConfig::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));

        if (CollUtil.isEmpty(hsaMessagesConfigList)) {
            return Collections.emptyList();
        }
        List<MessagesConfigVO> messagesConfigVOList = new ArrayList<>();
        Set<Integer> msgIdList = hsaMessagesConfigList.stream().map(HsaMessagesConfig::getMsgCategory).collect(Collectors.toSet());
        Map<Integer, List<HsaMessagesConfig>> messagesConfigMap = hsaMessagesConfigList.stream()
                .collect(Collectors.groupingBy(HsaMessagesConfig::getMsgCategory));
        for (Integer i : msgIdList) {
            if (CollUtil.isNotEmpty(messagesConfigMap.get(i))) {
                List<HsaMessagesConfig> messagesConfig = messagesConfigMap.get(i);
                for (HsaMessagesConfig hsaMessagesConfig : messagesConfig) {
                    MessagesConfigVO messagesConfigVO = new MessagesConfigVO();
                    BeanUtils.copyProperties(hsaMessagesConfig, messagesConfigVO);
                    messagesConfigVO.setMsgContent(hsaMessagesConfig.getMsgContent());
                    messagesConfigVO.setPushRule(systemRoleHelper.getReplace(hsaMessagesConfig.getPushRule(), ThreadLocalCache.getOperSubjectGuid()));
                    messagesConfigVOList.add(messagesConfigVO);
                }
            }
        }
        return messagesConfigVOList;
    }

    @Override
    public void initMessagesConfig(String msgTitle) {
        List<String> subjectVOList = getAllDistinctOperSubjectGuids();
        log.info("[listOperSubjectAndApplet]查询运营主体列表,subjectAndAppletResult={}",
                JacksonUtils.writeValueAsString(subjectVOList));

        if (CollUtil.isEmpty(subjectVOList)) {
            log.info("[listOperSubjectAndApplet]查询运营主体列表为空");
            return;
        }

        hsaMessagesConfigMapper.delete(new LambdaQueryWrapper<HsaMessagesConfig>()
                .in(HsaMessagesConfig::getOperSubjectGuid, subjectVOList)
                .eq(!StringUtils.isEmpty(msgTitle), HsaMessagesConfig::getMsgTitle, msgTitle));
        if (CollUtil.isNotEmpty(subjectVOList)) {
            for (String subjectGuid : subjectVOList) {
                List<MessagesConfigQO> messagesConfigQOList = MallMessagesConfigBaseHelp.initMessagesConfig(subjectGuid);

                if (!StringUtils.isEmpty(msgTitle)) {
                    messagesConfigQOList = messagesConfigQOList.stream()
                            .filter(e -> msgTitle.equals(e.getMsgTitle()))
                            .collect(Collectors.toList());
                }
                for (MessagesConfigQO configQO : messagesConfigQOList) {
                    HsaMessagesConfig hsaMessagesConfig = new HsaMessagesConfig();
                    hsaMessagesConfig.setGuid(guidGeneratorUtil.getStringGuid(HsaMessagesConfig.class.getSimpleName()));
                    BeanUtils.copyProperties(configQO, hsaMessagesConfig);
                    this.save(hsaMessagesConfig);
                }
            }
            log.info("[initMessagesConfig]初始化消息集合成功");
        }
    }

    @Override
    public void initMessagesBySubjectGuid(List<String> operSubjectGuidList) {
        log.info("自动初始化消息集合主体:{}", JacksonUtils.writeValueAsString(operSubjectGuidList));
        if (CollUtil.isNotEmpty(operSubjectGuidList)) {
            for (String guid : operSubjectGuidList) {
                List<MessagesConfigQO> messagesConfigQOList = MallMessagesConfigBaseHelp.initMessagesConfig(guid);
                saveMessagesConfig(messagesConfigQOList);
            }
        }
    }

    @Override
    public List<String> getAllDistinctOperSubjectGuids() {
        List<HsaMessagesConfig> allConfigs = hsaMessagesConfigMapper.selectList(new LambdaQueryWrapper<HsaMessagesConfig>()
                .select(HsaMessagesConfig::getOperSubjectGuid)
                .isNotNull(HsaMessagesConfig::getOperSubjectGuid));
        
        return allConfigs.stream()
                .map(HsaMessagesConfig::getOperSubjectGuid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    private void dealDeleteStatus(MessagesConfigStatusQO messagesConfigVO, HsaMessagesConfig messagesConfig) {
        if (messagesConfigVO.getWechatType() == 1) {
            weChatMessagesUtil.deleteAppletTemplateId(messagesConfigVO, messagesConfig);
            messagesConfig.setAppletStatus(0);
        } else if (messagesConfigVO.getWechatType() == 0) {
            weChatMessagesUtil.deleteTemplateId(messagesConfigVO, messagesConfig);
            messagesConfig.setStatus(0);
        } else {
            messagesConfig.setMessageStatus(0);
        }
    }

    private void dealOpenStatus(MessagesConfigStatusQO messagesConfigVO, HsaMessagesConfig messagesConfig) {
        UserOperSubjectVO userOperSubjectVO = commonManager.listOperSubjectAndApplet();
        if (CollUtil.isEmpty(userOperSubjectVO.getOperSubjectVOList())) {
            throw new MemberToolException("开启失败！请先绑定小程序");
        }
        List<String> operationList = userOperSubjectVO.getOperSubjectVOList().stream().map(OperSubjectVO::getOperation_id).collect(Collectors.toList());
        if (!operationList.contains(ThreadLocalCache.getOperSubjectGuid())) {
            throw new MemberToolException("开启失败！请先绑定小程序");
        }
        if (messagesConfigVO.getWechatType() == 1) {
            String templateId = weChatMessagesUtil.getAppletTemplateId(messagesConfigVO, messagesConfig);
            messagesConfig.setAppletStatus(1);
            messagesConfig.setAppletTemplateId(templateId);
        } else if (messagesConfigVO.getWechatType() == 0) {
            String templateId = weChatMessagesUtil.getTemplateId(messagesConfigVO, messagesConfig);
            messagesConfig.setStatus(1);
            messagesConfig.setMsgTemplateId(templateId);
        } else {
            messagesConfig.setMessageStatus(1);
        }
    }


}
