package com.holderzone.member.mall.tool.service.unilink.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.MemberUnilinkSystemTypeEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.util.repast.RepastUtils;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemConfigVO;
import com.holderzone.member.mall.tool.entity.unilink.HsaMemberUnilinkSystemConfig;
import com.holderzone.member.mall.tool.mapper.unilink.HsaMemberUnilinkSystemConfigMapper;
import com.holderzone.member.mall.tool.service.unilink.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 会员通-系统配置Service实现
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Slf4j
@Service
public class HsaMemberUnilinkSystemConfigServiceImpl extends ServiceImpl<HsaMemberUnilinkSystemConfigMapper, HsaMemberUnilinkSystemConfig> implements HsaMemberUnilinkSystemConfigService {

    @Resource
    private HsaMemberUnilinkSystemChannelConfigService systemChannelConfigService;

    @Resource
    private HsaMemberUnilinkSystemTerminalConfigService systemTerminalConfigService;

    @Resource
    private HsaMemberUnilinkSystemBizConfigService bizConfigService;

    @Resource
    private HsaMemberUnilinkSystemService hsaMemberUnilinkSystemService;

    @Resource
    private Executor memberMallToolThreadExecutor;

    @Override
    public MemberUnilinkSystemConfigVO listConfig() {
        // 判断当前运营主体是否有REPAST餐饮云系统授权
        Integer system = ThreadLocalCache.getSystem();
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        boolean hasRepastAuth;

        if (Objects.equals(system, SystemEnum.REPAST.getCode())) {
            hasRepastAuth = RepastUtils.isHasRepastAuth(operSubjectGuid);
        } else {
            hasRepastAuth = hsaMemberUnilinkSystemService.listByOperSubject(operSubjectGuid)
                    .stream()
                    .anyMatch(systemVO -> systemVO.getSystemType() != null
                            && MemberUnilinkSystemTypeEnum.REPAST == systemVO.getSystemType());
        }

        // 使用CompletableFuture并行查询三个列表
        CompletableFuture<List<MemberUnilinkSelectOptionVO>> channelFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        return systemChannelConfigService.listSystemChannel(Boolean.TRUE);
                    } catch (Exception e) {
                        return Collections.emptyList();
                    }
                }, memberMallToolThreadExecutor);

        CompletableFuture<List<MemberUnilinkSelectOptionVO>> terminalFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        return systemTerminalConfigService.listSystemTerminal();
                    } catch (Exception e) {
                        return Collections.emptyList();
                    }
                }, memberMallToolThreadExecutor);

        CompletableFuture<List<MemberUnilinkSelectOptionVO>> bizFuture = CompletableFuture
                .supplyAsync(() -> {
                    try {
                        return bizConfigService.listSystemBiz();
                    } catch (Exception e) {
                        return Collections.emptyList();
                    }
                }, memberMallToolThreadExecutor);

        // 等待所有查询完成，设置超时时间
        List<MemberUnilinkSelectOptionVO> channelList;
        List<MemberUnilinkSelectOptionVO> terminalList;
        List<MemberUnilinkSelectOptionVO> bizList;
        try {
            channelList = channelFuture.get(5, TimeUnit.SECONDS);
            terminalList = terminalFuture.get(5, TimeUnit.SECONDS);
            bizList = bizFuture.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("查询系统配置列表异常", e);
            // 发生异常时返回空列表
            return new MemberUnilinkSystemConfigVO()
                    .setChannel(Collections.emptyList())
                    .setTerminal(Collections.emptyList())
                    .setBiz(Collections.emptyList());
        }

        // 如果来源是餐饮云，且未授权餐饮云，则仅返回餐饮云系统配置
        if (Objects.equals(system, SystemEnum.REPAST.getCode()) && !hasRepastAuth) {
            String repastType = MemberUnilinkSystemTypeEnum.REPAST.name();
            channelList = channelList.stream()
                    .filter(x -> repastType.equals(x.getSystemType()))
                    .collect(Collectors.toList());
            terminalList = terminalList.stream()
                    .filter(x -> repastType.equals(x.getSystemType()))
                    .collect(Collectors.toList());
            bizList = bizList.stream()
                    .filter(x -> repastType.equals(x.getSystemType()))
                    .collect(Collectors.toList());
        }

        // 如果来源不是餐饮云，且未授权餐饮云，则返回排除餐饮云配置
        if (!Objects.equals(system, SystemEnum.REPAST.getCode()) && !hasRepastAuth) {
            String repastType = MemberUnilinkSystemTypeEnum.REPAST.name();
            channelList = channelList.stream()
                    .filter(x -> x.getSystemType() == null || !repastType.equals(x.getSystemType()))
                    .collect(Collectors.toList());
            terminalList = terminalList.stream()
                    .filter(x -> x.getSystemType() == null || !repastType.equals(x.getSystemType()))
                    .collect(Collectors.toList());
            bizList = bizList.stream()
                    .filter(x -> x.getSystemType() == null || !repastType.equals(x.getSystemType()))
                    .collect(Collectors.toList());
        }

        // 构建返回结果
        return new MemberUnilinkSystemConfigVO()
                .setChannel(Optional.ofNullable(channelList).orElse(Collections.emptyList()))
                .setTerminal(Optional.ofNullable(terminalList).orElse(Collections.emptyList()))
                .setBiz(Optional.ofNullable(bizList).orElse(Collections.emptyList()));
    }
}