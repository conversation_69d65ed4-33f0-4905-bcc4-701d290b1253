package com.holderzone.member.settlement.service.rule.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.ItemNum;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.module.settlement.rule.dto.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountAppendEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.qo.DeleteSettlementRuleDiscountQO;
import com.holderzone.member.common.module.settlement.rule.qo.SettlementRuleDiscountQO;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountTreeVO;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountVO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.settlement.assembler.SettlementRuleAssembler;
import com.holderzone.member.settlement.entity.rule.HsaSettlementDiscount;
import com.holderzone.member.settlement.entity.rule.HsaSettlementRuleDiscount;
import com.holderzone.member.settlement.mapper.rule.HsaSettlementRuleDiscountMapper;
import com.holderzone.member.settlement.service.rule.IHsaSettlementDiscountService;
import com.holderzone.member.settlement.service.rule.IHsaSettlementRuleDiscountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 结算规则已选优惠项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Service
@Slf4j
public class HsaSettlementRuleDiscountServiceImpl extends HolderBaseServiceImpl<HsaSettlementRuleDiscountMapper, HsaSettlementRuleDiscount> implements IHsaSettlementRuleDiscountService {

    /**
     * 规则优惠mapper
     */
    @Resource
    HsaSettlementRuleDiscountMapper settlementRuleDiscountMapper;

    /**
     * 结算优惠
     */
    @Lazy
    @Resource
    IHsaSettlementDiscountService settlementDiscountService;

    /**
     * 生成guid
     */
    @Resource
    GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private MemberMarketingFeign marketingFeign;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Override
    public List<SettlementRuleDiscountTreeVO> listAll(SettlementRuleDiscountQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //优惠项列表
        List<SettlementRuleDiscountTreeVO> list = settlementRuleDiscountMapper.list(qo);
        //去掉discountGuid为空的
        list.removeIf(l -> "".equals(l.getDiscountGuid()));
        return list;
    }

    @Override
    public List<String> getParnetVoList(SettlementRuleDiscountQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //优惠项列表
        List<SettlementRuleDiscountTreeVO> list = settlementRuleDiscountMapper.list(qo);
        //第一层
        final List<SettlementRuleDiscountTreeVO> parnetVoList = list.stream()
                .filter(l -> StringConstant.PARENT.equals(l.getParentGuid()))
                .collect(Collectors.toList());

        return parnetVoList.stream().map(SettlementRuleDiscountVO::getGuid).collect(Collectors.toList());

    }

    @Override
    public List<SettlementRuleDiscountTreeVO> listAllTree(SettlementRuleDiscountQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //优惠项列表
        List<SettlementRuleDiscountTreeVO> list = settlementRuleDiscountMapper.list(qo);
        //第一层
        final List<SettlementRuleDiscountTreeVO> parnetVoList = list.stream()
                .filter(l -> StringConstant.PARENT.equals(l.getParentGuid()))
                .collect(Collectors.toList());

        List<SettlementRuleDiscountTreeVO> settlementRuleDiscountTreeVOS = parnetVoList.stream()
                .filter(l -> l.getDiscountOption() == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode()).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(settlementRuleDiscountTreeVOS)) {
            parnetVoList.removeIf(l -> l.getDiscountOption() == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode());
            SettlementRuleDiscountTreeVO discountTreeVO = settlementRuleDiscountTreeVOS.get(0);
            parnetVoList.add(NumberConstant.NUMBER_0, discountTreeVO);
        }

        //查询新赠的优惠项的父级
        addAllNewImportParentList(list, parnetVoList, qo.getSettlementRuleGuid());
        if (CollUtil.isEmpty(parnetVoList)) {
            return Collections.emptyList();
        }
        final List<Integer> noShowDiscountGuidOptions = SettlementDiscountOptionEnum.noShowDiscountGuid();
        List<SettlementRuleDiscountTreeVO> noChildrenList = Lists.newArrayList();
        for (SettlementRuleDiscountTreeVO parentVo : parnetVoList) {
            //不显示discountGuid 的置为空
            parentVo.nullDiscountGuid(noShowDiscountGuidOptions);
            //没有二层
            final boolean noChildren = SettlementDiscountOptionEnum.noChildren(parentVo.getDiscountOption());
            if (noChildren) {
                //第一层不返回0
                parentVo.setParentGuid(parentVo.getGuid());
                continue;
            }
            //构造第二层
            buildDiscountTreeChildren(list, noShowDiscountGuidOptions, noChildrenList, parentVo);
        }
        //移除第二层没有的
        parnetVoList.removeAll(noChildrenList);
        return parnetVoList;
    }

    /**
     * 无父级的数据，增加父级
     *
     * @param list           优惠项列表
     * @param parnetVoList   父级优惠项
     * @param settlementGuid 规则
     */
    private void addAllNewImportParentList(List<SettlementRuleDiscountTreeVO> list,
                                           List<SettlementRuleDiscountTreeVO> parnetVoList,
                                           String settlementGuid) {
        if (StringUtils.isBlank(settlementGuid)) {
            return;
        }
        //新增的优惠项
        final Set<String> newImportParents = list.stream()
                .filter(l -> l.getIsAppend() == SettlementDiscountAppendEnum.NEW_IMPORT.getCode())
                .map(SettlementRuleDiscountTreeVO::getParentGuid)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(newImportParents)) {
            return;
        }
        //排除已有父级
        final List<String> hasParentGuids = parnetVoList.stream().map(SettlementRuleDiscountVO::getGuid).collect(Collectors.toList());
        final LambdaQueryWrapper<HsaSettlementDiscount> parentWrapper = new LambdaQueryWrapper<HsaSettlementDiscount>()
                .in(HsaSettlementDiscount::getGuid, newImportParents)
                .notIn(CollUtil.isNotEmpty(hasParentGuids), HsaBaseEntity::getGuid, hasParentGuids);
        //找到父级
        final List<HsaSettlementDiscount> parnetList = settlementDiscountService.list(parentWrapper);
        if (CollUtil.isEmpty(parnetList)) {
            return;
        }
        List<SettlementRuleDiscountTreeVO> newParentVoList = SettlementRuleAssembler.toSettlementRuleDiscountTreeVOList(parnetList);
        parnetVoList.addAll(newParentVoList);
    }

    /**
     * 构造优惠树
     *
     * @param list                      优惠列表
     * @param noShowDiscountGuidOptions 不显示 disocuntGuid
     * @param noChildrenList            无子级
     * @param parentVo                  父级
     */
    private static void buildDiscountTreeChildren(List<SettlementRuleDiscountTreeVO> list,
                                                  List<Integer> noShowDiscountGuidOptions,
                                                  List<SettlementRuleDiscountTreeVO> noChildrenList,
                                                  SettlementRuleDiscountTreeVO parentVo) {
        //第二层
        final List<SettlementRuleDiscountTreeVO> children =
                list.stream()
                        .filter(l -> parentVo.getGuid().equals(l.getParentGuid()))
                        .collect(Collectors.toList());
        if (CollUtil.isEmpty(children)) {
            noChildrenList.add(parentVo);
        } else {
            //不返回discountGuid
            children.forEach(v -> v.nullDiscountGuid(noShowDiscountGuidOptions));
            parentVo.setChildren(children);
            parentVo.setChildNum(children.size());
            //第一层不返回0
            parentVo.setParentGuid(parentVo.getGuid());
        }
    }

    @Override
    public List<SettlementRuleDiscountTreeVO> tree(SettlementRuleDiscountQO qo) {
        //查询指定层级
        final String parentGuid = StringUtils.isEmpty(qo.getParentGuid()) ? StringConstant.PARENT : qo.getParentGuid();
        qo.setParentGuid(parentGuid);
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //优惠项
        List<SettlementRuleDiscountTreeVO> list = settlementRuleDiscountMapper.list(qo);
        if (!StringConstant.PARENT.equals(qo.getParentGuid())) {
            return list;
        }
        final List<String> settlementDiscountGuidList = list.stream()
                .map(SettlementRuleDiscountTreeVO::getGuid)
                .collect(Collectors.toList());
        qo.setSettlementDiscountGuidList(settlementDiscountGuidList);
        //查询第二层的记录数量
        List<ItemNum> itemNumList = settlementRuleDiscountMapper.countItem(qo);
        if (CollUtil.isEmpty(itemNumList)) {
            return list;
        }
        final Map<String, Integer> childCountMap = itemNumList.stream()
                .collect(Collectors.toMap(ItemNum::getItem, ItemNum::getNum));
        int i = 1;
        for (SettlementRuleDiscountVO l : list) {
            //排序号
            l.setRank(i++);
            final String settlementDiscountGuid = l.getGuid();
            //填充第二层数量
            final Integer childNum = childCountMap.getOrDefault(settlementDiscountGuid, 0);
            l.setChildNum(childNum);
        }
        return list;
    }

    @Override
    public void save(SettlementRuleDTO dto) {
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        final SettlementRuleBaseDTO baseRule = dto.getBaseRule();
        final String settlementRuleGuid = baseRule.getGuid();
        final boolean isUpdate = dto.getBaseRule().getIsUpdate() || StringUtils.isNoneBlank(settlementRuleGuid);

        //组装前端传递的所有规则
        final List<HsaSettlementRuleDiscount> ruleDiscounts = toHsaSettlementRuleDiscounts(dto);
        if (CollUtil.isEmpty(ruleDiscounts)) {
            //先删除多余父级（父级需要排序才存的）
            settlementRuleDiscountMapper.deleteParent(operSubjectGuid, settlementRuleGuid);
            //全部改为不可叠加
            settlementRuleDiscountMapper.updateAppend(settlementRuleGuid, BooleanEnum.FALSE.getCode());
            return;
        }
        //同步互斥共享状态
        synDiscount(dto);

        if (isUpdate) {
            //先删再加
            removeDiscount(operSubjectGuid, settlementRuleGuid);
        }
        //目前存在的规则项
        Set<String> settlementDiscountGuids = settlementDiscountService.listGuidsSet(operSubjectGuid);
        //移除不存在的(前端未及时刷新的数据)
        ruleDiscounts.removeIf(r -> !settlementDiscountGuids.contains(r.getSettlementDiscountGuid()));
        if (ruleDiscounts.isEmpty()) {
            //所有优惠项都没了？
            return;
        }
        //纯新增
        setHsaSettlementRuleDiscountsGuid(ruleDiscounts);
        this.saveBatch(ruleDiscounts, 2000);
        log.info("保存结算规则优惠项成功");
    }

    private void synDiscount(SettlementRuleDTO dto) {
        if (dto.getBaseRule().getIsDefault() == BooleanEnum.FALSE.getCode()) {
            return;
        }
        List<SettlementSynDiscountDTO> synDiscounts = Lists.newArrayList();
        //可叠加
        if (CollUtil.isNotEmpty(dto.getAppendDiscounts())) {
            addSynDiscounts(dto.getAppendDiscounts(), synDiscounts, BooleanEnum.TRUE.getCode());
        }
        //不可叠加
        if (CollUtil.isNotEmpty(dto.getDisAppendDiscounts())) {
            addSynDiscounts(dto.getDisAppendDiscounts(), synDiscounts, BooleanEnum.FALSE.getCode());
        }
        groupSynByOption(synDiscounts, dto);
    }

    private void groupSynByOption(List<SettlementSynDiscountDTO> synDiscounts,
                                  SettlementRuleDTO dto) {
        if (CollUtil.isNotEmpty(synDiscounts)) {

            //区分类型
            List<Integer> discountOption = synDiscounts.stream().map(SettlementSynDiscountDTO::getDiscountOption).distinct()
                    .collect(Collectors.toList());

            Map<Integer, List<SettlementSynDiscountDTO>> settlementSynDiscountMap = synDiscounts.stream()
                    .collect(Collectors.groupingBy(SettlementSynDiscountDTO::getDiscountOption));

            for (Integer option : discountOption) {
                if (settlementSynDiscountMap.containsKey(option)) {
                    List<SettlementSynDiscountDTO> settlementSynDiscountDTOList = settlementSynDiscountMap.get(option);
                    SettlementDiscountOptionEnum anEnum = SettlementDiscountOptionEnum.getEnum(option);

                    SettlementSynMarketingDTO discountOptionSynDTO = new SettlementSynMarketingDTO();
                    discountOptionSynDTO.setSynDiscounts(settlementSynDiscountDTOList);
                    discountOptionSynDTO.setOperSubjectGuid(dto.getBaseRule().getOperSubjectGuid());
                    discountOptionSynDTO.setAnEnum(anEnum);
                    switch (Objects.requireNonNull(anEnum)) {
                        case COUPON_VOUCHER:
                        case COUPON_DISCOUNT:
                        case COUPON_EXCHANGE:
                            marketingFeign.synCouponRelationRule(discountOptionSynDTO);
                            break;
                        case FULL_OFF:
                        case LIMITED_TIME_SPECIAL:
                        case NTH_DISCOUNT:
                            marketingFeign.synActivityRelationRule(discountOptionSynDTO);
                            break;
                        case MEMBER_DISCOUNT:
                            memberBaseFeign.updateMemberDiscountRelationRule(discountOptionSynDTO);
                            break;
                        case INTEGRAL_EXPLAIN:
                            memberBaseFeign.updateRelationRule(discountOptionSynDTO);
                            break;
                        default:
                            log.info("结算台优惠类型错误");
                    }
                }

            }


        }
    }

    private static void addSynDiscounts(List<SettlementRuleDiscountDTO> settlementRuleDiscountDTOS,
                                        List<SettlementSynDiscountDTO> synDiscounts,
                                        Integer code) {

        for (SettlementRuleDiscountDTO discount : settlementRuleDiscountDTOS) {
            if (CollUtil.isNotEmpty(discount.getChildren())) {
                for (SettlementRuleDiscountDTO child : discount.getChildren()) {
                    SettlementSynDiscountDTO settlementSynDiscountDTO = new SettlementSynDiscountDTO();
                    BeanUtils.copyProperties(child, settlementSynDiscountDTO);
                    settlementSynDiscountDTO.setRelationRule(code);
                    synDiscounts.add(settlementSynDiscountDTO);
                }
            } else {
                SettlementSynDiscountDTO settlementSynDiscountDTO = new SettlementSynDiscountDTO();
                BeanUtils.copyProperties(discount, settlementSynDiscountDTO);
                settlementSynDiscountDTO.setRelationRule(code);
                synDiscounts.add(settlementSynDiscountDTO);
            }
        }
    }

    /**
     * 移除优惠
     *
     * @param operSubjectGuid    主体
     * @param settlementRuleGuid 规则
     */
    private void removeDiscount(String operSubjectGuid, String settlementRuleGuid) {
        //删除
        this.remove(
                new LambdaQueryWrapper<HsaSettlementRuleDiscount>()
                        .eq(HsaSettlementRuleDiscount::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaSettlementRuleDiscount::getSettlementRuleGuid, settlementRuleGuid)
        );
    }

    /**
     * 设置优惠guid
     *
     * @param ruleDiscounts 优惠项
     */
    private void setHsaSettlementRuleDiscountsGuid(List<HsaSettlementRuleDiscount> ruleDiscounts) {
        final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaSettlementRuleDiscount.class.getSimpleName(), ruleDiscounts.size());
        for (int i = 0; i < ruleDiscounts.size(); i++) {
            ruleDiscounts.get(i).setGuid(guids.get(i));
        }
    }

    /**
     * 转换为优惠list
     *
     * @param dto 规则对象
     * @return 规则项
     */
    @Override
    public List<HsaSettlementRuleDiscount> toHsaSettlementRuleDiscounts(SettlementRuleDTO dto) {
        final SettlementRuleBaseDTO baseRule = dto.getBaseRule();
        List<HsaSettlementRuleDiscount> voList = new ArrayList<>();
        //可叠加
        if (CollUtil.isNotEmpty(dto.getAppendDiscounts())) {
            toHsaSettlementRuleDiscounts(baseRule, dto.getAppendDiscounts(), voList, BooleanEnum.TRUE.getCode());
        }
        //不可叠加
        if (CollUtil.isNotEmpty(dto.getDisAppendDiscounts())) {
            toHsaSettlementRuleDiscounts(baseRule, dto.getDisAppendDiscounts(), voList, BooleanEnum.FALSE.getCode());
        }
        return voList;
    }

    /**
     * 转换单个优惠对象
     *
     * @param baseRule        规则
     * @param discountDTOList 优惠列表
     * @param ruleDiscounts   规则优惠项
     * @param isAppend        是否叠加
     */
    private void toHsaSettlementRuleDiscounts(SettlementRuleBaseDTO baseRule,
                                              List<SettlementRuleDiscountDTO> discountDTOList,
                                              List<HsaSettlementRuleDiscount> ruleDiscounts,
                                              int isAppend) {
        final String settlementRuleGuid = baseRule.getGuid();
        List<String> firstSettlementDiscountGuidList = new ArrayList<>();
        for (SettlementRuleDiscountDTO discountDTO : discountDTOList) {
            //组装第一层
            ruleDiscounts.add(SettlementRuleAssembler.toHsaSettlementRuleDiscount(discountDTO, isAppend, baseRule));
            final List<SettlementRuleDiscountDTO> children = discountDTO.getChildren();
            //子级
            if (CollUtil.isEmpty(children)) {
                //只有第一层的
                firstSettlementDiscountGuidList.add(discountDTO.getGuid());
                continue;
            }
            //组装第二层
            for (SettlementRuleDiscountDTO child : children) {
                ruleDiscounts.add(SettlementRuleAssembler.toHsaSettlementRuleDiscount(child, isAppend, baseRule));
            }
        }
        //通过第一层去查第二层
        if (CollUtil.isNotEmpty(firstSettlementDiscountGuidList)) {
            List<HsaSettlementDiscount> discountList = settlementDiscountService.getChildren(firstSettlementDiscountGuidList);
            if (CollUtil.isEmpty(discountList)) {
                return;
            }
            //组装对象
            for (HsaSettlementDiscount hsaSettlementDiscount : discountList) {
                ruleDiscounts.add(SettlementRuleAssembler.toHsaSettlementRuleDiscount(hsaSettlementDiscount, settlementRuleGuid, isAppend));
            }
        }
    }

    @Override
    public boolean deleteByRuleGuid(String guid) {
        return this.remove(new LambdaQueryWrapper<HsaSettlementRuleDiscount>()
                .eq(HsaSettlementRuleDiscount::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaSettlementRuleDiscount::getSettlementRuleGuid, guid)
        );
    }

    @Override
    public void deleteAppend(DeleteSettlementRuleDiscountQO qo) {
        baseMapper.deleteAppend(qo.getSettlementRuleGuid(), qo.getNewNotAdditivitySettlementDiscount(), qo.getIsAppend());
    }
}
