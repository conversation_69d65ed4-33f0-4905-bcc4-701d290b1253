package com.holderzone.member.settlement.service.apply.support;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.exception.MemberSettlementException;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.settlement.apply.dto.*;
import com.holderzone.member.common.module.settlement.apply.enums.SettlementCheckGroupEnum;
import com.holderzone.member.common.module.settlement.apply.enums.SettlementDiscountShowTypeEnum;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.apply.vo.detail.SettlementApplyDiscountDetailOfCouponVO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.settlement.service.apply.util.SettlementBusinessUtil;
import com.holderzone.member.settlement.service.handle.SettlementCalculateSupport;
import com.holderzone.member.settlement.service.handle.SettlementListSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠项计算
 *
 * <AUTHOR>
 * @date 2023/10/17
 * @since 1.8
 */
@AllArgsConstructor
@Component
@Slf4j
public class SettlementApplySupport {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private SettlementCalculateSupport settlementCalculateSupport;

    @Resource
    private SettlementListSupport settlementListSupport;


    /**
     * 自动选择，最优 :不可叠加的，或可叠加只有一个
     *
     * @param dto       订单入参
     * @param applyRule 结算规则
     * @return 订单计算结果
     */
    public SettlementApplyOrderVO autoSingleCheckDiscount(SettlementApplyOrderDTO dto, SettlementApplyRuleVO applyRule) {
        //可用优惠
        final List<SettlementApplyOrderVO> discountVoList = memberBaseFeign.listDiscount(dto);
        if (CollUtil.isEmpty(discountVoList)) {
            return SettlementApplyOrderVO.buildEmpty(applyRule);
        }

        //按规则项排序
        final Map<Integer, List<SettlementApplyOrderVO>> discountVoMap = getDiscountVoMap(applyRule, discountVoList);


        //最优项逻辑
        final SettlementApplyOrderVO bestOptimizeOrderVo = settlementListSupport.getOptimizeCheckOrder(applyRule, dto, discountVoMap);

        //公共处理
        return handlerOrderVoCommon(applyRule, bestOptimizeOrderVo);
    }


    /**
     * 计算最优
     * <p>
     * 1、计算每个单项，对比最优。
     * 若单选直接扣完应付金额，直接返回单选
     * <p>
     * 2、根据前端展示顺序计算叠加，SettlementDiscountShowTypeEnum 且在叠加项中的，来计算，
     * 结算结果：第一个计算不存在，取第二个，第二个不存在取第三个，依次来推..
     * 若单选结果大于叠加，优先单选
     * <p>
     * 3、展示：如果未选中的优惠是叠加的，且在已选择的后面。
     * 未选中的优惠项的优惠金额需减去已选择的来进行联动展示
     *
     * @param dto       订单入参
     * @param applyRule 规则
     * @return 应用结果
     */
    public SettlementApplyOrderVO autoCheckDiscount(SettlementApplyOrderDTO dto, SettlementApplyRuleVO applyRule) {
        //单选第一个最优
        SettlementApplyOrderVO singleOrderVO = autoSingleCheckDiscount(dto, applyRule);
        if (singleOrderVO == null) {
            return SettlementApplyOrderVO.buildEmpty(applyRule);
        }
        log.info("自动核销最优结果：{}", JSON.toJSONString(singleOrderVO));

        //直接返回了
        return singleOrderVO;
    }

    /**
     * 构造计算对象
     *
     * @param dto 订单入参
     * @return 计算对象
     */
    private SettlementApplyOrderCalculateDTO buildApplyOrderCalculateDTO(SettlementApplyOrderDTO dto) {
        SettlementApplyOrderCalculateDTO calculateDTO = new SettlementApplyOrderCalculateDTO();
        //已选择项
        final List<Integer> checkOptions = getCheckOptions(dto);
        calculateDTO.setOptionHandlers(checkOptions);
        BeanUtils.copyProperties(dto, calculateDTO);
        return calculateDTO;
    }


    /**
     * 手动多选: 叠加、不可叠加
     *
     * @param applyRule 应用规则
     * @param dto       订单入参
     * @return 订单应用结果对象
     */
    public SettlementApplyOrderVO handCheckOption(SettlementApplyRuleVO applyRule, SettlementApplyOrderDTO dto) {
        //已选择项
        SettlementApplyOrderCalculateDTO calculateDTO = buildApplyOrderCalculateDTO(dto);

        //可用优惠 [选中的项，以及相同类型的子项都返回来]
        final List<SettlementApplyOrderVO> discountVoList = memberBaseFeign.calculateDiscount(calculateDTO);


        if (CollUtil.isEmpty(discountVoList)) {
            return SettlementApplyOrderVO.buildEmpty(applyRule);
        }
        //按规则项排序
        final Map<Integer, List<SettlementApplyOrderVO>> discountVoMap = getDiscountVoMap(applyRule, discountVoList);

        //计算优惠项列表
        Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap = getCalculateDiscountList(applyRule, dto);

        //指定优惠计算
        final SettlementApplyOrderVO orderVO = settlementCalculateSupport.appointOptimizeDiscount(dto, applyRule, discountVoMap, settlementApplyOrderMap);

        //公共处理
        return handlerOrderVoCommon(applyRule, orderVO);
    }

    private Map<Integer, List<SettlementApplyOrderVO>> getCalculateDiscountList(SettlementApplyRuleVO applyRule, SettlementApplyOrderDTO dto) {
        SettlementApplyOrderDTO orderDTO = SettlementBusinessUtil.getSettlementApplyOrderDTO(dto);
        return getDiscountVoMap(applyRule, memberBaseFeign.listDiscount(orderDTO));
    }


    private static Map<Integer, List<SettlementApplyOrderVO>> getDiscountVoMap(SettlementApplyRuleVO applyRule, List<SettlementApplyOrderVO> discountVoList) {
        final List<Integer> allOptions = applyRule.getAllDiscountOptions();
        List<SettlementApplyOrderVO> applyDiscountVOList = new ArrayList<>(discountVoList.size());
        for (Integer sortOption : allOptions) {
            discountVoList.stream()
                    .filter(s -> CollUtil.isNotEmpty(s.getDiscountList()) 
                            && s.getDiscountList().get(NumberConstant.NUMBER_0) != null
                            && Objects.equals(s.getDiscountList().get(NumberConstant.NUMBER_0).getDiscountOption(), sortOption))
                    .forEach(applyDiscountVOList::add);
        }
        //排序后分组
        return applyDiscountVOList.stream()
                .filter(v -> CollUtil.isNotEmpty(v.getDiscountList()) && v.getDiscountList().get(NumberConstant.NUMBER_0) != null)
                .collect(Collectors.groupingBy(v ->
                                v.getDiscountList().get(NumberConstant.NUMBER_0).getDiscountOption(),
                        LinkedHashMap::new,
                        Collectors.toList()));
    }

    /**
     * 返回对象公共处理
     *
     * @param applyRule 应用规则
     * @param orderVO   订单结果
     */
    public SettlementApplyOrderVO handlerOrderVoCommon(SettlementApplyRuleVO applyRule, SettlementApplyOrderVO orderVO) {

        sortAllDiscount(applyRule, orderVO);
        //订单 优惠券张数
        orderVO.getOderInfo().setCouponLimitNum(applyRule.getBaseRule().couponLimitNum());
        orderVO.getOderInfo().setCouponLimit(applyRule.getBaseRule().getCouponLimit());

        //处理优惠券限制数量
        handlerVoCouponLimitNum(applyRule, orderVO.getDiscountList());
        //处理叠加标志
        SettlementBusinessUtil.handlerAppendSign(applyRule, orderVO.getDiscountList());

        orderVO.setApplyRule(applyRule);
        return orderVO;
    }

    /**
     * 返回对象公共处理
     *
     * @param orderVO   订单结果
     */
    public SettlementApplyOrderVO handlerOrderVoUnableRuleCommon(SettlementApplyOrderVO orderVO) {
        //处理优惠券限制数量
        handlerVoCouponLimitNum(orderVO.getDiscountList());
        return orderVO;
    }

    /**
     * 优惠项排序展示
     *
     * @param applyRule 应用规则
     * @param orderVO   返回订单
     */
    private void sortAllDiscount(SettlementApplyRuleVO applyRule,
                                 SettlementApplyOrderVO orderVO) {
        //排序规则
        final List<Integer> allSortOptions = applyRule.getAllDiscountOptions();
        //按规则项排序
        final List<SettlementApplyDiscountVO> sortVoList = sorted(allSortOptions, orderVO.getDiscountList());
        orderVO.setDiscountList(sortVoList);
    }

    /**
     * 选择项已经按结算规则排序过
     *
     * @param dto 订单入参
     * @return 选中项类型
     */
    private List<Integer> getCheckOptions(SettlementApplyOrderDTO dto) {
        //按顺序添加 并去重
        Set<Integer> checkOptionsSet = new LinkedHashSet<>();
        for (SettlementApplyDiscountBaseReqDTO reqDTO : dto.getCheckDiscountList()) {
            final Integer discountOption = reqDTO.getDiscountOption();
            checkOptionsSet.add(discountOption);
        }
        //已选择 叠加优惠项
        return Lists.newArrayList(checkOptionsSet);
    }

    /**
     * 选中并合并计算所有优惠
     *
     * @param orderVoList 所有优惠结果
     * @param dto         订单入参
     * @return 合并结果
     */
    public SettlementApplyOrderVO checkCalculateDiscount(List<SettlementApplyOrderVO> orderVoList, SettlementApplyOrderDTO dto) {
        //返回值
        SettlementApplyOrderVO resultOrderVo = SettlementApplyOrderVO.buildEmpty();
        if (orderVoList.isEmpty()) {
            return resultOrderVo;
        }
        for (SettlementApplyOrderVO tempOrderVo : orderVoList) {
            //发回订单
            final SettlementApplyOderInfoVO resultOrderInfo = resultOrderVo.getOderInfo();

            final SettlementApplyOderInfoVO tempOrderInfo = tempOrderVo.getOderInfo();
            //为空代表没选中，先不加
            if (Objects.isNull(tempOrderInfo)
                    || tempOrderInfo.getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //订单优惠累计
            resultOrderInfo.setDiscountAmount(resultOrderInfo.getDiscountAmount().add(tempOrderInfo.getDiscountAmount()));

            //优惠项累计
            final List<SettlementApplyDiscountVO> tempDiscountList = tempOrderVo.getDiscountList();
            //每个大类只返回一条
            resultOrderVo.getDiscountList().addAll(tempDiscountList);
        }

        int size = orderVoList.size() - 1;
        for (; size >= 0; size--) {
            //最后一个优惠的商品
            final List<SettlementApplyCommodityVO> commodityList = orderVoList.get(size).getCommodityList();
            if (CollUtil.isNotEmpty(commodityList)) {
                //商品分摊
                resultOrderVo.setCommodityList(commodityList);
                break;
            }
        }
        //商品总价
        resultOrderVo.getOderInfo().setCommodityTotalAmount(dto.getOrderInfo().getCommodityTotalAmount());
        return resultOrderVo;
    }


    /**
     * 指定顺序
     *
     * @param sortOptions 优惠项排序
     * @return 优惠排序结果
     */
    public List<SettlementApplyDiscountVO> sorted(List<Integer> sortOptions,
                                                  List<SettlementApplyDiscountVO> discountVOList) {
        if (CollUtil.isEmpty(sortOptions)) {
            return discountVOList;
        }
        List<SettlementApplyDiscountVO> applyDiscountVOList = new ArrayList<>(sortOptions.size());
        for (Integer sortOption : sortOptions) {
            discountVOList.stream()
                    .filter(s -> Objects.equals(s.getDiscountOption(), sortOption))
                    .forEach(applyDiscountVOList::add);
        }
        return applyDiscountVOList;
    }

    /**
     * 处理优惠券限制数量
     *
     * @param applyRule      规则
     * @param discountVOList 优惠券
     */
    public void handlerVoCouponLimitNum(SettlementApplyRuleVO
                                                applyRule, List<SettlementApplyDiscountVO> discountVOList) {
        //优惠券限制数量 > 1 的更新
        final Map<String, Integer> couponLimtNumberMap = applyRule.getCouponLimtNumberMap();
        if (!couponLimtNumberMap.isEmpty()) {
            discountVOList.parallelStream()
                    .filter(d -> SettlementDiscountOptionEnum.isCoupon(d.getDiscountOption()))
                    .flatMap(d -> d.getDiscountList().stream())
                    .forEach(d -> {
                        final String key = SettlementDiscountOptionEnum.optionMapKey(d.getDiscountOption(), d.getDiscountGuid());
                        //优惠券限制数量
                        Optional.ofNullable(couponLimtNumberMap.get(key))
                                .ifPresent(num ->
                                        ((SettlementApplyDiscountDetailOfCouponVO) d).setCouponLimitNum(num)
                                );
                    });
        }
    }

    public void handlerVoCouponLimitNum(List<SettlementApplyDiscountVO> discountVOList) {
        //优惠券限制数量的更新
        discountVOList.parallelStream()
                .filter(d -> SettlementDiscountOptionEnum.isCoupon(d.getDiscountOption()))
                .flatMap(d -> d.getDiscountList().stream())
                .forEach(d -> ((SettlementApplyDiscountDetailOfCouponVO) d)
                        .setCouponLimitNum(((SettlementApplyDiscountDetailOfCouponVO) d).getSingleOrderUsedLimit()));
    }
}
