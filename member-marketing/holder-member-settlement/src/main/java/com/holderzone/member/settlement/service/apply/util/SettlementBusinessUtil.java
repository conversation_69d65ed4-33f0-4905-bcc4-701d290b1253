package com.holderzone.member.settlement.service.apply.util;

import cn.hutool.core.collection.CollUtil;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementAfterApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyCommodityDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountTreeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算台业务公用类
 */
public class SettlementBusinessUtil {

    private SettlementBusinessUtil() {
        //default
    }

    /**
     * 处理叠加标志
     *
     * @param applyRule      结算规则
     * @param discountVOList 优惠项
     */
    public static void handlerAppendSign(SettlementApplyRuleVO applyRule, List<SettlementApplyDiscountVO> discountVOList) {
        if (CollUtil.isEmpty(discountVOList)) {
            return;
        }
        Optional.ofNullable(applyRule.getAppendDiscounts())
                .ifPresent(appendDiscounts ->
                        //设置叠加项
                        setDiscountVoAppend(discountVOList, appendDiscounts)
                );
    }

    public static void handlerAppendSignNew(SettlementApplyRuleVO applyRule, SettlementApplyDiscountDetailVO discountDetailVO) {
        if (Objects.isNull(discountDetailVO)) {
            return;
        }
        Optional.ofNullable(applyRule.getAppendDiscounts())
                .ifPresent(appendDiscounts ->
                        //设置叠加项
                        setDiscountDetailVoAppend(discountDetailVO, appendDiscounts)
                );
    }

    /**
     * 设置单个优惠详情的叠加项
     *
     * @param discountDetailVO  优惠详情
     * @param appendDiscounts 叠加数据
     */
    private static void setDiscountDetailVoAppend(SettlementApplyDiscountDetailVO discountDetailVO,
                                                  List<SettlementRuleDiscountTreeVO> appendDiscounts) {
        final Set<String> optionDiscountGuidSet = appendDiscounts.stream()
                .map(d -> SettlementDiscountOptionEnum.optionMapKey(d.getDiscountOption(), d.getDiscountGuid()))
                .collect(Collectors.toSet());

        final String optionKey = SettlementDiscountOptionEnum.optionMapKey(
                discountDetailVO.getDiscountOption(),
                discountDetailVO.getDiscountGuid()
        );

        //可叠加
        if (optionDiscountGuidSet.contains(optionKey)) {
            discountDetailVO.setIsAppend(BooleanEnum.TRUE.getCode());
        }
    }

    /**
     * 设置叠加项
     *
     * @param discountVOList  优惠项
     * @param appendDiscounts 叠加数据
     */
    private static void setDiscountVoAppend(List<SettlementApplyDiscountVO> discountVOList,
                                            List<SettlementRuleDiscountTreeVO> appendDiscounts) {
        final Set<String> optionDiscountGuidSet = appendDiscounts.stream()
                .map(d -> SettlementDiscountOptionEnum.optionMapKey(d.getDiscountOption(), d.getDiscountGuid()))
                .collect(Collectors.toSet());
        discountVOList.parallelStream().forEach(d -> {
            if (CollUtil.isNotEmpty(d.getDiscountList())) {
                d.getDiscountList().forEach(s -> {
                    final String optionKey = SettlementDiscountOptionEnum.optionMapKey(s.getDiscountOption(), s.getDiscountGuid());
                    //可叠加
                    if (optionDiscountGuidSet.contains(optionKey)) {
                        s.setIsAppend(BooleanEnum.TRUE.getCode());
                    }
                });
            }
        });
    }


    /**
     * 最优商品合并
     */
    public static void addOptimizeCommodityList(List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                                SettlementApplyOrderVO settlementApplyOrderVO) {
        List<SettlementApplyShareCommodityVO> commodityShareList = Lists.newArrayList();
        for (SettlementApplyDiscountDetailVO optimizeDiscountDetailVO : optimizeDiscountDetailVOS) {
            if (optimizeDiscountDetailVO.getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            SettlementApplyShareCommodityVO settlementApplyShareCommodityVO = new SettlementApplyShareCommodityVO();
            settlementApplyShareCommodityVO.setDiscountOption(optimizeDiscountDetailVO.getDiscountOption());
            settlementApplyShareCommodityVO.setDiscountGuid(optimizeDiscountDetailVO.getDiscountGuid());

            //优惠券需要额外增加字段 暂时只有折扣券
            if (SettlementDiscountOptionEnum.isCoupon(optimizeDiscountDetailVO.getDiscountOption())) {
                settlementApplyShareCommodityVO.setDiscountOptionId(optimizeDiscountDetailVO.getDiscountOptionId());
                settlementApplyShareCommodityVO.setDiscountName(optimizeDiscountDetailVO.getDiscountName());
            } else {
                settlementApplyShareCommodityVO.setDiscountName(SettlementDiscountOptionEnum.getDesByCode(optimizeDiscountDetailVO.getDiscountOption()));
                // 具体活动单独拿了个字段存 注意区分！！！主要零售已经对接过了。不好重新赋值,妥协加个字段
                settlementApplyShareCommodityVO.setDiscountDesc(optimizeDiscountDetailVO.getDiscountName());
            }

            List<SettlementApplyCommodityVO> settlementApplyCommodityVOS = optimizeDiscountDetailVO.getCommodityList();

            List<SettlementApplyCommodityVO> commodityList = Lists.newArrayList();
            if (CollUtil.isNotEmpty(settlementApplyCommodityVOS)) {
                commodityList.addAll(settlementApplyCommodityVOS);
                settlementApplyShareCommodityVO.setCommodityList(commodityList);
                commodityShareList.add(settlementApplyShareCommodityVO);
            }
        }
        settlementApplyOrderVO.setShareCommodityVOList(commodityShareList);
    }


    public static void addApplyDiscountDetail(List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                              SettlementApplyOrderVO settlementApplyOrderVO) {
        Map<Integer, List<SettlementApplyDiscountDetailVO>> applyDiscountDetailMap = routineDiscountDetailVOS.stream()
                .collect(Collectors.groupingBy(SettlementApplyDiscountDetailVO::getDiscountOption));
        List<SettlementApplyDiscountVO> discountList = new ArrayList<>();
        applyDiscountDetailMap.forEach((k, v) -> {
            SettlementApplyDiscountVO discountVO = new SettlementApplyDiscountVO();
            discountVO.setDiscountName(SettlementDiscountOptionEnum.getDesByCode(k));
            discountVO.setDiscountOption(k);
            discountVO.setDiscountList(v);
            discountList.add(discountVO);
        });
        settlementApplyOrderVO.setDiscountList(discountList);
    }

    public static List<SettlementApplyDiscountDetailVO> getDiscountDetailVOS(Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap,
                                                                             Integer discountOption) {
        return settlementApplyOrderMap.get(discountOption).get(0)
                .getDiscountList().stream().flatMap(detail -> detail.getDiscountList().stream())
                .collect(Collectors.toList());
    }


    public static void putDiscountDetailListVO(Map<String, SettlementApplyDiscountDetailVO> discountDetailVOMap,
                                               List<SettlementApplyDiscountDetailVO> allDiscountDetailVOS) {
        for (SettlementApplyDiscountDetailVO allDiscountDetailVO : allDiscountDetailVOS) {
            String discountGuid = StringUtils.isNotEmpty(allDiscountDetailVO.getDiscountOptionId()) ?
                    allDiscountDetailVO.getDiscountOptionId() : allDiscountDetailVO.getDiscountGuid();
            discountDetailVOMap.put(discountGuid, allDiscountDetailVO);
        }
    }


    public static void foldSettlementApplyOrderVOS(SettlementApplyOrderDTO dto,
                                                   List<SettlementApplyDiscountDetailVO> settlementApplyDiscountDetailVO,
                                                   List<String> discountOptionId,
                                                   Integer discountOption) {
        dto.setDiscountOptionId(discountOptionId);

        //需要查询的优惠项
        dto.setListOptions(Collections.singletonList(discountOption));

        //优惠清零
        dto.clearCommodityFee();

        //叠加条件 加上当前优惠
        addCurrentDiscountNew(dto, settlementApplyDiscountDetailVO);
    }

    public static SettlementApplyOrderDTO getSettlementApplyOrderDTO(SettlementApplyOrderDTO dto) {
        SettlementApplyOrderDTO orderDTO = new SettlementAfterApplyOrderDTO();
        BeanUtils.copyProperties(dto, orderDTO);
        orderDTO.setCheckDiscountList(null);
        orderDTO.setListOptions(null);
        return orderDTO;
    }

    public static void addCurrentDiscountNew(SettlementApplyOrderDTO dto,
                                             List<SettlementApplyDiscountDetailVO> discountDetailVOList) {
        final Map<String, SettlementApplyCommodityDTO> commodityDtoMap = dto.getOrderCommodityList().stream()
                .collect(Collectors.toMap(SettlementApplyCommodityDTO::key, c -> c, (v1, v2) -> v1));

        if (CollUtil.isNotEmpty(discountDetailVOList)) {
            //清洗商品优惠
            for (SettlementApplyDiscountDetailVO detailVO : discountDetailVOList) {

                List<SettlementApplyCommodityVO> settlementApplyCommodityVOS = detailVO.getCommodityList();

                if (CollUtil.isNotEmpty(settlementApplyCommodityVOS)) {
                    for (SettlementApplyCommodityVO commodityVO : settlementApplyCommodityVOS) {

                        setAfterDiscountTotalPrice(commodityVO, commodityDtoMap);
                    }
                }
            }
        }

    }


    private static void setAfterDiscountTotalPrice(SettlementApplyCommodityVO commodityVO, Map<String, SettlementApplyCommodityDTO> commodityDtoMap) {
        //需要减去前置优惠金额
        SettlementApplyCommodityDTO commodityDTO = commodityDtoMap.get(commodityVO.key());
        if (Objects.nonNull(commodityDTO)) {
            BigDecimal discountTotalPrice = Objects.nonNull(commodityDTO.getAfterDiscountTotalPrice()) ?
                    commodityDTO.getAfterDiscountTotalPrice() : commodityDTO.getDiscountTotalPriceInShopCar();
            if (commodityVO.getDiscountFee().compareTo(BigDecimal.ZERO) > NumberConstant.NUMBER_0
                    && discountTotalPrice.compareTo(BigDecimal.ZERO) > NumberConstant.NUMBER_0) {
                //若商品已存在优惠 则进行递减
                commodityDTO.setAfterDiscountTotalPrice(discountTotalPrice.subtract(commodityVO.getDiscountFee()));
                //若当前商品已存在兑换数量 则进行递加
                commodityDTO.setExchangeDiscountNum(commodityDTO.getExchangeDiscountNum().add(commodityVO.getExchangeDiscountNum()));
            }
        }
    }

    /**
     * 优惠保存
     */
    public static void checkDisOrderVoList(List<SettlementApplyOrderVO> disOrderVoList,
                                           List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS) {
        if (CollUtil.isNotEmpty(disOrderVoList)) {
            //获取优惠项
            final List<SettlementApplyDiscountVO> discountVOList = disOrderVoList
                    .stream()
                    .flatMap(vl -> vl.getDiscountList().stream())
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(discountVOList)) {
                for (SettlementApplyDiscountVO settlementApplyDiscountVO : discountVOList) {
                    optimizeDiscountDetailVOS.addAll(settlementApplyDiscountVO.getDiscountList());
                }
            }
        }
    }

    /**
     * 设置优惠详情的启用状态
     */
    public static void handlerDiscountDetailEnabled(SettlementApplyDiscountDetailVO in,
                                               Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap) {
        Integer discountOption = in.getDiscountOption();
        if (discountOption == null || !settlementApplyOrderMap.containsKey(discountOption)) {
            return;
        }
        
        SettlementApplyOrderVO orderVO = getFirstOrderVO(settlementApplyOrderMap.get(discountOption));
        if (orderVO == null) {
            return;
        }
        
        SettlementApplyDiscountDetailVO matchedDetail = findMatchedDiscountDetail(in, orderVO.getDiscountList());
        setEnabledByDiscountAmount(in, matchedDetail);
    }

    /**
     * 获取第一个订单VO
     */
    private static SettlementApplyOrderVO getFirstOrderVO(List<SettlementApplyOrderVO> orderVOS) {
        if (CollUtil.isEmpty(orderVOS)) {
            return null;
        }
        SettlementApplyOrderVO orderVO = orderVOS.get(NumberConstant.NUMBER_0);
        return (orderVO != null && CollUtil.isNotEmpty(orderVO.getDiscountList())) ? orderVO : null;
    }

    /**
     * 根据优惠金额设置是否启用
     */
    private static void setEnabledByDiscountAmount(SettlementApplyDiscountDetailVO target, 
                                                  SettlementApplyDiscountDetailVO matchedDetail) {
        if (matchedDetail == null) {
            target.setIsEnabled(BooleanEnum.FALSE.getCode());
            return;
        }
        target.setIsEnabled(matchedDetail.getIsEnabled());
    }

    /**
     * 查找匹配的优惠详情
     */
    private static SettlementApplyDiscountDetailVO findMatchedDiscountDetail(SettlementApplyDiscountDetailVO target, 
                                                                            List<SettlementApplyDiscountVO> discountList) {
        for (SettlementApplyDiscountVO discountVO : discountList) {
            if (CollUtil.isNotEmpty(discountVO.getDiscountList())) {
                SettlementApplyDiscountDetailVO matchedDetail = findMatchInDiscountList(target, discountVO.getDiscountList());
                if (matchedDetail != null) {
                    return matchedDetail;
                }
            }
        }
        return null;
    }

    /**
     * 在优惠详情列表中查找匹配项
     */
    private static SettlementApplyDiscountDetailVO findMatchInDiscountList(SettlementApplyDiscountDetailVO target,
                                                                          List<SettlementApplyDiscountDetailVO> detailList) {
        return detailList.stream()
                .filter(detailVO -> isMatchByDiscountOptionId(target, detailVO) || isMatchByDiscountGuid(target, detailVO))
                .findFirst()
                .orElse(null);
    }

    /**
     * 通过discountOptionId匹配
     */
    private static boolean isMatchByDiscountOptionId(SettlementApplyDiscountDetailVO target, 
                                                    SettlementApplyDiscountDetailVO detailVO) {
        return StringUtils.isNotEmpty(target.getDiscountOptionId()) 
            && StringUtils.isNotEmpty(detailVO.getDiscountOptionId())
            && target.getDiscountOptionId().equals(detailVO.getDiscountOptionId());
    }

    /**
     * 通过discountGuid匹配
     */
    private static boolean isMatchByDiscountGuid(SettlementApplyDiscountDetailVO target, 
                                                SettlementApplyDiscountDetailVO detailVO) {
        return StringUtils.isEmpty(target.getDiscountOptionId()) 
            && StringUtils.isNotEmpty(target.getDiscountGuid())
            && StringUtils.isNotEmpty(detailVO.getDiscountGuid())
            && target.getDiscountGuid().equals(detailVO.getDiscountGuid());
    }

    /**
     * 批量处理优惠详情的叠加标志和启用状态
     * 性能优化说明：
     * 1. 原始方案：每个优惠详情都要重复查找applyRule.getAppendDiscounts()和遍历settlementApplyOrderMap
     *    时间复杂度：O(n * m * k) 其中n=优惠详情数量，m=叠加配置数量，k=订单映射数量
     * 2. 优化方案：预处理构建快速查找数据结构，批量处理
     *    时间复杂度：O(n + m + k) 预处理 + O(n) 批量处理 = O(n + m + k)
     *    空间复杂度：O(m + k) 用于缓存查找结构
     * 3. 性能提升：当处理大量优惠详情时，性能提升显著，特别是n >> (m + k)的场景
     * 4. 智能优化：当优惠详情数量较少时，直接使用原始方法可能更高效
     * 
     * @param applyRule 结算规则
     * @param discountDetailVOS 优惠详情列表
     * @param settlementApplyOrderMap 结算订单映射
     */
    public static void batchHandlerDiscountDetails(SettlementApplyRuleVO applyRule, 
                                                  List<SettlementApplyDiscountDetailVO> discountDetailVOS,
                                                  Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap) {
        if (CollUtil.isEmpty(discountDetailVOS)) {
            return;
        }
        
        // 智能选择处理策略：数量较少时使用原始方法，避免过度优化
        if (discountDetailVOS.size() <= 10) {
            // 小批量数据直接使用原始逐个处理方法
            discountDetailVOS.forEach(detailVO -> {
                handlerAppendSignNew(applyRule, detailVO);
                handlerDiscountDetailEnabled(detailVO, settlementApplyOrderMap);
            });
            return;
        }
        
        // 大批量数据使用优化的批量处理
        // 预处理：获取叠加配置，避免重复获取
        List<SettlementRuleDiscountTreeVO> appendDiscounts = Optional.ofNullable(applyRule.getAppendDiscounts())
                .orElse(Collections.emptyList());
        
        // 预处理：构建叠加配置的快速查找Map
        final Set<String> appendDiscountSet = buildAppendDiscountSet(appendDiscounts);
        
        // 预处理：构建优惠详情的快速查找Map，避免重复查找settlementApplyOrderMap
        final Map<String, SettlementApplyDiscountDetailVO> orderDiscountMap = buildOrderDiscountMap(settlementApplyOrderMap);
        
        // 批量处理所有优惠详情
        discountDetailVOS.parallelStream().forEach(detailVO -> {
            // 设置叠加标志
            setBatchAppendSign(detailVO, appendDiscountSet);
            
            // 设置启用状态  
            setBatchEnabledSign(detailVO, orderDiscountMap);
        });
    }

    /**
     * 构建叠加配置的快速查找Set
     */
    private static Set<String> buildAppendDiscountSet(List<SettlementRuleDiscountTreeVO> appendDiscounts) {
        if (CollUtil.isEmpty(appendDiscounts)) {
            return Collections.emptySet();
        }
        
        return appendDiscounts.stream()
                .map(d -> SettlementDiscountOptionEnum.optionMapKey(d.getDiscountOption(), d.getDiscountGuid()))
                .collect(Collectors.toSet());
    }

    /**
     * 构建优惠详情的快速查找Map，key为optionMapKey，value为对应的优惠详情
     * 优化：使用流式处理降低复杂度，避免多层嵌套循环
     * 复杂度：从 O(n*m*k) 优化到 O(n+m+k)
     */
    private static Map<String, SettlementApplyDiscountDetailVO> buildOrderDiscountMap(
            Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap) {
        
        if (settlementApplyOrderMap.isEmpty()) {
            return Collections.emptyMap();
        }
        
        return settlementApplyOrderMap.values().stream()
                .filter(CollUtil::isNotEmpty)
                .map(orderVOS -> orderVOS.get(NumberConstant.NUMBER_0))
                .filter(Objects::nonNull)
                .filter(orderVO -> CollUtil.isNotEmpty(orderVO.getDiscountList()))
                .flatMap(orderVO -> orderVO.getDiscountList().stream())
                .filter(discountVO -> CollUtil.isNotEmpty(discountVO.getDiscountList()))
                .flatMap(discountVO -> discountVO.getDiscountList().stream())
                .collect(HashMap::new, SettlementBusinessUtil::collectDiscountDetailToMap, HashMap::putAll);
    }

    /**
     * 将优惠详情收集到Map中的优化版本
     * 提取方法降低复杂度，减少重复的key生成
     */
    private static void collectDiscountDetailToMap(Map<String, SettlementApplyDiscountDetailVO> map,
                                                  SettlementApplyDiscountDetailVO detailVO) {
        Integer discountOption = detailVO.getDiscountOption();
        
        // 批量生成keys，减少重复调用
        String optionIdKey = null;
        String guidKey = null;
        
        if (StringUtils.isNotEmpty(detailVO.getDiscountOptionId())) {
            optionIdKey = SettlementDiscountOptionEnum.optionMapKey(discountOption, detailVO.getDiscountOptionId());
            map.put(optionIdKey, detailVO);
        }
        
        if (StringUtils.isNotEmpty(detailVO.getDiscountGuid()) && 
            !Objects.equals(optionIdKey, SettlementDiscountOptionEnum.optionMapKey(discountOption, detailVO.getDiscountGuid()))) {
            guidKey = SettlementDiscountOptionEnum.optionMapKey(discountOption, detailVO.getDiscountGuid());
            map.put(guidKey, detailVO);
        }
    }

    /**
     * 批量设置叠加标志
     */
    private static void setBatchAppendSign(SettlementApplyDiscountDetailVO detailVO, 
                                          Set<String> appendDiscountSet) {
        if (CollUtil.isEmpty(appendDiscountSet)) {
            return;
        }
        
        final String optionKey = SettlementDiscountOptionEnum.optionMapKey(
                detailVO.getDiscountOption(), 
                detailVO.getDiscountGuid()
        );
        
        if (appendDiscountSet.contains(optionKey)) {
            detailVO.setIsAppend(BooleanEnum.TRUE.getCode());
        }
    }

    /**
     * 批量设置启用状态
     */
    private static void setBatchEnabledSign(SettlementApplyDiscountDetailVO detailVO,
                                           Map<String, SettlementApplyDiscountDetailVO> orderDiscountMap) {
        // 优先使用discountOptionId查找
        String lookupKey = null;
        if (StringUtils.isNotEmpty(detailVO.getDiscountOptionId())) {
            lookupKey = SettlementDiscountOptionEnum.optionMapKey(
                    detailVO.getDiscountOption(), detailVO.getDiscountOptionId());
        } else if (StringUtils.isNotEmpty(detailVO.getDiscountGuid())) {
            lookupKey = SettlementDiscountOptionEnum.optionMapKey(
                    detailVO.getDiscountOption(), detailVO.getDiscountGuid());
        }
        
        if (lookupKey != null) {
            SettlementApplyDiscountDetailVO matchedDetail = orderDiscountMap.get(lookupKey);
            if (matchedDetail != null) {
                BigDecimal discountAmount = matchedDetail.getDiscountAmount();
                boolean isEnabled = discountAmount != null && discountAmount.compareTo(BigDecimal.ZERO) > 0;
                detailVO.setIsEnabled(isEnabled ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
            } else {
                detailVO.setIsEnabled(BooleanEnum.FALSE.getCode());
            }
        }
    }
}
