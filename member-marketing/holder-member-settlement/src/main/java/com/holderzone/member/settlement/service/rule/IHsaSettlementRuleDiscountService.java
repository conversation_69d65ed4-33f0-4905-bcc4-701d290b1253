package com.holderzone.member.settlement.service.rule;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleDTO;
import com.holderzone.member.common.module.settlement.rule.qo.SettlementRuleDiscountQO;
import com.holderzone.member.common.module.settlement.rule.qo.DeleteSettlementRuleDiscountQO;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountTreeVO;
import com.holderzone.member.settlement.entity.rule.HsaSettlementRuleDiscount;

import java.util.List;

/**
 * <p>
 * 结算规则已选优惠项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface IHsaSettlementRuleDiscountService extends IHolderBaseService<HsaSettlementRuleDiscount> {

    /**
     * 查询所有优惠项
     *
     * @param qo 优惠入参
     * @return 所有优惠
     */
    List<SettlementRuleDiscountTreeVO> listAll(SettlementRuleDiscountQO qo);

    /**
     * 查询所有优惠，按树形展示
     *
     * @param qo 优惠入参
     * @return 所有优惠
     */
    List<SettlementRuleDiscountTreeVO> listAllTree(SettlementRuleDiscountQO qo);

    public List<String> getParnetVoList(SettlementRuleDiscountQO qo);

    /**
     * 树形展示优惠
     *
     * @param qo 订单入参
     * @return 优惠列表
     */
    List<SettlementRuleDiscountTreeVO> tree(SettlementRuleDiscountQO qo);

    /**
     * 保存规则
     *
     * @param dto 规则对象
     */
    void save(SettlementRuleDTO dto);

    /**
     * 删除规则
     *
     * @param guid 规则guid
     * @return 成功是否
     */
    boolean deleteByRuleGuid(String guid);

    /**
     * 删除已叠加项
     */
    void deleteAppend(DeleteSettlementRuleDiscountQO deleteSettlementRuleDiscountQO);


    List<HsaSettlementRuleDiscount> toHsaSettlementRuleDiscounts(SettlementRuleDTO dto);
}
