package com.holderzone.member.settlement.service.rule.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.*;
import com.holderzone.member.common.dto.EnumModel;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.exception.MemberSettlementExceptionEnum;
import com.holderzone.member.common.exception.MemberSettlementException;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.dto.SynSettlementRuleDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.qo.SettlementDiscountQO;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementDiscountVO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.settlement.assembler.SettlementRuleAssembler;
import com.holderzone.member.settlement.entity.rule.HsaSettlementDiscount;
import com.holderzone.member.settlement.entity.rule.HsaSettlementRuleDiscount;
import com.holderzone.member.settlement.mapper.rule.HsaSettlementDiscountMapper;
import com.holderzone.member.settlement.service.rule.IHsaSettlementDiscountService;
import com.holderzone.member.settlement.service.rule.IHsaSettlementRuleDiscountService;
import com.holderzone.member.settlement.service.rule.IHsaSettlementRuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 结算优惠项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Service
@AllArgsConstructor
@Slf4j
public class HsaSettlementDiscountServiceImpl extends HolderBaseServiceImpl<HsaSettlementDiscountMapper, HsaSettlementDiscount> implements IHsaSettlementDiscountService {

    /**
     * guid生成
     */
    private final GuidGeneratorUtil guidGeneratorUtil;

    /**
     * 优惠mapper
     */
    private final HsaSettlementDiscountMapper settlementDiscountMapper;

    /**
     * 规则项
     */
    @Lazy
    private final IHsaSettlementRuleDiscountService settlementRuleDiscountService;

    /**
     * redis
     */
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IHsaSettlementRuleService hsaSettlementRuleService;

    @Override
    public void init(String operSubjectGuid) {
        //第一层
        final List<HsaSettlementDiscount> list = this.list(
                new LambdaQueryWrapper<HsaSettlementDiscount>()
                        .eq(!StringUtils.isEmpty(operSubjectGuid), HsaSettlementDiscount::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaSettlementDiscount::getParentGuid, StringConstant.PARENT)
        );
        //枚举初始化
        final List<SettlementDiscountOptionEnum> optionEnums = SettlementDiscountOptionEnum.initValues();
        if (CollUtil.isNotEmpty(list)) {
            final Set<Integer> optionMap = list.stream().map(HsaSettlementDiscount::getDiscountOption).collect(Collectors.toSet());
            //移除已有
            optionEnums.removeIf(o -> optionMap.contains(o.getCode()));
        }
        if (optionEnums.isEmpty()) {
            return;
        }
        int size = optionEnums.size();
        final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaSettlementDiscount.class.getSimpleName(), size);
        int i = 0;
        List<HsaSettlementDiscount> discountList = new ArrayList<>(size);
        for (SettlementDiscountOptionEnum optionEnum : optionEnums) {
            HsaSettlementDiscount discount = new HsaSettlementDiscount()
                    .setOperSubjectGuid(operSubjectGuid)
                    .setParentGuid(StringConstant.PARENT)
                    //类型：0 单品级优惠 1订单级优惠 2资产优惠
                    .setDiscountType(optionEnum.getType().getCode())
                    .setDiscountItem(optionEnum.getItem().getCode())
                    .setDiscountOption(optionEnum.getCode())
                    .setDiscountGuid("")
                    .setDiscountName(optionEnum.getDes())
                    .setDiscountDynamic("")
                    .setDiscountNum(optionEnum.getLimitNum());
            discount.setGuid(guids.get(i++));
            discountList.add(discount);
        }
        this.saveBatch(discountList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sync(SettlementDiscountDTO dto){
        //参数校验
        dto.validate();
        final SettlementDiscountOptionEnum optionEnum = SettlementDiscountOptionEnum.getEnum(dto.getOption());
        RLock lock = redissonClient.getLock(RedisKeyConstant.SYNC_SETTLEMENT_RULE + dto.getOperSubjectGuid() + RedisKeyConstant.COLON + optionEnum.getCode());
        try {
            if (!lock.tryLock(NumberConstant.NUMBER_30, NumberConstant.NUMBER_30, TimeUnit.SECONDS)) {
                throw new MemberSettlementException("规则保存中，请勿重复操作");
            }
            if (optionEnum.equals(SettlementDiscountOptionEnum.NONE)) {
                throw new MemberSettlementException(MemberSettlementExceptionEnum.SETTLEMENT_OPTION_ERROR);
            }
            //获得父级
            final HsaSettlementDiscount parentDiscount = getParentDiscount(dto, optionEnum);
            if (Objects.isNull(parentDiscount)) {
                throw new MemberSettlementException(MemberSettlementExceptionEnum.SETTLEMENT_OPTION_ERROR);
            }
            //删除指定优惠项
            deleteDiscount(dto, optionEnum, parentDiscount);
            //更新优惠项
            saveOrUpdateDiscountData(dto, optionEnum, parentDiscount);
        }
        catch (Exception e) {
            log.error("规则同步错误", e);
            throw new MemberSettlementException("规则同步错误！");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取父级优惠
     *
     * @param dto        优惠入参
     * @param optionEnum 优惠类型
     * @return 父级优惠
     */
    private HsaSettlementDiscount getParentDiscount(SettlementDiscountDTO dto, SettlementDiscountOptionEnum optionEnum) {
        final boolean noChildren = SettlementDiscountOptionEnum.noChildren(optionEnum);
        if (noChildren) {
            //无父级
            final HsaSettlementDiscount parentDiscount = new HsaSettlementDiscount();
            parentDiscount.setGuid(StringConstant.PARENT);
            parentDiscount.setOperSubjectGuid(dto.getOperSubjectGuid());
            parentDiscount.setDiscountItem(optionEnum.getItem().getCode());
            parentDiscount.setDiscountNum(optionEnum.getLimitNum());
            return parentDiscount;
        }
        //父级优惠项
        return this.getOne(
                new LambdaQueryWrapper<HsaSettlementDiscount>()
                        .eq(HsaSettlementDiscount::getOperSubjectGuid, dto.getOperSubjectGuid())
                        .eq(HsaSettlementDiscount::getParentGuid, StringConstant.PARENT)
                        .eq(HsaSettlementDiscount::getDiscountItem, optionEnum.getItem().getCode())
        );
    }

    /**
     * 保存更新优惠数据
     *
     * @param dto            优惠数据
     * @param optionEnum     类型枚举
     * @param parentDiscount 父级优惠
     */
    private void saveOrUpdateDiscountData(SettlementDiscountDTO dto,
                                          SettlementDiscountOptionEnum optionEnum,
                                          HsaSettlementDiscount parentDiscount) {
        //有discountGuid的优惠项
        final List<SettlementDiscountDTO.Discount> discountList = dto.getDiscountList();
        if (CollUtil.isEmpty(discountList)) {
            return;
        }

        List<SettlementRuleDiscountDTO> appendDiscounts = new ArrayList<>();

        List<SettlementRuleDiscountDTO> disAppendDiscounts = new ArrayList<>();

        //更新已有
        final List<String> existsDiscountList = updateExistsDiscount(dto, optionEnum, parentDiscount, appendDiscounts, disAppendDiscounts);
        //首次新增
        insertNonDiscount(dto, parentDiscount, discountList, existsDiscountList, appendDiscounts, disAppendDiscounts);

        if (CollUtil.isNotEmpty(appendDiscounts) || CollUtil.isNotEmpty(disAppendDiscounts)) {
            SynSettlementRuleDiscountDTO settlementRuleMutualDTO = new SynSettlementRuleDiscountDTO();
            settlementRuleMutualDTO.setAppendDiscounts(appendDiscounts);
            settlementRuleMutualDTO.setDisAppendDiscounts(disAppendDiscounts);
            settlementRuleMutualDTO.setOperSubjectGuid(dto.getOperSubjectGuid());

            log.info("结算台优惠同步参数：{}", JSON.toJSONString(settlementRuleMutualDTO));
            //查询结算台优惠表
            hsaSettlementRuleService.updateSettlementRuleDiscounts(settlementRuleMutualDTO);
        }
    }


    /**
     * 删除优惠项
     *
     * @param dto            优惠类型
     * @param optionEnum     优惠枚举
     * @param parentDiscount 父级优惠
     */
    private void deleteDiscount(SettlementDiscountDTO dto,
                                SettlementDiscountOptionEnum optionEnum,
                                HsaSettlementDiscount parentDiscount) {
        //删除优惠项
        if (CollUtil.isEmpty(dto.getDelDiscountGuids())) {
            return;
        }
        //优惠项
        final LambdaQueryWrapper<HsaSettlementDiscount> queryWrapper = new LambdaQueryWrapper<HsaSettlementDiscount>()
                .eq(HsaSettlementDiscount::getOperSubjectGuid, dto.getOperSubjectGuid())
                .eq(HsaSettlementDiscount::getParentGuid, parentDiscount.getGuid())
                .eq(HsaSettlementDiscount::getDiscountOption, optionEnum.getCode())
                .in(HsaSettlementDiscount::getDiscountGuid, dto.getDelDiscountGuids());
        final List<HsaSettlementDiscount> list = this.list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        this.remove(queryWrapper);
        //删除规则中已关联优惠项
        deleteRuleDiscount(dto, list);
    }

    /**
     * 删除规则优惠
     *
     * @param dto  优惠参数
     * @param list 优惠列表
     */
    private void deleteRuleDiscount(SettlementDiscountDTO dto, List<HsaSettlementDiscount> list) {
        final List<String> discountGuidList = list.stream().map(HsaSettlementDiscount::getGuid).collect(Collectors.toList());
        //其他关联规则也同步删除
        settlementRuleDiscountService.remove(new LambdaQueryWrapper<HsaSettlementRuleDiscount>()
                .eq(HsaSettlementRuleDiscount::getOperSubjectGuid, dto.getOperSubjectGuid())
                .in(HsaSettlementRuleDiscount::getSettlementDiscountGuid, discountGuidList)
        );
        //子集删完了，父级也要删
        final Set<String> parentSet = list.stream().map(HsaSettlementDiscount::getParentGuid)
                .filter(p -> !p.equals(StringConstant.PARENT)).collect(Collectors.toSet());
        if (CollUtil.isEmpty(parentSet)) {
            return;
        }
        //还有子集的父级
        Set<String> existsChildGuids = settlementDiscountMapper.existsChild(parentSet);
        parentSet.removeAll(existsChildGuids);
        if (CollUtil.isEmpty(parentSet)) {
            return;
        }
        //没有子集的父
        settlementRuleDiscountService.remove(new LambdaQueryWrapper<HsaSettlementRuleDiscount>()
                .eq(HsaSettlementRuleDiscount::getOperSubjectGuid, dto.getOperSubjectGuid())
                .in(HsaSettlementRuleDiscount::getSettlementDiscountGuid, parentSet)
        );
    }

    /**
     * 新增优惠项
     *
     * @param parentDiscount     父级优惠项
     * @param discountList       优惠列表
     * @param existsDiscountList 已存在的优惠
     */
    private void insertNonDiscount(SettlementDiscountDTO dto,
                                   HsaSettlementDiscount parentDiscount,
                                   List<SettlementDiscountDTO.Discount> discountList,
                                   List<String> existsDiscountList,
                                   List<SettlementRuleDiscountDTO> appendDiscounts,
                                   List<SettlementRuleDiscountDTO> disAppendDiscounts) {
        //去除已存在
        discountList.removeIf(d -> existsDiscountList.contains(d.getDiscountGuid()));
        if (CollUtil.isEmpty(discountList)) {
            return;
        }
        final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaSettlementDiscount.class.getSimpleName(), discountList.size());
        int i = 0;
        List<HsaSettlementDiscount> dataDiscountList = new ArrayList<>();
        for (SettlementDiscountDTO.Discount discount : discountList) {
            HsaSettlementDiscount d = new HsaSettlementDiscount();
            //要按创建时间排序
            d.setGmtCreate(discount.getGmtCreate());
            d.setOperSubjectGuid(parentDiscount.getOperSubjectGuid());
            d.setParentGuid(parentDiscount.getGuid());
            d.setDiscountType(parentDiscount.getDiscountType());
            d.setDiscountItem(parentDiscount.getDiscountItem());
            d.setDiscountOption(dto.getOption());
            d.setDiscountGuid(discount.getDiscountGuid());
            d.setDiscountName(discount.getDiscountName());
            d.setDiscountDynamic(discount.getDiscountDynamic());
            d.setDiscountNum(discount.getDiscountNum());
            d.setGuid(guids.get(i++));
            dataDiscountList.add(d);

            SettlementRuleDiscountDTO discountDTO = new SettlementRuleDiscountDTO();
            discountDTO.setGuid(d.getGuid());
            discountDTO.setDiscountNum(d.getDiscountNum());
            discountDTO.setDiscountOption(d.getDiscountItem());
            addDiscounts(parentDiscount, appendDiscounts, disAppendDiscounts, discount, discountDTO);
        }
        this.saveBatch(dataDiscountList, SqlConstant.BATCH_SAVE_COUNT);
    }

    /**
     * 更新已有优惠项
     *
     * @param dto            入参
     * @param optionEnum     操作项
     * @param parentDiscount 父级优惠
     * @return 已存在优惠项guidList
     */
    private List<String> updateExistsDiscount(SettlementDiscountDTO dto,
                                              SettlementDiscountOptionEnum optionEnum,
                                              HsaSettlementDiscount parentDiscount,
                                              List<SettlementRuleDiscountDTO> appendDiscounts,
                                              List<SettlementRuleDiscountDTO> disAppendDiscounts) {
        final List<SettlementDiscountDTO.Discount> discountList = dto.getDiscountList();
        final List<String> discountGuidList = discountList.stream()
                .map(SettlementDiscountDTO.Discount::getDiscountGuid).collect(Collectors.toList());
        //查库：已存在数据
        final List<HsaSettlementDiscount> existsDiscountList = this.list(
                new LambdaQueryWrapper<HsaSettlementDiscount>()
                        .eq(HsaSettlementDiscount::getOperSubjectGuid, dto.getOperSubjectGuid())
                        .eq(HsaSettlementDiscount::getParentGuid, parentDiscount.getGuid())
                        .eq(HsaSettlementDiscount::getDiscountItem, optionEnum.getItem().getCode())
                        .eq(HsaSettlementDiscount::getDiscountOption, optionEnum.getCode())
                        .in(HsaSettlementDiscount::getDiscountGuid, discountGuidList)
        );
        if (CollUtil.isEmpty(existsDiscountList)) {
            return Collections.emptyList();
        }
        Map<String, Map<String, Integer>> settlementRuleMap;
        // 会员等级折扣给一个空map
        if (optionEnum.getCode() == SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode()) {
            settlementRuleMap = new HashMap<>();
        } else {
            settlementRuleMap = getSettlementRuleMap(dto, existsDiscountList);
        }

        List<String> resultDiscountGuidList = new ArrayList<>(existsDiscountList.size());
        final Map<String, SettlementDiscountDTO.Discount> discountMap = discountList.stream()
                .collect(Collectors.toMap(SettlementDiscountDTO.Discount::getDiscountGuid, d -> d, (v1, v2) -> v2));

        //更新已存在数据
        for (HsaSettlementDiscount discount : existsDiscountList) {
            final SettlementDiscountDTO.Discount currentDiscount = discountMap.get(discount.getDiscountGuid());
            //优惠字段更新
            discount.setDiscountGuid(currentDiscount.getDiscountGuid());
            discount.setDiscountName(currentDiscount.getDiscountName());
            discount.setDiscountDynamic(currentDiscount.getDiscountDynamic());
            discount.setDiscountNum(currentDiscount.getDiscountNum());
            resultDiscountGuidList.add(currentDiscount.getDiscountGuid());

            SettlementRuleDiscountDTO discountDTO = new SettlementRuleDiscountDTO();
            discountDTO.setGuid(discount.getGuid());
            discountDTO.setDiscountNum(discount.getDiscountNum());
            discountDTO.setSettlementRuleMap(settlementRuleMap);
            discountDTO.setDiscountOption(discount.getDiscountItem());
            addDiscounts(parentDiscount, appendDiscounts, disAppendDiscounts, currentDiscount, discountDTO);
        }
        //更新数据
        this.saveOrUpdateBatch(existsDiscountList, SqlConstant.BATCH_SAVE_COUNT);
        return resultDiscountGuidList;
    }

    private Map<String, Map<String, Integer>> getSettlementRuleMap(SettlementDiscountDTO dto,
                                                                   List<HsaSettlementDiscount> existsDiscountList) {
        List<String> existsDiscountGuidList = existsDiscountList.stream()
                .map(HsaSettlementDiscount::getGuid)
                .distinct()
                .collect(Collectors.toList());

        List<HsaSettlementRuleDiscount> settlementRuleDiscountList = settlementRuleDiscountService.list(
                new LambdaQueryWrapper<HsaSettlementRuleDiscount>()
                        .eq(HsaSettlementRuleDiscount::getOperSubjectGuid, dto.getOperSubjectGuid())
                        .in(HsaSettlementRuleDiscount::getSettlementDiscountGuid, existsDiscountGuidList)
        );
        if (CollectionUtils.isEmpty(settlementRuleDiscountList)) {
            return new HashMap<>();
        }
        Map<String, List<HsaSettlementRuleDiscount>> groupBySettlementRuleMap = settlementRuleDiscountList.stream()
                .collect(Collectors.groupingBy(HsaSettlementRuleDiscount::getSettlementRuleGuid));
        Map<String, Map<String, Integer>> settlementRuleMap = new HashMap<>();
        groupBySettlementRuleMap.forEach((settlementRuleGuid, settlementRuleList) -> {
            if (CollectionUtils.isEmpty(settlementRuleList)) {
                return;
            }
            Map<String, Integer> discountNumMap = settlementRuleList.stream()
                    .collect(Collectors.toMap(
                            HsaSettlementRuleDiscount::getSettlementDiscountGuid,
                            HsaSettlementRuleDiscount::getDiscountNum,
                            (existing, replacement) -> replacement));
            settlementRuleMap.put(settlementRuleGuid, discountNumMap);
        });
        return settlementRuleMap;
    }

    private static void addDiscounts(HsaSettlementDiscount parentDiscount,
                                     List<SettlementRuleDiscountDTO> appendDiscounts,
                                     List<SettlementRuleDiscountDTO> disAppendDiscounts,
                                     SettlementDiscountDTO.Discount currentDiscount,
                                     SettlementRuleDiscountDTO discountDTO) {
        discountDTO.setIsFirstAdd(currentDiscount.getIsFirstAdd());
        if (Objects.nonNull(currentDiscount.getRelationRule())
                && currentDiscount.getRelationRule() == BooleanEnum.TRUE.getCode()) {
            initDiscounts(parentDiscount, appendDiscounts, appendDiscounts);
            SettlementRuleDiscountDTO appendChildren = appendDiscounts.get(0);
            appendChildren.getChildren().add(discountDTO);
        } else {
            initDiscounts(parentDiscount, disAppendDiscounts, disAppendDiscounts);
            SettlementRuleDiscountDTO disAppendChildren = disAppendDiscounts.get(0);
            disAppendChildren.getChildren().add(discountDTO);
        }
    }

    private static void initDiscounts(HsaSettlementDiscount parentDiscount,
                                      List<SettlementRuleDiscountDTO> appendDiscounts,
                                      List<SettlementRuleDiscountDTO> appendChildren) {
        if (CollUtil.isEmpty(appendChildren)) {
            SettlementRuleDiscountDTO discountDTO = new SettlementRuleDiscountDTO();
            discountDTO.setDiscountNum(parentDiscount.getDiscountNum());
            discountDTO.setGuid(parentDiscount.getGuid());


            List<SettlementRuleDiscountDTO> children = new ArrayList<>();
            discountDTO.setChildren(children);
            discountDTO.setDiscountOption(parentDiscount.getDiscountItem());
            appendDiscounts.add(discountDTO);
        }
    }

    @Override
    public List<HsaSettlementDiscount> getChildren(List<String> list) {
        return this.list(
                new LambdaQueryWrapper<HsaSettlementDiscount>()
                        .eq(HsaSettlementDiscount::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                        .in(HsaSettlementDiscount::getParentGuid, list)
        );
    }

    @Override
    public Set<String> listGuidsSet(String operSubjectGuid) {
        return Optional.ofNullable(this.list(
                        new LambdaQueryWrapper<HsaSettlementDiscount>()
                                .select(HsaSettlementDiscount::getGuid)
                                .eq(HsaSettlementDiscount::getOperSubjectGuid, operSubjectGuid)
                )).map(list -> list.stream().map(HsaSettlementDiscount::getGuid)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
    }

    @Override
    public PageResult<SettlementDiscountVO> page(SettlementDiscountQO qo) {
        //分页参数
        qo.startPage();
        //关键字查询
        qo.buildKeywordsQuery();
        List<SettlementDiscountVO> voList = settlementDiscountMapper.list(qo);
        if (CollUtil.isEmpty(voList)) {
            return PageUtil.emptyPageResult();
        }
        //处理返回数据
        handlerDiscountVo(voList);
        return PageUtil.pageResult(voList);
    }

    /**
     * 处理优惠返回对象
     * 1、不展示discountGuid
     * 2、父级名称
     *
     * @param voList 优惠列表
     */
    private void handlerDiscountVo(List<SettlementDiscountVO> voList) {
        //按父级分组
        final Set<String> parentGuidSet = voList.stream().map(SettlementDiscountVO::getParentGuid).collect(Collectors.toSet());
        final List<HsaSettlementDiscount> parentList = this.list(new LambdaQueryWrapper<HsaSettlementDiscount>()
                .select(HsaSettlementDiscount::getGuid,
                        HsaSettlementDiscount::getDiscountName,
                        HsaSettlementDiscount::getDiscountNum
                )
                .in(HsaSettlementDiscount::getGuid, parentGuidSet));
        final Map<String, HsaSettlementDiscount> parentMap = parentList.stream().collect(Collectors.toMap(HsaBaseEntity::getGuid, p -> p, (v1, v2) -> v1));
        voList.parallelStream().forEach(v -> {
            //部分优惠项guid不显示到前端
            if (SettlementDiscountOptionEnum.noShowDiscountGuid(v.getDiscountOption())) {
                v.setDiscountGuid(null);
            }
            final String parentGuid = v.getParentGuid();
            if (StringConstant.PARENT.equals(parentGuid)) {
                //父子相等
                v.setParentGuid(v.getGuid());
                return;
            }
            //填充父级名称
            Optional.ofNullable(parentMap.get(parentGuid))
                    .ifPresent(parentDiscount -> {
                        v.setParentDiscountName(parentDiscount.getDiscountName());
                        v.setParentDiscountNum(parentDiscount.getDiscountNum());
                    });

        });
    }

    @Override
    public List<EnumModel> options() {
        return Arrays.stream(SettlementDiscountOptionEnum.values())
                .map(e -> new EnumModel()
                        .setCode(e.getCode())
                        .setDesc(e.getDes()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SettlementDiscountVO> listByGuids(List<String> guids) {
        if (CollUtil.isEmpty(guids)) {
            return Collections.emptyList();
        }
        List<HsaSettlementDiscount> list = this.list(new LambdaQueryWrapper<HsaSettlementDiscount>()
                .in(HsaSettlementDiscount::getGuid, guids)
        );
        return SettlementRuleAssembler.toSettlementDiscountVOList(list);
    }
}
