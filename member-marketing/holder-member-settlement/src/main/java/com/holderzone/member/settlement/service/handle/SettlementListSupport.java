package com.holderzone.member.settlement.service.handle;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderInfoDTO;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.settlement.service.apply.util.SettlementBusinessUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 结算台列表查询
 */
@AllArgsConstructor
@Component
@Slf4j
public class SettlementListSupport {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * 自动勾选的情况下 获取单选最优结算结果 以单品级-> 订单级 -> 资产级的顺序进行共享优惠计算
     *
     * @param discountVoMap 优惠列表
     * @return 最优结果
     */
    public SettlementApplyOrderVO getOptimizeCheckOrder(SettlementApplyRuleVO applyRule,
                                                        SettlementApplyOrderDTO dto,
                                                        Map<Integer, List<SettlementApplyOrderVO>> discountVoMap) {

        //最优金额  可以是单项优惠 也可以是组合优惠
        BigDecimal optimizeDiscountAmount = BigDecimal.ZERO;

        //最优具体优惠项  需要最终进行勾选的项
        List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS = new ArrayList<>();

        //常规优惠项 只有单项
        List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS = new ArrayList<>();

        //折扣券
        List<SettlementApplyDiscountDetailVO> orderCouponDiscountList = new ArrayList<>();


        //遍历所有类型
        for (Map.Entry<Integer, List<SettlementApplyOrderVO>> entry : discountVoMap.entrySet()) {
            final List<SettlementApplyOrderVO> orderVOList = entry.getValue();
            //大类，只有一个
            SettlementApplyOrderVO tempOrderVo = orderVOList.get(NumberConstant.NUMBER_0);

            //优惠列表  目前来看 一个大类只有一个
            Integer discountOption = tempOrderVo.getDiscountList().get(NumberConstant.NUMBER_0)
                    .getDiscountOption();

            //处理叠加标志
            SettlementBusinessUtil.handlerAppendSign(applyRule, tempOrderVo.getDiscountList());

            //暂存折扣券&代金券 后续列表查询需要
            setOrderCouponDiscountList(discountOption, tempOrderVo, orderCouponDiscountList);

            //共享优惠处理
            optimizeDiscountAmount = dealShareDiscountCheck(
                    dto,
                    discountVoMap,
                    routineDiscountDetailVOS,
                    optimizeDiscountAmount,
                    optimizeDiscountDetailVOS,
                    tempOrderVo);

            //资产级 暂时只有积分抵现
            if (SettlementDiscountOptionEnum.isPropertyDiscount(discountOption)) {
                log.info("资产级优惠处理");
                optimizeDiscountAmount = dealPropertyOptimize(dto, tempOrderVo, routineDiscountDetailVOS, optimizeDiscountAmount, optimizeDiscountDetailVOS);
            }
        }

        //处理选中优惠后的计算
        dealOptimizeAfter(dto, discountVoMap, optimizeDiscountDetailVOS, routineDiscountDetailVOS);

        //折扣券+代金券特殊处理 需要根据结算台规则来看先后计算逻辑 只有列表查询存在以下逻辑
        dealSpecialOrderCoupon(dto, orderCouponDiscountList, optimizeDiscountDetailVOS, routineDiscountDetailVOS, applyRule);

        if (!routineDiscountDetailVOS.isEmpty()) {
            //处理存在优惠
            return dealSettlementApplyOrderVO(
                    optimizeDiscountDetailVOS,
                    routineDiscountDetailVOS,
                    optimizeDiscountAmount);
        }
        return SettlementApplyOrderVO.buildEmpty();
    }

    private void dealOptimizeAfter(SettlementApplyOrderDTO dto,
                                   Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                   List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                   List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS) {
        if (CollUtil.isNotEmpty(optimizeDiscountDetailVOS)) {
            Set<Integer> optionalDiscountOption = discountVoMap.keySet();
            for (Integer i : optionalDiscountOption) {
                if (SettlementDiscountOptionEnum.FULL_OFF.getCode() == i) {
                    //获取前置优惠项
                    List<SettlementApplyDiscountDetailVO> discountOptionValue = optimizeDiscountDetailVOS.stream()
                            .filter(vo -> vo.getDiscountOption() == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode())
                            .collect(Collectors.toList());
                    dealOptimizeAfterDiscount(dto, discountOptionValue, routineDiscountDetailVOS, SettlementDiscountOptionEnum.FULL_OFF.getCode());

                } else if (SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode() == i) {
                    //获取前置优惠项
                    List<SettlementApplyDiscountDetailVO> discountOptionValue = optimizeDiscountDetailVOS.stream().filter(vo ->
                                    vo.getDiscountOption() == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode()
                                            || vo.getDiscountOption() == SettlementDiscountOptionEnum.FULL_OFF.getCode()
                                            || vo.getDiscountOption() == SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode())
                            .collect(Collectors.toList());
                    dealOptimizeAfterDiscount(dto, discountOptionValue, routineDiscountDetailVOS, SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode());
                }
            }
        }
    }

    private void dealOptimizeAfterDiscount(SettlementApplyOrderDTO dto,
                                           List<SettlementApplyDiscountDetailVO> discountOptionValue,
                                           List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                           Integer optionValue) {
        if (CollUtil.isNotEmpty(discountOptionValue)) {
            //优惠清零
            dto.clearCommodityFee();
            //叠加条件 加上当前优惠
            SettlementBusinessUtil.addCurrentDiscountNew(dto, discountOptionValue);
            //需要查询的优惠项
            dto.setListOptions(Collections.singletonList(optionValue));

            List<SettlementApplyOrderVO> disOrderVoList = memberBaseFeign.listDiscount(dto);
            //获取优惠项
            List<SettlementApplyDiscountVO> discountVOList = disOrderVoList
                    .stream()
                    .flatMap(vl -> vl.getDiscountList().stream())
                    .collect(Collectors.toList());
            routineDiscountDetailVOS.removeIf(vo -> Objects.equals(vo.getDiscountOption(), optionValue));

            List<SettlementApplyDiscountDetailVO> discountList = discountVOList.get(0).getDiscountList();

            routineDiscountDetailVOS.addAll(discountList);
        }
    }


    private void setOrderCouponDiscountList(Integer discountOption,
                                            SettlementApplyOrderVO tempOrderVo,
                                            List<SettlementApplyDiscountDetailVO> orderCouponDiscountList) {
        // 这里折扣券和代金券的逻辑是一样的 都需要单独处理
        if (SettlementDiscountOptionEnum.isOrderCouponDiscount(discountOption)
                    || SettlementDiscountOptionEnum.isCouponVoucher(discountOption)) {
            List<SettlementApplyDiscountVO> settlementApplyDiscount = tempOrderVo.getDiscountList();

            for (SettlementApplyDiscountVO settlementApplyDiscountDetailVO : settlementApplyDiscount) {
                orderCouponDiscountList.addAll(settlementApplyDiscountDetailVO.getDiscountList());
            }
        }
    }

    private static BigDecimal dealPropertyOptimize(SettlementApplyOrderDTO dto,
                                                   SettlementApplyOrderVO tempOrderVo,
                                                   List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                                   BigDecimal optimizeDiscountAmount,
                                                   List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS) {
        List<SettlementApplyDiscountDetailVO> discountList = tempOrderVo.getDiscountList().get(NumberConstant.NUMBER_0).getDiscountList();

        if (CollUtil.isNotEmpty(discountList)) {
            //取最优
            List<SettlementApplyDiscountDetailVO> discountVOList = discountList.stream().filter(in -> in.getOptimal() == BooleanEnum.TRUE.getCode())
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(discountVOList)) {
                routineDiscountDetailVOS.addAll(discountList);
                return optimizeDiscountAmount;
            }
            SettlementApplyDiscountDetailVO optimalDiscountVO = discountVOList.get(0);
            List<SettlementApplyDiscountDetailVO> notOptimalDiscountList = discountList.stream().filter(in -> in.getOptimal() == BooleanEnum.FALSE.getCode())
                    .collect(Collectors.toList());

            //单项优惠判断
            BigDecimal singleDiscountAmount = optimalDiscountVO.getDiscountAmount();

            //兜底优惠 若前置优惠已大于实付 则不在计算单项
            if (optimizeDiscountAmount.compareTo(dto.getOrderInfo().getShopCarOriginPayPrice()) >= 0) {
                optimalDiscountVO.setDiscountAmount(BigDecimal.ZERO);
                optimalDiscountVO.setIsEnabled(BooleanEnum.FALSE.getCode());
            }

            //若以在共享计算中加入  则不在计算单项优惠
            if (isCheckIntegralShare(routineDiscountDetailVOS)) {
                addRoutineDiscount(routineDiscountDetailVOS, optimalDiscountVO);
            }

            //获取积分抵扣优惠 最优项
            optimizeDiscountAmount = singleDiscountCheck(optimizeDiscountAmount, optimizeDiscountDetailVOS, singleDiscountAmount, optimalDiscountVO, routineDiscountDetailVOS);
            if (CollUtil.isNotEmpty(notOptimalDiscountList)) {
                routineDiscountDetailVOS.addAll(notOptimalDiscountList);
            }
        }
        return optimizeDiscountAmount;
    }

    /**
     * 单品级优惠 最优计算
     * 现在只处理了：限时特价，满减满折，会员折扣，会员卡折扣，兑换券，代金券
     */
    private BigDecimal dealShareDiscountCheck(SettlementApplyOrderDTO dto,
                                              Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                              List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                              BigDecimal optimizeDiscountAmount,
                                              List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                              SettlementApplyOrderVO tempOrderVo) {

        Integer discountOption = tempOrderVo.getDiscountList().get(NumberConstant.NUMBER_0).getDiscountOption();

        if (discountOption == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode()
                || discountOption == SettlementDiscountOptionEnum.FULL_OFF.getCode()
                || discountOption == SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode()) {
            optimizeDiscountAmount = doDealApplyDiscountShare(
                    dto,
                    discountVoMap,
                    routineDiscountDetailVOS,
                    optimizeDiscountAmount,
                    optimizeDiscountDetailVOS,
                    tempOrderVo,
                    SettlementDiscountOptionEnum.getEnum(discountOption));
        }

        if (discountOption == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode()) {
            List<SettlementApplyDiscountDetailVO> discountList = getSettlementApplyCouponExchangeList(dto, tempOrderVo);
            routineDiscountDetailVOS.addAll(discountList);
        }
        return optimizeDiscountAmount;
    }

    private BigDecimal doDealApplyDiscountShare(SettlementApplyOrderDTO dto,
                                                Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                                BigDecimal optimizeDiscountAmount,
                                                List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                                SettlementApplyOrderVO tempOrderVo,
                                                SettlementDiscountOptionEnum optionEnum) {
        //获取该优惠项优惠列表
        List<SettlementApplyDiscountDetailVO> discountList = getSettlementApplyDiscountList(dto, tempOrderVo, optionEnum);

        //优惠项 循环计算
        // 使用传统for循环避免ConcurrentModificationException
        // 因为循环内部可能会修改同一个列表引用
        for (int i = 0; i < discountList.size(); i++) {
            SettlementApplyDiscountDetailVO discountDetailVO = discountList.get(i);
            //当前优惠项是否共享
            boolean isLimitedShare = discountDetailVO.getIsAppend() == BooleanEnum.TRUE.getCode();
            log.info("当前优惠项是否共享:{},{}", isLimitedShare, discountDetailVO.getDiscountName());
            //当前单项优惠判断
            BigDecimal singleDiscountAmount = discountDetailVO.getDiscountAmount();
            //加入当前单项优惠
            addRoutineDiscount(routineDiscountDetailVOS, discountDetailVO);
            //判断是否满足订单使用条件
            if (singleDiscountAmount.compareTo(BigDecimal.ZERO) <= NumberConstant.NUMBER_0
                    || discountDetailVO.getIsEnabled() == BooleanEnum.FALSE.getCode()
                    || optimizeDiscountAmount.compareTo(dto.getOrderInfo().getShopCarOriginPayPrice()) >= 0) {
                log.info("当前优惠活动不符合订单条件：{}", discountDetailVO.getDiscountName());
                continue;
            }

            //共享优惠判断
            List<Integer> settlementDiscountOptionEnumList = SettlementDiscountOptionEnum.getShareOptionCode(dto, discountVoMap, optionEnum);
            //单项最优判断
            optimizeDiscountAmount = getSingleDiscountAmount(optimizeDiscountAmount, optimizeDiscountDetailVOS, singleDiscountAmount, discountDetailVO);
            log.info("当前共享优惠：{}", JSON.toJSONString(settlementDiscountOptionEnumList));
            //是否满足共享优惠
            if (CollUtil.isNotEmpty(settlementDiscountOptionEnumList) && isLimitedShare) {
                //过滤可以叠加的优惠guid
                List<String> appendDiscountOptionGuid = new ArrayList<>();
                for (Integer settlementDiscountOptionEnum : settlementDiscountOptionEnumList) {
                    // 根据优惠项枚举获取到具体的活动列表
                    List<SettlementApplyDiscountDetailVO> shareDiscountDetailVOS = getSettlementApplyDiscountList(dto, tempOrderVo, SettlementDiscountOptionEnum.getEnum(settlementDiscountOptionEnum));
                    if (CollUtil.isNotEmpty(shareDiscountDetailVOS)) {
                        appendDiscountOptionGuid.addAll(shareDiscountDetailVOS.stream().map(SettlementApplyDiscountDetailVO::getDiscountGuid).collect(Collectors.toList()));
                    }
                }
                dto.setAppendDiscountOptionGuid(appendDiscountOptionGuid);

                //获取共享优惠
                optimizeDiscountAmount = getShareOptimizeDiscountAmount(
                        dto,
                        discountDetailVO,
                        singleDiscountAmount,
                        optimizeDiscountDetailVOS,
                        optimizeDiscountAmount,
                        settlementDiscountOptionEnumList);
            }
        }
        return optimizeDiscountAmount;
    }

    private static BigDecimal getSingleDiscountAmount(BigDecimal optimizeDiscountAmount,
                                                      List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                                      BigDecimal singleDiscountAmount,
                                                      SettlementApplyDiscountDetailVO discountDetailVO) {
        if (singleDiscountAmount.compareTo(optimizeDiscountAmount) > NumberConstant.NUMBER_0) {
            optimizeDiscountDetailVOS.clear();
            optimizeDiscountDetailVOS.add(discountDetailVO);
            optimizeDiscountAmount = singleDiscountAmount;
        }
        return optimizeDiscountAmount;
    }


    public BigDecimal limitedTimeSpecialOptimize(List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                                 BigDecimal optimizeDiscountAmount,
                                                 List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                                 SettlementApplyDiscountDetailVO discountDetailVO,
                                                 BigDecimal singleDiscountAmount,
                                                 SettlementDiscountOptionEnum optionEnum) {
        if (singleDiscountAmount.compareTo(optimizeDiscountAmount) > NumberConstant.NUMBER_0) {

            optimizeDiscountDetailVOS.clear();
            optimizeDiscountDetailVOS.add(discountDetailVO);
            optimizeDiscountAmount = singleDiscountAmount;

            //若更新限时特价优惠且共享则移除积分 后续会再计算积分
            if (discountDetailVO.getDiscountOption() == optionEnum.getCode()) {
                routineDiscountDetailVOS.removeIf(in -> in.getDiscountOption() == SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode());
            }
        }
        return optimizeDiscountAmount;
    }


    private static List<SettlementApplyDiscountDetailVO> getSettlementApplyDiscountList(SettlementApplyOrderDTO dto,
                                                                                        SettlementApplyOrderVO tempOrderVo,
                                                                                        SettlementDiscountOptionEnum optionEnum) {
        //优惠列表  目前来看 一个大类只有一个
        SettlementApplyDiscountVO settlementApplyDiscountVO = tempOrderVo.getDiscountList().get(NumberConstant.NUMBER_0);

        List<SettlementApplyDiscountDetailVO> discountList = settlementApplyDiscountVO.getDiscountList();

        //限时特价活动需按照结算台里的顺序
        Set<String> discountGuidSet = dto.getAppendMap().get(optionEnum.getCode());

        if (CollUtil.isEmpty(discountGuidSet)) {
            return discountList;
        }

        // 将 Set 转换为 List
        List<String> discountGuidList = new ArrayList<>(discountGuidSet);

        // 根据 Set 中的顺序对 List 进行排序
        discountList.sort(Comparator.comparingInt(o -> discountGuidList.indexOf(o.getDiscountGuid())));
        return discountList;
    }

    private static List<SettlementApplyDiscountDetailVO> getSettlementApplyCouponExchangeList
            (SettlementApplyOrderDTO dto,
             SettlementApplyOrderVO tempOrderVo) {
        //优惠列表  目前来看 一个大类只有一个
        List<SettlementApplyDiscountVO> settlementApplyDiscountVOList = tempOrderVo.getDiscountList();
        List<SettlementApplyDiscountDetailVO> discountList = Lists.newArrayList();
        for (SettlementApplyDiscountVO settlementApplyDiscountVO : settlementApplyDiscountVOList) {
            List<SettlementApplyDiscountDetailVO> discountDetailVOS = settlementApplyDiscountVO.getDiscountList();
            discountList.addAll(discountDetailVOS);
        }

        //限时特价活动需按照结算台里的顺序
        Set<String> discountGuidSet = dto.getAppendMap().get(SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode());

        if (CollUtil.isEmpty(discountGuidSet)) {
            return discountList;
        }

        // 将 Set 转换为 List
        List<String> discountGuidList = new ArrayList<>(discountGuidSet);

        // 根据 Set 中的顺序对 List 进行排序
        discountList.sort(Comparator.comparingInt(o -> discountGuidList.indexOf(o.getDiscountGuid())));

        return discountList;
    }

    /**
     *
     * 共享最优计算
     */
    private BigDecimal getShareOptimizeDiscountAmount(SettlementApplyOrderDTO dto,
                                                      SettlementApplyDiscountDetailVO discountDetailVO,
                                                      BigDecimal singleDiscountAmount,
                                                      List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                                      BigDecimal optimizeDiscountAmount,
                                                      List<Integer> optionEnum) {

        SettlementApplyOrderInfoDTO orderInfo = dto.getOrderInfo();
        //优惠清零
        dto.clearCommodityFee();
        //叠加条件 加上当前优惠
        SettlementBusinessUtil.addCurrentDiscountNew(dto, Collections.singletonList(discountDetailVO));
        //循环累计优惠
        BigDecimal optionDiscountAmount = singleDiscountAmount;

        //叠加商品列表优惠
        List<SettlementApplyDiscountDetailVO> discountDetailVOList = Lists.newArrayList();
        discountDetailVOList.add(discountDetailVO);
        for (Integer option : optionEnum) {
            //需要查询的优惠项
            dto.setListOptions(Collections.singletonList(option));

            log.info("共享优惠查询：{}", JSON.toJSONString(dto));

            //叠加优惠计算
            List<SettlementApplyOrderVO> disOrderVoList = memberBaseFeign.listDiscount(dto);
            //获取优惠项
            List<SettlementApplyDiscountVO> discountVOList = disOrderVoList
                    .stream()
                    .flatMap(vl -> vl.getDiscountList().stream())
                    .collect(Collectors.toList());

            for (SettlementApplyDiscountVO accountVO : discountVOList) {
                //具体优惠项
                if (CollUtil.isNotEmpty(accountVO.getDiscountList())) {
                    // 取最优
                    List<SettlementApplyDiscountDetailVO> discountDetailVOS = accountVO.getDiscountList()
                            .stream()
                            .filter(in -> in.getOptimal() == BooleanEnum.TRUE.getCode())
                            .collect(Collectors.toList());
                    //最优判断
                    if (CollUtil.isNotEmpty(discountDetailVOS)) {
                        SettlementApplyDiscountDetailVO detailVO = discountDetailVOS.get(0);
                        optionDiscountAmount = optionDiscountAmount.add(detailVO.getDiscountAmount());
                        discountDetailVOList.add(detailVO);
                        //叠加条件 加上当前优惠
                        //优惠清零
                        dto.clearCommodityFee();
                        SettlementBusinessUtil.addCurrentDiscountNew(dto, discountDetailVOList);
                        optimizeDiscountAmount = checkOptimalDetailVO(
                                optimizeDiscountDetailVOS,
                                optimizeDiscountAmount,
                                detailVO,
                                optionDiscountAmount,
                                discountDetailVOList);
                    }
                }
            }
            if (optimizeDiscountAmount.compareTo(orderInfo.getShopCarOriginPayPrice()) >= 0) {
                log.info("当前共享优惠计算已到达上限金额：{}", JSON.toJSONString(optimizeDiscountDetailVOS));
                break;
            }
        }
        return optimizeDiscountAmount;
    }


    private static BigDecimal checkOptimalDetailVO(List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                                   BigDecimal optimizeDiscountAmount,
                                                   SettlementApplyDiscountDetailVO detailVO,
                                                   BigDecimal optionDiscountAmount,
                                                   List<SettlementApplyDiscountDetailVO> discountDetailVOList) {
        if (Objects.nonNull(detailVO.getDiscountAmount())
                && (optionDiscountAmount.compareTo(optimizeDiscountAmount) > NumberConstant.NUMBER_0)) {
            optimizeDiscountDetailVOS.clear();

            //添加到最优优惠项 可替换为guid 在最后进行匹配
            optimizeDiscountDetailVOS.addAll(discountDetailVOList);

            optimizeDiscountAmount = optionDiscountAmount;

        }
        return optimizeDiscountAmount;
    }

    private SettlementApplyOrderVO dealSettlementApplyOrderVO
            (List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
             List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
             BigDecimal optimizeDiscountAmount) {
        SettlementApplyOrderVO settlementApplyOrderVO = new SettlementApplyOrderVO();

        //若没有最优  则表示无可用优惠
        if (CollUtil.isEmpty(optimizeDiscountDetailVOS)) {
            return getSettlementApplyOrderVO(routineDiscountDetailVOS, optimizeDiscountAmount, settlementApplyOrderVO);
        }

        Map<String, SettlementApplyDiscountDetailVO> optimizeDiscountDetailMap = optimizeDiscountDetailVOS.stream()
                .collect(Collectors.toMap(in ->
                                in.getDiscountOption() == SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode() ? in.getDiscountOptionId() : in.getDiscountGuid(),
                        Function.identity(), (entity1, entity2) -> entity1));

        //最优商品列表合并
        SettlementBusinessUtil.addOptimizeCommodityList(optimizeDiscountDetailVOS, settlementApplyOrderVO);

        //列表查询优惠选中
        setDiscountListCheck(routineDiscountDetailVOS, optimizeDiscountDetailMap);

        //优惠项组装
        return getSettlementApplyOrderVO(routineDiscountDetailVOS, optimizeDiscountAmount, settlementApplyOrderVO);
    }

    private SettlementApplyOrderVO getSettlementApplyOrderVO
            (List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
             BigDecimal optimizeDiscountAmount,
             SettlementApplyOrderVO settlementApplyOrderVO) {
        //优惠项组装
        SettlementBusinessUtil.addApplyDiscountDetail(routineDiscountDetailVOS, settlementApplyOrderVO);

        //订单详情
        SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO();
        oderInfo.setDiscountAmount(optimizeDiscountAmount);
        settlementApplyOrderVO.setOderInfo(oderInfo);

        return settlementApplyOrderVO;
    }

    private static void addRoutineDiscount(List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                           SettlementApplyDiscountDetailVO discountDetailVO) {
        //若优惠小于0 则修改此状态
        if (discountDetailVO.getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0) {
            discountDetailVO.setIsEnabled(BooleanEnum.FALSE.getCode());
        }
        routineDiscountDetailVOS.add(discountDetailVO);
    }


    private static void setDiscountListCheck(List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                             Map<String, SettlementApplyDiscountDetailVO> optimizeDiscountDetailMap) {
        routineDiscountDetailVOS.forEach(in -> {
            SettlementApplyDiscountDetailVO discountDetailVO;
            if (in.getDiscountOption() == SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode()) {
                discountDetailVO = optimizeDiscountDetailMap.get(in.getDiscountOptionId());
            } else {
                discountDetailVO = optimizeDiscountDetailMap.get(in.getDiscountGuid());
            }

            if (Objects.nonNull(discountDetailVO)) {
                //优先使用参与共享的优惠
                BeanUtils.copyProperties(discountDetailVO, in);
                //自动选中时 才勾选
                in.setIsChecked(BooleanEnum.TRUE.getCode());

            }
            in.setCommodityList(null);
        });
    }


    private static boolean isCheckIntegralShare(List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS) {
        if (!routineDiscountDetailVOS.isEmpty()) {
            List<SettlementApplyDiscountDetailVO> settlementApplyDiscountDetailVOS = routineDiscountDetailVOS
                    .stream()
                    .filter(in -> in.getDiscountOption() == SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode()
                            && in.getOptimal() == 1)
                    .collect(Collectors.toList());
            return CollUtil.isEmpty(settlementApplyDiscountDetailVOS);
        }
        return true;
    }

    private void dealSpecialOrderCoupon(SettlementApplyOrderDTO dto,
                                        List<SettlementApplyDiscountDetailVO> orderCouponDiscountList,
                                        List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                        List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                        SettlementApplyRuleVO applyRule) {
        if (dto.getIsAppointDiscount() == BooleanEnum.FALSE.getCode() && CollUtil.isNotEmpty(orderCouponDiscountList)) {
            //判断是否有限时特价勾选  若有 则判断限时特价是否共享  若共享 则在限时特价的基础上分别进行计算  若不共享 则直接返回折扣券单项优惠
            //判断是否选中限时特价
            List<SettlementApplyDiscountDetailVO> applyDiscountDetailVOS =
                    optimizeDiscountDetailVOS
                            .stream()
                            .filter(d -> d.getDiscountOption() == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode())
                            .collect(Collectors.toList());
            //判断限时特价部位空格 并且共享
            if (CollUtil.isNotEmpty(applyDiscountDetailVOS)
                    && applyDiscountDetailVOS.get(0).getIsAppend() == BooleanEnum.TRUE.getCode()) {
                checkDiscountCoupon(dto, orderCouponDiscountList, routineDiscountDetailVOS, applyDiscountDetailVOS.get(0), applyRule);
            } else {
                //直接返回折扣券单项优惠
                routineDiscountDetailVOS.addAll(orderCouponDiscountList
                        .stream()
                        .sorted(Comparator.comparing(SettlementApplyDiscountDetailVO::getDiscountAmount).reversed())
                        .collect(Collectors.toList()));
            }
        }
    }

    public void checkDiscountCoupon(SettlementApplyOrderDTO dto,
                                    List<SettlementApplyDiscountDetailVO> orderCouponDiscountList,
                                    List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS,
                                    SettlementApplyDiscountDetailVO settlementApplyDiscountDetailVO,
                                    SettlementApplyRuleVO applyRule) {

        // 按结算台规则顺序依次处理不同类型的优惠券
        List<Integer> orderedDiscountOptions = applyRule.getAllDiscountOptions();
        if (CollUtil.isNotEmpty(orderedDiscountOptions)) {
            // 按优惠类型分组
            Map<Integer, List<SettlementApplyDiscountDetailVO>> couponGroupMap = 
                orderCouponDiscountList.stream()
                    .collect(Collectors.groupingBy(SettlementApplyDiscountDetailVO::getDiscountOption));
            
            // 按规则顺序依次处理每种类型
            for (Integer discountOption : orderedDiscountOptions) {
                List<SettlementApplyDiscountDetailVO> couponsOfType = couponGroupMap.get(discountOption);
                if (CollUtil.isEmpty(couponsOfType)) {
                    continue;
                }
                
                // 筛选可叠加的优惠券
                List<String> discountOptionId = couponsOfType.stream()
                    .filter(in -> in.getIsEnabled() == BooleanEnum.TRUE.getCode() && in.getIsAppend() == BooleanEnum.TRUE.getCode())
                    .map(SettlementApplyDiscountDetailVO::getDiscountOptionId)
                    .collect(Collectors.toList());
                
                if (CollUtil.isNotEmpty(discountOptionId)) {
                    // 调用base服务重新计算当前类型的优惠券
                    SettlementBusinessUtil.foldSettlementApplyOrderVOS(dto, Collections.singletonList(settlementApplyDiscountDetailVO), discountOptionId, discountOption);
                    List<SettlementApplyOrderVO> disOrderVoList = memberBaseFeign.listDiscount(dto);
                    SettlementBusinessUtil.checkDisOrderVoList(disOrderVoList, couponsOfType);
                }
                
                // 移除已处理的券
                orderCouponDiscountList.removeIf(in -> discountOptionId.contains(in.getDiscountOptionId()));
            }
        } else {
            // 如果没有配置顺序，使用原来的逻辑
            List<String> discountOptionId = orderCouponDiscountList.stream().filter(in -> in.getIsEnabled() == BooleanEnum.TRUE.getCode()
                    && in.getIsAppend() == BooleanEnum.TRUE.getCode()).map(SettlementApplyDiscountDetailVO::getDiscountOptionId).collect(Collectors.toList());

            orderCouponDiscountList.removeIf(in -> discountOptionId.contains(in.getDiscountOptionId()));

            if (CollUtil.isNotEmpty(discountOptionId)) {
                SettlementBusinessUtil.foldSettlementApplyOrderVOS(dto, Collections.singletonList(settlementApplyDiscountDetailVO), discountOptionId, SettlementDiscountOptionEnum.COUPON_DISCOUNT.getCode());
                List<SettlementApplyOrderVO> disOrderVoList = memberBaseFeign.listDiscount(dto);
                SettlementBusinessUtil.checkDisOrderVoList(disOrderVoList, orderCouponDiscountList);
            }
        }

        //直接返回剩余的折扣券单项优惠
        routineDiscountDetailVOS.addAll(orderCouponDiscountList
                .stream()
                .sorted(Comparator.comparing(SettlementApplyDiscountDetailVO::getDiscountAmount).reversed())
                .collect(Collectors.toList()));
    }

    /**
     * 单项优惠 最优替换
     */
    private static BigDecimal singleDiscountCheck(
            BigDecimal optimizeDiscountAmount,
            List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
            BigDecimal singleDiscountAmount,
            SettlementApplyDiscountDetailVO discountDetailVO,
            List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS) {
        if (singleDiscountAmount.compareTo(optimizeDiscountAmount) > NumberConstant.NUMBER_0) {

            optimizeDiscountDetailVOS.clear();
            optimizeDiscountDetailVOS.add(discountDetailVO);
            optimizeDiscountAmount = singleDiscountAmount;

            routineDiscountDetailVOS.removeIf(in -> in.getDiscountOption() == SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode());

            routineDiscountDetailVOS.add(discountDetailVO);
        }
        return optimizeDiscountAmount;
    }
}
