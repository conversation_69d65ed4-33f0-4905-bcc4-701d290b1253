package com.holderzone.member.settlement.service.handle;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementAfterApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyDiscountBaseReqDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderCalculateDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.settlement.service.apply.util.SettlementBusinessUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 结算台计算器
 */
@AllArgsConstructor
@Component
@Slf4j
public class SettlementCalculateSupport {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * @param discountVoMap 指定优惠计算
     * @return 最优结果
     */
    public SettlementApplyOrderVO appointOptimizeDiscount(SettlementApplyOrderDTO dto,
                                                          SettlementApplyRuleVO applyRule,
                                                          Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                          Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap) {
        SettlementApplyOrderVO settlementApplyOrderVO = new SettlementApplyOrderVO();

        //最优具体优惠项  需要最终进行勾选的项
        List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS = new ArrayList<>();

        //补全优惠项
        Map<String, SettlementApplyDiscountDetailVO> discountDetailVOS = new HashMap<>();

        //处理叠加标志
        dealMapAppendSign(applyRule, settlementApplyOrderMap);
        dealMapAppendSign(applyRule, discountVoMap);

        //遍历所有类型
        for (Map.Entry<Integer, List<SettlementApplyOrderVO>> entry : settlementApplyOrderMap.entrySet()) {
            final List<SettlementApplyOrderVO> orderVOList = entry.getValue();
            SettlementApplyOrderVO tempOrderVo = orderVOList.get(NumberConstant.NUMBER_0);

            Integer discountOption = tempOrderVo.getDiscountList().get(NumberConstant.NUMBER_0)
                    .getDiscountOption();

            //优惠统一算法
            doSettlementDiscountHandler(
                    dto,
                    settlementApplyOrderMap,
                    discountOption,
                    optimizeDiscountDetailVOS,
                    tempOrderVo,
                    discountDetailVOS,
                    discountVoMap);
        }

        //处理计算结果
        dealAppointSettlementApply(optimizeDiscountDetailVOS, settlementApplyOrderVO, discountDetailVOS, applyRule, settlementApplyOrderMap);
        return settlementApplyOrderVO;
    }

    private void dealMapAppendSign(SettlementApplyRuleVO applyRule, Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap) {
        for (Map.Entry<Integer, List<SettlementApplyOrderVO>> entry : settlementApplyOrderMap.entrySet()) {
            //处理叠加标志
            final List<SettlementApplyOrderVO> orderVOList = entry.getValue();
            //大类，只有一个
            SettlementApplyOrderVO tempOrderVo = orderVOList.get(NumberConstant.NUMBER_0);
            SettlementBusinessUtil.handlerAppendSign(applyRule, tempOrderVo.getDiscountList());
        }
    }

    private void dealAppointSettlementApply(List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                            SettlementApplyOrderVO settlementApplyOrderVO,
                                            Map<String, SettlementApplyDiscountDetailVO> discountVoMap,
                                            SettlementApplyRuleVO applyRule,
                                            Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap) {

        List<SettlementApplyDiscountDetailVO> discountDetailVOS = new ArrayList<>(discountVoMap.values());

        if (!optimizeDiscountDetailVOS.isEmpty()) {
            //最优商品列表合并
            SettlementBusinessUtil.addOptimizeCommodityList(optimizeDiscountDetailVOS, settlementApplyOrderVO);

            //批量处理叠加标志和启用状态，提升性能
            SettlementBusinessUtil.batchHandlerDiscountDetails(applyRule, optimizeDiscountDetailVOS, settlementApplyOrderMap);

            //兜底防重复
            List<String> allDiscountOptionIds = new ArrayList<>();
            //选中优惠
            //指定计算的情况下直接勾选所有共享优惠
            optimizeDiscountDetailVOS.forEach(in -> {
                //判断是否有优惠金额
                if (in.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                    in.setIsChecked(BooleanEnum.TRUE.getCode());
                } else {
                    in.setIsChecked(BooleanEnum.FALSE.getCode());
                }

                in.setCommodityList(null);

                if (StringUtils.isNotEmpty(in.getDiscountOptionId())) {
                    allDiscountOptionIds.add(in.getDiscountOptionId());
                } else {
                    allDiscountOptionIds.add(in.getDiscountGuid());
                }
            });

            discountDetailVOS.removeIf(in -> StringUtils.isNotEmpty(in.getDiscountOptionId()) ?
                    allDiscountOptionIds.contains(in.getDiscountOptionId()) :
                    allDiscountOptionIds.contains(in.getDiscountGuid()));

            dealWeightProof(optimizeDiscountDetailVOS, discountDetailVOS);

            //优惠项组装
            SettlementBusinessUtil.addApplyDiscountDetail(optimizeDiscountDetailVOS, settlementApplyOrderVO);

            //订单详情
            settlementApplyOrderVO.setOderInfo(new SettlementApplyOderInfoVO());
        }
    }

    private static void dealWeightProof(List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                        List<SettlementApplyDiscountDetailVO> discountDetailVOS) {
        if (CollUtil.isNotEmpty(discountDetailVOS)) {
            discountDetailVOS.forEach(in -> {
                in.setIsChecked(BooleanEnum.FALSE.getCode());
                in.setCommodityList(null);

                if ((Objects.isNull(in.getDiscountAmount())
                        || in.getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0)
                        && ThreadLocalCache.getSystem() != SystemEnum.MALL.getCode()) {
                    in.setIsEnabled(BooleanEnum.FALSE.getCode());
                }
            });
            optimizeDiscountDetailVOS.addAll(discountDetailVOS);
        }
    }

    private void doSettlementDiscountHandler(
            SettlementApplyOrderDTO dto,
            Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap,
            Integer discountOption,
            List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
            SettlementApplyOrderVO tempOrderVo,
            Map<String, SettlementApplyDiscountDetailVO> discountDetailVOMap,
            Map<Integer, List<SettlementApplyOrderVO>> discountVoMap) {

        if (discountVoMap.containsKey(discountOption)) {
            SettlementApplyOrderVO applyOrderVo = getApplyOrderVO(discountOption, discountVoMap);
            log.info("当前处理选中优惠项：{}", discountOption);
            
            doDiscountProcessor(
                    dto,
                    settlementApplyOrderMap,
                    optimizeDiscountDetailVOS,
                    applyOrderVo,
                    discountDetailVOMap,
                    SettlementDiscountOptionEnum.getEnum(discountOption));
        } else {
            log.info("当前处理未选中优惠项：{}", discountOption);
            //处理未选中的优惠
            doNotInDiscountProcessor(dto, discountOption, optimizeDiscountDetailVOS, tempOrderVo, discountDetailVOMap);
        }
    }

    private void doNotInDiscountProcessor(SettlementApplyOrderDTO dto,
                                          Integer discountOption,
                                          List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                          SettlementApplyOrderVO tempOrderVo,
                                          Map<String, SettlementApplyDiscountDetailVO> discountDetailVOMap) {

        //当前优惠列表
        List<SettlementApplyDiscountDetailVO> allDiscountDetailVOS =
                tempOrderVo.getDiscountList().stream().flatMap(detail -> detail.getDiscountList().stream())
                        .collect(Collectors.toList());

        //有前置优惠
        if (CollUtil.isNotEmpty(optimizeDiscountDetailVOS)) {
            //计算未勾选的券  需要判断是否共享
            doUncheckedDiscount(dto, optimizeDiscountDetailVOS, discountDetailVOMap, allDiscountDetailVOS, SettlementDiscountOptionEnum.getEnum(discountOption));
        } else {
            SettlementBusinessUtil.putDiscountDetailListVO(discountDetailVOMap, allDiscountDetailVOS);
        }
    }

    private static SettlementApplyOrderVO getApplyOrderVO(Integer discountOption, Map<Integer, List<SettlementApplyOrderVO>> discountVoMap) {
        List<SettlementApplyOrderVO> applyOrderList = discountVoMap.get(discountOption);

        //大类，只有一个
        return applyOrderList.get(NumberConstant.NUMBER_0);
    }

    private void putDiscountDetailVO(Map<String, SettlementApplyDiscountDetailVO> discountDetailVOMap,
                                     List<SettlementApplyOrderVO> settlementApplyOrderList) {
        List<SettlementApplyDiscountVO> discountVOList = settlementApplyOrderList
                .stream()
                .flatMap(vl -> vl.getDiscountList().stream())
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(discountVOList)) {
            for (SettlementApplyDiscountVO settlementApplyDiscountVO : discountVOList) {
                SettlementBusinessUtil.putDiscountDetailListVO(discountDetailVOMap, settlementApplyDiscountVO.getDiscountList());
            }
        }
    }

    /**
     * 处理未选中优惠 叠加计算
     */
    private void doUncheckedDiscount(SettlementApplyOrderDTO dto,
                                     List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                     Map<String, SettlementApplyDiscountDetailVO> discountDetailVOMap,
                                     List<SettlementApplyDiscountDetailVO> allDiscountDetailVOS,
                                     SettlementDiscountOptionEnum anEnum) {
        if (CollUtil.isNotEmpty(allDiscountDetailVOS)) {
            //当前可共享的优惠项
            List<SettlementApplyDiscountDetailVO> appendDiscountDetailVOS = allDiscountDetailVOS.stream()
                    .filter(detail -> detail.getIsAppend() == BooleanEnum.TRUE.getCode())
                    .collect(Collectors.toList());
            //当前不可共享的优惠项
            List<SettlementApplyDiscountDetailVO> notAppendDiscountDetailVOS = allDiscountDetailVOS.stream()
                    .filter(detail -> detail.getIsAppend() == BooleanEnum.FALSE.getCode())
                    .collect(Collectors.toList());

            //若有选中则取第一个来判断是否可以共享
            SettlementApplyDiscountDetailVO detailVO = optimizeDiscountDetailVOS.get(0);

            //若选中优惠为互斥  则将所有当前优惠不叠加计算
            if (detailVO.getIsAppend() == BooleanEnum.FALSE.getCode()) {
                notAppendDiscountDetailVOS.addAll(appendDiscountDetailVOS);

                appendDiscountDetailVOS.clear();
            }

            //若可以共享
            if (CollUtil.isNotEmpty(appendDiscountDetailVOS)) {
                //分别计算可共享的优惠项
                List<String> discountOptionIds = appendDiscountDetailVOS
                        .stream()
                        .map(SettlementApplyDiscountDetailVO::getDiscountOptionId)
                        .collect(Collectors.toList());
                //积分是最底层 不能重复叠加计算
                SettlementBusinessUtil.foldSettlementApplyOrderVOS(dto, optimizeDiscountDetailVOS, discountOptionIds, anEnum.getCode());
                SettlementApplyOrderDTO orderDTO = SettlementBusinessUtil.getSettlementApplyOrderDTO(dto);
                orderDTO.setListOptions(Collections.singletonList(anEnum.getCode()));
                List<SettlementApplyOrderVO> disOrderVoList = memberBaseFeign.listDiscount(orderDTO);
                putDiscountDetailVO(discountDetailVOMap, disOrderVoList);
            }

            //加入不能共享的优惠项
            if (CollUtil.isNotEmpty(notAppendDiscountDetailVOS)) {
                SettlementBusinessUtil.putDiscountDetailListVO(discountDetailVOMap, notAppendDiscountDetailVOS);
            }
        }
    }

    /**
     * 优惠统一处理
     */
    private void doDiscountProcessor(SettlementApplyOrderDTO dto,
                                     Map<Integer, List<SettlementApplyOrderVO>> settlementApplyOrderMap,
                                     List<SettlementApplyDiscountDetailVO> optimizeDiscountDetailVOS,
                                     SettlementApplyOrderVO tempOrderVo,
                                     Map<String, SettlementApplyDiscountDetailVO> discountDetailVOMap,
                                     SettlementDiscountOptionEnum enumEnum) {
        //当前优惠所有选项
        List<SettlementApplyOrderVO> settlementApplyOrderVOS = settlementApplyOrderMap.get(enumEnum.getCode());

        //未勾选的优惠项
        List<SettlementApplyDiscountDetailVO> allDiscountDetailVOS = new ArrayList<>();

        //勾选的优惠id
        List<String> discountOptionIds = getDiscountOptionIds(dto, enumEnum);

        //过滤已勾选的优惠项
        allDiscountDetailVOS = getFiltrationAllDiscountVOS(settlementApplyOrderVOS, allDiscountDetailVOS, discountOptionIds);

        //是否存在前置优惠
        if (CollUtil.isNotEmpty(optimizeDiscountDetailVOS)) {
            //加入未勾选优惠
            doUncheckedDiscount(dto, optimizeDiscountDetailVOS, discountDetailVOMap, allDiscountDetailVOS, enumEnum);
            //是否勾选
            if (isChooseDiscount(dto, enumEnum)) {
                SettlementBusinessUtil.foldSettlementApplyOrderVOS(dto, optimizeDiscountDetailVOS, discountOptionIds, enumEnum.getCode());
                
                // 获取优惠订单列表
                List<SettlementApplyOrderVO> disOrderVoList = getDiscountOrderList(dto, enumEnum, discountOptionIds);
                if (disOrderVoList != null) {
                    SettlementBusinessUtil.checkDisOrderVoList(disOrderVoList, optimizeDiscountDetailVOS);
                }
            }
        } else {
            if (isChooseDiscount(dto, enumEnum)) {
                for (SettlementApplyDiscountVO settlementApplyDiscountVO : tempOrderVo.getDiscountList()) {
                    optimizeDiscountDetailVOS.addAll(settlementApplyDiscountVO.getDiscountList());
                }
            }
            //加入未勾选优惠
            if (CollUtil.isNotEmpty(allDiscountDetailVOS)) {
                SettlementBusinessUtil.putDiscountDetailListVO(discountDetailVOMap, allDiscountDetailVOS);
            }
        }
    }

    private static List<SettlementApplyDiscountDetailVO> getFiltrationAllDiscountVOS(List<SettlementApplyOrderVO> settlementApplyOrderVOS,
                                                                                     List<SettlementApplyDiscountDetailVO> allDiscountDetailVOS,
                                                                                     List<String> discountOptionIds) {
        if (CollUtil.isNotEmpty(settlementApplyOrderVOS)) {
            allDiscountDetailVOS = settlementApplyOrderVOS.get(0).getDiscountList().stream().flatMap(detail -> detail.getDiscountList().stream())
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(discountOptionIds)) {
                Map<String, String> map = discountOptionIds.stream().collect(Collectors.
                        toMap(Function.identity(), Function.identity(), (entity1, entity2) -> entity1));
                //过滤已勾选的优惠
                allDiscountDetailVOS.removeIf(in -> map.containsKey(in.getDiscountOptionId()));
                allDiscountDetailVOS.removeIf(in -> map.containsKey(in.getDiscountGuid()));
            }
        }
        return allDiscountDetailVOS;
    }

    private static List<String> getDiscountOptionIds(SettlementApplyOrderDTO dto, SettlementDiscountOptionEnum optionEnum) {
        return dto.getCheckDiscountList().stream()
                .filter(in -> optionEnum.getCode() == in.getDiscountOption())
                .map(on -> StringUtils.isNotEmpty(on.getDiscountOptionId()) ? on.getDiscountOptionId() : on.getDiscountGuid())
                .collect(Collectors.toList());
    }

    /**
     * 获取优惠订单列表
     * 根据优惠类型决定调用calculateDiscount还是listDiscount
     */
    private List<SettlementApplyOrderVO> getDiscountOrderList(SettlementApplyOrderDTO dto, SettlementDiscountOptionEnum enumEnum, List<String> discountOptionIds) {
        if (SettlementDiscountOptionEnum.isOrderDiscount(enumEnum.getCode())) {
            // 对于订单级券类型（代金券、折扣券），调用calculateDiscount获取商品分摊信息
            SettlementApplyOrderCalculateDTO calculateDTO = new SettlementApplyOrderCalculateDTO();
            BeanUtils.copyProperties(dto, calculateDTO);
            // 设置选中的优惠项
            calculateDTO.setOptionHandlers(discountOptionIds.stream()
                .map(id -> enumEnum.getCode())
                .distinct()
                .collect(Collectors.toList()));
            return memberBaseFeign.calculateDiscount(calculateDTO);
        } else {
            // 商品级券类型直接调用listDiscount
            return memberBaseFeign.listDiscount(dto);
        }
    }

    /**
     * 判断是否选中优惠
     */
    private static boolean isChooseDiscount(SettlementApplyOrderDTO dto, SettlementDiscountOptionEnum integralExplain) {
        return CollUtil.isNotEmpty(dto.getCheckDiscountList().stream()
                .filter(in -> integralExplain.getCode() == in.getDiscountOption())
                .map(on -> StringUtils.isNotEmpty(on.getDiscountOptionId()) ? on.getDiscountOptionId() : on.getDiscountGuid())
                .collect(Collectors.toList()));
    }


}
