package com.holderzone.member.settlement.service.rule.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.SqlConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.ChoinceEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberSettlementException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyRuleVO;
import com.holderzone.member.common.module.settlement.rule.dto.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.qo.DeleteSettlementRuleDiscountQO;
import com.holderzone.member.common.module.settlement.rule.qo.SettlementRuleDiscountQO;
import com.holderzone.member.common.module.settlement.rule.qo.SettlementRuleQO;
import com.holderzone.member.common.module.settlement.rule.qo.SettlementRuleSameQO;
import com.holderzone.member.common.module.settlement.rule.vo.*;
import com.holderzone.member.common.support.StoreCommonSupport;
import com.holderzone.member.common.util.StringHandlerUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.verify.VerifyUtil;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import com.holderzone.member.settlement.assembler.SettlementRuleAssembler;
import com.holderzone.member.settlement.entity.rule.HsaSettlementRule;
import com.holderzone.member.settlement.entity.rule.HsaSettlementRuleDiscount;
import com.holderzone.member.settlement.entity.rule.HsaSettlementRuleStore;
import com.holderzone.member.settlement.mapper.rule.HsaSettlementRuleMapper;
import com.holderzone.member.settlement.service.rule.IHsaSettlementRuleDiscountService;
import com.holderzone.member.settlement.service.rule.IHsaSettlementRuleService;
import com.holderzone.member.settlement.service.rule.IHsaSettlementRuleStoreService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <p>
 * 结算规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Slf4j
@Service
public class HsaSettlementRuleServiceImpl extends HolderBaseServiceImpl<HsaSettlementRuleMapper, HsaSettlementRule> implements IHsaSettlementRuleService {

    /**
     * 规则mapper
     */
    @Resource
    HsaSettlementRuleMapper settlementRuleMapper;

    /**
     * 门店公共类
     */
    @Resource
    StoreCommonSupport storeCommonSupport;

    @Resource
    private ExternalSupport externalSupport;

    /**
     * 规则门店
     */
    @Resource
    IHsaSettlementRuleStoreService settlementRuleStoreService;

    /**
     * 生成guid
     */
    @Resource
    GuidGeneratorUtil guidGeneratorUtil;

    /**
     * 规则优惠项
     */
    @Resource
    IHsaSettlementRuleDiscountService settlementRuleDiscountService;

    /**
     * 事务处理
     */
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * redis
     */
    @Resource
    private RedissonClient redissonClient;

    /**
     * 线程池
     */
    @Resource
    public Executor memberSettlementThreadExecutor;

    /**
     * redis
     */
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Integer exists(String operSubjectGuid) {
        return settlementRuleMapper.exists(operSubjectGuid);
    }

    @Override
    public SettlementApplyRuleVO getCacheApplyRule(String operSubjectGuid, String storeGuid, String applyBusiness) {
        return getApplyRule(storeGuid, applyBusiness);
    }

    @Override
    public SettlementApplyRuleVO getApplyRule(String storeGuid, String applyBusiness) {
        //获取规则
        HsaSettlementRule applyRule;
        applyRule = getApplyRuleData(storeGuid, applyBusiness);
        if (Objects.isNull(applyRule)) {
            applyRule = settlementRuleMapper.selectOne(new LambdaQueryWrapper<HsaSettlementRule>()
                    .eq(HsaSettlementRule::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                    .eq(HsaSettlementRule::getIsDefault, BooleanEnum.TRUE.getCode()));
        }
        SettlementApplyRuleVO settlementApplyRuleVO = new SettlementApplyRuleVO();
        //基本规则
        SettlementRuleBaseDTO ruleBaseDTO = SettlementRuleAssembler.toSettlementRuleBaseDTO(applyRule);
        settlementApplyRuleVO.setBaseRule(ruleBaseDTO);

        //可叠加项
        SettlementRuleDiscountQO discountQo = new SettlementRuleDiscountQO()
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setSettlementRuleGuid(applyRule.getGuid())
                .setIsAppend(BooleanEnum.TRUE.getCode());
        final List<SettlementRuleDiscountTreeVO> appendDiscounts = settlementRuleDiscountService.listAll(discountQo);
        settlementApplyRuleVO.setAppendDiscounts(appendDiscounts);
        //不可叠加项
        discountQo.setIsAppend(BooleanEnum.FALSE.getCode());
        final List<SettlementRuleDiscountTreeVO> disAppendDiscounts = settlementRuleDiscountService.listAll(discountQo);
        settlementApplyRuleVO.setDisAppendDiscounts(disAppendDiscounts);

        settlementApplyRuleVO.setAllDiscounts(new ArrayList<>(appendDiscounts));
        settlementApplyRuleVO.getAllDiscounts().addAll(disAppendDiscounts);

        //规则排序
        settlementApplyRuleVO.setAllDiscountOptions(getAllOptions(settlementApplyRuleVO));


        //优惠券限制数量 > 1 的
        final Map<String, Integer> couponNumberMap = settlementApplyRuleVO.getAllDiscounts().parallelStream()
                .filter(d -> SettlementDiscountOptionEnum.isCoupon(d.getDiscountOption()) && d.getDiscountNum() > 1)
                .collect(Collectors.toMap(d ->
                                SettlementDiscountOptionEnum.optionMapKey(d.getDiscountOption(), d.getDiscountGuid()),
                        SettlementRuleDiscountVO::getDiscountNum,
                        (v1, v2) -> v2));
        settlementApplyRuleVO.setCouponLimtNumberMap(couponNumberMap);
        return settlementApplyRuleVO;
    }

    /**
     * 优惠项排序[只排大类]： 可叠加项 -> 不可叠加项
     *
     * @param applyRule 结算规则
     * @return
     */
    private List<Integer> getAllOptions(SettlementApplyRuleVO applyRule) {
        if (applyRule.getBaseRule().getGuid().equals(StringConstant.PARENT)) {
            //默认顺序
            return SettlementDiscountOptionEnum.implOptions();
        }
        final List<Integer> collect = applyRule.getAllDiscounts().stream()
                .sorted(Comparator.comparing(SettlementRuleDiscountTreeVO::getDiscountType))
                .map(SettlementRuleDiscountVO::getDiscountOption)
                .collect(Collectors.toList());
        List<Integer> integerList = collect.stream()
                .distinct()
                .collect(Collectors.toList());

        //特殊优惠项排序处理
        integerList.removeIf(in -> in == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode());
        integerList.add(0, SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode());
        return integerList;
    }

    /**
     * 获取应用规则数据
     *
     * @param storeGuid     门店
     * @param applyBusiness 业务
     * @return
     */
    private HsaSettlementRule getApplyRuleData(String storeGuid, String applyBusiness) {
        SettlementRuleQO ruleQo = new SettlementRuleQO();
        ruleQo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //包含业务
        ruleQo.setApplyBusiness(StringHandlerUtil.toMysqlRegStr(Collections.singletonList(applyBusiness)));
        //包含门店
        if (ThreadLocalCache.getSystem() != SystemEnum.MALL.getCode()) {
            ruleQo.setStoreGuids(Collections.singletonList(storeGuid));
        }
        return settlementRuleMapper.getApplyRule(ruleQo);
    }

    @Override
    public PageResult<SettlementRuleListVO> page(SettlementRuleQO qo) {
        //构建查询条件
        final boolean noData = buildRuleQo(qo);
        if (noData) {
            return PageUtil.emptyPageResult();
        }
        List<HsaSettlementRule> rules = settlementRuleMapper.list(qo);
        //组装数据
        List<SettlementRuleListVO> listVOList = SettlementRuleAssembler.toSettlementRuleListVOList(rules);
        //构造 业务、门店
        return PageUtil.pageResult(rules, listVOList);
    }

    @Override
    public SettlementRuleListVO getBusinessStore(String ruleGuid) {
        //构建查询条件
        final HsaSettlementRule rule = this.queryByGuid(ruleGuid);
        //组装数据
        SettlementRuleListVO vo = SettlementRuleAssembler.toSettlementRuleListVO(rule);
        //构造规则关联门店
        buildSettlementRuleListVO(vo);
        return vo;
    }

    /**
     * 构造规则列表对象
     *
     * @param vo 业务
     */
    private void buildSettlementRuleListVO(SettlementRuleListVO vo) {
        if (Objects.isNull(vo)) {
            return;
        }
        //全部场景
        if (vo.getApplyBusiness() == ChoinceEnum.ALL.getCode()) {
            final List<String> allApplyBusiness = storeCommonSupport.getAllApplyBusiness();
            vo.setApplyBusinessList(allApplyBusiness);
        }
        if (vo.getApplicableAllStore() == ChoinceEnum.ALL.getCode()) {
            //全部门店
            List<StoreBaseInfo> storeBaseInfoList = externalSupport.storeServer(ThreadLocalCache.getSystem()).queryStore(StringConstant.EMPTY);
            List<SettlementRuleStoreDTO> ruleStoreDTOList = SettlementRuleAssembler.formSyncStoreDTOs(storeBaseInfoList);
            vo.setApplicableStore(ruleStoreDTOList);
        } else if (vo.getApplicableAllStore() == ChoinceEnum.PART.getCode()) {
            //部分门店
            final List<SettlementRuleStoreDTO> ruleStoreDTOList = settlementRuleStoreService.listStore(vo.getGuid());
            //需求：门店禁用完了就为空
            vo.setApplicableStore(ruleStoreDTOList);
        }
    }

    /**
     * 构造规则查询条件
     *
     * @param qo 规则查询参数
     * @return
     */
    private boolean buildRuleQo(SettlementRuleQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //关键字是否包含业务
        final List<String> applyBusiness = storeCommonSupport.getApplyBusiness(qo.getKeywords());
        if (CollUtil.isNotEmpty(applyBusiness)) {
            qo.setApplyBusiness(StringHandlerUtil.toMysqlRegStr(applyBusiness));
        }
        //关键字是否包含门店
        final List<String> storeIds = storeCommonSupport.getStoreIds(qo.getKeywords());
        qo.setStoreGuids(storeIds);
        //关键字查询 可以根据应用业务/应用门店ID/应用门店名称模糊搜索；
        final boolean noData = StringUtil.isNotBlank(qo.getKeywords())
                && StringUtil.isBlank(qo.getApplyBusiness())
                && CollectionUtils.isEmpty(qo.getStoreGuids());
        if (noData) {
            return true;
        }
        //分页查询规则
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        return false;
    }

    @Override
    public boolean save(SettlementRuleDTO dto) {
        //参数校验
        dto.validate();
        //获取要更新的规则实体
        HsaSettlementRule settlementRule = getUpdateSettlementRule(dto);
        //填充规则字段
        SettlementRuleAssembler.putHsaSettlementRule(settlementRule, dto.getBaseRule());
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        RLock lock = redissonClient.getLock(RedisKeyConstant.SETTLEMENT_RULE + operSubjectGuid + RedisKeyConstant.COLON + settlementRule.getGuid());
        try {
            if (lock.isLocked()) {
                throw new MemberSettlementException("规则保存中，请勿重复操作");
            }
            lock.lock();
            //事务管理
            transactionTemplate.execute((status) -> {

                log.info("规则保存开始");
                //删除其他门店、场景重复的规则
                if (settlementRule.getIsDefault() == BooleanEnum.FALSE.getCode()) {
                    deleteSameByBusiness(dto);
                }

                //排最上面
                setGmtModified(settlementRule);
                //规则更新
                this.saveOrUpdate(settlementRule);
                //规则优惠更新
                settlementRuleDiscountService.save(dto);

                //门店保存、更新
                settlementRuleStoreService.save(settlementRule.getGuid(), settlementRule.getApplicableAllStore(), dto.getApplicableStore());
                log.info("规则保存结束");
                return Boolean.TRUE;
            });
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new MemberSettlementException("规则保存错误！");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private static void setGmtModified(HsaSettlementRule settlementRule) {
        if (settlementRule.getIsDefault() == BooleanEnum.FALSE.getCode()) {
            settlementRule.setGmtModified(LocalDateTime.now());
        } else {
            settlementRule.setGmtModified(settlementRule.getGmtCreate());
        }
    }

    /**
     * 获取或创建规则
     *
     * @param dto 规则入参
     * @return
     */
    public HsaSettlementRule getUpdateSettlementRule(SettlementRuleDTO dto) {
        final SettlementRuleBaseDTO baseRule = dto.getBaseRule();
        HsaSettlementRule settlementRule = new HsaSettlementRule();
        if (!StringUtils.isEmpty(baseRule.getGuid())) {
            settlementRule = queryByGuid(baseRule.getGuid());
            VerifyUtil.notNull(settlementRule, "结算规则不存在");
            settlementRule.setGmtModified(LocalDateTime.now());

            dto.getBaseRule().setIsDefault(settlementRule.getIsDefault());
        } else {
            final String guid = guidGeneratorUtil.getStringGuid(HsaSettlementRule.class.getSimpleName());
            settlementRule.setGuid(guid);
            baseRule.setGuid(guid);
            settlementRule.setIsDefault(BooleanEnum.FALSE.getCode());

            dto.getBaseRule().setIsDefault(BooleanEnum.FALSE.getCode());
        }
        //名称校验
        validateName(baseRule.getGuid(), baseRule.getName());
        return settlementRule;
    }

    @Override
    public SettlementRuleVO detail(String guid) {
        final HsaSettlementRule settlementRule = queryByGuid(guid);
        if (Objects.isNull(settlementRule)) {
            throw new MemberSettlementException(MemberAccountExceptionEnum.ERROR_NOT_EXISTS_DATA);
        }
        SettlementRuleVO ruleVO = new SettlementRuleVO();
        //基本规则
        SettlementRuleBaseDTO ruleBaseDTO = SettlementRuleAssembler.toSettlementRuleBaseDTO(settlementRule);
        ruleVO.setBaseRule(ruleBaseDTO);
        //适用门店
        if (ruleBaseDTO.getApplicableAllStore() == ChoinceEnum.PART.getCode()) {
            final List<SettlementRuleStoreDTO> ruleStoreDTOList = settlementRuleStoreService.listStore(guid);
            ruleVO.setApplicableStore(ruleStoreDTOList);
        }
        //优惠项
        SettlementRuleDiscountQO qo = new SettlementRuleDiscountQO()
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setSettlementRuleGuid(guid)
                .setIsAppend(BooleanEnum.TRUE.getCode());
        final List<SettlementRuleDiscountTreeVO> appendDiscounts = settlementRuleDiscountService.listAllTree(qo);
        ruleVO.setAppendDiscounts(appendDiscounts);
        return ruleVO;
    }


    public SettlementRuleVO settlementDetail(String guid) {
        final HsaSettlementRule settlementRule = queryByGuid(guid);
        if (Objects.isNull(settlementRule)) {
            throw new MemberSettlementException(MemberAccountExceptionEnum.ERROR_NOT_EXISTS_DATA);
        }
        SettlementRuleVO ruleVO = new SettlementRuleVO();
        //基本规则
        SettlementRuleBaseDTO ruleBaseDTO = SettlementRuleAssembler.toSettlementRuleBaseDTO(settlementRule);
        ruleVO.setBaseRule(ruleBaseDTO);
        //优惠项
        SettlementRuleDiscountQO qo = new SettlementRuleDiscountQO()
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setSettlementRuleGuid(guid)
                .setIsAppend(BooleanEnum.TRUE.getCode());
        final List<SettlementRuleDiscountTreeVO> appendDiscounts = settlementRuleDiscountService.listAllTree(qo);
        ruleVO.setAppendDiscounts(appendDiscounts);
        return ruleVO;
    }


    /**
     * 查询门店、场景 相同的规则
     *
     * @param dto 规则入参
     * @return
     */
    private Pair<SettlementRuleSameQO, List<SettlementRuleStoreSameVO>> querySameData(SettlementRuleDTO dto) {
        //门店或场景为空，不用校验
        if (dto.businessStoreIsNull()) {
            return Pair.of(null, Collections.emptyList());
        }
        //转换查询类
        SettlementRuleSameQO sameQo = toSettlementRuleSameQo(dto);
        //场景、门店相同的规则
        final List<SettlementRuleStoreSameVO> ruleStoreSameVOList = settlementRuleMapper.querySame(sameQo);
        //无相同数据
        return Pair.of(sameQo, ruleStoreSameVOList);
    }

    /**
     * 填充相同门店的规则
     *
     * @param ruleStoreSameVOList 相同门店
     * @return
     */
    private List<SettlementRuleStoreSameVO> fillSameDataStoreName(List<SettlementRuleStoreSameVO> ruleStoreSameVOList) {
        if (CollUtil.isEmpty(ruleStoreSameVOList)) {
            return ruleStoreSameVOList;
        }
        //填充实际门店名称、编号等
        final Map<String, List<SettlementRuleStoreSameVO>> ruleStoreMap = ruleStoreSameVOList.stream()
                .filter(s -> StringUtil.isNotBlank(s.getStoreGuid()))
                .collect(Collectors.groupingBy(SettlementRuleStoreSameVO::getStoreTeamInfoId));
        final Map<String, SyncStoreDTO> realStoreMap = storeCommonSupport.mapStore(Lists.newArrayList(ruleStoreMap.keySet()));
        if (CollUtil.isEmpty(realStoreMap)) {
            //实际无门店
            return ruleStoreSameVOList;
        }
        List<SettlementRuleStoreSameVO> realRuleStoreSameVOList = new ArrayList<>(ruleStoreSameVOList.size());
        //全部门店
        final List<SettlementRuleStoreSameVO> nonStoreSameVoList = ruleStoreSameVOList.stream()
                .filter(s -> Objects.isNull(s.getStoreGuid())).collect(Collectors.toList());
        realRuleStoreSameVOList.addAll(nonStoreSameVoList);
        //部分门店名称填充
        for (Map.Entry<String, List<SettlementRuleStoreSameVO>> entry : ruleStoreMap.entrySet()) {
            //过滤掉不正常的门店
            Optional.ofNullable(realStoreMap.get(entry.getKey()))
                    .ifPresent(store -> {
                        final List<SettlementRuleStoreSameVO> realStoreSameVOList = entry.getValue();
                        realStoreSameVOList.forEach(sameVo -> {
                            sameVo.setStoreName(store.getStoreName());
                            sameVo.setStoreNumber(store.getStoreNumber());
                        });
                        realRuleStoreSameVOList.addAll(realStoreSameVOList);
                    });
        }
        return realRuleStoreSameVOList;
    }

    @Override
    public SettlementRuleSameVO querySame(SettlementRuleDTO dto) {
        //查询门店、场景 相同的规则
        final Pair<SettlementRuleSameQO, List<SettlementRuleStoreSameVO>> pair = querySameData(dto);
        final List<SettlementRuleStoreSameVO> ruleStoreSameVOList = pair.getValue();
        //无相同规则
        if (CollUtil.isEmpty(ruleStoreSameVOList)) {
            return null;
        }
        //填充门店名称
        List<SettlementRuleStoreSameVO> realRuleStoreSameVOList = fillSameDataStoreName(ruleStoreSameVOList);
        final SettlementRuleSameQO sameQo = pair.getKey();
        SettlementRuleSameVO sameVO = new SettlementRuleSameVO();
        //返回值
        sameVO.setApplyBusiness(sameQo.getApplyBusiness());
        sameVO.setApplicableAllStore(sameQo.getApplicableAllStore());

        //当前规则的场景
        setSameVoBusiness(sameVO, realRuleStoreSameVOList, sameQo);
        //当前规则的门店
        setSameVoStore(sameVO, realRuleStoreSameVOList, sameQo, dto);
        return sameVO;
    }

    /**
     * 设置相同门店数据给前端展示
     *
     * @param sameVO                   相同数据
     * @param otherRuleStoreSameVOList 其他规则
     * @param sameQo                   相同入参
     * @param dto                      规则入参
     */
    private void setSameVoStore(SettlementRuleSameVO sameVO,
                                List<SettlementRuleStoreSameVO> otherRuleStoreSameVOList,
                                SettlementRuleSameQO sameQo,
                                SettlementRuleDTO dto) {
        //当前规则的门店
        final List<SettlementRuleStoreSameVO> currentStoreList = sameVO.getApplicableAllStore() == ChoinceEnum.PART.getCode()
                ? SettlementRuleAssembler.toSettlementRuleStoreSameVOList(sameQo, dto.getApplicableStore())
                : SettlementRuleAssembler.toSettlementRuleStoreSameVOList(storeCommonSupport.getStoreList(""), sameQo);
        //全部门店的拿出来
        final List<SettlementRuleStoreSameVO> otherAllStoreList = otherRuleStoreSameVOList.stream()
                .filter(r -> r.getApplicableAllStore() == ChoinceEnum.ALL.getCode()).collect(Collectors.toList());
        //留下部分门店
        otherRuleStoreSameVOList.removeAll(otherAllStoreList);
        if (CollUtil.isNotEmpty(otherRuleStoreSameVOList)) {
            final Set<String> partStoreList = currentStoreList.stream().map(SettlementRuleStoreDTO::getStoreGuid).collect(Collectors.toSet());
            //与当前门店取交集
            otherRuleStoreSameVOList.removeIf(s -> !partStoreList.contains(s.getStoreGuid()));
        }
        if (CollUtil.isNotEmpty(otherAllStoreList)) {
            //重新构造全部门店拼接返回
            appendOtherAllStoreList(otherRuleStoreSameVOList, currentStoreList, otherAllStoreList);
        }
        sameVO.setApplicableStore(otherRuleStoreSameVOList);
    }

    /**
     * 拼接其他门店
     *
     * @param otherRuleStoreSameVOList 其他相同规则
     * @param currentStoreList         当前门店
     * @param otherAllStoreList        其他所有门店
     */
    private static void appendOtherAllStoreList(List<SettlementRuleStoreSameVO> otherRuleStoreSameVOList,
                                                List<SettlementRuleStoreSameVO> currentStoreList,
                                                List<SettlementRuleStoreSameVO> otherAllStoreList) {
        int size = currentStoreList.size();
        //重新构造全部门店返回前端
        for (SettlementRuleStoreSameVO ruleStoreSameVO : otherAllStoreList) {
            //需求显示：把当前规则的门店+其他规则的名称拼出去
            List<SettlementRuleStoreSameVO> ruleStoreList = new ArrayList<>(size);
            for (SettlementRuleStoreSameVO storeSameVO : currentStoreList) {
                SettlementRuleStoreSameVO currentVo = new SettlementRuleStoreSameVO();
                //规则
                currentVo.setSettlementRuleName(ruleStoreSameVO.getSettlementRuleName());
                currentVo.setSettlementRuleGuid(ruleStoreSameVO.getSettlementRuleGuid());
                //门店
                currentVo.setStoreGuid(storeSameVO.getStoreGuid());
                currentVo.setStoreName(storeSameVO.getStoreName());
                currentVo.setStoreNumber(storeSameVO.getStoreNumber());
                ruleStoreList.add(currentVo);
            }
            otherRuleStoreSameVOList.addAll(ruleStoreList);
        }
    }

    /**
     * 设置其他业务给前端展示
     *
     * @param sameVO              相同业务
     * @param ruleStoreSameVOList 相同规则
     * @param sameQo              相同的入参
     */
    private void setSameVoBusiness(SettlementRuleSameVO sameVO,
                                   List<SettlementRuleStoreSameVO> ruleStoreSameVOList,
                                   SettlementRuleSameQO sameQo) {
        //当前规则的场景
        final List<String> businessList = sameVO.getApplyBusiness() == ChoinceEnum.PART.getCode()
                ? sameQo.getApplyBusinessList()
                : storeCommonSupport.getApplyBusinessList().stream().map(ApplyTypeVO::getType).collect(Collectors.toList());
        //其中一个是全部，则直接返回
        if (ruleStoreSameVOList.stream().noneMatch(r -> r.getApplyBusiness() == ChoinceEnum.ALL.getCode())) {
            //当前是部分场景
            final Set<String> allBusinessList = ruleStoreSameVOList.stream()
                    .flatMap(l -> JacksonUtils.toObjectList(String.class, l.getApplyBusinessJson())
                            .stream()).collect(Collectors.toSet());
            //取交集
            businessList.removeIf(b -> !allBusinessList.contains(b));
        }
        sameVO.setApplyBusinessList(businessList);
    }

    /**
     * 转换相同规则对象入参
     *
     * @param dto 规则入参
     * @return
     */
    private SettlementRuleSameQO toSettlementRuleSameQo(SettlementRuleDTO dto) {
        SettlementRuleSameQO sameQo = new SettlementRuleSameQO();
        final SettlementRuleBaseDTO baseRule = dto.getBaseRule();
        final String guid = StringUtil.isBlank(baseRule.getGuid()) ? StringConstant.PARENT : baseRule.getGuid();
        sameQo.setGuid(guid);
        sameQo.setName(baseRule.getName());
        sameQo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        sameQo.setApplyBusiness(baseRule.getApplyBusiness());
        sameQo.setApplicableAllStore(baseRule.getApplicableAllStore());
        //部分门店
        if (baseRule.getApplicableAllStore() == ChoinceEnum.PART.getCode()) {
            final List<SettlementRuleStoreDTO> applicableStore = dto.getApplicableStore();
            //获取门店id
            final List<String> storeGuidList = applicableStore.stream().map(SettlementRuleStoreDTO::getStoreGuid).collect(Collectors.toList());
            sameQo.setStoreGuids(storeGuidList);
        } else if (baseRule.getApplicableAllStore() == ChoinceEnum.ALL.getCode()) {
            sameQo.setStoreGuids(storeCommonSupport.getAllStoreIds());
        }
        //部分业务
        if (baseRule.getApplyBusiness() == ChoinceEnum.PART.getCode()) {
            Optional.ofNullable(baseRule.getApplyBusinessList())
                    .ifPresent(list -> {
                        sameQo.setApplyBusinessStr(StringHandlerUtil.toMysqlRegStr(list));
                        sameQo.setApplyBusinessList(list);
                    });
        }
        return sameQo;
    }

    @Override
    public boolean deleteSameByBusiness(SettlementRuleDTO dto) {
        //获得相同场景、门店的规则
        final Pair<SettlementRuleSameQO, List<SettlementRuleStoreSameVO>> listPair = querySameData(dto);
        final List<SettlementRuleStoreSameVO> otherSameRules = listPair.getValue();
        //无相同规则
        if (CollUtil.isEmpty(otherSameRules)) {
            return false;
        }
        final Set<String> otherSameRuleGuidList = otherSameRules.stream()
                .map(SettlementRuleStoreDTO::getSettlementRuleGuid)
                .collect(Collectors.toSet());
        //其他规则
        final List<HsaSettlementRule> otherRules = this.list(new LambdaQueryWrapper<HsaSettlementRule>()
                .in(HsaSettlementRule::getGuid, otherSameRuleGuidList)
        );
        //当前场景
        final SettlementRuleSameQO currentSameQo = listPair.getKey();
        final boolean currentAllBusiness = currentSameQo.getApplyBusiness() == ChoinceEnum.ALL.getCode();
        final List<String> businessList = currentSameQo.getApplyBusinessList();
        //当前规则应用门店类型
        final boolean currentAllStore = currentSameQo.getApplicableAllStore() == ChoinceEnum.ALL.getCode();
        for (HsaSettlementRule rule : otherRules) {
            //应用场景
            updateRuleBusiness(currentAllBusiness, businessList, rule);
        }
        this.saveOrUpdateBatch(otherRules, SqlConstant.BATCH_SAVE_COUNT);

        //删除其他规则相同的门店
        deleteSameByBusinessStore(otherSameRuleGuidList, otherRules, currentAllStore, currentSameQo.getStoreGuids());
        log.info("删除相同业务规则成功");
        return true;
    }

    /**
     * 删除相同业务门店
     *
     * @param otherSameRuleGuidList 其他相同规则
     * @param otherRules            其他规则
     * @param currentAllStore       当前门店
     * @param currentStoreGuids     当前门店集合
     */
    private void deleteSameByBusinessStore(Set<String> otherSameRuleGuidList,
                                           List<HsaSettlementRule> otherRules,
                                           boolean currentAllStore,
                                           List<String> currentStoreGuids) {
        if (currentAllStore) {
            //其他清空
            settlementRuleStoreService.remove(new LambdaQueryWrapper<HsaSettlementRuleStore>()
                    .in(HsaSettlementRuleStore::getSettlementRuleGuid, otherSameRuleGuidList)
            );
            otherRules.forEach(r -> r.setApplicableAllStore(ChoinceEnum.NONE.getCode()));
            this.saveOrUpdateBatch(otherRules, SqlConstant.BATCH_SAVE_COUNT);
            return;
        }
        //当前规则是部分门店
        final Map<Integer, List<HsaSettlementRule>> storeRuleMap = otherRules.stream()
                .collect(Collectors.groupingBy(HsaSettlementRule::getApplicableAllStore));
        //其他规则是部分的
        Optional.ofNullable(storeRuleMap.get(ChoinceEnum.PART.getCode()))
                .map(l -> l.stream().map(HsaSettlementRule::getGuid).collect(Collectors.toList()))
                .ifPresent(partStoreRuleGuids -> {
                    //删除交集
                    settlementRuleStoreService.remove(new LambdaQueryWrapper<HsaSettlementRuleStore>()
                            .in(HsaSettlementRuleStore::getSettlementRuleGuid, partStoreRuleGuids)
                            .in(HsaSettlementRuleStore::getStoreGuid, currentStoreGuids)
                    );
                    List<String> existsRuleGuids = settlementRuleStoreService.existsRule(partStoreRuleGuids);
                    partStoreRuleGuids.removeAll(existsRuleGuids);
                    if (CollUtil.isNotEmpty(partStoreRuleGuids)) {
                        //标志为 无
                        settlementRuleMapper.updateApplicableAllStore(partStoreRuleGuids, ChoinceEnum.NONE.getCode());
                    }
                });
        //其他规则是全部的
        deleteByBusinessStoreOfAll(storeRuleMap, currentStoreGuids);

    }

    /**
     * 删除业务、门店仙童的
     *
     * @param sotreRuleMap      规则门店
     * @param currentStoreGuids 当前规则
     */
    private void deleteByBusinessStoreOfAll(Map<Integer, List<HsaSettlementRule>> sotreRuleMap,
                                            List<String> currentStoreGuids) {
        //其他规则是全部的
        final List<String> allStoreRuleGuids = Optional.ofNullable(sotreRuleMap.get(ChoinceEnum.ALL.getCode()))
                .map(l -> l.stream().map(HsaSettlementRule::getGuid)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        if (allStoreRuleGuids.isEmpty()) {
            return;
        }
        //其他清空
        settlementRuleStoreService.remove(new LambdaQueryWrapper<HsaSettlementRuleStore>()
                .in(HsaSettlementRuleStore::getSettlementRuleGuid, allStoreRuleGuids)
        );
        final List<SyncStoreDTO> allStoreList = storeCommonSupport.getStoreList("");
        //去除已有
        allStoreList.removeIf(s -> currentStoreGuids.contains(s.getId()));
        if (CollUtil.isEmpty(allStoreList)) {
            //标志为 无
            settlementRuleMapper.updateApplicableAllStore(allStoreRuleGuids, ChoinceEnum.NONE.getCode());
            return;
        }
        List<HsaSettlementRuleStore> ruleStores = Lists.newArrayList();
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        for (String otherRuleGuid : allStoreRuleGuids) {
            //剩余门店拼上去
            for (SyncStoreDTO baseInfo : allStoreList) {
                HsaSettlementRuleStore couponStore = new HsaSettlementRuleStore();
                couponStore.setGuid(guidGeneratorUtil.getStringGuid(HsaSettlementRuleStore.class.getSimpleName()));
                couponStore.setStoreGuid(baseInfo.getId());
                couponStore.setSettlementRuleGuid(otherRuleGuid);
                couponStore.setOperSubjectGuid(operSubjectGuid);
                couponStore.setStoreTeamInfoId(baseInfo.getStoreTeamInfoId());
                ruleStores.add(couponStore);
            }
        }
        settlementRuleStoreService.saveBatch(ruleStores, SqlConstant.BATCH_SAVE_COUNT);
        //标志为 部分
        settlementRuleMapper.updateApplicableAllStore(allStoreRuleGuids, ChoinceEnum.PART.getCode());
    }

    /**
     * 更新规则业务
     *
     * @param allBusiness  所有业务
     * @param businessList 业务列表
     * @param rule         规则
     */
    private void updateRuleBusiness(boolean allBusiness, List<String> businessList, HsaSettlementRule rule) {
        if (allBusiness) {
            //其他清空
            rule.setApplyBusinessJson(StringConstant.EMPTY_JSON_ARRAY);
            rule.setApplyBusiness(ChoinceEnum.NONE.getCode());
            return;
        }
        //所有、部分业务
        List<String> applyBusinessList = rule.getApplyBusiness() == ChoinceEnum.ALL.getCode()
                ? storeCommonSupport.getAllApplyBusiness()
                : JacksonUtils.toObjectList(String.class, rule.getApplyBusinessJson());
        applyBusinessList.removeAll(businessList);
        rule.setApplyBusinessJson(JacksonUtils.writeValueAsString(applyBusinessList));
        //部分场景至少有一个
        final int applyBusiness = rule.getApplyBusinessJson().equals(StringConstant.EMPTY_JSON_ARRAY)
                ? ChoinceEnum.NONE.getCode()
                : ChoinceEnum.PART.getCode();
        rule.setApplyBusiness(applyBusiness);
    }

    @Override
    public void validateName(String guid, String name) {
        final String ruleGuid = StringUtil.isBlank(guid) ? StringConstant.PARENT : guid;
        final Integer count = settlementRuleMapper.existsName(ThreadLocalCache.getOperSubjectGuid(), name, ruleGuid);
        if (count != null) {
            throw new MemberSettlementException("规则名称已存在，请修改");
        }
    }

    @Override
    public void init(String operSubjectGuid) {
        HsaSettlementRule hsaSettlementRule = baseMapper.selectOne(new LambdaQueryWrapper<HsaSettlementRule>()
                .eq(HsaSettlementRule::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaSettlementRule::getIsDefault, BooleanEnum.TRUE.getCode()));
        if (Objects.nonNull(hsaSettlementRule)) {
            return;
        }

        HsaSettlementRule settlementRule = new HsaSettlementRule();
        settlementRule.setOperSubjectGuid(operSubjectGuid);
        settlementRule.setName("默认结算规则");
        settlementRule.setCouponLimit(BooleanEnum.FALSE.getCode());
        settlementRule.setCouponRollback(BooleanEnum.TRUE.getCode());
        settlementRule.setUseType(BooleanEnum.FALSE.getCode());
        settlementRule.setApplyBusiness(0);

        settlementRule.setApplyBusinessJson(StringConstant.EMPTY_JSON_ARRAY);

        settlementRule.setApplicableAllStore(ChoinceEnum.ALL.getCode());
        settlementRule.setGuid(guidGeneratorUtil.getStringGuid(HsaSettlementRule.class.getSimpleName()));
        settlementRule.setIsDefault(BooleanEnum.TRUE.getCode());
        settlementRule.setGmtModified(LocalDateTime.now().plusYears(99));
        settlementRule.setGmtCreate(LocalDateTime.now().plusYears(99));
        baseMapper.insert(settlementRule);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSettlementRuleDiscounts(SynSettlementRuleDiscountDTO dto) {
        //更新默认规则
        updateDefaultRule(dto);

        //更新自定义规则
        updateNotDefaultRule(dto);
    }

    @Override
    public SettlementRuleVO queryDefaultDetail(String operSubjectGuid) {
        HsaSettlementRule settlementRule = settlementRuleMapper.queryDefaultDetail(operSubjectGuid);
        if (Objects.isNull(settlementRule)) {
            throw new MemberSettlementException(MemberAccountExceptionEnum.ERROR_NOT_EXISTS_DATA);
        }
        SettlementRuleVO ruleVO = new SettlementRuleVO();
        //基本规则
        SettlementRuleBaseDTO ruleBaseDTO = SettlementRuleAssembler.toSettlementRuleBaseDTO(settlementRule);
        ruleVO.setBaseRule(ruleBaseDTO);
        //适用门店
        if (ruleBaseDTO.getApplicableAllStore() == ChoinceEnum.PART.getCode()) {
            final List<SettlementRuleStoreDTO> ruleStoreDTOList = settlementRuleStoreService.listStore(settlementRule.getGuid());
            ruleVO.setApplicableStore(ruleStoreDTOList);
        }
        //优惠项
        SettlementRuleDiscountQO qo = new SettlementRuleDiscountQO()
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setSettlementRuleGuid(settlementRule.getGuid())
                .setIsAppend(BooleanEnum.TRUE.getCode());
        final List<SettlementRuleDiscountTreeVO> appendDiscounts = settlementRuleDiscountService.listAllTree(qo);
        ruleVO.setAppendDiscounts(appendDiscounts);
        return ruleVO;
    }

    public void updateNotDefaultRule(SynSettlementRuleDiscountDTO dto) {
        //过滤出首次新增的诱惑
        List<SettlementRuleDiscountDTO> appendDiscounts = dto.getAppendDiscounts();
        if (CollUtil.isEmpty(appendDiscounts)) {
            log.info("没有可叠加的的优惠");
            return;
        }

        List<SettlementRuleDiscountDTO> discountDTOS = filtrationSettlementRuleDiscount(dto);

        if (CollUtil.isEmpty(discountDTOS)) {
            log.info("没有新增可叠加的的优惠");
            return;
        }

        //查询结算台优惠表
        List<HsaSettlementRule> hsaSettlementRuleList = baseMapper.selectList(new LambdaQueryWrapper<HsaSettlementRule>()
                .eq(HsaSettlementRule::getOperSubjectGuid, dto.getOperSubjectGuid())
                .eq(HsaBaseEntity::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(HsaSettlementRule::getIsDefault, BooleanEnum.FALSE.getCode()));

        if (CollUtil.isNotEmpty(hsaSettlementRuleList)) {
            for (HsaSettlementRule hsaSettlementRule : hsaSettlementRuleList) {
                SettlementRuleVO settlementRuleVO = settlementDetail(hsaSettlementRule.getGuid());
                settlementRuleVO.getBaseRule().setIsUpdate(Boolean.TRUE);
                //规则优惠更新
                SettlementRuleDTO settlementRuleDTO = new SettlementRuleDTO();
                settlementRuleDTO.setBaseRule(settlementRuleVO.getBaseRule());
                settlementRuleDTO.setApplicableStore(settlementRuleVO.getApplicableStore());
                settlementRuleDTO.setAppendDiscounts(appendDiscounts);
                settlementRuleDTO.setDisAppendDiscounts(new ArrayList<>());

                List<HsaSettlementRuleDiscount> hsaSettlementRuleDiscounts = settlementRuleDiscountService.toHsaSettlementRuleDiscounts(settlementRuleDTO);
                if (CollUtil.isNotEmpty(hsaSettlementRuleDiscounts)) {
                    setHsaSettlementRuleDiscountsGuid(hsaSettlementRuleDiscounts);

                    List<String> settlementRuleDiscountGuidList =
                            hsaSettlementRuleDiscounts
                                    .stream()
                                    .map(HsaSettlementRuleDiscount::getSettlementDiscountGuid)
                                    .collect(Collectors.toList());

                    if (CollUtil.isNotEmpty(dto.getAppendDiscounts())) {
                        settlementRuleDiscountService.deleteAppend(new DeleteSettlementRuleDiscountQO()
                                .setNewNotAdditivitySettlementDiscount(settlementRuleDiscountGuidList)
                                .setSettlementRuleGuid(hsaSettlementRule.getGuid())
                                .setIsAppend(BooleanEnum.TRUE.getCode()));
                    }

                    settlementRuleDiscountService.saveBatch(hsaSettlementRuleDiscounts);
                }
            }
        }
    }

    private static List<SettlementRuleDiscountDTO> filtrationSettlementRuleDiscount(SynSettlementRuleDiscountDTO dto) {
        List<SettlementRuleDiscountDTO> discountDTOS = Lists.newArrayList();
        for (SettlementRuleDiscountDTO appendDiscount : dto.getAppendDiscounts()) {
            List<SettlementRuleDiscountDTO> children = appendDiscount.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                children = appendDiscount.getChildren()
                        .stream()
                        .filter(in -> Objects.nonNull(in.getIsFirstAdd()) && in.getIsFirstAdd() == BooleanEnum.TRUE.getCode())
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(children)) {
                    SettlementRuleDiscountDTO settlementRuleDiscountDTO = new SettlementRuleDiscountDTO();
                    settlementRuleDiscountDTO.setChildren(children);
                    BeanUtils.copyProperties(appendDiscount, settlementRuleDiscountDTO);
                    discountDTOS.add(settlementRuleDiscountDTO);
                }
            }
        }
        return discountDTOS;
    }

    private void updateDefaultRule(SynSettlementRuleDiscountDTO dto) {
        List<HsaSettlementRule> hsaSettlementRuleList = baseMapper.selectList(new LambdaQueryWrapper<HsaSettlementRule>()
                .eq(HsaSettlementRule::getOperSubjectGuid, dto.getOperSubjectGuid())
                .eq(HsaSettlementRule::getIsDefault, BooleanEnum.TRUE.getCode())
                .eq(HsaBaseEntity::getIsDelete, BooleanEnum.FALSE.getCode()));

        if (CollUtil.isEmpty(hsaSettlementRuleList)) {
            log.info("未找到默认结算规则");
            return;
        }

        for (HsaSettlementRule hsaSettlementRule : hsaSettlementRuleList) {
            SettlementRuleVO settlementRuleVO = settlementDetail(hsaSettlementRule.getGuid());
            settlementRuleVO.getBaseRule().setIsUpdate(Boolean.TRUE);
            SettlementRuleDTO settlementRuleDTO = getSettlementRuleDTO(dto, settlementRuleVO);

            List<HsaSettlementRuleDiscount> hsaSettlementRuleDiscounts = settlementRuleDiscountService.toHsaSettlementRuleDiscounts(settlementRuleDTO);

            if (CollUtil.isNotEmpty(hsaSettlementRuleDiscounts)) {
                setHsaSettlementRuleDiscountsGuid(hsaSettlementRuleDiscounts);

                //单级优惠处理
                deleteSettlementNoChildren(dto, hsaSettlementRule, hsaSettlementRuleDiscounts);

                //多级优惠处理
                deleteSettlementChildren(dto, hsaSettlementRule, hsaSettlementRuleDiscounts);

                settlementRuleDiscountService.saveBatch(hsaSettlementRuleDiscounts);
            }
        }
    }

    private static SettlementRuleDTO getSettlementRuleDTO(SynSettlementRuleDiscountDTO dto, SettlementRuleVO settlementRuleVO) {
        SettlementRuleDTO settlementRuleDTO = new SettlementRuleDTO();
        settlementRuleDTO.setBaseRule(settlementRuleVO.getBaseRule());
        settlementRuleDTO.setApplicableStore(settlementRuleVO.getApplicableStore());


        settlementRuleDTO.setAppendDiscounts(dto.getAppendDiscounts());
        settlementRuleDTO.setDisAppendDiscounts(dto.getDisAppendDiscounts());
        return settlementRuleDTO;
    }

    private void deleteSettlementNoChildren(SynSettlementRuleDiscountDTO dto,
                                            HsaSettlementRule hsaSettlementRule,
                                            List<HsaSettlementRuleDiscount> hsaSettlementRuleDiscounts) {
        //过滤出只有一层的优惠
        List<HsaSettlementRuleDiscount> settlementNoChildren = hsaSettlementRuleDiscounts
                .stream()
                .filter(in -> SettlementDiscountOptionEnum.noChildren(in.getDiscountOption()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(settlementNoChildren)) {
            List<String> settlementRuleDiscountGuidList =
                    settlementNoChildren
                            .stream()
                            .map(HsaSettlementRuleDiscount::getSettlementDiscountGuid)
                            .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(dto.getAppendDiscounts())) {
                settlementRuleDiscountService.deleteAppend(new DeleteSettlementRuleDiscountQO()
                        .setNewNotAdditivitySettlementDiscount(settlementRuleDiscountGuidList)
                        .setSettlementRuleGuid(hsaSettlementRule.getGuid()));
            }

            if (CollUtil.isNotEmpty(dto.getDisAppendDiscounts())) {
                settlementRuleDiscountService.deleteAppend(new DeleteSettlementRuleDiscountQO()
                        .setNewNotAdditivitySettlementDiscount(settlementRuleDiscountGuidList)
                        .setSettlementRuleGuid(hsaSettlementRule.getGuid()));
            }
        }
    }

    private void deleteSettlementChildren(SynSettlementRuleDiscountDTO dto,
                                          HsaSettlementRule hsaSettlementRule,
                                          List<HsaSettlementRuleDiscount> hsaSettlementRuleDiscounts) {


        //过滤出有多层的优惠
        List<HsaSettlementRuleDiscount> settlementChildren = hsaSettlementRuleDiscounts
                .stream()
                .filter(in -> !SettlementDiscountOptionEnum.noChildren(in.getDiscountOption()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(settlementChildren)) {
            List<String> settlementRuleDiscountGuidList =
                    settlementChildren
                            .stream()
                            .map(HsaSettlementRuleDiscount::getSettlementDiscountGuid)
                            .collect(Collectors.toList());
            List<HsaSettlementRuleDiscount> herderRuleDiscountList = settlementChildren.stream().filter(in -> in.getDiscountNum() > 0)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(dto.getAppendDiscounts())) {
                settlementRuleDiscountService.deleteAppend(new DeleteSettlementRuleDiscountQO()
                        .setNewNotAdditivitySettlementDiscount(settlementRuleDiscountGuidList)
                        .setSettlementRuleGuid(hsaSettlementRule.getGuid())
                        .setIsAppend(BooleanEnum.TRUE.getCode()));

                deleteHea(hsaSettlementRule, herderRuleDiscountList, BooleanEnum.FALSE.getCode());
            }

            if (CollUtil.isNotEmpty(dto.getDisAppendDiscounts())) {
                settlementRuleDiscountService.deleteAppend(new DeleteSettlementRuleDiscountQO()
                        .setNewNotAdditivitySettlementDiscount(settlementRuleDiscountGuidList)
                        .setSettlementRuleGuid(hsaSettlementRule.getGuid())
                        .setIsAppend(BooleanEnum.FALSE.getCode()));

                deleteHea(hsaSettlementRule, herderRuleDiscountList, BooleanEnum.TRUE.getCode());
            }
        }
    }

    private void deleteHea(HsaSettlementRule hsaSettlementRule,
                           List<HsaSettlementRuleDiscount> herderRuleDiscountList,
                           Integer isAppend) {
        if (CollUtil.isNotEmpty(herderRuleDiscountList)) {
            //删除头部
            List<String> herderRuleDiscountGuidList =
                    herderRuleDiscountList
                            .stream()
                            .map(HsaSettlementRuleDiscount::getSettlementDiscountGuid)
                            .collect(Collectors.toList());
            settlementRuleDiscountService.deleteAppend(new DeleteSettlementRuleDiscountQO()
                    .setNewNotAdditivitySettlementDiscount(herderRuleDiscountGuidList)
                    .setSettlementRuleGuid(hsaSettlementRule.getGuid())
                    .setIsAppend(isAppend));
        }
    }

    /**
     * 设置优惠guid
     *
     * @param ruleDiscounts 优惠项
     */
    private void setHsaSettlementRuleDiscountsGuid(List<HsaSettlementRuleDiscount> ruleDiscounts) {
        final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaSettlementRuleDiscount.class.getSimpleName(), ruleDiscounts.size());
        for (int i = 0; i < ruleDiscounts.size(); i++) {
            ruleDiscounts.get(i).setGuid(guids.get(i));
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String guid) {
        //删除规则
        this.remove(new LambdaQueryWrapper<HsaSettlementRule>()
                .eq(HsaSettlementRule::getGuid, guid));
        //删除规则门店
        settlementRuleStoreService.deleteByRuleGuid(guid);
        //删除规则优惠项
        settlementRuleDiscountService.deleteByRuleGuid(guid);
        return true;
    }
}

