package com.holderzone.member.settlement.service.handle;

import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyCommodityDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderInfoDTO;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleBaseDTO;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountTreeVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SettlementListSupportTest {

    @Mock
    private MemberBaseFeign mockMemberBaseFeign;

    private SettlementListSupport settlementListSupportUnderTest;

    @Before
    public void setUp() throws Exception {
        settlementListSupportUnderTest = new SettlementListSupport(mockMemberBaseFeign);
    }

    @Test
    public void testGetOptimizeCheckOrder() {
        // Setup
        final SettlementApplyRuleVO applyRule = new SettlementApplyRuleVO();
        final SettlementRuleBaseDTO baseRule = new SettlementRuleBaseDTO();
        baseRule.setGuid("0efcbf75-8f7c-4000-b214-3595bf50de58");
        baseRule.setIsUpdate(false);
        baseRule.setOperSubjectGuid("operSubjectGuid");
        baseRule.setName("name");
        baseRule.setCouponLimit(0);
        baseRule.setCouponLimitNum(0);
        baseRule.setCouponViewLimitNum(0);
        baseRule.setCouponRollback(0);
        baseRule.setUseType(0);
        baseRule.setApplyBusiness(0);
        baseRule.setApplyBusinessList(Arrays.asList("value"));
        applyRule.setBaseRule(baseRule);
        final SettlementRuleDiscountTreeVO settlementRuleDiscountTreeVO = new SettlementRuleDiscountTreeVO();
        settlementRuleDiscountTreeVO.setDiscountOption(22);
        settlementRuleDiscountTreeVO.setDiscountGuid("discountGuid");
        applyRule.setAppendDiscounts(Arrays.asList(settlementRuleDiscountTreeVO));

        final SettlementApplyOrderDTO dto = new SettlementApplyOrderDTO();
        dto.setAutoCheck(0);
        dto.setConfirmCheck(0);
        final SettlementApplyOrderInfoDTO orderInfo = new SettlementApplyOrderInfoDTO();
        orderInfo.setOperSubjectGuid("operSubjectGuid");
        orderInfo.setShopCarOriginPayPrice(new BigDecimal("0.00"));
        dto.setOrderInfo(orderInfo);
        final SettlementApplyCommodityDTO commodityDTO = new SettlementApplyCommodityDTO();
        commodityDTO.setDiscountTotalPriceInShopCar(new BigDecimal("0.00"));
        commodityDTO.setAfterDiscountTotalPrice(new BigDecimal("0.00"));
        dto.setOrderCommodityList(Arrays.asList(commodityDTO));
        dto.setCheckDiscountMap(new HashMap<>());
        dto.setDiscountOptionId(Arrays.asList("value"));
        dto.setListOptions(Arrays.asList(0));
        dto.setAppendMap(new HashMap<>());
        dto.setIsAppointDiscount(1);

        final Map<Integer, List<SettlementApplyOrderVO>> discountVoMap = new HashMap<>();
        final SettlementApplyOrderVO expectedResult = new SettlementApplyOrderVO();
        final SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO();
        oderInfo.setDiscountAmount(new BigDecimal("7.00"));
        expectedResult.setOderInfo(oderInfo);
        final SettlementApplyShareCommodityVO settlementApplyShareCommodityVO = new SettlementApplyShareCommodityVO();
        settlementApplyShareCommodityVO.setDiscountOption(22);
        settlementApplyShareCommodityVO.setDiscountGuid("discountGuid");
        settlementApplyShareCommodityVO.setDiscountOptionId("discountOptionId");
        final SettlementApplyCommodityVO settlementApplyCommodityVO = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyShareCommodityVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO));
        expectedResult.setShareCommodityVOList(Arrays.asList(settlementApplyShareCommodityVO));
        final SettlementApplyDiscountVO settlementApplyDiscountVO = new SettlementApplyDiscountVO();
        settlementApplyDiscountVO.setDiscountOption(22);
        settlementApplyDiscountVO.setDiscountName("discountName");
        final SettlementApplyDiscountDetailVO detailVO = new SettlementApplyDiscountDetailVO();
        detailVO.setDiscountAmount(new BigDecimal("5.00"));
        detailVO.setIsChecked(0);
        detailVO.setIsAppend(0);
        detailVO.setIsEnabled(1);
        detailVO.setDiscountOption(22);
        detailVO.setDiscountGuid("discountGuid");
        detailVO.setDiscountOptionId("discountOptionId");
        detailVO.setDiscountName("discountName");
        final SettlementApplyCommodityVO settlementApplyCommodityVO1 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO1.setDiscountFee(new BigDecimal("0.00"));
        detailVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO1));
        settlementApplyDiscountVO.setDiscountList(Arrays.asList(detailVO));
        expectedResult.setDiscountList(Arrays.asList(settlementApplyDiscountVO));

        // Configure MemberBaseFeign.listDiscount(...).
        final SettlementApplyOrderVO settlementApplyOrderVO = new SettlementApplyOrderVO();
        final SettlementApplyOderInfoVO oderInfo1 = new SettlementApplyOderInfoVO();
        oderInfo1.setDiscountAmount(new BigDecimal("2.00"));
        settlementApplyOrderVO.setOderInfo(oderInfo1);
        final SettlementApplyShareCommodityVO settlementApplyShareCommodityVO1 = new SettlementApplyShareCommodityVO();
        settlementApplyShareCommodityVO1.setDiscountOption(22);
        settlementApplyShareCommodityVO1.setDiscountGuid("discountGuid");
        settlementApplyShareCommodityVO1.setDiscountOptionId("discountOptionId");
        final SettlementApplyCommodityVO settlementApplyCommodityVO2 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO2.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyShareCommodityVO1.setCommodityList(Arrays.asList(settlementApplyCommodityVO2));
        settlementApplyOrderVO.setShareCommodityVOList(Arrays.asList(settlementApplyShareCommodityVO1));
        final SettlementApplyDiscountVO settlementApplyDiscountVO1 = new SettlementApplyDiscountVO();
        settlementApplyDiscountVO1.setDiscountOption(22);
        settlementApplyDiscountVO1.setDiscountName("discountName");
        final SettlementApplyDiscountDetailVO detailVO1 = new SettlementApplyDiscountDetailVO();
        detailVO1.setDiscountAmount(new BigDecimal("8.00"));
        detailVO1.setIsChecked(0);
        detailVO1.setIsAppend(0);
        detailVO1.setIsEnabled(1);
        detailVO1.setDiscountOption(22);
        detailVO1.setDiscountGuid("discountGuid");
        detailVO1.setDiscountOptionId("discountOptionId");
        detailVO1.setDiscountName("discountName");
        final SettlementApplyCommodityVO settlementApplyCommodityVO3 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO3.setDiscountFee(new BigDecimal("6.00"));
        detailVO1.setCommodityList(Arrays.asList(settlementApplyCommodityVO3));
        settlementApplyDiscountVO1.setDiscountList(Arrays.asList(detailVO1));
        settlementApplyOrderVO.setDiscountList(Arrays.asList(settlementApplyDiscountVO1));
        final List<SettlementApplyOrderVO> settlementApplyOrderVOS = Arrays.asList(settlementApplyOrderVO);
        when(mockMemberBaseFeign.listDiscount(new SettlementApplyOrderDTO())).thenReturn(settlementApplyOrderVOS);

        discountVoMap.put(0, settlementApplyOrderVOS);
        // Run the test
        final SettlementApplyOrderVO result = settlementListSupportUnderTest.getOptimizeCheckOrder(applyRule, dto,
                discountVoMap);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOptimizeCheckOrder_MemberBaseFeignReturnsNoItems() {
        // Setup
        final SettlementApplyRuleVO applyRule = new SettlementApplyRuleVO();
        final SettlementRuleBaseDTO baseRule = new SettlementRuleBaseDTO();
        baseRule.setGuid("0efcbf75-8f7c-4000-b214-3595bf50de58");
        baseRule.setIsUpdate(false);
        baseRule.setOperSubjectGuid("operSubjectGuid");
        baseRule.setName("name");
        baseRule.setCouponLimit(0);
        baseRule.setCouponLimitNum(0);
        baseRule.setCouponViewLimitNum(0);
        baseRule.setCouponRollback(0);
        baseRule.setUseType(0);
        baseRule.setApplyBusiness(0);
        baseRule.setApplyBusinessList(Arrays.asList("value"));
        applyRule.setBaseRule(baseRule);
        final SettlementRuleDiscountTreeVO settlementRuleDiscountTreeVO = new SettlementRuleDiscountTreeVO();
        settlementRuleDiscountTreeVO.setDiscountOption(11);
        settlementRuleDiscountTreeVO.setDiscountGuid("discountGuid");
        applyRule.setAppendDiscounts(Arrays.asList(settlementRuleDiscountTreeVO));

        final SettlementApplyOrderDTO dto = new SettlementApplyOrderDTO();
        dto.setAutoCheck(0);
        dto.setConfirmCheck(0);
        final SettlementApplyOrderInfoDTO orderInfo = new SettlementApplyOrderInfoDTO();
        orderInfo.setOperSubjectGuid("operSubjectGuid");
        orderInfo.setShopCarOriginPayPrice(new BigDecimal("0.00"));
        dto.setOrderInfo(orderInfo);
        final SettlementApplyCommodityDTO commodityDTO = new SettlementApplyCommodityDTO();
        commodityDTO.setDiscountTotalPriceInShopCar(new BigDecimal("0.00"));
        commodityDTO.setAfterDiscountTotalPrice(new BigDecimal("0.00"));
        dto.setOrderCommodityList(Arrays.asList(commodityDTO));
        dto.setCheckDiscountMap(new HashMap<>());
        dto.setDiscountOptionId(Arrays.asList("value"));
        dto.setListOptions(Arrays.asList(0));
        dto.setAppendMap(new HashMap<>());
        dto.setIsAppointDiscount(1);

        final Map<Integer, List<SettlementApplyOrderVO>> discountVoMap = new HashMap<>();
        final SettlementApplyOrderVO expectedResult = new SettlementApplyOrderVO();
        final SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO();
        oderInfo.setDiscountAmount(new BigDecimal("0.00"));
        expectedResult.setOderInfo(oderInfo);
        final SettlementApplyShareCommodityVO settlementApplyShareCommodityVO = new SettlementApplyShareCommodityVO();
        settlementApplyShareCommodityVO.setDiscountOption(11);
        settlementApplyShareCommodityVO.setDiscountGuid("discountGuid");
        settlementApplyShareCommodityVO.setDiscountOptionId("discountOptionId");
        final SettlementApplyCommodityVO settlementApplyCommodityVO = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyShareCommodityVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO));
        expectedResult.setShareCommodityVOList(Arrays.asList(settlementApplyShareCommodityVO));
        final SettlementApplyDiscountVO settlementApplyDiscountVO = new SettlementApplyDiscountVO();
        settlementApplyDiscountVO.setDiscountOption(11);
        settlementApplyDiscountVO.setDiscountName("discountName");
        final SettlementApplyDiscountDetailVO detailVO = new SettlementApplyDiscountDetailVO();
        detailVO.setDiscountAmount(new BigDecimal("0.00"));
        detailVO.setIsChecked(0);
        detailVO.setIsAppend(0);
        detailVO.setIsEnabled(0);
        detailVO.setDiscountOption(11);
        detailVO.setDiscountGuid("discountGuid");
        detailVO.setDiscountOptionId("discountOptionId");
        detailVO.setDiscountName("discountName");
        final SettlementApplyCommodityVO settlementApplyCommodityVO1 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO1.setDiscountFee(new BigDecimal("0.00"));
        detailVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO1));
        settlementApplyDiscountVO.setDiscountList(Arrays.asList(detailVO));
        expectedResult.setDiscountList(Arrays.asList(settlementApplyDiscountVO));

        when(mockMemberBaseFeign.listDiscount(new SettlementApplyOrderDTO())).thenReturn(Collections.emptyList());

        // Run the test
        final SettlementApplyOrderVO result = settlementListSupportUnderTest.getOptimizeCheckOrder(applyRule, dto,
                discountVoMap);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckDiscountCoupon() {
        // Setup
        final SettlementApplyOrderDTO dto = new SettlementApplyOrderDTO();
        dto.setAutoCheck(0);
        dto.setConfirmCheck(0);
        final SettlementApplyOrderInfoDTO orderInfo = new SettlementApplyOrderInfoDTO();
        orderInfo.setOperSubjectGuid("operSubjectGuid");
        orderInfo.setShopCarOriginPayPrice(new BigDecimal("0.00"));
        dto.setOrderInfo(orderInfo);
        final SettlementApplyCommodityDTO commodityDTO = new SettlementApplyCommodityDTO();
        commodityDTO.setDiscountTotalPriceInShopCar(new BigDecimal("0.00"));
        commodityDTO.setAfterDiscountTotalPrice(new BigDecimal("0.00"));
        dto.setOrderCommodityList(Arrays.asList(commodityDTO));
        dto.setCheckDiscountMap(new HashMap<>());
        dto.setDiscountOptionId(Arrays.asList("value"));
        dto.setListOptions(Arrays.asList(0));
        dto.setAppendMap(new HashMap<>());
        dto.setIsAppointDiscount(0);

        final SettlementApplyDiscountDetailVO detailVO = new SettlementApplyDiscountDetailVO();
        detailVO.setDiscountAmount(new BigDecimal("88.00"));
        detailVO.setIsChecked(0);
        detailVO.setIsAppend(0);
        detailVO.setIsEnabled(0);
        detailVO.setDiscountOption(0);
        detailVO.setDiscountGuid("discountGuid");
        detailVO.setDiscountOptionId("discountOptionId");
        detailVO.setDiscountName("discountName");
        detailVO.setOptimal(0);
        final SettlementApplyCommodityVO settlementApplyCommodityVO = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO.setRid("rid");
        settlementApplyCommodityVO.setCommodityId("commodityId");
        settlementApplyCommodityVO.setCommodityName("commodityName");
        settlementApplyCommodityVO.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyCommodityVO.setSingleDiscountFee(new BigDecimal("0.00"));
        detailVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO));
        final List<SettlementApplyDiscountDetailVO> orderCouponDiscountList = Arrays.asList(detailVO);
        final SettlementApplyDiscountDetailVO detailVO1 = new SettlementApplyDiscountDetailVO();
        detailVO1.setDiscountAmount(new BigDecimal("66.00"));
        detailVO1.setIsChecked(0);
        detailVO1.setIsAppend(0);
        detailVO1.setIsEnabled(1);
        detailVO1.setDiscountOption(0);
        detailVO1.setDiscountGuid("discountGuid");
        detailVO1.setDiscountOptionId("discountOptionId");
        detailVO1.setDiscountName("discountName");
        detailVO1.setOptimal(0);
        final SettlementApplyCommodityVO settlementApplyCommodityVO1 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO1.setRid("rid");
        settlementApplyCommodityVO1.setCommodityId("commodityId");
        settlementApplyCommodityVO1.setCommodityName("commodityName");
        settlementApplyCommodityVO1.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyCommodityVO1.setSingleDiscountFee(new BigDecimal("0.00"));
        detailVO1.setCommodityList(Arrays.asList(settlementApplyCommodityVO1));
        final List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS = Arrays.asList(detailVO1);
        final SettlementApplyDiscountDetailVO settlementApplyDiscountDetailVO = new SettlementApplyDiscountDetailVO();
        settlementApplyDiscountDetailVO.setDiscountAmount(new BigDecimal("33.00"));
        settlementApplyDiscountDetailVO.setIsChecked(0);
        settlementApplyDiscountDetailVO.setIsAppend(0);
        settlementApplyDiscountDetailVO.setIsEnabled(1);
        settlementApplyDiscountDetailVO.setDiscountOption(0);
        settlementApplyDiscountDetailVO.setDiscountGuid("discountGuid");
        settlementApplyDiscountDetailVO.setDiscountOptionId("discountOptionId");
        settlementApplyDiscountDetailVO.setDiscountName("discountName");
        settlementApplyDiscountDetailVO.setOptimal(0);
        final SettlementApplyCommodityVO settlementApplyCommodityVO2 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO2.setRid("rid");
        settlementApplyCommodityVO2.setCommodityId("commodityId");
        settlementApplyCommodityVO2.setCommodityName("commodityName");
        settlementApplyCommodityVO2.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyCommodityVO2.setSingleDiscountFee(new BigDecimal("0.00"));
        settlementApplyDiscountDetailVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO2));

        // Configure MemberBaseFeign.listDiscount(...).
        final SettlementApplyOrderVO settlementApplyOrderVO = new SettlementApplyOrderVO();
        final SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO();
        oderInfo.setDiscountAmount(new BigDecimal("4.00"));
        settlementApplyOrderVO.setOderInfo(oderInfo);
        final SettlementApplyShareCommodityVO settlementApplyShareCommodityVO = new SettlementApplyShareCommodityVO();
        settlementApplyShareCommodityVO.setDiscountOption(0);
        settlementApplyShareCommodityVO.setDiscountGuid("discountGuid");
        settlementApplyShareCommodityVO.setDiscountOptionId("discountOptionId");
        final SettlementApplyCommodityVO settlementApplyCommodityVO3 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO3.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyShareCommodityVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO3));
        settlementApplyOrderVO.setShareCommodityVOList(Arrays.asList(settlementApplyShareCommodityVO));
        final SettlementApplyDiscountVO settlementApplyDiscountVO = new SettlementApplyDiscountVO();
        settlementApplyDiscountVO.setDiscountOption(0);
        settlementApplyDiscountVO.setDiscountName("discountName");
        final SettlementApplyDiscountDetailVO detailVO2 = new SettlementApplyDiscountDetailVO();
        detailVO2.setDiscountAmount(new BigDecimal("5.00"));
        detailVO2.setIsChecked(0);
        detailVO2.setIsAppend(0);
        detailVO2.setIsEnabled(1);
        detailVO2.setDiscountOption(0);
        detailVO2.setDiscountGuid("discountGuid");
        detailVO2.setDiscountOptionId("discountOptionId");
        detailVO2.setDiscountName("discountName");
        final SettlementApplyCommodityVO settlementApplyCommodityVO4 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO4.setDiscountFee(new BigDecimal("0.00"));
        detailVO2.setCommodityList(Arrays.asList(settlementApplyCommodityVO4));
        settlementApplyDiscountVO.setDiscountList(Arrays.asList(detailVO2));
        settlementApplyOrderVO.setDiscountList(Arrays.asList(settlementApplyDiscountVO));
        final List<SettlementApplyOrderVO> settlementApplyOrderVOS = Arrays.asList(settlementApplyOrderVO);
        when(mockMemberBaseFeign.listDiscount(new SettlementApplyOrderDTO())).thenReturn(settlementApplyOrderVOS);
        final SettlementApplyRuleVO applyRule = SettlementApplyRuleVO.defaultRule();
        // Run the test
        settlementListSupportUnderTest.checkDiscountCoupon(dto, orderCouponDiscountList, routineDiscountDetailVOS,
                settlementApplyDiscountDetailVO, applyRule);

        // Verify the results
    }

    @Test
    public void testCheckDiscountCoupon_MemberBaseFeignReturnsNoItems() {
        // Setup
        final SettlementApplyOrderDTO dto = new SettlementApplyOrderDTO();
        dto.setAutoCheck(0);
        dto.setConfirmCheck(0);
        final SettlementApplyOrderInfoDTO orderInfo = new SettlementApplyOrderInfoDTO();
        orderInfo.setOperSubjectGuid("operSubjectGuid");
        orderInfo.setShopCarOriginPayPrice(new BigDecimal("0.00"));
        dto.setOrderInfo(orderInfo);
        final SettlementApplyCommodityDTO commodityDTO = new SettlementApplyCommodityDTO();
        commodityDTO.setDiscountTotalPriceInShopCar(new BigDecimal("0.00"));
        commodityDTO.setAfterDiscountTotalPrice(new BigDecimal("0.00"));
        dto.setOrderCommodityList(Arrays.asList(commodityDTO));
        dto.setCheckDiscountMap(new HashMap<>());
        dto.setDiscountOptionId(Arrays.asList("value"));
        dto.setListOptions(Arrays.asList(0));
        dto.setAppendMap(new HashMap<>());
        dto.setIsAppointDiscount(0);

        final SettlementApplyDiscountDetailVO detailVO = new SettlementApplyDiscountDetailVO();
        detailVO.setDiscountAmount(new BigDecimal("0.00"));
        detailVO.setIsChecked(0);
        detailVO.setIsAppend(0);
        detailVO.setIsEnabled(1);
        detailVO.setDiscountOption(0);
        detailVO.setDiscountGuid("discountGuid");
        detailVO.setDiscountOptionId("discountOptionId");
        detailVO.setDiscountName("discountName");
        detailVO.setOptimal(0);
        final SettlementApplyCommodityVO settlementApplyCommodityVO = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO.setRid("rid");
        settlementApplyCommodityVO.setCommodityId("commodityId");
        settlementApplyCommodityVO.setCommodityName("commodityName");
        settlementApplyCommodityVO.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyCommodityVO.setSingleDiscountFee(new BigDecimal("0.00"));
        detailVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO));
        final List<SettlementApplyDiscountDetailVO> orderCouponDiscountList = Arrays.asList(detailVO);
        final SettlementApplyDiscountDetailVO detailVO1 = new SettlementApplyDiscountDetailVO();
        detailVO1.setDiscountAmount(new BigDecimal("0.00"));
        detailVO1.setIsChecked(0);
        detailVO1.setIsAppend(0);
        detailVO1.setIsEnabled(0);
        detailVO1.setDiscountOption(0);
        detailVO1.setDiscountGuid("discountGuid");
        detailVO1.setDiscountOptionId("discountOptionId");
        detailVO1.setDiscountName("discountName");
        detailVO1.setOptimal(0);
        final SettlementApplyCommodityVO settlementApplyCommodityVO1 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO1.setRid("rid");
        settlementApplyCommodityVO1.setCommodityId("commodityId");
        settlementApplyCommodityVO1.setCommodityName("commodityName");
        settlementApplyCommodityVO1.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyCommodityVO1.setSingleDiscountFee(new BigDecimal("0.00"));
        detailVO1.setCommodityList(Arrays.asList(settlementApplyCommodityVO1));
        final List<SettlementApplyDiscountDetailVO> routineDiscountDetailVOS = Arrays.asList(detailVO1);
        final SettlementApplyDiscountDetailVO settlementApplyDiscountDetailVO = new SettlementApplyDiscountDetailVO();
        settlementApplyDiscountDetailVO.setDiscountAmount(new BigDecimal("0.00"));
        settlementApplyDiscountDetailVO.setIsChecked(0);
        settlementApplyDiscountDetailVO.setIsAppend(0);
        settlementApplyDiscountDetailVO.setIsEnabled(0);
        settlementApplyDiscountDetailVO.setDiscountOption(0);
        settlementApplyDiscountDetailVO.setDiscountGuid("discountGuid");
        settlementApplyDiscountDetailVO.setDiscountOptionId("discountOptionId");
        settlementApplyDiscountDetailVO.setDiscountName("discountName");
        settlementApplyDiscountDetailVO.setOptimal(0);
        final SettlementApplyCommodityVO settlementApplyCommodityVO2 = new SettlementApplyCommodityVO();
        settlementApplyCommodityVO2.setRid("rid");
        settlementApplyCommodityVO2.setCommodityId("commodityId");
        settlementApplyCommodityVO2.setCommodityName("commodityName");
        settlementApplyCommodityVO2.setDiscountFee(new BigDecimal("0.00"));
        settlementApplyCommodityVO2.setSingleDiscountFee(new BigDecimal("0.00"));
        settlementApplyDiscountDetailVO.setCommodityList(Arrays.asList(settlementApplyCommodityVO2));
        final SettlementApplyRuleVO applyRule = SettlementApplyRuleVO.defaultRule();
        when(mockMemberBaseFeign.listDiscount(new SettlementApplyOrderDTO())).thenReturn(Collections.emptyList());

        // Run the test
        settlementListSupportUnderTest.checkDiscountCoupon(dto, orderCouponDiscountList, routineDiscountDetailVOS,
                settlementApplyDiscountDetailVO,applyRule);

        // Verify the results
    }
}
