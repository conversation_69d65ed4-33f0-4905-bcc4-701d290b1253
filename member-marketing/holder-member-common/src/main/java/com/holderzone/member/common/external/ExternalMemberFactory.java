package com.holderzone.member.common.external;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.enums.PlatformEnum;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description
 */
@Component
public class ExternalMemberFactory {

    private final ExternalMemberService saasMemberServiceImpl;

    private final ExternalMemberService marketMemberServiceImpl;

    public ExternalMemberFactory(@Qualifier("saasMemberServiceImpl")ExternalMemberService saasMemberServiceImpl,
                                 @Qualifier("marketMemberServiceImpl")ExternalMemberService marketMemberServiceImpl){
        this.saasMemberServiceImpl = saasMemberServiceImpl;
        this.marketMemberServiceImpl = marketMemberServiceImpl;
    }

    public ExternalMemberService build(PlatformEnum platformEnum) {
        switch (platformEnum) {
            case SAAS:
                return saasMemberServiceImpl;
            case PASS_RETAIL:
            case MALL:
                return marketMemberServiceImpl;
            default:
                throw new BusinessException("平台有误");
        }
    }
}
