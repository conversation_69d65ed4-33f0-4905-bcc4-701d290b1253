package com.holderzone.member.common.module.settlement.rule.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 结算台优惠类型
 *
 * <AUTHOR>
 * @create 2023-09-02
 * @description 结算台优惠类型
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountItemEnum {

    /**
     * 会员权益
     */
    MEMBER_RIGHTS(0, "会员权益", SettlementDiscountTypeEnum.ORDER, 1),
    /**
     * 兑换券
     */
    COUPON_EXCHANGE(12, "兑换券", SettlementDiscountTypeEnum.SINGLE_ITEM, 0),
    /**
     * 代金券
     */
    COUPON_VOUCHER(10, "代金券", SettlementDiscountTypeEnum.ORDER, 0),
    /**
     * 折扣券
     */
    COUPON_DISCOUNT(11, "折扣券", SettlementDiscountTypeEnum.ORDER, 0),
    /**
     * 满减活动
     */
    FULL_OFF(20, "满减满折活动", SettlementDiscountTypeEnum.ORDER, 1),
    /**
     * 满折活动
     */
    FULL_DISCOUNT(21, "满折活动", SettlementDiscountTypeEnum.ORDER, 1),
    /**
     * 限时特价
     */
    LIMITED_TIME_SPECIAL(22, "限时特价", SettlementDiscountTypeEnum.SINGLE_ITEM, 1),
    /**
     * 第N份优惠
     */
    NTH_DISCOUNT(23, "第N份优惠", SettlementDiscountTypeEnum.ORDER, 1),
    /**
     * 资产优惠
     */
    ASSET_PREFERENCE(91, "资产优惠", SettlementDiscountTypeEnum.PROPERTY, 1);

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 类型
     */
    private final SettlementDiscountTypeEnum type;

    /**
     * 单笔限用数量
     */
    private final int limitNum;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */

    public static String getDesByCode(int code) {
        for (SettlementDiscountItemEnum anEnum : SettlementDiscountItemEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum.getDes();
            }
        }
        return null;
    }

    public static SettlementDiscountItemEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (SettlementDiscountItemEnum typeEnum : SettlementDiscountItemEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum;
            }
        }
        return null;
    }
}
