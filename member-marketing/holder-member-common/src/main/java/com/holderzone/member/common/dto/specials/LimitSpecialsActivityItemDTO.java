package com.holderzone.member.common.dto.specials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/5/17
 * @description 限时特价商品
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "限时特价商品", description = "限时特价商品")
public class LimitSpecialsActivityItemDTO implements Serializable {

    private static final long serialVersionUID = -1828523635280753827L;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String commodityId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    /**
     * 商品类型
     * @see com.holderzone.member.common.enums.growth.CommodityComboTypeEnum
     */
    @ApiModelProperty(value = "商品类型")
    private Integer commodityType;

    /**
     * 特价类型
     */
    @ApiModelProperty(value = "特价类型 1打折 2减价 3指定价格")
    private Integer specialsType;

    /**
     * 特价数额
     */
    @ApiModelProperty(value = "特价数额")
    private BigDecimal specialsNumber;

    /**
     * 优惠限购
     */
    @ApiModelProperty(value = "优惠限购")
    private Integer limitNumber;

    /**
     * 商品渠道
     */
    @ApiModelProperty(value = "商品渠道")
    private String channel;

    /**
     * 商品是否存在 0否 1是
     */
    @ApiModelProperty(value = "商品是否存在 0否 1是")
    private Integer isExist;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private String system;

}
