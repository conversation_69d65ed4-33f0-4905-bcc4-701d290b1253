package com.holderzone.member.common.dto.member;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 会员
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberQueryDTO {

    /**
     * 会员id
     */
    private String memberGuid;

    /**
     * 手机号码
     */
    private String phoneNum;

    /**
     * 微信 open id
     */
    private String openid;

    /**
     * 会员持卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 卡号
     */
    private String cardNum;

    /**
     * 会员guid列表
     */
    private List<String> memberGuidList;

    /**
     * 会员状态
     */
    private String memberStatus;

    /**
     * 查询赚餐券标识
     */
    private Boolean queryZhuanCanFlag;

}
