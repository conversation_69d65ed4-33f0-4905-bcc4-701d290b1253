package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.*;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.order.CrmOrderDTO;
import com.holderzone.member.common.dto.order.CrmOrderStockDTO;
import com.holderzone.member.common.dto.s2b2c.ShopQueryPageDTO;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.s2b2bc.ShopMode;
import com.holderzone.member.common.enums.s2b2bc.ShopStatus;
import com.holderzone.member.common.external.ExternalStoreService;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.S2b2cMallFeign;
import com.holderzone.member.common.qo.crm.CrmOrderDetailQo;
import com.holderzone.member.common.qo.crm.CrmOrderDetailVo;
import com.holderzone.member.common.qo.growth.AppletGrowthStoreQO;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.s2b2c.ShopListVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 私域商城门店实现类
 * <AUTHOR>
 * @version 1.0, 2025/5/22
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class MallStoreServiceImpl implements ExternalStoreService {

	@Resource
	private final S2b2cMallFeign s2b2cMallFeign;

	@Resource
	private final MemberMallToolFeign memberMallToolFeign;


	@Override
	public AppIdRespDTO getAppId (CrmAppIdQueryDTO crmAppIdReqDTO) {
		AppIdRespDTO appIdRespDTO = new AppIdRespDTO();
		Result<WeChatConfigInfoVO> weChatConfigInfoVOResult = memberMallToolFeign.queryByOperSubjectGuid();
		log.info("getAppId:{}", JSON.toJSONString(weChatConfigInfoVOResult));

		if (Objects.isNull(weChatConfigInfoVOResult) || Objects.isNull(weChatConfigInfoVOResult.getData())) {
			return null;
		}
		WeChatConfigInfoVO weChatConfigInfoVO = weChatConfigInfoVOResult.getData();
		appIdRespDTO.setAppId(weChatConfigInfoVO.getAppId());
		appIdRespDTO.setAppsecret(weChatConfigInfoVO.getApplyPrivateKey());
		appIdRespDTO.setAppName(weChatConfigInfoVO.getAppName());
		appIdRespDTO.setAppLogo(weChatConfigInfoVO.getAppLogo());
		return appIdRespDTO;
	}

	@Override
	public ResAppletOrderCallBack appletOrderCallBack (AppletOrderCallBack query) {
		return null;
	}

	@Override
	public List<StoreBaseInfo> getStoreByStrategyOrCommodity (AppletGrowthStoreQO query) {
		return Collections.emptyList();
	}

	@Override
	public PaySettingBaseRes getPaySetting (PaySettingDTO paySettingDTO) {
		return null;
	}

	@Override
	public int refundAggPayOrder (CrmRefundPayDTO crmRefundPayDTO) {
		return 0;
	}

	@Override
	public void getReceiptPrinting (ReceiptPrintingBaseDTO receiptPrintingBaseDTO) {
		// 实际的业务逻辑实现
	}

	@Override
	public List<StoreInfoVO> listStoreStall (StoreInfoDTO storeInfoDTO) {
		return Collections.emptyList();
	}

	@Override
	public CrmOperatingSubjectRespDTO getOperatingSubject (CrmOperatingSubjectQueryDTO subjectReqDTO) {
		return null;
	}

	@Override
	public CrmOrderDetailVo getOrderDetail (CrmOrderDetailQo crmOrderDetailQo) {
		return null;
	}

	@Override
	public List<ResOrderCommodity> queryOrderCommodity (QueryOrderCommodity queryOrderCommodity) {
		return Collections.emptyList();
	}

	@Override
	public List<StoreInfoVO> getStoreStall (StoreInfoDTO query) {
		return Collections.emptyList();
	}

	@Override
	public Object getStoreById (StoreByIdQO storageByIdQuery) {
		if(CollUtil.isEmpty(storageByIdQuery.getStore_ids())){
			return null;
		}
		QueryStoreBasePage queryStoreBasePage = new QueryStoreBasePage();
		Set<String> storeIds =  Optional.ofNullable(storageByIdQuery.getStore_ids())
										 .map(collection -> collection.stream()
																	.map(String::valueOf)
																	.collect(Collectors.toSet()))
										 .orElse(Collections.emptySet());
		queryStoreBasePage.setStoreIds(new ArrayList<>(storeIds));
		ShopQueryPageDTO shopQuery = toShopQueryPageDTO(queryStoreBasePage);
		Result<Page<ShopListVO>> result = s2b2cMallFeign.platformShopPageList(shopQuery);
		return Optional.ofNullable(result).map(Result::getData)
					   .filter(voPage -> voPage.getRecords() != null)
					   .map(voPage -> voPage.getRecords().stream()
										   .map(this::toStoreBaseInfo)
										   .collect(Collectors.toList()))
					   .orElse(Collections.emptyList());
	}

	@Override
	public List<StoreInfoVO> queryStoreV2 (QueryStoreBasePage query) {
		return Collections.emptyList();
	}

	@Override
	public List<StoreBaseInfo> queryStore (QueryStoreBasePage queryStoreBasePage) {
		return Collections.emptyList();
	}

	@Override
	public JSONObject pushOrder (CrmOrderDTO crmOrderDTO) {
		return null;
	}

	@Override
	public JSONObject pushOrderStock (CrmOrderStockDTO crmOrderStockDTO) {
		return null;
	}

	@Override
	public List<StoreBaseInfo> queryStore (String name) {
		return Collections.emptyList();
	}

	@Override
	public List<OperSubjectVO> listOperSubjectAndApplet () {
		return Collections.emptyList();
	}

	@Override
	public List<QueryOrderDTO> queryOrder (QueryOrderCommodity queryOrderCommodity) {
		return Collections.emptyList();
	}

	@Override
	public StoreCountDTO countStores() {
		com.holderzone.framework.util.Page<StoreBaseInfo>  page = storePage(new QueryStoreBasePage());
		return new StoreCountDTO()
			.setRetailStoreCount(0)
			.setMallStoreCount(page != null ? (int)page.getTotalCount() : NumberConstant.NUMBER_0)
			.setTotalCount(page != null ? (int) page.getTotalCount() : NumberConstant.NUMBER_0);
	}

	private ShopQueryPageDTO toShopQueryPageDTO(QueryStoreBasePage query) {
		Set<Long> shopIdSet = Optional.ofNullable(query.getStoreIds())
									  .map(collection -> collection.stream()
																 .map(Long::valueOf)
																 .collect(Collectors.toSet()))
									  .orElse(Collections.emptySet());

		ShopQueryPageDTO shopQuery = new ShopQueryPageDTO();
		shopQuery.setKeywords(query.getName());
		// 状态正常
		shopQuery.setShopIds(shopIdSet);
		shopQuery.setStatus(ShopStatus.NORMAL);
		shopQuery.setShopModes(new HashSet<>(Arrays.asList(ShopMode.COMMON, ShopMode.O2O)));
		shopQuery.setCurrent(Optional.ofNullable(query.getPage()).orElse(NumberConstant.NUMBER_1));
		shopQuery.setSize(Optional.ofNullable(query.getPageSize()).orElse(NumberConstant.NUMBER_10));
		return shopQuery;
	}


	private StoreBaseInfo toStoreBaseInfo(ShopListVO vo) {
		StoreBaseInfo info = new StoreBaseInfo();
		info.setId(Optional.ofNullable(vo.getId()).map(Object::toString).orElse(null));
		info.setChannel(SystemEnum.MALL.getDes());
		info.setSystem(SystemEnum.MALL.name());
		info.setStoreName(vo.getName());
		info.setStoreNumber(vo.getNo());
		info.setPhoneNumber(vo.getContractNumber());
		info.setAddress(vo.getAddress());
		info.setStoreLogo(vo.getLogo());
		info.setStatus(Optional.ofNullable(vo.getStatus()).map(Enum::name).orElse(null));
		if (vo.getLongitude() != null && vo.getLatitude() != null) {
			// 纬度 + "," + 经度
			info.setAddress_point(vo.getLatitude() + StringConstant.COMMA + vo.getLongitude());
		}
		return info;
	}

	@Override
	public List<StoreBaseInfo> listStore(QueryStoreBasePage query) {
		ShopQueryPageDTO shopQuery = toShopQueryPageDTO(query);
		Result<Page<ShopListVO>> result = s2b2cMallFeign.platformShopPageList(shopQuery);
		return Optional.ofNullable(result).map(Result::getData)
					   .filter(voPage -> voPage.getRecords() != null)
					   .map(voPage -> voPage.getRecords().stream().map(this::toStoreBaseInfo).collect(Collectors.toList())).orElse(Collections.emptyList());
	}

	@Override
	public com.holderzone.framework.util.Page<StoreBaseInfo> storePage (QueryStoreBasePage query) {
		ShopQueryPageDTO shopQuery = toShopQueryPageDTO(query);
		Result<Page<ShopListVO>> result = s2b2cMallFeign.platformShopPageList(shopQuery);
		return Optional.ofNullable(result).map(Result::getData).filter(voPage -> voPage.getRecords() != null).map(voPage -> {
			List<StoreBaseInfo> records =
					voPage.getRecords().stream().map(this::toStoreBaseInfo).collect(Collectors.toList());
			com.holderzone.framework.util.Page<StoreBaseInfo> pageResult = new com.holderzone.framework.util.Page<>();
			pageResult.setCurrentPage((int) voPage.getCurrent());
			pageResult.setPageSize((int) voPage.getSize());
			pageResult.setTotalCount((int) voPage.getTotal());
			pageResult.setData(records);
			return pageResult;
		}).orElseGet(com.holderzone.framework.util.Page::new);
	}

    @Override
    public List<StoreBaseInfo> listStoreAndStall(QueryStoreBasePage query) {
        Set<Long> shopIdSet = Optional.ofNullable(query.getStoreIds())
                .map(collection -> collection.stream()
                        .map(Long::valueOf)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());

        ShopQueryPageDTO shopQuery = new ShopQueryPageDTO();
        shopQuery.setKeywords(query.getName());
        // 状态正常
        shopQuery.setShopIds(shopIdSet);
        shopQuery.setStatus(ShopStatus.NORMAL);
        shopQuery.setCurrent(Optional.ofNullable(query.getPage()).orElse(NumberConstant.NUMBER_1));
        shopQuery.setSize(Optional.ofNullable(query.getPageSize()).orElse(NumberConstant.NUMBER_9999999));

        Result<Page<ShopListVO>> result = s2b2cMallFeign.platformShopPageList(shopQuery);
        return Optional.ofNullable(result)
                .map(Result::getData)
                .filter(page -> page.getRecords() != null)
                .map(page -> page.getRecords().stream().map(this::toStoreBaseInfo).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }
}
