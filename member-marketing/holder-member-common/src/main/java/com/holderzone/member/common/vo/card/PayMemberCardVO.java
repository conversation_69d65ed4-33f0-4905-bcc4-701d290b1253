package com.holderzone.member.common.vo.card;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付会员卡
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
@Data
public class PayMemberCardVO implements Serializable {

    private static final long serialVersionUID = -8738738601644778492L;

    /**
     * 会员持卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 会员卡名称
     */
    private String cardName;

    /**
     * 卡图片
     */
    private String cardImage;

    /**
     * 会员卡金额
     */
    private BigDecimal cardAmount;

    /**
     * 电子卡号
     */
    private String electronicCardNum;

    /**
     * 是否超额
     */
    private Integer isExcess;

    /**
     * 0超额次数；1超额金额
     */
    private Integer excessType;

    /**
     * 可超额金额
     */
    private BigDecimal excessAmount;

    /**
     * 可超额次数
     */
    private Integer excessTimes;

    /**
     * 是否需要密码校验
     */
    private Integer needCheckPwd;

    /**
     * 默认卡
     */
    private Integer defaultCard;

    /**
     * 是否适用所有门店：0-部分门店，1-所有门店
     */
    private Integer applicableAllStore;
}
