package com.holderzone.member.common.external.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.CrmCommodityReqDTO;
import com.holderzone.member.common.dto.excel.ItemUploadVO;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.growth.CommodityComboTypeEnum;
import com.holderzone.member.common.enums.growth.CommodityStatusEnum;
import com.holderzone.member.common.enums.s2b2bc.ProductSortEnum;
import com.holderzone.member.common.enums.s2b2bc.ProductStatus;
import com.holderzone.member.common.external.ExternalItemService;
import com.holderzone.member.common.feign.S2b2cMallFeign;
import com.holderzone.member.common.qo.base.CommodityBasePageQO;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.mall.QueryProductCategoryCrmQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.util.pay.AmountCalculateHelper;
import com.holderzone.member.common.vo.base.CommodityBaseTypeVO;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.s2b2c.PlatformProductVO;
import com.holderzone.member.common.vo.s2b2c.params.PlatformProductParam;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 私域商城 商品相关服务
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MallItemServiceImpl implements ExternalItemService {

    @Resource
    private final S2b2cMallFeign s2b2cMallFeign;


    @Override
    public List<ResArrayStrategyBase> getOperatingStrategy(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public List<ResCategoryBase> queryCategoryByStrategy(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public Pair<Integer, List<ResCommodityBase>> pageStrategyCommodity(QueryCommodityBasePage commodityBasePage) {
        return null;
    }

    @Override
    public List<ResCommodityBase> listCommodityBase(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public List<ResCommodityBase> listCommodityByDetail(QueryArrayShopBase queryStoreBasePage) {
        // 参数映射
        PlatformProductParam param = new PlatformProductParam();
        // 只查已上架
        param.setStatus(ProductStatus.SELL_ON);
        // 按审核时间排序
        param.setSort(ProductSortEnum.AUDIT_TIME_DESC);
        // 设置大页码以获取所有数据
        param.setSize(NumberConstant.NUMBER_9999999);
        param.setCurrent(NumberConstant.NUMBER_1);

        if (CollectionUtils.isEmpty(queryStoreBasePage.getCommodityIdList())) {
            return Collections.emptyList();
        }

        // 如果有商品ID列表，设置到查询参数中，需要转换为Long类型
        param.setProductIds(queryStoreBasePage.getCommodityIdList().stream().map(Long::valueOf).collect(Collectors.toList()));
        // 调用feign获取所有商品
        Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<PlatformProductVO>> result = s2b2cMallFeign.getPlatformAllProducts(param);

        return Optional.ofNullable(result)
                .map(Result::getData)
                .filter(page -> page.getRecords() != null)
                .map(page -> page.getRecords().stream().map(vo -> new ResCommodityBase()
                        .setCommodityId(vo.getId() != null ? vo.getId().toString() : null)
                        .setCommodityCode(vo.getId() != null ? vo.getId().toString() : null)
                        .setCommodityComboType(String.valueOf(CommodityComboTypeEnum.COMMODITY_SINGLE.getCode()))
                        .setBasePrice(getBasePricce(vo))
                        .setSystem(SystemEnum.MALL.name())
                        .setName(vo.getName())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * 获取商品基础价格
     *
     * @param vo 商品信息
     * @return 商品基础价格
     */
    private String getBasePricce(PlatformProductVO vo) {
        String basePrice = null;
        if (vo.getSalePrice() != null) {
            basePrice = AmountCalculateHelper.toYuan(vo.getSalePrice()).toString();
        } else if (vo.getMinPrice() != null && vo.getMaxPrice() != null && vo.getMinPrice().equals(vo.getMaxPrice())) {
            basePrice = AmountCalculateHelper.toYuan(vo.getMinPrice()).toString();
        } else if (vo.getMinPrice() != null && vo.getMaxPrice() != null) {
            basePrice = AmountCalculateHelper.toYuan(vo.getMinPrice()) + "-" + AmountCalculateHelper.toYuan(vo.getMaxPrice());
        } else if (vo.getMinPrice() != null) {
            basePrice = AmountCalculateHelper.toYuan(vo.getMinPrice()).toString();
        } else if (vo.getMaxPrice() != null) {
            basePrice = AmountCalculateHelper.toYuan(vo.getMaxPrice()).toString();
        }

        return basePrice;
    }

    @Override
    public Pair<Integer, List<ResGradeCommodityBase>> listStoreCommodityPage(GradeCommodityBasePageQO pageQO) {
        // 参数映射
        PlatformProductParam param = new PlatformProductParam();
        // 只查已上架
        param.setStatus(ProductStatus.SELL_ON);
        // 按审核时间排序
        param.setSort(ProductSortEnum.AUDIT_TIME_DESC);
        // 设置大页码以获取所有数据
        param.setSize(NumberConstant.NUMBER_9999);
        param.setCurrent(NumberConstant.NUMBER_1);

        // 如果有商品ID列表，设置到查询参数中，需要转换为Long类型
        if (CollectionUtils.isNotEmpty(pageQO.getCommityIds())) {
            List<Long> productIds =
                    pageQO.getCommityIds().stream().map(Long::valueOf).collect(Collectors.toList());
            param.setProductIds(productIds);
        }

        // 调用feign获取所有商品
        Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<PlatformProductVO>> result = s2b2cMallFeign.getPlatformAllProducts(param);

        // 转换结果
        return Optional.ofNullable(result)
                .map(Result::getData)
                .map(page -> {
                    List<ResGradeCommodityBase> commodityList = page.getRecords().stream()
                            .map(vo -> {
                                ResGradeCommodityBase base = new ResGradeCommodityBase();
                                base.setCommodityId(vo.getId() != null ? vo.getId().toString() : null);
                                base.setName(vo.getName());
                                base.setPhoto(vo.getPic());
                                base.setPrice(getBasePricce(vo));
                                // 已上架
                                base.setState(CommodityStatusEnum.COMMODITY_UP.getDes());
                                // 单品
                                base.setType(CommodityComboTypeEnum.COMMODITY_SINGLE.getDes());
                                base.setCode(vo.getId() != null ? vo.getId().toString() : null);
                                base.setSystem(SystemEnum.MALL.name());
                                return base;
                            })
                            .collect(Collectors.toList());
                    return Pair.of(page.getRecords().size(), commodityList);
                })
                .orElse(Pair.of(NumberConstant.NUMBER_0, Collections.emptyList()));
    }

    @Override
    public Page<CommodityBaseVO> pageCommodityBase(CommodityBasePageQO pageQO) {
        // 私域商城只有单品数据
        if (pageQO.getCommodityType() != null
                && !pageQO.getCommodityType().equals(CommodityComboTypeEnum.COMMODITY_SINGLE.getCode())) {
            return new Page<>();
        }

        // 参数映射
        PlatformProductParam param = new PlatformProductParam();
        // 只查已上架
        param.setStatus(ProductStatus.SELL_ON);
        // 按审核时间排序
        param.setSort(ProductSortEnum.AUDIT_TIME_DESC);
        param.setKeywords(pageQO.getSearchKey());
        param.setCurrent(Optional.ofNullable(pageQO.getCurrentPage()).orElse(NumberConstant.NUMBER_1));
        param.setSize(Optional.ofNullable(pageQO.getPageSize()).orElse(NumberConstant.NUMBER_10));

        // 调用feign
        Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<PlatformProductVO>> result =
                s2b2cMallFeign.getPlatformAllProducts(param);

        return Optional.ofNullable(result)
                .map(Result::getData)
                .filter(voPage -> voPage.getRecords() != null)
                .map(voPage -> {
                    List<CommodityBaseVO> records = voPage.getRecords().stream()
                            .map(vo -> {
                                CommodityBaseVO base = new CommodityBaseVO();
                                base.setCommodityId(vo.getId() != null ? vo.getId().toString() : null);
                                base.setCommodityCode(vo.getId() != null ? vo.getId().toString() : null);
                                base.setCommodityName(vo.getName());
                                base.setCommodityImg(vo.getPic());
                                // 私域商城只有单品
                                base.setCommodityType(CommodityComboTypeEnum.COMMODITY_SINGLE.getCode());
                                base.setCommodityPrice(getBasePricce(vo));
                                base.setCommodityDescribe(vo.getSaleDescribe());
                                base.setChannel(SystemEnum.MALL.getDes());
                                base.setSystem(SystemEnum.MALL.name());
                                base.setBusinessType(SystemEnum.MALL.getCode());
                                // 可补充更多字段
                                return base;
                            })
                            .collect(Collectors.toList());
                    Page<CommodityBaseVO> page = new Page<>();
                    page.setCurrentPage(voPage.getCurrent());
                    page.setPageSize(voPage.getSize());
                    page.setTotalCount(voPage.getTotal());
                    page.setData(records);
                    return page;
                })
                .orElseGet(Page::new);
    }

    @Override
    public List<CommodityBaseTypeVO> getStoreGoodsComboType() {
        return Collections.emptyList();
    }

    @Override
    public List<CommodityInfoDTO> listCommodity(CommodityInfoQO commodityInfoQO) {
        return Collections.emptyList();
    }

    @Override
    public CrmFeignModel<CommodityInfoDTO> listCommodityHasCount(CommodityInfoQO commodityInfoQO) {
        return null;
    }

    @Override
    public List<StrategyInfoVO> listStrategyInfo(StrategyInfoDTO strategyInfoDTO) {
        return Collections.emptyList();
    }

    @Override
    public String getCommodityUrl(CrmCommodityReqDTO reqDTO) {
        return "";
    }

    @Override
    public List<CommodityDetailsVO> getCommodityDetails(CommodityDetailsQO detailsQO) {
        return Collections.emptyList();
    }

    @Override
    public List<CommodityDetailsVO> getNewCommodityDetails(CommodityDetailsQO detailsQO) {
        return Collections.emptyList();
    }

    @Override
    public List<ProductCrmCategoryVO> getCommodityCategory(QueryProductCategoryCrmQO categoryCrmQO) {
        return Collections.emptyList();
    }

    @Override
    public ItemUploadVO itemUploadExcelUrl(String fileUrl, Integer activityType) {
        return null;
    }
}
