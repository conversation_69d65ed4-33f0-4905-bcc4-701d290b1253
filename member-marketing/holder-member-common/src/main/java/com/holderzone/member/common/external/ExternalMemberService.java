package com.holderzone.member.common.external;

import com.holderzone.framework.util.Page;
import com.holderzone.member.common.dto.activity.MemberBaseActivityDTO;
import com.holderzone.member.common.dto.card.ResponseCardInfoDTO;
import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.excel.MemberUploadExcelVO;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.member.TableBasicDTO;
import com.holderzone.member.common.qo.activity.MemberBaseActivityQO;
import com.holderzone.member.common.qo.activity.RequestLabelBaseQO;
import com.holderzone.member.common.qo.gift.RequestQueryMemberBaseQO;
import com.holderzone.member.common.qo.gift.RequestQueryVolumeBaseQO;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;
import com.holderzone.member.common.vo.grade.QueryMemberGradeVO;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2024-04-02
 * @description 外部服务支撑接口
 */
public interface ExternalMemberService {

    /**
     * 查询桌台数据
     */
    List<TableBasicDTO> queryTable(String storeGuid, List<String> tableGuidList);

    QueryMemberGradeVO queryGrowthValueTaskDetail(String roleType, Integer gradeType);

    /**
     * 会员标签
     */
    List<ResponseOperationLabel> getLabelList(RequestLabelBaseQO requestLabelDTO);

    boolean saveAutomaticLabel(RequestLabelSetting req);

    void addMemberInfoLabel(AddMemberLabelCorrelationQO addMemberLabelCorrelationQO);

    void removeMemberInfoLabel(RemoveMemberLabelCorrelationQO removeMemberLabelCorrelationQO);

    /**
     * 会员
     */
    MemberBasicInfoVO getMemberInfo(MemberQueryDTO queryDTO);

    /**
     * 查询会员名称
     */
    String getMemberName(MemberQueryDTO queryDTO);

    Page<ResponseOperationMemberInfo> getOperationMemberInfoPage(RequestQueryMemberBaseQO request);


    /**
     * 调整成长值
     */
    void updateGrowthValue(RequestMemberGrowthValue request);

    /**
     * 优惠券
     */
    Page<IslandCouponDTO> queryVolumeList(RequestQueryVolumeBaseQO request);

    void sendMemberCoupon(SendCertifiedStampsDTO sendCertifiedStampsDTO);

    MemberUploadExcelVO memberUploadExcelUrl(String fileUrl);

    String downloadExcelUrl();

    MemberBaseActivityDTO memberBaseDataInfo(MemberBaseActivityQO memberBaseActivityQO);

    List<ResponseCardInfoDTO> getAllCardList();

    List<Integer> getBusinessScene();

    /**
     * 获取订单消费类型
     */
    String getConsumptionOrderType(Integer orderType);
}
