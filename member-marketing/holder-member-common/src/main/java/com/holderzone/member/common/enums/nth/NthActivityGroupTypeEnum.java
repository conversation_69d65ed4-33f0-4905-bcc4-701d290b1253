package com.holderzone.member.common.enums.nth;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 适用人群类型
 */
@Getter
@AllArgsConstructor
public enum NthActivityGroupTypeEnum {

    /**
     * 不限制
     */
    UN_LIMIT(0, "不限制"),

    /**
     * 所有注册会员
     */
    ALL_MEMBER(1, "所有注册会员"),

    /**
     * 指定标签会员
     */
    SPECIFIED_MEMBER(2, "指定人群"),
    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public static NthActivityGroupTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (NthActivityGroupTypeEnum typeEnum : NthActivityGroupTypeEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum;
            }
        }
        return null;
    }
}
