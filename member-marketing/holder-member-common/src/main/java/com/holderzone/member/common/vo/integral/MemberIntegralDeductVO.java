package com.holderzone.member.common.vo.integral;

import com.holderzone.member.common.qo.equities.MemberPriceCommodityQO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员积分折扣列表
 */
@Data
@Accessors(chain = true)
public class MemberIntegralDeductVO implements Serializable {

    /**
     * 订单积分优惠后总价
     */
    @ApiModelProperty("订单优惠后总价")
    private BigDecimal orderRealPaymentAmount;

    /**
     * 订单积分优惠金额
     */
    @ApiModelProperty("订单优惠金额")
    private BigDecimal discountDynamicsAmount = BigDecimal.ZERO;

    /**
     * 折扣类型
     *
     * @see com.holderzone.member.common.enums.equities.DiscountTypeEnum
     */
    @ApiModelProperty("折扣类型")
    private Integer discountType;

    /**
     * 会员积分抵扣ID
     */
    @ApiModelProperty("会员积分抵扣ID")
    private String versionId;

    /**
     * 会员积分抵扣GUID
     */
    @ApiModelProperty("会员积分抵扣GUID")
    private String deductGuid;

    /**
     * 会员积分抵扣名称
     */
    @ApiModelProperty("会员积分抵扣名称")
    private String deductName;

    /**
     * 使用积分
     */
    @ApiModelProperty("使用积分")
    private Integer integralNum;

    /**
     * 积分优惠金额
     */
    @ApiModelProperty("积分优惠金额")
    private BigDecimal integralDiscountAmount;

    /**
     * 周期优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @ApiModelProperty("周期优惠限制 0：不限制 1：限制")
    private Integer periodDiscountLimited;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;


    /**
     * 积分抵现规则
     */
    private String integralNowMoneyJson;

    /**
     * 周期剩余优惠
     */
    @ApiModelProperty("周期剩余优惠")
    private BigDecimal cycleResidueAmount;

    /**
     * 周期类型 -1:自定义 0：日 1：周 2：月 3：年 4 累计
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private Integer equitiesTimeLimitedType;

    /**
     * 是否选中
     */
    @ApiModelProperty("是否选中")
    private Integer isChecked;

    private Integer isOptimal;

    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private BigDecimal freightDiscountAmount;

    /**
     * 桌台费
     */
    @ApiModelProperty("桌台费")
    private BigDecimal tableDiscountAmount;

    /**
     * 购物车商品明细
     */
    @ApiModelProperty("购物车商品明细")
    private List<MemberPriceCommodityQO> memberPriceCommodityQOS;

    /**
     * 每多少积分（配置）
     */
    private Integer disIntegralNum;

    /**
     * 抵扣金额（配置）
     */
    private BigDecimal disForNowMoney;

    /**
     * 排序
     */
    private Integer sort;
}
