package com.holderzone.member.common.qo.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 会员成长值调整请求模型
 *
 * <AUTHOR>
 * @since 2021-11-23 11:14:31
 */
@Data
@ApiModel(value = "RequestMemberGrowthValue", description = "会员成长值调整请求模型")
@Accessors(chain = true)
public class RequestMemberGrowthValue implements Serializable {

    @ApiModelProperty(value = "会员GUID", required = true)
    @NotEmpty(message = "会员GUID不能为空")
    private List<String> memberInfoGuidList;

    @ApiModelProperty(value = "成长值类型，0增加，1减少", required = true)
    @NotNull(message = "成长值类型不能为空")
    @Min(value = 0)
    @Max(value = 1)
    private Integer growthValueType;

    @ApiModelProperty(value = "成长值", required = true)
    @Min(value = 0)
    @Max(value = 9999999)
    @NotNull(message = "成长值不能为空")
    private Integer growthValue;

    @ApiModelProperty(value = "备注", required = true)
    @NotEmpty(message = "备注不能为空")
    @Length(max = 200)
    private String remark;

    /**
     * @see com.holderzone.member.common.enums.member.AmountSourceTypeEnum
     */
    private Integer sourceType;

    /**
     * 操作人员账号名字
     */
    private String operatorAccountName;

    /**
     * @see com.holderzone.member.common.enums.member.AmountSourceTypeEnum
     */
    private Integer source;

    /**
     * 订单号
     */
    private String orderNum;

    private String storeName;

    /**
     * 会员手机号
     */
    private String phoneNum;

}