package com.holderzone.member.common.qo.redeem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠兑换码关联修改
 */
@Data
@Accessors(chain = true)
public class RequestUpdateDtlQO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> guids;

    @ApiModelProperty("会员guid")
    private String memberGuid;

    @ApiModelProperty("会员名称")
    private String memberName;

    @ApiModelProperty("会员电话")
    private String memberPhone;

}
