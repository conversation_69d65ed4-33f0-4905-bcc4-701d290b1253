package com.holderzone.member.common.module.settlement.apply.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.member.common.module.settlement.util.SettlementVerifyUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 结算规则应用订单数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementLockedOrderInfoDTO implements Serializable {

    private static final long serialVersionUID = 2114357817386642843L;

    /**
     * 主体
     */
    @ApiModelProperty("operSubjectGuid")
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    @ApiModelProperty("会员guid")
    @NotBlank(message = "会员guid必传！")
    private String memberInfoGuid;

    /**
     * 核销来源
     * （核销渠道：小程序、一体机、管理后台）
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     * todo 食堂核销来源，需要确定是否正确
     */
    @JsonIgnore
    private Integer source;

    /**
     * 商品合计
     */
    @Min(value = 0, message = "商品合计金额必传！")
    @ApiModelProperty("商品合计金额")
    private BigDecimal commodityTotalAmount;


    /**
     * 业务（订单类型） :见同步表
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    @ApiModelProperty("业务")
    @NotBlank(message = "业务必传！")
    private String business;

    /**
     * 终端
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private String terminal;

    /**
     * 门店guid
     * 档口也传门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 门店
     */
    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 渠道：pos、商城...
     */
    @ApiModelProperty("渠道")
    @NotBlank(message = "渠道必传！")
    private String channel;

    /**
     * 当前订单号 : 计算时必传
     * orderNum
     */
    @ApiModelProperty("当前订单号")
    @NotBlank(message = "订单号必传！")
    private String orderNumber;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    @NotBlank(message = "下单时间必传！")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime orderTime;

    /**
     * 退款是否退优惠：0否 1是
     */
    @ApiModelProperty("退款是否退优惠：0否 1是")
    @NotBlank(message = "退款是否退优惠必传")
    private Integer couponRollback;

    /**
     * 订单实付金额
     * （带动消费金额）
     */
    private BigDecimal orderPaidAmount;

    /**
     * 操作人
     */
    @JsonIgnore
    private String operatorAccountName;

    private String userName;

    private String userPhone;

    public void validate() {
        SettlementVerifyUtil.isBlank(business, "业务必传!");
        SettlementVerifyUtil.isNull(commodityTotalAmount, "商品合计金额必传!");
        SettlementVerifyUtil.isBlank(orderNumber, "订单号必传!");
        SettlementVerifyUtil.isNull(orderTime,  "下单时间必传!");
        SettlementVerifyUtil.isNull(orderPaidAmount, "订单实付金额必传!");
    }

}
