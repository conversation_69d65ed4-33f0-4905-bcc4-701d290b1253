package com.holderzone.member.common.enums.growth;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 成长值（积分） 变动类型
 * @date 2021/11/23
 */
public enum SumValueChangeEnum {

    INITIAL(1, "初始%s"),

    SYSTEM(2, "系统调整"),

    INVALIDATION(3, "%s失效"),

    REFUND(4, "消费退款"),

    TASK(5, "%s任务"),

    BAG(6, "升级礼包"),

    GIVE_GROWTH_VALUE(7, "赠送%s"),

    INTEGRAL_DISCOUNTING(8, "积分抵现"),

    PAY_UPGRADE(9, "付费升级"),

    RENEW(10, "续费"),

    PAY_EXPIRE(11, "付费过期"),

    RECHARGE_GIFT(12,"充值赠送"),

    RECHARGE_REFUND(21,"充值退款"),

    REDEEM_CODE(22,"兑换码"),

    MARKET_POS(23, "零售云-POS"),

    CONSUME(24, "积分兑换"),

    CERTIFIED(25, "认证有礼"),
    ;

    /**
     * 编号
     */
    private int code;

    /**
     * 名称
     */
    private String des;

    SumValueChangeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static String getNameByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (SumValueChangeEnum type : SumValueChangeEnum.values()) {
            if (type.code == code) {
                return type.getDes();
            }
        }
        return "";
    }

}
