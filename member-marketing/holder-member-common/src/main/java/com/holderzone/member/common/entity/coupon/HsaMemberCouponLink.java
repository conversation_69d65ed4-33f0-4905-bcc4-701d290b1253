package com.holderzone.member.common.entity.coupon;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponShowStateEnum;
import com.holderzone.member.common.vo.coupon.ResponseCouponCommodityVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponStoreVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class HsaMemberCouponLink extends HsaBaseEntity implements Serializable {

    private static final long serialVersionUID = 5811513500791194759L;
    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业
     */
    private String enterpriseGuid;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 会员名称
     */
    private String userName;

    /**
     * memberGuid
     */
    private String memberGuid;

    /**
     * memberPhone
     */
    private String memberPhone;

    /**
     * orderGuid
     */
    private String orderGuid;

    /**
     * 发放渠道
     */
    private Integer source;

    /**
     * 发券方式类型
     *
     * @see com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum
     */
    private Integer couponPackageType;

    /**
     * 券包编码
     */
    private String couponPackageCode;

    /**
     * 劵包类型对应的：活动名称
     * activityName
     */
    private String couponPackageName;

    /**
     * 门店Guid
     */
    private Integer storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 发放状态
     *
     * @see CouponMemberStateEnum
     */
    private Integer state;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reachTime;

    /**
     * 发放券码
     */
    private String code;


    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 优惠券类型
     *
     * @see com.holderzone.member.common.enums.coupon.CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 可兑换次数 (商品券存在)
     */
    private Integer exchangeTimes;

    /**
     * 兑换次数限制
     */
    private Integer exchangeLimit;

    /**
     * 使用门槛
     * 0 无门槛
     * 1 订单满多少可用
     */
    private Integer thresholdType;

    /**
     * 满足金额
     */
    private BigDecimal thresholdAmount;

    /**
     * 优惠力度
     */
    private BigDecimal discountAmount;

    /**
     * 活动备注
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 优惠券有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveStartTime;

    /**
     * 优惠券有效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveEndTime;

    /**
     * 用券时段限制 0：不限制 1：限制
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyDateLimited;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyTimeLimitedType;

    /**
     * 限制时段限制类型json
     * @see CouponTimeJson
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyTimeLimitedJson;

    /**
     * 适用场景 0:全部业务 1：部分业务
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    private Integer applyBusiness;

    /**
     * 适用场景json
     */
    private String applyBusinessJson;

    /**
     * 适用终端 0:全部终端 1：部分终端
     */
    private Integer applyTerminal;

    /**
     * 适用终端json
     */
    private String applyTerminalJson;

    /**
     * 打标标签guid
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyLabelGuidJson;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
    private Integer applicableAllStore;

    /**
     * 适用门店集合
     * @see ResponseCouponStoreVO
     */
    private String applicableAllStoreJson;

    /**
     * 0：全部商品适用 1：部分商品适用 2 不适用商品
     * @see com.holderzone.member.common.enums.ApplyCommodityEnum
     */
    private Integer applyCommodity;

    /**
     * 商品集合
     * @see ResponseCouponCommodityVO
     */
    private String applyCommodityJson;

    /**
     * 是否过期提醒
     *
     * @return
     */
    private Integer isExpireRemind;

    /**
     * 优惠金额上限
     * null/0 表示不限制
     */
    private BigDecimal discountAmountLimit;

    /**
     * 单笔订单限用数量
     * 单笔订单最多使用{singleOrderUsedLimit}张此优惠券
     * 当前优惠券限用数量需 ≤ 结算规则限制数量
     */
    private Integer singleOrderUsedLimit;

    /**
     * 共享互斥关系 0互斥 1共享
     */
    private Integer shareRelation;

    /**
     * 业务字段
     */
    private String dtlGuid;

    /**
     * 管理后台展示状态
     *
     * @return
     */
    public Integer getState() {
        //未过期看时间
        if (Objects.isNull(this.state) || Objects.equals(CouponMemberStateEnum.UN_EXPIRE.getCode(), this.state)) {
            //优惠劵过期
            return Optional.ofNullable(couponEffectiveEndTime).map(e ->
                            e.isBefore(LocalDateTime.now())
                                    ? CouponMemberStateEnum.EXPIRE.getCode()
                                    : CouponMemberStateEnum.UN_EXPIRE.getCode())
                    .orElse(state);
        }
        return state;

    }

    /**
     * 用户端展示状态
     *
     * @return
     */
    public Integer getShowState() {
        //未过期看时间
        if (Objects.isNull(this.state) || Objects.equals(CouponMemberStateEnum.UN_EXPIRE.getCode(), this.state)) {
            final LocalDateTime now = LocalDateTime.now();
            //未生效
            if (couponEffectiveStartTime.isAfter(now)) {
                return CouponShowStateEnum.INVALID.getCode();
            }
            //已过期
            if (couponEffectiveEndTime.isBefore(now)) {
                return CouponShowStateEnum.EXPIRE.getCode();
            }
        }
        return state;
    }

}
