package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.activity.AlipayPassInstanceRelationDTO;
import com.holderzone.member.common.dto.activity.AlipayPassTemplateRelationDTO;
import com.holderzone.member.common.dto.activity.MemberBaseActivityDTO;
import com.holderzone.member.common.dto.activity.VolumeInfoUpdateDTO;
import com.holderzone.member.common.dto.ali.AliSynAuthDTO;
import com.holderzone.member.common.dto.ali.AliSynMemberDTO;
import com.holderzone.member.common.dto.base.ResponseMemberUploadExcel;
import com.holderzone.member.common.dto.card.ResponseCardInfoDTO;
import com.holderzone.member.common.dto.gift.QueryMerchantStoreDTO;
import com.holderzone.member.common.dto.gift.ResponseMainCardLevelDTO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.order.OrderDTO;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.order.StoreAndTableDTO;
import com.holderzone.member.common.dto.order.aggregation.OrderGuidsDTO;
import com.holderzone.member.common.dto.order.aggregation.StoreOrderDTO;
import com.holderzone.member.common.dto.order.aggregation.StoreOrderItemDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.qo.activity.MemberBaseActivityQO;
import com.holderzone.member.common.qo.activity.RequestLabelDTO;
import com.holderzone.member.common.qo.certificate.VolumeInfoQO;
import com.holderzone.member.common.qo.gift.QueryGiftStoreQO;
import com.holderzone.member.common.qo.gift.RequestQueryOperationMemberQO;
import com.holderzone.member.common.qo.growth.RequestCertifiedGrowthValue;
import com.holderzone.member.common.qo.member.RequestManualLabel;
import com.holderzone.member.common.qo.member.RequestOperationLabel;
import com.holderzone.member.common.qo.member.ResponseMemberInfo;
import com.holderzone.member.common.qo.member.ResponseOperationMemberInfo;
import com.holderzone.member.common.qo.saas.RequestMemberInfoVolumeQuery;
import com.holderzone.member.common.vo.activity.ResponseGiftVolumeVO;
import com.holderzone.member.common.vo.base.ResponseOperationLabelList;
import com.holderzone.member.common.vo.certificate.RequestMemberInfoVolumeSave;
import com.holderzone.member.common.vo.certificate.RequestVolumeGiftSend;
import com.holderzone.member.common.vo.certificate.ResponseAppletsMemberInfo;
import com.holderzone.member.common.vo.certificate.VolumeInfoVO;
import com.holderzone.member.common.vo.feign.*;
import com.holderzone.member.common.vo.saas.ResponseVolumeInfo;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 老门店(老会员)管理
 * <p>
 * memberMerchant、member-wechat 需要包装
 *
 * <AUTHOR>
 * @date 2022/2/11
 **/
@FeignClient(name = FilterConstant.FEIGN_SAAS, fallbackFactory = SaasStoreFeign.ServiceFallBack.class, url = "${feign.saas}")
public interface SaasStoreFeign {


    @ApiOperation("精准撤销赠送的优惠券")
    @DeleteMapping("/gateway/memberMerchant/hsm-member-info-volume/platform-withdraw")
    FeignModel<Long> withdrawMemberInfoVolumeBatch(@RequestParam(value = "batchNum") String batchNum);


    /**
     * 消费有礼发券
     *
     * @param request RequestMemberInfoVolumeSave
     * @return 保存的会员持券guid集合
     */
    @ApiOperation("消费有礼发券")
    @PostMapping("/gateway/memberMerchant/hsm-member-info-volume/gift/batch_send_volume")
    FeignModel<ResponseGiftVolumeVO> consumptionGiftSendVolume(@RequestBody RequestVolumeGiftSend request);


    /**
     * 认证有礼会员成长值调整
     *
     * @param request request model
     * @return Boolean
     */
    @ApiOperation("认证有礼会员成长值调整")
    @PostMapping("/gateway/memberMerchant/hsa-member/certifiedGrowthAdjust")
    FeignModel<Boolean> certifiedGrowthAdjust(@RequestBody @Validated RequestCertifiedGrowthValue request);

    /**
     * 查询运营主体
     *
     * @return
     */
    @GetMapping(value = "/gateway/memberMerchant/member-store/querySubjects")
    FeignModel<List<OperSubjectInfo>> queryOperatingSubject();

    /**
     * 查询用户信息
     *
     * @return
     */
    @PostMapping(value = "/gateway/merchant/user/get_user_base_info")
    FeignModel<HeaderUserInfo> queryUserInformation();

    /**
     * 同步会员信息
     *
     * @param aliSynMemberDTO aliSynMemberDTO
     */
    @PostMapping(value = "/gateway/memberMerchant/platform_member/judge_add")
    FeignModel<Boolean> synSaasMember(@RequestBody AliSynMemberDTO aliSynMemberDTO);

    /**
     * 同步会员授权信息
     *
     * @param aliSynMemberDTO aliSynMemberDTO
     */
    @PostMapping(value = "/gateway/merchant/takeout/notify_alipay_auth")
    void synSaasAuth(@RequestBody AliSynAuthDTO aliSynMemberDTO);

    /**
     * 标签查询列表
     *
     * @param
     * @return
     */
    @ApiOperation("标签查询列表")
    @PostMapping("/gateway/memberMerchant/hsa-LabelSetting/getAutomaticLabelList")
    FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationLabelList>> getAutomaticLabelList(@RequestBody RequestLabelDTO requestLabelDTO);

    @ApiOperation("新增/编辑自动标签")
    @PostMapping("/gateway/memberMerchant/hsa-LabelSetting/saveOrUpdateAutomaticLabel")
    void saveOrUpdateAutomaticLabel(@RequestBody RequestOperationLabel req);

    @ApiOperation("会员导入，传递excel文件地址")
    @GetMapping(value = "/gateway/memberMerchant/hsa-member/memberUploadExcelUrl", produces = "application/json;charset=utf-8")
    ResponseMemberUploadExcel memberUploadExcelUrl(@RequestParam("fileUrl") String fileUrl);

    /**
     * 会员导入模板下载地址
     *
     * @return 操作结果
     */
    @ApiOperation("会员导入模板下载地址，返回excel文件地址")
    @GetMapping(value = "/gateway/memberMerchant/hsa-member/downloadExcelUrl", produces = "application/json;charset=utf-8")
    FeignModel<String> downloadExcelUrl();

    @ApiOperation("会员批量打手动标签")
    @PostMapping("/gateway/memberMerchant/hsa-LabelSetting/batchMemberManualLabel")
    void batchMemberManualLabel(@RequestBody RequestManualLabel req);

    @ApiOperation("用户手动标签批量删除")
    @GetMapping("/gateway/memberMerchant/hsa-LabelSetting/delete-user-label-batch")
    void deleteLabelBatch(@RequestParam(value = "memberGuid") String memberGuid, @RequestParam(value = "labelGuid") List<String> labelGuid);

    @ApiOperation("根据会员电话查询会员信息")
    @GetMapping("/gateway/memberMerchant/hsa-member/getMemberInfoByPhone")
    FeignModel<ResponseOperationMemberInfo> getMemberInfoByPhone(@RequestParam("phone") String phone);

    @GetMapping("/gateway/memberMerchant/hsa-member/getMemberGuidByPhoneSubjectGuid")
    FeignModel<ResponseOperationMemberInfo> getMemberInfoByPhone(@RequestParam("phone") String phone, @RequestParam("operSubjectGuid") String operSubjectGuid);

    @GetMapping("/gateway/member-wechat/hsa-member/getMemberInfoByOpenIdAndPhone")
    FeignModel<ResponseMemberInfo> getMemberInfoByOpenIdAndPhone(@RequestParam("openId") String openId,
                                                                 @RequestParam("phone") String phone,
                                                                 @RequestParam("operSubjectGuid") String operSubjectGuid);

    @ApiOperation(value = "查询优惠券列表")
    @GetMapping(value = "/gateway/memberMerchant/hsm-volume-info/queryList")
    FeignModel<Page<VolumeInfoVO>> queryCouponList(@RequestParam("volumeState") Integer volumeState,
                                                   @RequestParam("currentPage") Integer currentPage,
                                                   @RequestParam("pageSize") Integer pageSize);


    @ApiOperation(value = "查询分页可用优惠券列表", notes = "查询分页可用优惠券列表")
    @PostMapping(value = "/gateway/memberMerchant/hsm-volume-info/query_usable_volume_page", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    FeignModel<Page<VolumeInfoVO>> queryUsableVolumePage(@RequestBody VolumeInfoQO volumeInfoQO);

    @ApiOperation(value = "查询分页可用优惠券guid列表", notes = "查询分页可用优惠券guid列表")
    @PostMapping(value = "/gateway/memberMerchant/hsm-volume-info/query_usable_volume_guid", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    FeignModel<List<String>> queryUsableVolumeGuidList(@RequestBody VolumeInfoQO volumeInfoQO);

    @ApiOperation(value = "查询可用优惠券列表", notes = "查询优惠券列表")
    @PostMapping(value = "/gateway/memberMerchant/hsm-volume-info/query_usable_volume_list", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    FeignModel<List<VolumeInfoVO>> queryUsableVolumeList(@RequestBody VolumeInfoQO volumeInfoQO);

    @ApiOperation(value = "根据guid列表查询优惠券", notes = "根据guid列表查询优惠券")
    @PostMapping(value = "/gateway/memberMerchant/hsm-volume-info/query_by_guid_List", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    FeignModel<List<VolumeInfoUpdateDTO>> queryByGuidList(@RequestBody SingleDataDTO singleDataDTO);

    @ApiOperation("支付宝卡包模板关联")
    @PostMapping("/gateway/memberMerchant/hsm-volume-info/alipay_pass_template_relation")
    FeignModel<Void> alipayPassTemplateRelation(@RequestBody AlipayPassTemplateRelationDTO request);

    @ApiOperation("后台同时给多个用户赠送多个优惠券")
    @PostMapping("/gateway/memberMerchant/hsm-member-info-volume/platform-save")
    FeignModel<List<String>> precisionSendVolume(@RequestBody @Validated RequestMemberInfoVolumeSave request);

    @ApiOperation("支付宝卡包发券关联")
    @PostMapping("/gateway/memberMerchant/hsm-member-info-volume/alipay_pass_instance_relation")
    FeignModel<Void> alipayPassInstanceRelation(@RequestBody AlipayPassInstanceRelationDTO request);

    @ApiOperation("外部批量发券")
    @PostMapping("/gateway/memberMerchant/hsm-member-info-volume/batch_send_volume")
    FeignModel<List<VolumeInfoUpdateDTO>> batchSendVolume(@RequestBody @Validated RequestMemberInfoVolumeSave request);

    /**
     * 通过userId获取会员基础信息
     */
    @GetMapping(value = "/gateway/memberMerchant/applets-member-info/get_by_user_id", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("通过userId获取会员基础信息")
    FeignModel<ResponseAppletsMemberInfo> getAppletsMemberInfoByUserId(@RequestParam(value = "userId") String userId);


    @ApiOperation(value = "根据持券guid查询优惠券列表")
    @PostMapping(value = "/gateway/memberMerchant/hsm-member-info-volume/query_volume_list_by_member_volume_guid",
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    FeignModel<List<VolumeInfoUpdateDTO>> queryVolumeListByMemberVolumeGuid(@RequestBody VolumeInfoQO volumeInfoQO);

    /**
     * 查询满足的 url* 权限
     *
     * @return
     */
    @PostMapping(value = "/gateway/merchant/menu/get_page_Url_by_menu/v2")
    FeignModel<List<SassMenuVo>> getPageUrlByMenu(@RequestBody Map<String, String> data);

    @PostMapping(value = "/gateway/merchant/store/query_by_condition")
    FeignModel<Page<StoreDTO>> queryStore(@RequestBody OldStoreQO oldStoreQO);

    @PostMapping(value = "/gateway/merchant/store/query_by_condition_no_page")
    FeignModel<List<StoreDTO>> queryStoreNoPage(@RequestBody OldStoreQO oldStoreQO);

    @PostMapping(value = "/gateway/merchant/table/web/query")
    FeignModel<List<TableBasicDTO>> queryTable(@RequestBody OldTableQO oldTableQO);

    @RequestMapping(value = "/gateway/memberMerchant/member-store/queryBySubject", method = RequestMethod.GET)
    FeignModel<List<ResponseStoreInfo>> queryStoresBySubject(@RequestParam("keywords") String keywords, @RequestParam(name = "needAuthority") Boolean needAuthority);

    /**
     * 订单状态 (-1：未查询到订单 1：未结账， 2：已结账， 3：已退款，4：已作废)
     *
     * @param orderGuid
     * @return
     */
    @RequestMapping(value = "/gateway/app/dine_in_bill/get_order_state_by_guid", method = RequestMethod.GET)
    Integer getOrderStateByGuid(@RequestParam("orderGuid") String orderGuid);

    @RequestMapping(value = "/gateway/app/dine_in_bill/get_order_fee_by_guid", method = RequestMethod.GET)
    BigDecimal getOrderFeeByGuid(@RequestParam("orderGuid") String orderGuid);

    @RequestMapping(value = "/gateway/app/hsa-member/checkMemberInfo", method = RequestMethod.GET)
    FeignModel<Integer> checkMemberInfo(@RequestParam("memberInfoGuid") String memberInfoGuid);

    @PostMapping("/gateway/app/dine_in_order/find_by_order_guid")
    FeignModel<OrderDTO> findByOrderGuid(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/gateway/app/dine_in_order/add/version")
    FeignModel<Boolean> addVersion(@RequestBody String orderGuid);

    /**
     * 根据门店列表查询桌台Guid列表
     *
     * @param singleDataDTO datas必传，门店guid
     * @return 门店和桌台的guid
     */
    @PostMapping("/gateway/app/table/list_table_by_store_guid")
    List<StoreAndTableDTO> listTableByStoreGuid(@RequestBody SingleDataDTO singleDataDTO);

    @GetMapping("/gateway/app/hsm_member/query_name_by_phone")
    FeignModel<String> queryNameByPhone(@RequestParam("phoneNum") String phoneNum, @RequestHeader("source") Integer source);

    @PostMapping("/gateway/app/order_detail/get_order_items")
    List<StoreOrderItemDTO> getStoreOrderItems(@RequestBody OrderGuidsDTO orderGuidsDTO);

    @PostMapping("/gateway/app/order_detail/find_by_wx_order_guid")
    StoreOrderDTO getStoreOrderDetail(@RequestBody OrderGuidsDTO orderGuidsDTO);

    /**
     * 查询会员 优惠券列表
     */
    @PostMapping(value = "/gateway/member-wechat/hsa-volume/queryByMember")
    FeignModel<Page<ResponseVolumeInfo>> queryCouponListByMember(@RequestBody RequestMemberInfoVolumeQuery query);

    /**
     * 查询会员优惠券适用商品和适用门店
     */
    @GetMapping(value = "/gateway/member-wechat/hsa-volume/queryUseConditions")
    FeignModel<ResponseVolumeInfo> queryCouponUseConditions(@RequestParam("volumeInfoGuid") String volumeInfoGuid);

    /**
     * 查询会员未使用优惠券总数
     */
    @PostMapping(value = "/gateway/member-wechat/hsa-volume/queryUnUseCountByMember")
    FeignModel<Long> queryCouponUnUseCountByMember(@RequestBody RequestMemberInfoVolumeQuery query);

    @ApiOperation("条件查询门店")
    @PostMapping("/gateway/merchant/store/query_by_condition")
    MerchantModel<QueryMerchantStoreDTO> queryStoreByCondition(@RequestBody QueryGiftStoreQO req);

    @ApiOperation("查询主卡等级")
    @GetMapping("/gateway/memberMerchant/hsa-member/getMainCardLevelList")
    FeignModel<List<ResponseMainCardLevelDTO>> getMainCardLevelList();

    @ApiOperation("根据简单或复杂条件查询顾客分页列表")
    @PostMapping("/gateway/memberMerchant/hsa-member/getOperationMemberInfoPage")
    FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationMemberInfo>> getOperationMemberInfoPage(@RequestBody RequestQueryOperationMemberQO request);


    /**
     * 查询公共数据
     *
     * @param re
     * @return
     */
    @PostMapping("/gateway/base/feign/queryMemberBase")
    MemberBaseActivityDTO queryMemberBase(@RequestBody MemberBaseActivityQO re);

    @ApiOperation("查询所有会员卡")
    @GetMapping("/gateway/memberMerchant/hsa-card/getCardList")
    FeignModel<List<ResponseCardInfoDTO>> getAllCardList();

    @ApiOperation("查询赚餐二维码参数")
    @GetMapping("/gateway/weixin/wx_mp/zhuancan/qr")
    String getZhuanCanQrCodeInfo(@RequestParam(value = "t") String t);

    /**
     * 通过运营主体查询品牌列表
     * 什么参数都不传=查询该用户有权限看到的所有品牌
     *
     * @param keywords      模糊查询
     * @param needAuthority 是否员工权限过滤
     * @return 品牌列表
     */
    @ApiOperation("通过运营主体查询品牌列表")
    @GetMapping("/gateway/memberMerchant/member-store/query_brands_by_subject")
    FeignModel<List<ResponseBrandInfo>> queryBrandsBySubject(@RequestParam(name = "keywords", required = false) String keywords,
                                                             @RequestParam(name = "needAuthority", defaultValue = "true") Boolean needAuthority);

    @ApiOperation(value = "查询品牌列表下的所有分类")
    @PostMapping("/gateway/merchant/type/query_type_by_brand/v2")
    List<TypeWebRespDTO> queryTypeByBrand(@RequestBody ItemStringListDTO query);

    /**
     * 查询品牌下所有商品
     */
    @ApiOperation(value = "查询品牌下所有商品")
    @PostMapping("/gateway/merchant/item/query_item_by_brand/v2")
    Page<ItemWebRespDTO> queryItemByBrand(@RequestBody ItemQueryReqDTO queryReqDTO);

    /**
     * 根据商品id查询商品信息
     */
    @ApiOperation(value = "根据商品id查询商品信息")
    @PostMapping("/gateway/merchant/item/query_item_by_guid")
    MerchantModel<List<ItemWebRespDTO>> queryItemByGuid(@RequestBody ItemStringListDTO query);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<SaasStoreFeign> {

        @Override
        public SaasStoreFeign create(Throwable throwable) {
            return new SaasStoreFeign() {
                @Override
                public FeignModel<Long> withdrawMemberInfoVolumeBatch(String batchNum) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "withdrawMemberInfoVolumeBatch", batchNum, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<ResponseGiftVolumeVO> consumptionGiftSendVolume(RequestVolumeGiftSend request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "consumptionGiftSendVolume", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Boolean> certifiedGrowthAdjust(RequestCertifiedGrowthValue request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "certifiedGrowthAdjust", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<OperSubjectInfo>> queryOperatingSubject() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryOperatingSubject", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<HeaderUserInfo> queryUserInformation() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUserInformation", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Boolean> synSaasMember(AliSynMemberDTO aliSynMemberDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "synSaasMember", aliSynMemberDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void synSaasAuth(AliSynAuthDTO aliSynMemberDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "synSaasAuth", aliSynMemberDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationLabelList>> getAutomaticLabelList(RequestLabelDTO requestLabelDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAutomaticLabelList", requestLabelDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveOrUpdateAutomaticLabel(RequestOperationLabel req) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveOrUpdateAutomaticLabel", req, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseMemberUploadExcel memberUploadExcelUrl(String fileUrl) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "memberUploadExcelUrl", fileUrl, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<String> downloadExcelUrl() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "downloadExcelUrl", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void batchMemberManualLabel(RequestManualLabel req) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchMemberManualLabel", req, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void deleteLabelBatch(String memberGuid, List<String> labelGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "deleteLabelBatch", memberGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<ResponseOperationMemberInfo> getMemberInfoByPhone(String phone) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMemberInfoByPhone", phone, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<ResponseOperationMemberInfo> getMemberInfoByPhone(String phone, String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMemberInfoByPhone", phone, operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<ResponseMemberInfo> getMemberInfoByOpenIdAndPhone(String openId, String phone, String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMemberInfoByOpenIdAndPhone", openId, phone,
                            operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Page<VolumeInfoVO>> queryCouponList(Integer volumeState, Integer currentPage, Integer pageSize) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryCouponList", volumeState,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Page<VolumeInfoVO>> queryUsableVolumePage(VolumeInfoQO volumeInfoQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUsableVolumePage", volumeInfoQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<String>> queryUsableVolumeGuidList(VolumeInfoQO volumeInfoQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUsableVolumeGuidList", volumeInfoQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<VolumeInfoVO>> queryUsableVolumeList(VolumeInfoQO volumeInfoQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUsableVolumeList", JacksonUtils.writeValueAsString(volumeInfoQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<VolumeInfoUpdateDTO>> queryByGuidList(SingleDataDTO singleDataDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryByGuidList", singleDataDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Void> alipayPassTemplateRelation(AlipayPassTemplateRelationDTO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "alipayPassTemplateRelation", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<String>> precisionSendVolume(RequestMemberInfoVolumeSave request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "precisionSendVolume", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Void> alipayPassInstanceRelation(AlipayPassInstanceRelationDTO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "alipayPassInstanceRelation", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<VolumeInfoUpdateDTO>> batchSendVolume(RequestMemberInfoVolumeSave request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchSendVolume", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<ResponseAppletsMemberInfo> getAppletsMemberInfoByUserId(String userId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAppletsMemberInfoByUserId", userId,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<VolumeInfoUpdateDTO>> queryVolumeListByMemberVolumeGuid(VolumeInfoQO volumeInfoQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryVolumeListByMemberVolumeGuid", JacksonUtils.writeValueAsString(volumeInfoQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<SassMenuVo>> getPageUrlByMenu(Map<String, String> data) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSourceByMenu", data, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Page<StoreDTO>> queryStore(OldStoreQO oldStoreQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStore", oldStoreQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<StoreDTO>> queryStoreNoPage(OldStoreQO oldStoreQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStoreNoPage", oldStoreQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<TableBasicDTO>> queryTable(OldTableQO oldTableQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryTable", oldTableQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<ResponseStoreInfo>> queryStoresBySubject(String keywords, Boolean needAuthority) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStoresBySubject", needAuthority, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<ResponseBrandInfo>> queryBrandsBySubject(String keywords, Boolean needAuthority) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryBrandsBySubject", keywords + "-" + needAuthority,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TypeWebRespDTO> queryTypeByBrand(ItemStringListDTO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryTypeByBrand", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<ItemWebRespDTO> queryItemByBrand(ItemQueryReqDTO queryReqDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryItemByBrand", JacksonUtils.writeValueAsString(queryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MerchantModel<List<ItemWebRespDTO>> queryItemByGuid(ItemStringListDTO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryItemByGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public Integer getOrderStateByGuid(String orderGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOrderStateByGuid", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BigDecimal getOrderFeeByGuid(String orderGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOrderFeeByGuid", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Integer> checkMemberInfo(String memberInfoGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "checkMemberInfo", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<OrderDTO> findByOrderGuid(SingleDataDTO singleDataDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findByOrderGuid", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Boolean> addVersion(String orderGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "addVersion", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<StoreAndTableDTO> listTableByStoreGuid(SingleDataDTO singleDataDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listTableByStoreGuid", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<String> queryNameByPhone(String phoneNum, Integer source) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryNameByPhone", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<StoreOrderItemDTO> getStoreOrderItems(OrderGuidsDTO orderGuidsDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreOrderItem", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public StoreOrderDTO getStoreOrderDetail(OrderGuidsDTO orderGuidsDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreOrderDetail", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Page<ResponseVolumeInfo>> queryCouponListByMember(RequestMemberInfoVolumeQuery query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryCouponListByMember", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<ResponseVolumeInfo> queryCouponUseConditions(String volumeInfoGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryCouponUseConditions", volumeInfoGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Long> queryCouponUnUseCountByMember(RequestMemberInfoVolumeQuery query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryCouponUnUseCountByMember", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MerchantModel<QueryMerchantStoreDTO> queryStoreByCondition(QueryGiftStoreQO req) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStoreByCondition", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<ResponseMainCardLevelDTO>> getMainCardLevelList() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMainCardLevelList", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationMemberInfo>> getOperationMemberInfoPage(RequestQueryOperationMemberQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOperationMemberInfoPage", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberBaseActivityDTO queryMemberBase(MemberBaseActivityQO re) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberBase", JacksonUtils.writeValueAsString(re),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<ResponseCardInfoDTO>> getAllCardList() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAllCardList",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String getZhuanCanQrCodeInfo(String t) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getZhuanCanQrCodeInfo", t,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
