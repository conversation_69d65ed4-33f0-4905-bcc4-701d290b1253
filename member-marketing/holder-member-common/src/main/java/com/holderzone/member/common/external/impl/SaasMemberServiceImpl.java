package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.assembler.CertifiedAssembler;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.MemberBaseActivityDTO;
import com.holderzone.member.common.dto.card.MemberCardExcelVO;
import com.holderzone.member.common.dto.card.ResponseCardInfoDTO;
import com.holderzone.member.common.dto.certificate.IslandUserDTO;
import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.excel.CardMemberUploadExcelError;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.excel.MemberUploadExcel;
import com.holderzone.member.common.dto.excel.MemberUploadExcelVO;
import com.holderzone.member.common.dto.gift.ResponseMainCardLevelDTO;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.member.TableBasicDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.base.ActivityBusinessTypeEnum;
import com.holderzone.member.common.enums.certificate.ApplyCertifiedResultEnum;
import com.holderzone.member.common.enums.certificate.VolumeStateEnum;
import com.holderzone.member.common.enums.exception.FollowActivityExceptionEnum;
import com.holderzone.member.common.enums.member.ConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.external.ExternalMemberService;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.feign.ZhuanCanFeign;
import com.holderzone.member.common.qo.activity.MemberBaseActivityQO;
import com.holderzone.member.common.qo.activity.RequestLabelBaseQO;
import com.holderzone.member.common.qo.activity.RequestLabelDTO;
import com.holderzone.member.common.qo.certificate.VolumeInfoQO;
import com.holderzone.member.common.qo.gift.RequestQueryMemberBaseQO;
import com.holderzone.member.common.qo.gift.RequestQueryOperationMemberQO;
import com.holderzone.member.common.qo.gift.RequestQueryVolumeBaseQO;
import com.holderzone.member.common.qo.growth.RequestCertifiedGrowthValue;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.util.excel.CardMemberUploadExcelUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.PlatFormUserDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;
import com.holderzone.member.common.vo.base.ResponseOperationLabelList;
import com.holderzone.member.common.vo.certificate.VolumeInfoVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.feign.OldTableQO;
import com.holderzone.member.common.vo.grade.HsaMemberGradeInfoVO;
import com.holderzone.member.common.vo.grade.QueryMemberGradeVO;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description 食堂门店
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SaasMemberServiceImpl implements ExternalMemberService {

    private final SaasStoreFeign saasStoreFeign;

    @Lazy
    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private FileOssService fileOssService;

    @Resource
    private ZhuanCanFeign zhuancanFeign;

    private static final Integer PAGE_SIZE = 9999;


    @Override
    public List<TableBasicDTO> queryTable(String storeGuid, List<String> tableGuidList) {
        try {
            //区域id传 -1 表示查询所有区域
            FeignModel<List<TableBasicDTO>> feignModel = saasStoreFeign.queryTable(new OldTableQO("-1", storeGuid, tableGuidList));
            List<TableBasicDTO> tableBasicDTOList = feignModel.getData();
            if (CollUtil.isEmpty(tableBasicDTOList)) {
                return Collections.emptyList();
            }
            return tableBasicDTOList.stream()
                    .sorted(Comparator.comparing(TableBasicDTO::getAreaName))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("查询桌台失败，门店guid====>{}", storeGuid);
            throw new MemberBaseException(FollowActivityExceptionEnum.QUERY_TABLE_ERROR);
        }
    }

    @Override
    public QueryMemberGradeVO queryGrowthValueTaskDetail(String roleType, Integer gradeType) {
        FeignModel<List<ResponseMainCardLevelDTO>> responseMainCardLevelDTOS = saasStoreFeign.getMainCardLevelList();
        log.info("老会员查询主卡等级返回结果==============>" + JSON.toJSONString(responseMainCardLevelDTOS));
        if (CollUtil.isEmpty(responseMainCardLevelDTOS.getData())) {
            return new QueryMemberGradeVO();
        }
        QueryMemberGradeVO queryMemberGradeVO = new QueryMemberGradeVO();
        List<HsaMemberGradeInfoVO> hsaMemberGradeInfoVOS = Lists.newArrayList();

        List<ResponseMainCardLevelDTO> responseMainCardLevelDTOSData = responseMainCardLevelDTOS.getData();
        for (ResponseMainCardLevelDTO responseMainCardLevelDTO : responseMainCardLevelDTOSData) {
            HsaMemberGradeInfoVO hsaMemberGradeInfoVO = new HsaMemberGradeInfoVO();
            hsaMemberGradeInfoVO.setName(responseMainCardLevelDTO.getLevelName())
                    .setGuid(responseMainCardLevelDTO.getCardLevelGuid());
            hsaMemberGradeInfoVOS.add(hsaMemberGradeInfoVO);
        }
        queryMemberGradeVO.setHsaMemberGradeInfoVOS(hsaMemberGradeInfoVOS);
        return queryMemberGradeVO;
    }

    @Override
    public List<ResponseOperationLabel> getLabelList(RequestLabelBaseQO requestLabelDTO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        if (SystemEnum.REPAST.getCode() != headerUserInfo.getSystem()) {
            return Collections.emptyList();
        }

        RequestLabelDTO dto = new RequestLabelDTO();

        dto.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        dto.setPageSize(NumberConstant.NUMBER_999999);
        dto.setPageIndex(NumberConstant.NUMBER_1);
        if (StringUtils.isNotBlank(requestLabelDTO.getLabelName())) {
            dto.setLabelName(requestLabelDTO.getLabelName());
        }
        FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationLabelList>> feignModel = saasStoreFeign.getAutomaticLabelList(dto);
        log.info("feignModel={}", JacksonUtils.writeValueAsString(feignModel));
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationLabelList> labelFeignModel = feignModel.getData();
        List<ResponseOperationLabelList> labelList = labelFeignModel.getRecords();
        if (CollectionUtils.isEmpty(labelList)) {
            return Lists.newArrayList();
        }
        List<ResponseOperationLabel> responseOperationLabelList = CertifiedAssembler.toResponseOperationLabelList(labelList);
        return responseOperationLabelList
                .stream()
                .sorted(Comparator.comparing(ResponseOperationLabel::getGmtCreate)
                        .reversed())
                .collect(Collectors.toList());
    }

    @Override
    public boolean saveAutomaticLabel(RequestLabelSetting req) {
        RequestOperationLabel requestOperationLabel = new RequestOperationLabel();
        requestOperationLabel.getRequestBaseInfo().setSetBaseInfo(false);
        requestOperationLabel.getRequestConsumptionInfo().setSetConsumption(false);
        requestOperationLabel.getRequestEquityCardInfo().setSetEquityCardInfo(false);
        requestOperationLabel.getRequestIntegraInfo().setSetIntegral(false);
        requestOperationLabel.getRequestMemberInfo().setSetCardInfo(false);
        requestOperationLabel.getRequestRechargeInfo().setSetRecharge(false);
        requestOperationLabel.getRequestRegisterInfo().setSetRegisterInfo(false);
        requestOperationLabel.setRequestLabelSetting(req);
        log.info("保存标签：{}", JSON.toJSONString(requestOperationLabel));
        saasStoreFeign.saveOrUpdateAutomaticLabel(requestOperationLabel);
        return true;
    }

    @Override
    public void addMemberInfoLabel(AddMemberLabelCorrelationQO addMemberLabelCorrelationQO) {
        RequestManualLabel requestManualLabel = new RequestManualLabel();
        String memberInfoGuidArray = StringUtils.join(addMemberLabelCorrelationQO.getMemberInfoGuid(), ",");
        requestManualLabel.setMemberInfoGuidArray(memberInfoGuidArray);
        String labelSettingGuid = StringUtils.join(addMemberLabelCorrelationQO.getLabelGuid(), ",");
        requestManualLabel.setLabelSettingGuid(labelSettingGuid);
        log.info("标签关联开始：{}", JSON.toJSONString(requestManualLabel));
        saasStoreFeign.batchMemberManualLabel(requestManualLabel);
    }

    @Override
    public void removeMemberInfoLabel(RemoveMemberLabelCorrelationQO removeMemberLabelCorrelationQO) {
        saasStoreFeign.deleteLabelBatch(removeMemberLabelCorrelationQO.getMemberInfoGuid().get(0),
                removeMemberLabelCorrelationQO.getLabelGuid());
    }

    @Override
    public MemberBasicInfoVO getMemberInfo(MemberQueryDTO queryDTO) {
        if (Boolean.TRUE.equals(queryDTO.getQueryZhuanCanFlag())) {
            IslandUserDTO islandUserDTO = queryZhuanCanMember(queryDTO);
            if (Objects.isNull(islandUserDTO)) {
                return null;
            }
            MemberBasicInfoVO memberBasicInfoVO = new MemberBasicInfoVO();
            memberBasicInfoVO.setGuid(String.valueOf(islandUserDTO.getId()));
            return memberBasicInfoVO;
        }

        FeignModel<ResponseOperationMemberInfo> memberInfoByPhoneModel = saasStoreFeign.getMemberInfoByPhone(queryDTO.getPhoneNum());
        log.info("memberInfoByPhoneModel={}", JacksonUtils.writeValueAsString(memberInfoByPhoneModel));
        ResponseOperationMemberInfo responseOperationMemberInfo = memberInfoByPhoneModel.getData();
        if (Objects.isNull(responseOperationMemberInfo)) {
            log.info("会员不存在：{}", JSON.toJSONString(queryDTO));
            return null;
        }
        MemberBasicInfoVO memberBasicInfoVO = new MemberBasicInfoVO();
        memberBasicInfoVO.setGuid(responseOperationMemberInfo.getGuid())
                .setPhoneNum(responseOperationMemberInfo.getPhoneNum())
                .setUserName(responseOperationMemberInfo.getUserName())
                .setSex(responseOperationMemberInfo.getSex());
        return memberBasicInfoVO;
    }

    @Override
    public String getMemberName(MemberQueryDTO queryDTO) {
        FeignModel<String> feignModel = saasStoreFeign.queryNameByPhone(queryDTO.getPhoneNum(),
                SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode());
        if (feignModel.getCode() != 0 || Objects.isNull(feignModel.getData())) {
            return null;
        }
        return feignModel.getData();
    }


    /**
     * 查询赚餐会员
     */
    private IslandUserDTO queryZhuanCanMember(MemberQueryDTO queryDTO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        ResponseEntity<PlatFormUserDTO> platFormThirdNo = zhuancanFeign.getPlatFormThirdNo(headerUserInfo.getEnterpriseGuid(),
                headerUserInfo.getOperSubjectGuid());
        if (Objects.isNull(platFormThirdNo.getBody())) {
            log.info("未查询到平台数据：{}", JSON.toJSONString(headerUserInfo));
            throw new MemberBaseException("未查询到平台数据");
        }
        Long platFormId = platFormThirdNo.getBody().getId();
        ResponseEntity<List<IslandUserDTO>> landUserDTO = zhuancanFeign.getIslandUserByPlatformIdAndLikephone(platFormId,
                queryDTO.getPhoneNum());
        log.info("landUserDTO={}", JSON.toJSONString(landUserDTO));
        if (Objects.isNull(landUserDTO.getBody()) || CollectionUtils.isEmpty(landUserDTO.getBody())) {
            log.info("当前会员未注册，{}", queryDTO.getPhoneNum());
            return null;
        }
        return landUserDTO.getBody().get(0);
    }

    @Override
    public Page<IslandCouponDTO> queryVolumeList(RequestQueryVolumeBaseQO request) {
        Page<IslandCouponDTO> page = new Page<>();
        if (Boolean.TRUE.equals(request.getQueryZhuanCanFlag())) {
            // 赚餐优惠券
            List<IslandCouponDTO> islandCouponDTOS = queryZhuanCanVolumeList(request);
            if (CollectionUtils.isEmpty(islandCouponDTOS)) {
                return page;
            }
            page.setData(islandCouponDTOS);
            page.setCurrentPage(request.getCurrentPage());
            page.setTotalCount(islandCouponDTOS.size());
            page.setPageSize(request.getPageSize());
            return page;
        }
        // 门店优惠券
        VolumeInfoQO volumeInfoQO = new VolumeInfoQO();
        BeanUtils.copyProperties(request, volumeInfoQO);
        volumeInfoQO.setVolumeState(VolumeStateEnum.NOT_EXPIRED.getCode());
        volumeInfoQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        volumeInfoQO.setPageSize(request.getPageSize());
        volumeInfoQO.setCurrentPage(request.getCurrentPage());
        FeignModel<Page<VolumeInfoVO>> pageFeignModel = saasStoreFeign.queryUsableVolumePage(volumeInfoQO);
        log.info("分页查询优惠券返回结果==============>" + JSON.toJSONString(pageFeignModel));
        if (Objects.isNull(pageFeignModel.getData())) {
            return page;
        }
        List<VolumeInfoVO> infoVOList = pageFeignModel.getData().getData();
        page.setData(CertifiedAssembler.toIslandCouponDTO(infoVOList));
        page.setCurrentPage(pageFeignModel.getData().getCurrentPage());
        page.setTotalCount(pageFeignModel.getData().getTotalCount());
        page.setPageSize(pageFeignModel.getData().getPageSize());
        return page;
    }

    @Override
    public void sendMemberCoupon(SendCertifiedStampsDTO sendCertifiedStampsDTO) {
        zhuancanFeign.certifiedActivityCouponSend(sendCertifiedStampsDTO);
    }


    /**
     * 查询赚餐优惠券
     */
    private List<IslandCouponDTO> queryZhuanCanVolumeList(RequestQueryVolumeBaseQO request) {
        List<String> couponId = request.getVolumeGuidList();
        if (Objects.nonNull(couponId) && !CollectionUtils.isEmpty(couponId)) {
            List<Long> couponLongId = couponId.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            return zhuancanFeign.getIslandCouponDetailsNew(couponLongId).getBody();
        }
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("headerUserInfo：{}", JSON.toJSONString(headerUserInfo));
        ResponseEntity<PlatFormUserDTO> userDTOResponseEntity = zhuancanFeign.getPlatFormThirdNo(headerUserInfo.getEnterpriseGuid(),
                headerUserInfo.getOperSubjectGuid());
        if (Objects.isNull(userDTOResponseEntity.getBody())) {
            log.info("未查询到平台数据");
            return Lists.newArrayList();
        }
        Long platFormId = userDTOResponseEntity.getBody().getId();
        ResponseEntity<List<IslandCouponDTO>> listResponseEntity = zhuancanFeign.getIslandCouponsList(
                Long.valueOf(request.getPageSize()),
                platFormId,
                null,
                null,
                Boolean.FALSE);
        if (Objects.isNull(listResponseEntity.getBody())) {
            log.info("此平台未查询到优惠券：{}", JSON.toJSONString(platFormId));
            return Lists.newArrayList();
        }
        return listResponseEntity.getBody()
                .stream()
                .sorted(Comparator.comparing(IslandCouponDTO::getUpdateDate)
                        .reversed())
                .collect(Collectors.toList());
    }

    @Override
    public Page<ResponseOperationMemberInfo> getOperationMemberInfoPage(RequestQueryMemberBaseQO requestQueryMemberBaseQO) {
        Page<ResponseOperationMemberInfo> page = new Page<>();
        RequestQueryOperationMemberQO request = new RequestQueryOperationMemberQO();
        BeanUtils.copyProperties(requestQueryMemberBaseQO, request);
        request.setMemberAccountPhoneOrName(requestQueryMemberBaseQO.getKeyword());
        request.setPageIndex(requestQueryMemberBaseQO.getCurrentPage());
        request.setPageSize(requestQueryMemberBaseQO.getPageSize());
        if (Objects.nonNull(requestQueryMemberBaseQO.getStartAge())) {
            LocalDate startBirthday = LocalDate.now().minusYears(requestQueryMemberBaseQO.getStartAge());
            request.setStartBirthday(startBirthday);

        }

        if (Objects.nonNull(requestQueryMemberBaseQO.getEndAge())) {
            LocalDate endBirthday = LocalDate.now().minusYears(requestQueryMemberBaseQO.getEndAge());
            request.setEndBirthday(endBirthday);

        }
        FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationMemberInfo>> operationMemberInfoPage =
                saasStoreFeign.getOperationMemberInfoPage(request);
        log.info("老会员分页查询返回结果==============>" + JSON.toJSONString(operationMemberInfoPage));
        if (operationMemberInfoPage.getCode() != 0) {
            throw new MemberMarketingException(operationMemberInfoPage.getCode(), operationMemberInfoPage.getMessage());
        }
        List<ResponseOperationMemberInfo> responseOperationMemberInfoList = operationMemberInfoPage.getData().getRecords();
        page.setData(responseOperationMemberInfoList);
        page.setCurrentPage(requestQueryMemberBaseQO.getCurrentPage());
        page.setTotalCount(operationMemberInfoPage.getData().getTotal());
        page.setPageSize(requestQueryMemberBaseQO.getPageSize());
        return page;
    }

    @Override
    public void updateGrowthValue(RequestMemberGrowthValue request) {
        RequestCertifiedGrowthValue growthValue = new RequestCertifiedGrowthValue();
        growthValue.setGrowthValue(request.getGrowthValue());
        growthValue.setPhoneNumber(request.getPhoneNum());
        growthValue.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        growthValue.setCertifiedName(request.getRemark());
        log.info("认证有礼调整成长值请求参数：{}", JSON.toJSONString(growthValue));
        FeignModel<Boolean> feignModel = saasStoreFeign.certifiedGrowthAdjust(growthValue);
        log.info("认证有礼调整成长值结果：{}", feignModel.getData());
    }

    @Override
    public MemberUploadExcelVO memberUploadExcelUrl(String fileUrl) {
        MemberUploadExcelVO excelVO = new MemberUploadExcelVO();
        List<MemberUploadExcel> memberUploadExcels = CardMemberUploadExcelUtil.read(fileUrl);
        // 条件验证(默认不读取第一条模板数据)
        if (CollUtil.isEmpty(memberUploadExcels) || memberUploadExcels.size() == 1) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_EMPTY_UPLOAD_EXCEL);
        }
        if (org.springframework.util.StringUtils.isEmpty(memberUploadExcels.get(memberUploadExcels.size() - 1).getPhoneNum())) {
            memberUploadExcels.remove(memberUploadExcels.size() - 1);
        }
        if (memberUploadExcels.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<String> phoneNumbers = memberUploadExcels.stream().map(MemberUploadExcel::getPhoneNum).collect(Collectors.toList());

        //会员
        Map<String, ResponseOperationMemberInfo> hsaOperationMemberInfoMap = null;
        RequestQueryOperationMemberQO queryOperationMemberQO = new RequestQueryOperationMemberQO();
        queryOperationMemberQO.setPageSize(PAGE_SIZE);
        queryOperationMemberQO.setPhoneNumberList(phoneNumbers);
        FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationMemberInfo>> operationMemberInfoPage = getPageFeignModel(queryOperationMemberQO);

        List<ResponseOperationMemberInfo> responseOperationMemberInfoList = operationMemberInfoPage.getData().getRecords();

        if (CollUtil.isNotEmpty(responseOperationMemberInfoList)) {
            hsaOperationMemberInfoMap = responseOperationMemberInfoList
                    .stream()
                    .collect(Collectors.toMap(ResponseOperationMemberInfo::getPhoneNum, Function.identity(), (entity1, entity2) -> entity1));
        }


        //返回会员数据
        List<MemberCardExcelVO> memberCardExcelVOS = Lists.newArrayList();
        //手机号重复校验
        Set<String> checkPhoneNumSet = new HashSet<>();
        //返回表单失败数据
        List<CardMemberUploadExcelError> cardMemberUploadExcelErrors = Lists.newArrayList();
        for (int i = 1, j = memberUploadExcels.size(); i < j; i++) {
            CardMemberUploadExcelError cardMemberUploadExcelError = checkUploadExcelUrl(memberUploadExcels.get(i), hsaOperationMemberInfoMap, checkPhoneNumSet, memberCardExcelVOS);
            if (ObjectUtil.isNotNull(cardMemberUploadExcelError)) {
                cardMemberUploadExcelError.setSerial(String.valueOf(cardMemberUploadExcelErrors.size() + 1));
                cardMemberUploadExcelErrors.add(cardMemberUploadExcelError);
                log.info("错误信息-->>>序号:" + (i + 1) + "\t错误原因" + cardMemberUploadExcelError.getErrorMessage());
            }
        }


        //上传至阿里oos
        LocalDateTime now = LocalDateTime.now();
        log.info("上传时间" + now);
        excelVO.setSuccess(Math.max((memberUploadExcels.size() - cardMemberUploadExcelErrors.size() - 1), 0));
        excelVO.setFail(cardMemberUploadExcelErrors.size());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formatDateTime = now.format(formatter);
        if (!cardMemberUploadExcelErrors.isEmpty()) {
            ExcelResult excelResult = com.aimilin.utils.BeanUtils.toResult(cardMemberUploadExcelErrors);
            byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
            try {
                FileDto fileDto = new FileDto();
                fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
                fileDto.setFileName(systemRoleHelper.getReplace("快速导入选择会员模板失败数据", ThreadLocalCache.getOperSubjectGuid()) + formatDateTime + "." + ExcelType.XLSX);
                String upload = fileOssService.upload(fileDto);
                String newUpload = upload.replace("http", "https");
                log.info("错误信息文件下载路径->>>>>{}", newUpload);
                excelVO.setFailUrl(newUpload);
                excelVO.setFail(cardMemberUploadExcelErrors.size());
            } catch (Exception e) {
                log.error("上传文件失败");
                excelVO.setFailUrl("上传错误信息失败");
            }
        }
        excelVO.setSuccess(memberCardExcelVOS.size());
        excelVO.setMemberCardExcelVOS(memberCardExcelVOS);
        return excelVO;
    }

    @Override
    public String downloadExcelUrl() {
        return null;
    }

    @Override
    public MemberBaseActivityDTO memberBaseDataInfo(MemberBaseActivityQO memberBaseActivityQO) {
        return null;
    }

    @Override
    public List<ResponseCardInfoDTO> getAllCardList() {
        FeignModel<List<ResponseCardInfoDTO>> allCardList = saasStoreFeign.getAllCardList();
        log.info("老会员查询所有卡返回结果==============>" + JSON.toJSONString(allCardList));
        if (CollUtil.isNotEmpty(allCardList.getData())) {
            return allCardList.getData();
        }
        return new ArrayList<>();
    }

    @Override
    public List<Integer> getBusinessScene() {
        List<Integer> businessScene = new ArrayList<>();
        ActivityBusinessTypeEnum[] businessTypeEnums = ActivityBusinessTypeEnum.values();
        for (ActivityBusinessTypeEnum systemEnum : businessTypeEnums) {
            businessScene.add(systemEnum.getCode());
        }
        return businessScene;
    }

    @Override
    public String getConsumptionOrderType(Integer orderType) {
        return ConsumptionOrderTypeEnum.getDesByCode(orderType);
    }

    private FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationMemberInfo>> getPageFeignModel(RequestQueryOperationMemberQO queryOperationMemberQO) {
        FeignModel<com.baomidou.mybatisplus.extension.plugins.pagination.Page<ResponseOperationMemberInfo>> operationMemberInfoPage =
                saasStoreFeign.getOperationMemberInfoPage(queryOperationMemberQO);
        log.info("老会员分页查询返回结果==============>" + JSON.toJSONString(operationMemberInfoPage));
        if (operationMemberInfoPage.getCode() != 0) {
            throw new MemberMarketingException(operationMemberInfoPage.getCode(), operationMemberInfoPage.getMessage());
        }
        return operationMemberInfoPage;
    }


    private CardMemberUploadExcelError checkUploadExcelUrl(MemberUploadExcel memberUploadExcel,
                                                           Map<String, ResponseOperationMemberInfo> responseOperationMemberInfoMap,
                                                           Set<String> checkPhoneNumSet,
                                                           List<MemberCardExcelVO> hsaMemberImportRecordErrorList) {
        CardMemberUploadExcelError error = new CardMemberUploadExcelError();
        String phoneNum = memberUploadExcel.getPhoneNum();
        StringBuilder stringBuilder = new StringBuilder();
        ResponseOperationMemberInfo info = null;
        info = checkMember(responseOperationMemberInfoMap, phoneNum, stringBuilder, info);

        checkPhoneNum(checkPhoneNumSet, phoneNum, stringBuilder);

        try {
            //导入失败
            if (org.springframework.util.StringUtils.hasText(stringBuilder.toString())) {
                error.setErrorMessage(stringBuilder.toString());
                error.setPhoneNum(phoneNum);
                error.setUserName(memberUploadExcel.getUserName());
                return error;
            } else {
                //导入成功
                MemberCardExcelVO vo = new MemberCardExcelVO();
                if (info != null) {
                    vo.setMemberAccount(info.getMemberAccount())
                            .setPhoneCountryCode(StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE)
                            .setMemberGuid(String.valueOf(info.getGuid()))
                            .setPhoneNum(phoneNum)
                            .setUserName(info.getUserName());
                }
                hsaMemberImportRecordErrorList.add(vo);
                return null;
            }
        } catch (Exception e) {
            log.error("导入失败==============", e);
            error.setErrorMessage("【导入失败】" + e.getMessage());
            return error;
        }

    }

    private static ResponseOperationMemberInfo checkMember(Map<String, ResponseOperationMemberInfo> hsaOperationMemberInfoMap, String phoneNum, StringBuilder stringBuilder, ResponseOperationMemberInfo info) {
        if (CollUtil.isNotEmpty(hsaOperationMemberInfoMap) && !hsaOperationMemberInfoMap.containsKey(phoneNum)) {
            stringBuilder.append("【手机号】未注册" + "\r\n");
        } else {
            info = hsaOperationMemberInfoMap.get(phoneNum);
            if (Boolean.TRUE.equals(info.getAccountState())) {
                stringBuilder.append("【手机号】已禁用" + "\r\n");
            }

        }
        return info;
    }


    private static void checkPhoneNum(Set<String> checkPhoneNumSet, String phoneNum, StringBuilder stringBuilder) {
        if (org.springframework.util.StringUtils.hasText(phoneNum)) {
            if (!NumberUtil.isPhoneNum11(phoneNum)) {
                stringBuilder.append("【手机号】格式错误" + "\r\n");
            }
            if (checkPhoneNumSet.contains(phoneNum)) {
                stringBuilder.append("【手机号】重复导入" + "\r\n");
            }
            checkPhoneNumSet.add(phoneNum);
        } else {
            stringBuilder.append("【手机号】格式错误" + "\r\n");
        }
    }
}
