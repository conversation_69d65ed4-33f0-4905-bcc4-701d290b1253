package com.holderzone.member.common.qo.activity;

import com.holderzone.member.common.dto.activity.SettleApplyCommodityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "满减满折活动商品查询")
public class FullReductionFoldActivityRunQO implements Serializable {

    private static final long serialVersionUID = -3344795346015372923L;

    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    private String memberGradeGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店
     * 跨店铺下单情况
     */
    @ApiModelProperty("门店")
    private List<String> storeGuidList;

    /**
     *
     * @see com.holderzone.member.common.enums.member.MarketConsumptionOrderTypeEnum
     */
    private Integer applyBusiness;

    private List<String> memberLabelGuidList;

    /**
     * 结算台商品
     */
    private List<SettleApplyCommodityDTO> settleApplyCommodityDTOS;

    /**
     * 指定活动
     */
    private List<String> discountGuidList;

}
