package com.holderzone.member.common.qo.coupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.holderzone.member.common.constant.SqlConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

/**
 * 会员优惠券
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MemberCouponListQO implements Serializable {

    private static final long serialVersionUID = -5265856326177700120L;

    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    @NotBlank(message = "会员guid必填")
    private String memberGuid;

    /**
     * 订单guid 会员guid为空时
     */
    private String orderGuid;

    /**
     * 0可使用（默认） 1历史
     */
    private Integer useType = 0;

    /**
     * 过期时间
     */
    @JsonIgnore
    private LocalDateTime expireDate;

    /**
     * 历史券才需要，分页查询使用
     * 开始时间:首次为空，第二次为上次最后一条记录的时间(pageTime)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime pageTime;

    /**
     * 用于分页
     * 开始id:首次为空，第二次为上次最后一条记录的id
     */
    private Long pageId;

    /**
     * 每页记录数
     */
    private Integer pageSize = 20;

    private List<String> discountOptionId;

    /**
     * 优惠券查询类型
     * @see CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 可使用优惠项查询
     */
    public static MemberCouponListQO buildUseQuery(SettlementApplyOrderDTO dto, Integer couponType) {
        return new MemberCouponListQO()
                .setMemberGuid(dto.getOrderInfo().getMemberInfoGuid())
                .setDiscountOptionId(dto.getDiscountOptionId())
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setCouponType(couponType)
                .setPageSize(SqlConstant.MAX_PAGE_SIZE)
                .setUseType(BooleanEnum.FALSE.getCode());
    }
}
