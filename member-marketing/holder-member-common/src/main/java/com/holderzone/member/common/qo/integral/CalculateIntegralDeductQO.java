package com.holderzone.member.common.qo.integral;

import com.holderzone.member.common.qo.equities.MemberPriceCommodityQO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 积分抵现折扣
 * @author: rw
 */
@Data
@Accessors(chain = true)
public class CalculateIntegralDeductQO implements Serializable {

    /**
     * 会员guid
     */
    @ApiModelProperty("会员guid")
    private String memberInfoGuid;


    /**
     * 终端
     */
    @ApiModelProperty("终端")
    private String terminal;

    /**
     * 业务
     */
    @ApiModelProperty("业务")
    private String business;

    /**
     * 小计总额
     */
    @ApiModelProperty("小计总额")
    private BigDecimal commodityTotalAmount;

    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private BigDecimal freightAmount = BigDecimal.ZERO;

    /**
     * 桌台费
     */
    @ApiModelProperty("桌台费")
    private BigDecimal tableAmount = BigDecimal.ZERO;

    /**
     * 折扣类型
     *
     * @see com.holderzone.member.common.enums.equities.DiscountTypeEnum
     */
    @ApiModelProperty("折扣类型")
    private Integer discountType;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 积分锁定数量
     */
    @ApiModelProperty("积分锁定数量")
    private Integer lockedIntegral;

    /**
     * 抵扣金额
     */
    @ApiModelProperty("抵扣金额")
    private BigDecimal lockedForNowMoney;

    /**
     * 优惠类型具体项的id
     */
    @ApiModelProperty(value = "优惠id")
    private String discountOptionId;

    /**
     * 购物车商品明细
     */
    @ApiModelProperty("购物车商品明细")
    private List<MemberPriceCommodityQO> memberPriceCommodityQOS;

}
