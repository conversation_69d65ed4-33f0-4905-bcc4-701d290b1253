package com.holderzone.member.common.vo.ipass;

import lombok.Data;

import java.io.Serializable;

@Data
public class SubjectVO implements Serializable {

    private static final long serialVersionUID = -3201776205165119823L;
    /**
     * 运营主体ID
     */
    private String id;

    /**
     * 运营主体名称
     */
    private String name;

    /**
     * 企业ID
     */
    private Long teamInfoId;

    /**
     * 企业名称
     */
    private String teamInfoName;
}
