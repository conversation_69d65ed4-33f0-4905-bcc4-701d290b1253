package com.holderzone.member.common.qo.tool;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @description 消息通知配置
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MessagesSendQO extends MallMessagesSendQO implements Serializable {

    /**
     * memberGuid
     */
    private String memberGuid;

    /**
     * 手机号  用于获取openid
     */
    private String phone;

    /**
     * orderNum
     */
    private String orderNum;

    /**
     * memberInfoCardGuid
     */
    private String memberInfoCardGuid;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 卡图片
     */
    private String cardImage;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyAmount;

    /**
     * 卡余额
     */
    private BigDecimal cardBalance;

    /**
     * 资金变动类型
     */
    private Integer amountSourceType;

    /**
     * 调整金额
     */
    private BigDecimal changeAmount;

    /**
     * 调整类型
     */
    @ApiModelProperty(value = "资金来往类型(0增加,1减少", required = true)
    private Integer amountFundingType;

    /**
     * 活动GUID
     */
    private String activityGuid;

    /**
     * 活动记录GUID
     */
    private String activityRecordGuid;

}
