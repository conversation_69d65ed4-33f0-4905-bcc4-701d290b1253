package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.member.common.client.JwtParserPassUtil;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.PageModel;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.ipaas.AddAndUpdateIPaasDTO;
import com.holderzone.member.common.dto.ipaas.CheckPasswordDTO;
import com.holderzone.member.common.dto.mall.TeamStoreInfoModel;
import com.holderzone.member.common.dto.mall.TeamStoreInfoPageParamModel;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.permission.RolePermissionMapModel;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.equities.ApplyModuleEnum;
import com.holderzone.member.common.enums.member.CloudSourceTypeEnum;
import com.holderzone.member.common.enums.member.MarketConsumptionOrderTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalBaseService;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.permission.OperSubjectUserPermissionQO;
import com.holderzone.member.common.vo.base.BusinessDataModel;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.ipass.StoreListVO;
import com.holderzone.member.common.vo.ipass.SubjectListVO;
import com.holderzone.member.common.vo.ipass.SubjectVO;
import com.holderzone.member.common.vo.ipass.UserInfoVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSelectOptionVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description pass系统
 */
@Service
@Slf4j
public class MarketBaseServiceImpl implements ExternalBaseService {

    @Resource
    private IPaasFeign iPaasFeign;

    @Resource
    private JwtParserPassUtil jwtParserPassUtil;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Override
    public HeaderUserInfo queryUserInformation(String enterpriseGuid) {
        return new HeaderUserInfo();
    }

    @Override
    public HeaderUserInfo queryUserInformation(String enterpriseGuid, String token) {

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        if ((Objects.isNull(headerUserInfo.getToken()) && StringUtils.isEmpty(token))) {
            return headerUserInfo;
        }

        //获取有效token
        token = StringUtils.isEmpty(headerUserInfo.getToken()) ? token : headerUserInfo.getToken();
        //根据token解析出用户账号
        String account = jwtParserPassUtil.acquirePassPreferredUsernameToken(token);
        log.info("根据token解析出用户账号:{}", account);
        IPaasFeignModel<UserInfoVO> userInfo = iPaasFeign.queryUserInfoDetail(account);

        //校验是否存在异常
        if (userInfo.getReturnCode() != 0) {
            throw new MemberBaseException(userInfo.getReturnCode(), userInfo.getReturnMessage());
        }

        //为空则提前返回
        if (Objects.isNull(userInfo.getData())) {
            return headerUserInfo;
        }

        //不为空则组装返回
        UserInfoVO userInfoVO = userInfo.getData();
        headerUserInfo.setAccount(userInfoVO.getAccount());
        headerUserInfo.setUserGuid(String.valueOf(userInfoVO.getId()));
        headerUserInfo.setUserName(userInfoVO.getUsername());
        headerUserInfo.setTel(userInfoVO.getPhoneNumber());
        headerUserInfo.setSystem(SystemEnum.RETAIL.getCode());

        log.info("零售根据token解析出用户信息:{}", JSON.toJSONString(headerUserInfo));
        return headerUserInfo;
    }

    @Override
    public OperSubjectInfoVO getOperatingSubjectInfo() {
        OperSubjectInfoVO operSubjectInfoVO = new OperSubjectInfoVO();
        List<OperSubjectInfo> operSubjectInfoList = queryOperatingSubject(ThreadLocalCache.getEnterpriseGuid());
        HeaderUserInfo headerUserInfo = queryUserInformation(ThreadLocalCache.getEnterpriseGuid(), null);

        BeanUtils.copyProperties(headerUserInfo, operSubjectInfoVO);

        operSubjectInfoVO.setOperSubjectInfos(operSubjectInfoList);

        return operSubjectInfoVO;
    }

    /**
     * @description: 查询企业下所有运营主体
     * @author: li ao
     * @date: 2024/3/20 15:14
     * @param: enterpriseGuid
     * @return: java.util.List<com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO>
     **/
    @Override
    public List<HsaOperSubjectPermissionVO> queryOperatingSubjectByEnterpriseGuid(String enterpriseGuid) {
        List<HsaOperSubjectPermissionVO> vo = new ArrayList<>();

        //根据企业查询运营主体
        IPaasFeignModel<SubjectListVO> subjectList = iPaasFeign.queryOperatingSubjectByTeam(Long.valueOf(enterpriseGuid));

        if (subjectList.getReturnCode() != 0) {
            throw new MemberBaseException(subjectList.getReturnCode(), subjectList.getReturnMessage());
        }

        if (CollectionUtils.isEmpty(subjectList.getDataList())) {
            return vo;
        }

        //包装返回
        List<SubjectListVO> dataList = subjectList.getDataList();
        dataList.forEach(subject -> {
            HsaOperSubjectPermissionVO dataVo = new HsaOperSubjectPermissionVO();
            dataVo.setOperSubjectGuid(String.valueOf(subject.getId()));
            dataVo.setName(subject.getOperatingSubjectName());
            dataVo.setStatus(subject.getState());
            dataVo.setLogo(subject.getLogo());
            vo.add(dataVo);
        });
        return vo;
    }

    /**
     * 校验用户密码是否正确
     **/
    @Override
    public Boolean validateUser(String account, String password) {
        CheckPasswordDTO dto = new CheckPasswordDTO();
        dto.setAccount(account);
        dto.setPassword(password);
        IPaasFeignModel<Boolean> iPaasFeignModel = iPaasFeign.checkPassword(dto);
        if (Objects.isNull(iPaasFeignModel) || iPaasFeignModel.getReturnCode() != 0) {
            throw new MemberBaseException("校验用户密码异常");
        }
        return iPaasFeignModel.getData();
    }

    @Override
    public BusinessDataModel queryBusinessData(String operSubjectGuid) {
        return null;
    }

    @Override
    public BusinessDataModel queryBusinessData(String operSubjectGuid, String token) {
        return null;
    }

    @Override
    public List<OperSubjectInfo> queryOperatingSubject(String enterpriseGuid) {
        List<OperSubjectInfo> operSubjectInfoList = Lists.newArrayList();
        IPaasFeignModel<SubjectListVO> subjectList = iPaasFeign.queryOperatingSubjectByTeam(Long.valueOf(enterpriseGuid));
        log.info("查询企业下所有运营主体,返回结果:{}", JSON.toJSONString(subjectList));
        if (subjectList.getReturnCode() != 0) {
            throw new MemberBaseException(subjectList.getReturnCode(), subjectList.getReturnMessage());
        }
        if (CollUtil.isNotEmpty(subjectList.getDataList())) {
            for (SubjectListVO subjectListVO : subjectList.getDataList()) {
                OperSubjectInfo operSubjectInfo = new OperSubjectInfo();
                if (subjectListVO.getIsDelete() == 1) {
                    continue;
                }
                operSubjectInfo.setMultiMemberName(subjectListVO.getOperatingSubjectName());
                operSubjectInfo.setOperSubjectGuid(String.valueOf(subjectListVO.getId()));
                operSubjectInfo.setMultiMemberStatus(subjectListVO.getState() != BooleanEnum.TRUE.getCode());
                operSubjectInfoList.add(operSubjectInfo);
            }
        }
        return operSubjectInfoList;
    }

    @Override
    public List<OperSubjectInfo> queryOperatingSubject() {
        return Lists.newArrayList();
    }

    @Override
    public String findBusinessDataAddress(String enterpriseGuid) {
        return null;
    }

    @Override
    public List<String> listAllOperationSubjectId() {
        return null;
    }

    @Override
    public Boolean getHasPermissionName(OperationPermissionQO request, String token) {
        return true;
    }

    @Override
    public List<MemberSystemPermissionDTO> listSystemPermission(OperationPermissionQO operationPermissionRequest, String token) {
        return null;
    }

    @Override
    public List<String> listIdentificationNames() {
        // 企业id
        String enterpriseGuid = ThreadLocalCache.getEnterpriseGuid();
        // 用户信息
        HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        String account = userInfo.getTel();
        if (StrUtil.isBlank(account)) {
            return Collections.emptyList();
        }

        IPaasFeignModel<String> result = iPaasFeign.queryIdentificationNames(account, enterpriseGuid);
        if (result == null) {
            return Collections.emptyList();
        }
        return result.getDataList();
    }

    @Override
    public List<SubjectVO> listIdentificationSubjects(String identificationName) {
        // 用户信息
        HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        String account = userInfo.getTel();
        if (StrUtil.isBlank(account)) {
            return Collections.emptyList();
        }

        IPaasFeignModel<SubjectVO> result = iPaasFeign.listSubjectByPermission(account, identificationName);
        if (result == null) {
            return Collections.emptyList();
        }

        return result.getDataList();
    }

    @Override
    public List<String> listUserRoleIds(OperSubjectUserPermissionQO userPermissionQO, String token) {
        return null;
    }

    @Override
    public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionRequest, String token) {
        return new RolePermissionMapModel().setIsAll(BooleanEnum.TRUE.getCode());
    }

    @Override
    public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionQO) {
        return null;
    }

    @Override
    public String getTemporaryTokenByAccount(String account) {
        return null;
    }

    @Override
    public int requestVerification(Integer type, String info) {
        return 0;
    }

    @Override
    public int validateVerification(String code, String info) {
        return 0;
    }

    @Override
    public List<OperSubjectInfo> queryOperatingSubjectByTeam(String teamId) {
        return null;
    }

    @Override
    public List<MemberSystemPermissionDTO> getSystemPermissionList(OperationPermissionQO operationPermissionRequest) {
        return null;
    }

    @Override
    public RoleAndPostIdDTO findUserRoleAndPost(String enterpriseGuid, String tel) {
        return null;
    }

    @Override
    public PageModel<TeamStoreInfoModel> findStorePageInfo(TeamStoreInfoPageParamModel teamStoreInfoPageParamModel) {
        return null;
    }

    @Override
    public List<String> queryUserPermission(Long teamId, String account, Integer permissionType, Long typeId, String token) {
        return null;
    }

    @Override
    public String getTemporaryToken(String phoneNum) {
        return null;
    }

    /**
     * @description: 查询企业下所有门店信息
     * @author: li ao
     * @date: 2024/3/20 15:25
     * @param: teamInfoId
     * @return: java.util.List<com.holderzone.member.common.vo.iPaas.StoreListVO>
     **/
    @Override
    public List<StoreListVO> getStoreByTeam(Long teamInfoId) {
        IPaasFeignModel<StoreListVO> storeByTeam = iPaasFeign.getStoreByTeam(teamInfoId);
        if (Objects.isNull(storeByTeam) || CollectionUtils.isEmpty(storeByTeam.getDataList())) {
            return Lists.newArrayList();
        }
        return storeByTeam.getDataList();
    }

    /**
     * @description: 查询门店详情
     * @author: li ao
     * @date: 2024/3/20 15:31
     * @param: id 门店ID
     * @return: com.holderzone.member.common.vo.iPaas.StoreListVO
     **/
    @Override
    public StoreListVO getStoreDetail(Long id) {
        IPaasFeignModel<StoreListVO> storeDetail = iPaasFeign.getStoreDetail(id);
        if (Objects.isNull(storeDetail) || Objects.isNull(storeDetail.getData())) {
            return new StoreListVO();
        }
        return storeDetail.getData();
    }

    /**
     * @description: 新增/编辑门店基础信息
     * @author: li ao
     * @date: 2024/3/20 15:36
     * @param: addAndUpdateIPaas 新增/编辑门店基础信息参数
     **/
    @Override
    public void addAndUpdateStore(AddAndUpdateIPaasDTO addAndUpdateIPaasDTO) {
        iPaasFeign.addAndUpdateStore(addAndUpdateIPaasDTO);
    }

    /**
     * @description: 查询用户详情
     * @author: li ao
     * @date: 2024/3/20 15:53
     * @param: account 用户账号
     * @return: com.holderzone.member.common.vo.iPaas.UserInfoVO
     **/
    @Override
    public HeaderUserInfo queryUserInfoDetail(String account) {
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        //查询用户信息
        IPaasFeignModel<UserInfoVO> feignModel = iPaasFeign.queryUserInfoDetail(account);
        log.info("查询用户详情,返回结果:{}", JSON.toJSONString(feignModel));
        if (feignModel.getReturnCode() != 0) {
            throw new MemberBaseException(feignModel.getReturnCode(), feignModel.getReturnMessage());
        }

        UserInfoVO userInfoVO = feignModel.getData();
        headerUserInfo.setAccount(userInfoVO.getAccount());
        headerUserInfo.setUserGuid(String.valueOf(userInfoVO.getId()));
        headerUserInfo.setUserName(userInfoVO.getUsername());
        headerUserInfo.setTel(userInfoVO.getPhoneNumber());
        headerUserInfo.setSystem(SystemEnum.RETAIL.getCode());

        return headerUserInfo;
    }

    @Override
    public MemberSystemPermissionVO getAccountPermission() {
        MemberSystemPermissionVO memberSystemPermissionVO = new MemberSystemPermissionVO();
        memberSystemPermissionVO.setIsAll(BooleanEnum.TRUE.getCode());
        return memberSystemPermissionVO;
    }

    @Override
    public List<ApplyTypeVO> getApplyBusiness() {
        List<ApplyTypeVO> applyTypeVOS = new ArrayList<>();
        Integer system = ThreadLocalCache.getSystem();
        if (system == SystemEnum.MALL.getCode()) {
            MemberUnilinkSystemConfigVO config = memberMallToolFeign.listConfig().getData();
            for (MemberUnilinkSelectOptionVO memberUnilinkSelectOptionVO : config.getBiz()) {
                ApplyTypeVO applyTypeVO = new ApplyTypeVO();
                applyTypeVO.setTypeName(memberUnilinkSelectOptionVO.getLabel());
                applyTypeVO.setType(memberUnilinkSelectOptionVO.getValue());
                applyTypeVO.setModule(ApplyModuleEnum.APPLY_BUSINESS.getType());
                applyTypeVOS.add(applyTypeVO);
            }
            return applyTypeVOS;
        }
        for (MarketConsumptionOrderTypeEnum consumptionOrderTypeEnum : MarketConsumptionOrderTypeEnum.values()) {
            if (consumptionOrderTypeEnum.getDes().equals(MarketConsumptionOrderTypeEnum.STORE_ORDER.getDes())) {
                ApplyTypeVO applyTypeVO = new ApplyTypeVO();
                applyTypeVO.setType(consumptionOrderTypeEnum.getCode() + StringConstant.EMPTY);
                applyTypeVO.setTypeName(consumptionOrderTypeEnum.getDes());
                applyTypeVO.setModule(ApplyModuleEnum.APPLY_BUSINESS.getType());
                applyTypeVO.setChannel(consumptionOrderTypeEnum.getChannel());
                applyTypeVOS.add(applyTypeVO);
            }
        }
        return applyTypeVOS;
    }

    @Override
    public List<ApplyTypeVO> getApplyTerminal() {
        List<ApplyTypeVO> applyTypeVOS = new ArrayList<>();
        for (CloudSourceTypeEnum cloudSourceEnum : CloudSourceTypeEnum.values()) {
            if (cloudSourceEnum.getDes().equals(CloudSourceTypeEnum.ADD_CLOUD_MARKET.getDes())
                    || cloudSourceEnum.getDes().equals(CloudSourceTypeEnum.ADD_BACKGROUND.getDes())
                    || CloudSourceTypeEnum.getMallSource().contains(cloudSourceEnum.getCode())) {
                ApplyTypeVO applyTypeVO = new ApplyTypeVO();
                applyTypeVO.setType(cloudSourceEnum.getCode() + StringConstant.EMPTY);
                applyTypeVO.setTypeName(cloudSourceEnum.getDes());
                applyTypeVO.setModule(ApplyModuleEnum.APPLY_TERMINAL.getType());
                applyTypeVO.setChannel(cloudSourceEnum.getDes());
                applyTypeVOS.add(applyTypeVO);
            }
        }
        return applyTypeVOS;
    }

    @Override
    public String getBusinessName(Integer code) {
        log.info("零售 getBusinessName:{}", code);
        return MarketConsumptionOrderTypeEnum.getDesByCode(code);
    }

    @Override
    public String getSourceTypeEnum(int code) {
        return CloudSourceTypeEnum.getMsgByCode(code);
    }

    @Override
    public String getOrderTypeEnum(int code) {
        return MarketConsumptionOrderTypeEnum.getDesByCode(code);
    }

    @Override
    public String getOrderSourceEnum(int code) {
        return CloudSourceTypeEnum.getMsgByCode(code);
    }

    @Override
    public SubjectListVO findSubjectDetail(Integer id) {
        IPaasFeignModel<SubjectListVO> model = iPaasFeign.findSubjectDetail(id);
        if (model.getReturnCode() != 0) {
            throw new MemberBaseException(model.getReturnCode(), model.getReturnMessage());
        }
        return model.getData();
    }
}
