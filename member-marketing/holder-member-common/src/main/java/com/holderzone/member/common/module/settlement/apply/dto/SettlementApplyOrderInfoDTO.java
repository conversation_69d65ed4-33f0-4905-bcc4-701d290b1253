package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.util.SettlementVerifyUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 结算规则应用订单数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyOrderInfoDTO implements Serializable {

    private static final long serialVersionUID = 2114357817386642843L;

    /**
     * 主体
     */
    @ApiModelProperty("operSubjectGuid")
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    /**
     * 终端
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    @ApiModelProperty("终端")
    private String terminal;

    /**
     * 业务（订单类型）:见同步表
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    @ApiModelProperty("业务")
    private String business;

    /**
     * 门店
     * 兼容零售单门店情况
     */
    @ApiModelProperty("门店")
    private String storeGuid;

    /**
     * 门店
     * 跨店铺下单情况
     */
    @ApiModelProperty("门店")
    private List<String> storeGuidList;

    /**
     * 渠道：pos、商城...
     * 
     */
    @ApiModelProperty("渠道")
    private String channel;

    /**
     * 是否锁定累计优惠: 下单前操作
     * 预先扣减订单使用优惠,用于计算优惠使用限制次数（折扣、优惠券）
     * 积分最后实时扣除，不用锁定
     */
    @ApiModelProperty("是否锁定累计优惠")
    private Integer isOfferLocked = BooleanEnum.FALSE.getCode();

    /**
     * 当前订单号 : 计算时必传
     * orderNum
     */
    @ApiModelProperty("当前订单号")
    private String orderNumber;

    private String orderGuid;

    /**
     * 商品原价合计
     */
    @ApiModelProperty("原价合计金额")
    private BigDecimal commodityTotalAmount;

    /**
     * 商品实付合计
     */
    @ApiModelProperty("实付合计金额")
    private BigDecimal shopCarOriginPayPrice;

    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private BigDecimal freightAmount = BigDecimal.ZERO;

    /**
     * 桌台费
     */
    @ApiModelProperty("桌台费")
    private BigDecimal tableAmount = BigDecimal.ZERO;

    /**
     * 优惠券单笔限制张数
     * 后端使用
     * 1、限制，具体张数，不限制 {@link com.holderzone.member.common.module.settlement.constant.SettlementConstant#COUPON_LIMIT_NUM}
     */
    private Integer couponLimitNum;

    /**
     * 积分锁定数量
     */
    @ApiModelProperty("积分锁定数量")
    private Integer lockedIntegral;

    /**
     * 重新计算id
     */
    @ApiModelProperty("重新计算id")
    private String recountId;

    @ApiModelProperty("商品金额算法分位设置;o保留，1四舍五入，2四舍五入,条码截位，3舍弃")
    private int goodsAmountCent;

    @ApiModelProperty("商品金额算法厘位设置;o四舍五入1舍弃")
    private int goodsAmountMilli;

    public void couponLimitNumSubtract1() {
        this.couponLimitNum = couponLimitNum - 1;
    }

    public void validate() {
        SettlementVerifyUtil.isBlank(terminal, "终端必传!");
        SettlementVerifyUtil.isBlank(business, "业务必传!");
    }
}
