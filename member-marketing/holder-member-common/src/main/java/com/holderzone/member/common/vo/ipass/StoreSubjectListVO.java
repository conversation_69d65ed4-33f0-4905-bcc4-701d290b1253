package com.holderzone.member.common.vo.ipass;

import lombok.Data;

import java.io.Serializable;

/**
 * 根据主体返回的门店
 */
@Data
public class StoreSubjectListVO implements Serializable {

    private static final long serialVersionUID = 3242334218666917105L;

    /**
     * 门店id
     */
    private Long id;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 门店地址
     */
    private String address;

    /**
     * 部门id
     */
    private Long storeTeamInfoId;

    /**
     * 企业id
     */
    private Long teamInfoId;

    /**
     * 企业名称
     */
    private String teamInfoName;

    /**
     * 运营主体ID
     */
    private Long subjectId;

    /**
     * 运营主体名称
     */
    private String subjectName;

    /**
     * 负责人手机号
     */
    private String responsibleAccount;

    /**
     * 负责人名称
     */
    private String responsibleName;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 其他业务信息 json格式
     */
    private String business;
}
