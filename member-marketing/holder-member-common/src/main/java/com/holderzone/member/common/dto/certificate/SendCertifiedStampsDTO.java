package com.holderzone.member.common.dto.certificate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 认证活动发券请求
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "认证活动发券请求")
@Accessors(chain = true)
public class SendCertifiedStampsDTO implements Serializable {

    /*** 活动id */
    @ApiModelProperty(value = "活动id")
    private String activityGuid;

    /*** 审核通过记录guid */
    @ApiModelProperty(value = "审核通过记录guid")
    private String auditStepGuid;

    /*** 认证活动 */
    @ApiModelProperty(value = "认证活动")
    private String activityName;

    /*** 认证活动编号 */
    @ApiModelProperty(value = "认证活动编号")
    private String activityNumber;

    /*** 用户id */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /*** 平台id */
    @ApiModelProperty(value = "平台id")
    private Long platformId;

    /**
     * 会员中台来源
     */
    private Integer memberSource;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源 7 赚餐小程序 6 点菜宝")
    private Integer source;

    /*** 优惠券开始时间 */
    @ApiModelProperty(value = "优惠券开始时间")
    @JsonFormat(pattern = "yyy-MM-dd")
    private LocalDate startTime;

    /*** 优惠券结束时间 */
    @ApiModelProperty(value = "优惠券结束时间")
    @JsonFormat(pattern = "yyy-MM-dd")
    private LocalDate endTime;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 证件有效期
     */
    @JsonFormat(pattern = "yyy-MM-dd HH:mm:ss")
    private LocalDateTime certificateValidity;

    /*** 优惠券id */
    @ApiModelProperty(value = "优惠券id")
    private List<CertifiedCouponDTO> certifiedCouponDTOS;

}
