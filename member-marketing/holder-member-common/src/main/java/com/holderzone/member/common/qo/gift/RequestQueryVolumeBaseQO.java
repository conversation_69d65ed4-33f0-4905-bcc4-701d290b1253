package com.holderzone.member.common.qo.gift;

import com.holderzone.member.common.dto.page.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class RequestQueryVolumeBaseQO extends PageDTO {

    @ApiModelProperty("券类型，0代金券,1折扣券,2兑换券,3商品券")
    private Integer volumeType;

    @ApiModelProperty("券名称")
    private String volumeName;

    @ApiModelProperty(" 状态,0未过期(发送中),1已过期(保留状态),2停止发放,3作废")
    private Integer volumeState;

    private List<String> volumeGuidList;

    private String operSubjectGuid;

    @ApiModelProperty("查询赚餐券标识")
    private Boolean queryZhuanCanFlag;
}
