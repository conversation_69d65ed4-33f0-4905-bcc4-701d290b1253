package com.holderzone.member.common.vo.card;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.vo.credit.TerminalCreditVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BaseLoginMemberCardVO implements Serializable {

    /**
     * 账号状态,0正常,1冻结
     */
    private Integer accountState;

    private String memberInfoGuid;

    /**
     * 会员等级guid
     */
    private String memberGradeInfoGuid;

    /**
     * 会员等级name
     */
    private String memberGradeInfoName;

    /**
     * 登录方式
     * 13 微信支付 22 扫码支付  23 刷卡支付 24 输入手机号/卡号支付
     *
     * @see com.holderzone.member.common.enums.member.TerminalCheckStatusEnum
     */
    private Integer terminalCheckStatus;

    /**
     * 手机国家编码
     */
    private String phoneCountryCode;

    /**
     * 手机号码
     */
    private String phoneNum;

    /**
     * 等级图标
     */
    private String gradeIcon;

    /**
     * 会员积分
     */
    private Integer memberIntegral;

    /**
     * 冻结积分
     */
    private Integer freezeIntegral;

    /**
     * 人脸识别（用户id）
     */
    private String userId;
    /**
     * 顾客姓名
     */
    private String userName;

    /**
     * 顾客昵称
     */
    private String nickName;

    /**
     * 头像url
     */
    private String headImgUrl;

    /**
     * 挂账信息
     */
    private TerminalCreditVO terminalCreditVO;

    /**
     * 卡信息
     */
    private List<LoginMemberCardVO> terLoginMemberCardListVOS;

    /**
     * 当前亲属
     */
    private KinsfolkManagementVO kinsfolkManagement;

    /**
     * 人脸
     */
    private String message;

    /**
     * 差额
     */
    private BigDecimal amount;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private Integer sourceType;

    /**
     * 门店GUID
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String storeGuid;

    /**
     * 会员来源门店名称
     */
    private String storeName;
}
