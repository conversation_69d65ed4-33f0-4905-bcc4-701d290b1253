package com.holderzone.member.common.qo.member;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新增会员标签关联
 * @date 2021/9/28 17:39
 */
@Data
@Accessors(chain = true)
public class AddMemberLabelCorrelationQO implements Serializable {


    private List<String> memberInfoGuid;


    private List<String> labelGuid;

    /**
     * 批次ID，加锁,防止重复
     */
    private String batchId;

    /**
     * 关联类型 0 手动关联 1 自动关联
     */
    private Integer connectionType;
}
