package com.holderzone.member.common.vo.ipass;

import com.holderzone.member.common.enums.s2b2bc.ShopMode;
import com.holderzone.member.common.enums.s2b2bc.ShopType;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 商家同步信息
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@Getter
@Setter
@Accessors(chain = true)
public class ShopBusinessVO implements Serializable {

    private static final long serialVersionUID = 3940856140944621683L;

    /**
     * 店铺id
     */
    private Long id;

    /**
     * 店铺类型
     */
    private ShopMode shopMode;

    /**
     * 店铺类型
     */
    private ShopType shopType;
}