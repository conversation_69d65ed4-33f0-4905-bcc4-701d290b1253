package com.holderzone.member.common.qo.card;

import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.TerminalCheckStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 一体机会员卡信息请求
 * @date 2021/10/20 9:48
 */
@Data
@Accessors(chain = true)
public class TerLoginMemberCardQO implements Serializable {

    /**
     * 会员手机号或卡号
     */
    @NotEmpty(message = "登录信息不能为空")
    private String loginNum;

    /**
     * 支付 1/充值 0
     */
    @NotNull(message = "操作类型不能为空")
    private Integer type = 1;

    /**
     * 人脸识别（图片的唯一表示）
     */
    private String faceToken;

    /**
     * 人脸识别（用户id）
     */
    private String userId;

    /**
     * 用户类型：0 会员 1 会员或亲属
     */
    private int userType;

    /**
     * 门店guid,小程序查询适用门店会员卡时传递
     */
    private String storeGuid;

    /**
     * 白卡是否限制 1 限制
     */
    private Integer limit;

    /**
     * 是否过滤会员卡 冻结、禁用状态 1 是 0 否
     */
    private Integer isFilterStatus = 1;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 登录方式
     * 13 微信支付 22 扫码支付  23 刷卡支付 24 输入手机号/卡号支付 30 手机号  40 卡号
     *
     * @see com.holderzone.member.common.enums.member.TerminalCheckStatusEnum
     */
    private Integer terminalCheckStatus;

    /**
     * 手机国家编码
     */
    @ApiModelProperty(value = "手机国家编码", required = true)
    private String phoneCountryCode;

    /**
     * 订单号
     */
    private String orderGuid;

    /**
     * 是否外接刷卡
     */
    private Integer isSwipingCard;

    public void checkPhoneCountryCode() {
        if (StringUtils.isEmpty(phoneCountryCode)) {
            this.phoneCountryCode = StringConstant.STR_DEFAULT_PHONE_COUNTRY_CODE;
        }

        if (Objects.nonNull(isSwipingCard) && isSwipingCard == BooleanEnum.TRUE.getCode()){
            terminalCheckStatus = TerminalCheckStatusEnum.SWIPING_CARD_PAY.getCode();
        }
    }
}
