package com.holderzone.member.common.module.settlement.apply.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品优惠明细
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyShareCommodityVO implements Serializable {

    private static final long serialVersionUID = 3559064153837638379L;

    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    @ApiModelProperty(value = "优惠类型")
    private Integer discountOption;

    /**
     * 优惠大项guid
     */
    @ApiModelProperty(value = "优惠大项guid")
    private String discountGuid;

    /**
     * 优惠具体id
     */
    @ApiModelProperty(value = "优惠具体id")
    private String discountOptionId;

    /**
     * 优惠具体名称
     */
    @ApiModelProperty(value = "优惠具体名称")
    private String discountName;

    /**
     * 优惠具体名称描述
     * 这里主要用来对接优惠活动
     *
     */
    @ApiModelProperty(value = "优惠具体名称描述")
    private String discountDesc;

    /**
     * 商品明细
     */
    @ApiModelProperty("商品明细")
    private List<SettlementApplyCommodityVO> commodityList;

}
