package com.holderzone.member.common.util.repast;

import java.util.Objects;


/**
 * 餐饮云utils
 */
public class RepastUtils {

    public static final int REPAST_OPERSUBJECTGUID_LENGTH = 19;

    /**
     * 判断是否已经授权餐饮云
     *
     * @param operSubjectGuid 运营主体
     * @return true:已经授权，false:未授权
     */
    public static boolean isHasRepastAuth(String operSubjectGuid) {
        boolean hasRepastAuth = false;

        // 餐饮云原运营主体长度大于等于19，如果不是证明已经授权
        if (Objects.nonNull(operSubjectGuid) && operSubjectGuid.length() < REPAST_OPERSUBJECTGUID_LENGTH) {
            hasRepastAuth = true;
        }

        return hasRepastAuth;
    }

}
