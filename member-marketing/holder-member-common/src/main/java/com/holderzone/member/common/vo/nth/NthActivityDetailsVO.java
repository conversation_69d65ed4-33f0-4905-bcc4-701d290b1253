package com.holderzone.member.common.vo.nth;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.member.common.dto.nth.NthActivityItemDTO;
import com.holderzone.member.common.dto.nth.NthActivityStoreDTO;
import com.holderzone.member.common.enums.base.ActivityBusinessTypeEnum;
import com.holderzone.member.common.qo.gift.ConsumptionGiftDetailsDTO;
import com.holderzone.member.common.vo.grade.EffectiveTimeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 第N份优惠活动详情
 */
@Data
public class NthActivityDetailsVO implements Serializable {

    private static final long serialVersionUID = 8703620733617395226L;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String guid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    /**
     * 是否限制活动时段 0-否 1-是
     */
    private Integer isLimitPeriod;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private Integer equitiesTimeLimitedType;

    /**
     * 限制时段限制类型json
     *
     * @see EffectiveTimeVO
     */
    @ApiModelProperty(value = "限制时段限制类型json")
    private String equitiesTimeLimitedJson;

    /**
     * 每购买份数
     */
    @ApiModelProperty(value = "每购买份数")
    private Integer perCount;

    /**
     * 每购买份数享多少折
     */
    @ApiModelProperty(value = "每购买份数享多少折")
    private BigDecimal perDiscount;

    /**
     * 门槛条件 1:购买任意商品
     */
    @ApiModelProperty(value = "门槛条件")
    private Integer thresholdType;

    /**
     * 计算规则 1:从价格最低开始优惠
     */
    @ApiModelProperty(value = "计算规则")
    private Integer calculateRule;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;

    /**
     * 适用场景 0:全部业务 1：部分业务
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    @ApiModelProperty(value = "适用场景 0:全部业务 1：部分业务")
    private Integer applyBusiness;

    /**
     * 适用场景
     *
     * @see ActivityBusinessTypeEnum
     */
    @ApiModelProperty(value = "适用场景")
    private List<String> applyBusinessList;

    /**
     * 是否适用于所有门店 1:全部门店；0:部分门店(外关联表)
     */
    private Integer isAllStore;

    /**
     * 适用门店
     */
    @ApiModelProperty(value = "适用门店")
    private List<NthActivityStoreDTO> storeDTOList;

    /**
     * 适用人群类型 0不限制 1所有注册会员 2指定人群
     *
     * @see com.holderzone.member.common.enums.gift.RechargeGiftTypeEnum
     */
    @ApiModelProperty(value = "适用人群类型 0不限制 1所有注册会员 2指定人群")
    private Integer groupType;

    /**
     * 指定标签
     */
    @ApiModelProperty(value = "指定标签")
    private ConsumptionGiftDetailsDTO labelDTO;

    /**
     * 指定等级
     */
    @ApiModelProperty(value = "指定等级")
    private ConsumptionGiftDetailsDTO gradeDTO;

    /**
     * 指定会员
     */
    @ApiModelProperty(value = "指定会员")
    private ConsumptionGiftDetailsDTO memberDTO;

    /**
     * 活动商品
     */
    @ApiModelProperty(value = "活动商品")
    private List<NthActivityItemDTO> itemDTOList;
}
