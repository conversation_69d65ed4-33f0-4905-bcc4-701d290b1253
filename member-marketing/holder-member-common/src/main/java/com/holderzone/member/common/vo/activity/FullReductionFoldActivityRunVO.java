package com.holderzone.member.common.vo.activity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.vo.grade.EffectiveTimeVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityItemRunVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 满减满折活动商品返回
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "满减满折活动商品返回")
public class FullReductionFoldActivityRunVO implements Serializable {

    private static final long serialVersionUID = -7503721853573933952L;

    /**
     * 商品数据
     */
    private List<FullReductionFoldActivityItemRunVO> foldActivityItemRunVOList;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityCode;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 活动标签
     * 为参与活动客户打标签
     * 多个用逗号隔开
     */
    @ApiModelProperty(value = "活动标签")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String labelGuidJson;

    /**
     * 活动状态
     * 0-未发布 1-已发布 2-已暂停
     * ActivityStateEnum
     */
    @ApiModelProperty(value = "活动状态 0-未发布 1-已发布 2-已暂停")
    private Integer state;

    /**
     * 是否限制活动时段 0-否 1-是
     */
    @ApiModelProperty(value = "是否限制活动时段 0-否 1-是")
    private Integer isLimitPeriod;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @ApiModelProperty(value = "限制时段类型 -1:自定义 0：日 1：周 2：月 3：年")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer limitPeriodType;

    /**
     * 限制时段限制类型json
     *
     * @see EffectiveTimeVO
     */
    @ApiModelProperty(value = "限制时段限制类型json")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String limitPeriodJson;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;

    /**
     * 适用场景 0:全部业务 1：部分业务
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    @ApiModelProperty(value = "适用场景 0:全部业务 1：部分业务")
    private Integer applyBusiness;

    /**
     * 适用场景json
     * 多个用逗号隔开
     */
    @ApiModelProperty(value = "适用场景json")
    private String applyBusinessJson;

    /**
     * 是否可用
     */
    private Integer isEnabled;

    /**
     * 活动标签
     * 为参与活动客户打标签
     * labelGuidJson
     */
    @ApiModelProperty(value = "活动标签")
    private List<String> labelGuidList;

    /**
     * 满减策略类型
     * 0 按阶梯门槛，满足条件后优惠  满 1 单门槛可叠加优惠 每满
     */
    private Integer fullReductionTacticsType;


    /**
     * 满减策略类型
     * 优惠门槛 0 消费门槛 1 购买门槛
     */
    private Integer discountSillType;

    /**
     * 活动规则json
     *
     * @see com.holderzone.member.common.dto.activity.FullReductionFoldActivityDTO
     */
    @ApiModelProperty(value = "活动规则json")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String fullReductionFoldJson;

    private Integer applyCommodity;

    /**
     * 限制次数
     */
    private Integer limitCount;



    /**
     * 门店
     * 跨店铺下单情况
     */
    @ApiModelProperty("适用门店")
    private List<String> storeGuidList;
}
