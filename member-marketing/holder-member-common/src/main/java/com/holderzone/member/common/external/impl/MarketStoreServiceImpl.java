package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import java.util.stream.Collectors;

import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.*;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.ipaas.TeamOperationStoreIdModel;
import com.holderzone.member.common.dto.mall.TeamOperationSubjectModel;
import com.holderzone.member.common.dto.mall.TeamStoreAllParamModel;
import com.holderzone.member.common.dto.order.CrmOrderDTO;
import com.holderzone.member.common.dto.order.CrmOrderStockDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.s2b2bc.ShopMode;
import com.holderzone.member.common.external.ExternalBaseService;
import com.holderzone.member.common.external.ExternalStoreService;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.feign.MarketFeign;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.crm.CrmOrderDetailQo;
import com.holderzone.member.common.qo.crm.CrmOrderDetailVo;
import com.holderzone.member.common.qo.growth.AppletGrowthStoreQO;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.ipass.*;
import com.holderzone.member.common.vo.ipass.StoreListVO;
import com.holderzone.member.common.vo.ipass.StoreSubjectListVO;
import com.holderzone.member.common.vo.market.StorePageListOut;
import com.holderzone.member.common.vo.market.StoreQueryVO;
import com.holderzone.member.common.vo.wechat.PaySettingVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @description 零售门店
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarketStoreServiceImpl implements ExternalStoreService {

    private final MarketFeign marketFeign;

    private final IPaasFeign iPaasFeign;

    private final MemberMallToolFeign memberMallToolFeign;

    private final ExternalBaseService marketBaseServiceImpl;

    @Override
    public List<StoreBaseInfo> listStore(QueryStoreBasePage query) {
        log.info("零售 listStoreAndStall:{}", JSON.toJSONString(query));
        return listStoreAndStall(new QueryStoreBasePage());
    }

    @Override
    public Page<StoreBaseInfo> storePage(QueryStoreBasePage query) {
        List<StoreBaseInfo> allStores = listStoreAndStall(query);
        // 使用hutool工具判断name
        if (StrUtil.isNotBlank(query.getName())) {
            String nameFilter = query.getName().trim();
            allStores = allStores.stream()
                    .filter(store -> (StrUtil.isNotBlank(store.getStoreNumber()) && store.getStoreNumber().contains(nameFilter))
                            || (StrUtil.isNotBlank(store.getStoreName()) && store.getStoreName().contains(nameFilter)))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(allStores)) {
            return new Page<>();
        }

        int pageNum = Optional.ofNullable(query.getPage()).orElse(NumberConstant.NUMBER_1);
        int pageSize = Optional.ofNullable(query.getPageSize()).orElse(NumberConstant.NUMBER_10);
        int total = allStores.size();
        int fromIndex = Math.max((pageNum - 1) * pageSize, 0);
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<StoreBaseInfo> pageList = fromIndex < toIndex ? allStores.subList(fromIndex, toIndex) : Collections.emptyList();
        Page<StoreBaseInfo> pageResult = new Page<>();
        pageResult.setCurrentPage(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(total);
        pageResult.setData(pageList);
        return pageResult;
    }

    private static StoreBaseInfo getStoreBaseInfo(StoreSubjectListVO storeQueryVO) {
        StoreBaseInfo storeBaseInfo = new StoreBaseInfo();
        storeBaseInfo.setId(storeQueryVO.getId() + StringConstant.EMPTY);
        storeBaseInfo.setStoreName(storeQueryVO.getName());
        storeBaseInfo.setStoreId(storeQueryVO.getId() + StringConstant.EMPTY);
        storeBaseInfo.setStoreTeamInfoId(String.valueOf(storeQueryVO.getStoreTeamInfoId()));
        storeBaseInfo.setStoreNumber(storeBaseInfo.getId());
        storeBaseInfo.setChannel(SystemEnum.RETAIL.getDes());
        storeBaseInfo.setSystem(SystemEnum.RETAIL.name());
        return storeBaseInfo;
    }

    @Override
    public List<StoreBaseInfo> listStoreAndStall(QueryStoreBasePage query) {
        List<StoreBaseInfo> storeBaseInfoList = Lists.newArrayList();

        List<String> operSubjectIdList = Collections.singletonList(ThreadLocalCache.getOperSubjectGuid());
        TeamOperationSubjectModel teamOperationSubjectModel = new TeamOperationSubjectModel();
        teamOperationSubjectModel.setOperationSubjectIds(operSubjectIdList);
        log.info("Ipass model:{}", JSON.toJSONString(operSubjectIdList));
        IPaasFeignModel<StoreSubjectListVO> marketFeignStoreAllPage = iPaasFeign.findStoreByOperationSubjectId(teamOperationSubjectModel);
        log.info("Ipass marketFeignStoreAllPage:{}", JSON.toJSONString(marketFeignStoreAllPage));

        if (Objects.nonNull(marketFeignStoreAllPage) && CollUtil.isNotEmpty(marketFeignStoreAllPage.getDataList())) {
            for (StoreSubjectListVO storeQueryVO : marketFeignStoreAllPage.getDataList()) {
                //排除供应商
                if (StrUtil.isNotEmpty(storeQueryVO.getBusiness())) {
                    ShopBusinessVO shopBusinessVO = JSON.parseObject(storeQueryVO.getBusiness(), ShopBusinessVO.class);
                    if (shopBusinessVO != null && shopBusinessVO.getShopMode() != null && shopBusinessVO.getShopMode().equals(ShopMode.SUPPLIER)) {
                        continue;
                    }
                }

                StoreBaseInfo storeBaseInfo = getStoreBaseInfo(storeQueryVO);
                storeBaseInfoList.add(storeBaseInfo);
            }
        }
        return storeBaseInfoList;
    }

    @Override
    public AppIdRespDTO getAppId(CrmAppIdQueryDTO crmAppIdReqDTO) {
        AppIdRespDTO appIdRespDTO = new AppIdRespDTO();
        Result<WeChatConfigInfoVO> weChatConfigInfoVOResult = memberMallToolFeign.queryByOperSubjectGuid();
        log.info("getAppId:{}", JSON.toJSONString(weChatConfigInfoVOResult));

        if (Objects.isNull(weChatConfigInfoVOResult) || Objects.isNull(weChatConfigInfoVOResult.getData())) {
            return null;
        }
        WeChatConfigInfoVO weChatConfigInfoVO = weChatConfigInfoVOResult.getData();
        appIdRespDTO.setAppId(weChatConfigInfoVO.getAppId());
        appIdRespDTO.setAppsecret(weChatConfigInfoVO.getApplyPrivateKey());
        appIdRespDTO.setAppName(weChatConfigInfoVO.getAppName());
        appIdRespDTO.setAppLogo(weChatConfigInfoVO.getAppLogo());
        return appIdRespDTO;
    }

    @Override
    public ResAppletOrderCallBack appletOrderCallBack(AppletOrderCallBack query) {
        log.info("零售appletOrderCallBack:{}", JSON.toJSONString(query));
        return new ResAppletOrderCallBack();
    }

    @Override
    public List<StoreBaseInfo> getStoreByStrategyOrCommodity(AppletGrowthStoreQO query) {
        log.info("零售getStoreByStrategyOrCommodity:{}", JSON.toJSONString(query));
        return Collections.emptyList();
    }

    @Override
    public PaySettingBaseRes getPaySetting(PaySettingDTO paySettingDTO) {
        log.info("零售getPaySetting:{}", JSON.toJSONString(paySettingDTO));
        PaySettingBaseRes paySettingBaseRes = new PaySettingBaseRes();
        PaySettingVO paySettingVO = memberMallToolFeign.getPaySetting(paySettingDTO);
        log.info("getPaySetting:{}", JSON.toJSONString(paySettingVO));

        if (Objects.isNull(paySettingVO)) {
            return null;
        }
        paySettingBaseRes.setAppId(paySettingVO.getAppId());
        paySettingBaseRes.setAppSecret(paySettingVO.getApplyPrivateKey());
        paySettingBaseRes.setPayMerchantNum(paySettingVO.getPayMerchantNum());
        paySettingBaseRes.setPayMerchantKey(paySettingVO.getPayMerchantKey());
        return paySettingBaseRes;
    }

    @Override
    public int refundAggPayOrder(CrmRefundPayDTO crmRefundPayDTO) {
        log.info("零售refundAggPayOrder:{}", JSON.toJSONString(crmRefundPayDTO));
        return marketFeign.refundAggPayOrder(crmRefundPayDTO).getData();
    }

    @Override
    public void getReceiptPrinting(ReceiptPrintingBaseDTO receiptPrintingBaseDTO) {
        log.info("零售getReceiptPrinting:{}", JSON.toJSONString(receiptPrintingBaseDTO));
    }

    @Override
    public List<StoreInfoVO> listStoreStall(StoreInfoDTO storeInfoDTO) {
        log.info("零售listStoreStall:{}", JSON.toJSONString(storeInfoDTO));
        return Collections.emptyList();
    }

    @Override
    public CrmOperatingSubjectRespDTO getOperatingSubject(CrmOperatingSubjectQueryDTO subjectReqDTO) {
        log.info("零售getOperatingSubject:{}", JSON.toJSONString(subjectReqDTO));
        return new CrmOperatingSubjectRespDTO();
    }

    @Override
    public CrmOrderDetailVo getOrderDetail(CrmOrderDetailQo crmOrderDetailQo) {
        log.info("零售getOrderDetail:{}", JSON.toJSONString(crmOrderDetailQo));
        return new CrmOrderDetailVo();
    }

    @Override
    public List<ResOrderCommodity> queryOrderCommodity(QueryOrderCommodity queryOrderCommodity) {
        log.info("零售queryOrderCommodity:{}", JSON.toJSONString(queryOrderCommodity));
        return Collections.emptyList();
    }

    @Override
    public List<StoreInfoVO> getStoreStall(StoreInfoDTO query) {
        log.info("零售getStoreStall:{}", JSON.toJSONString(query));
        return Collections.emptyList();
    }

    @Override
    public Object getStoreById(StoreByIdQO storageByIdQuery) {
        log.info("零售getStoreById:{}", JSON.toJSONString(storageByIdQuery));

        List<Integer> integerIds = storageByIdQuery.getStore_ids().stream()
                .map(Long::intValue)
                .collect(Collectors.toList());
        IPaasFeignModel<StoreSubjectListVO> storeDetail = iPaasFeign.findStoreByTeamInfoIds(new TeamOperationStoreIdModel().setTeamInfoIds(integerIds));
        log.info("Ipass marketFeignStoreAllPage:{}", JSON.toJSONString(storeDetail));
        List<StoreBaseInfo> storeBaseInfoList = Lists.newArrayList();
        if (Objects.nonNull(storeDetail) && CollUtil.isNotEmpty(storeDetail.getDataList())) {
            for (StoreSubjectListVO storeQueryVO : storeDetail.getDataList()) {
                StoreBaseInfo storeBaseInfo = getStoreBaseInfo(storeQueryVO);
                storeBaseInfoList.add(storeBaseInfo);
            }
        }
        return storeBaseInfoList;
    }

    @Override
    public List<StoreInfoVO> queryStoreV2(QueryStoreBasePage query) {
        log.info("零售queryStoreV2:{}", JSON.toJSONString(query));
        return Collections.emptyList();
    }

    @Override
    public List<StoreBaseInfo> queryStore(QueryStoreBasePage queryStoreBasePage) {
        log.info("零售queryStore:{}", JSON.toJSONString(queryStoreBasePage));
        return Collections.emptyList();
    }

    @Override
    public JSONObject pushOrder(CrmOrderDTO crmOrderDTO) {
        log.info("零售pushOrder:{}", JSON.toJSONString(crmOrderDTO));
        return new JSONObject();
    }


    @Override
    public JSONObject pushOrderStock(CrmOrderStockDTO crmOrderStockDTO) {
        log.info("零售pushOrderStock:{}", JSON.toJSONString(crmOrderStockDTO));
        return new JSONObject();
    }

    @Override
    public List<StoreBaseInfo> queryStore(String name) {
        log.info("零售queryStore:{}", JSON.toJSONString(name));
        return listStoreAndStall(new QueryStoreBasePage());
    }

    @Override
    public List<OperSubjectVO> listOperSubjectAndApplet() {
        List<OperSubjectVO> operSubjectVOList = com.beust.jcommander.internal.Lists.newArrayList();
        //查询主体
        List<OperSubjectInfo> operSubjectInfoList = marketBaseServiceImpl.queryOperatingSubject(ThreadLocalCache.getEnterpriseGuid());
        if (CollUtil.isEmpty(operSubjectInfoList)) {
            return Collections.emptyList();
        }

        List<String> operSubjectGuidList = operSubjectInfoList.stream().map(OperSubjectInfo::getOperSubjectGuid).collect(Collectors.toList());

        //查询小程序配置appId
        Result<List<WeChatConfigInfoVO>> result = memberMallToolFeign.queryByOperSubjectGuidList(operSubjectGuidList);

        log.debug("result:{}", JSON.toJSONString(result));

        Map<String, WeChatConfigInfoVO> weChatConfigInfoVOMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(result.getData())) {
            List<WeChatConfigInfoVO> weChatConfigInfoVOS = result.getData();
            weChatConfigInfoVOMap = weChatConfigInfoVOS.stream().collect(Collectors.
                    toMap(WeChatConfigInfoVO::getOperSubjectGuid, Function.identity(), (entity1, entity2) -> entity1));
        }

        //遍历运营主体  并组装参数
        for (OperSubjectInfo info : operSubjectInfoList) {
            OperSubjectVO operSubjectVO = new OperSubjectVO();
            operSubjectVO.setOperation_id(info.getOperSubjectGuid());
            operSubjectVO.setOperation_name(info.getMultiMemberName());
            //根据HashMap匹配小程序配置
            if (CollUtil.isNotEmpty(weChatConfigInfoVOMap) && weChatConfigInfoVOMap.containsKey(info.getOperSubjectGuid())) {
                WeChatConfigInfoVO weChatConfigInfoVO = weChatConfigInfoVOMap.get(info.getOperSubjectGuid());
                operSubjectVO.setAppId(weChatConfigInfoVO.getAppId());
                operSubjectVO.setApp_secret(weChatConfigInfoVO.getApplyPrivateKey());
                operSubjectVO.setAuthorized_app(weChatConfigInfoVO.getAppName());
            }
            operSubjectVOList.add(operSubjectVO);
        }

        return operSubjectVOList;
    }

    @Override
    public List<QueryOrderDTO> queryOrder(QueryOrderCommodity queryOrderCommodity) {
        log.info("零售queryOrder:{}", JSON.toJSONString(queryOrderCommodity));
        return Collections.emptyList();
    }

    @Override
    public StoreCountDTO countStores() {
        List<StoreBaseInfo> stores = listStoreAndStall(new QueryStoreBasePage());
        int count = CollUtil.isEmpty(stores) ? 0 : stores.size();
        return new StoreCountDTO()
            .setRetailStoreCount(count)
            .setMallStoreCount(0)
            .setTotalCount(count);
    }
}
