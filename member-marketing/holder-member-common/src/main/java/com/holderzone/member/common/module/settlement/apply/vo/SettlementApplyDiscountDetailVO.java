package com.holderzone.member.common.module.settlement.apply.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * 优惠列表返回基类
 * <p>
 * 规定了必须返回的字段
 * <p>
 * JsonTypeInfo.Id.CLASS feign 序列化子类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyDiscountDetailVO implements Serializable {


    private static final long serialVersionUID = -915990565314720267L;
    /**
     * 是否选中(默认否)
     */
    @ApiModelProperty("是否选中")
    private Integer isChecked = BooleanEnum.FALSE.getCode();

    /**
     * 是否叠加(默认否)
     * (是否互斥：叠加（不互斥），不叠加（互斥）)
     */
    @ApiModelProperty("是否叠加")
    private Integer isAppend = BooleanEnum.FALSE.getCode();

    /**
     * 是否可用(默认否)
     */
    @ApiModelProperty("是否可用")
    private Integer isEnabled = BooleanEnum.TRUE.getCode();

    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    @ApiModelProperty(value = "优惠类型")
    private Integer discountOption;

    /**
     * 优惠guid，必须
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;
    /**
     * 优惠项的主键
     * <p>
     * eg: 会员已领优惠券id
     */
    @ApiModelProperty("优惠项的主键")
    private String discountOptionId;

    /**
     * 显示名称
     */
    @ApiModelProperty("显示名称")
    private String discountName;

    /**
     * 实际优惠金额
     */
    @ApiModelProperty("实际优惠金额")
    private BigDecimal discountAmount;

    /**
     * 最优标识：0 否 1是
     */
    private int optimal = 0;

    /**
     * 商品
     */
    @ApiModelProperty("商品明细")
    private List<SettlementApplyCommodityVO> commodityList;

    private String applyCommodityJson;

    private Integer applyCommodity;

    /**
     * 使用次数(兑换券 前端传递)
     */
    private Integer requiresTimes;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 共享互斥关系 0互斥 1共享
     */
    private Integer shareRelation;

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount == null ? BigDecimal.ZERO : discountAmount;

        if (Objects.nonNull(this.discountOption)
                && (this.discountOption == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode()
                || this.discountOption == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode()
                || this.discountOption == SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode())) {
            return;
        }

        //优惠为0，不可用
        if (this.discountAmount.compareTo(BigDecimal.ZERO) == 0) {
            this.isEnabled = BooleanEnum.FALSE.getCode();
        }
    }
}
