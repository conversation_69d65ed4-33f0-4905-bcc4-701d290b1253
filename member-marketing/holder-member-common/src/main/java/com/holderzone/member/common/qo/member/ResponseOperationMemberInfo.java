package com.holderzone.member.common.qo.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 运营主体会员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@Data
@ApiModel(
        value = "ResponseOperationMemberInfo",
        description = "运营主体或联盟会员信息"
)
public class ResponseOperationMemberInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序号")
    private String id;

    @ApiModelProperty(value = "会员或顾客GUID")
    private String guid;

    @ApiModelProperty(value = "会员或顾客账号，运营主体账号以OXXX123456789012，前缀不足补充在后面")
    private String memberAccount;

    @ApiModelProperty(value = "会员或顾客姓名")
    private String userName;

    @ApiModelProperty(value = "用户等级guid")
    private String memberInfoGradeGuid;

    @ApiModelProperty(value = "用户等级Name")
    private String memberGradeName;

    @ApiModelProperty(value = "用户手动标签名")
    private String memberTagName;

    @ApiModelProperty(value = "性别，值为1时是男性，值为2时是女性，值为0时是未知")
    private Integer sex;

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    @ApiModelProperty(value = "手机号码")
    private String phoneNum;

    @ApiModelProperty(value = "会员卡名字")
    private String memberInfoCardName;

    @ApiModelProperty(value = "注册时间")
    private LocalDate gmtCreate;

    @ApiModelProperty(value = "会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)")
    private Integer sourceType;

    @ApiModelProperty(value = "注册来源名称")
    private String sourceTypeName;

    @ApiModelProperty(value = "会员主卡等级名字")
    private String operationMemberInfoCardLevelName;

    @ApiModelProperty(value = "账号状态,0正常,1冻结")
    private Boolean accountState;

    /**
     * 会员卡
     */
    private String memberCard;

    /**
     * 会员成长值
     */
    private Integer memberGrowthValue;

    /**
     * 会员标签
     */
    private String memberLabel;
}
