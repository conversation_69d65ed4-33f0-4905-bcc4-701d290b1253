package com.holderzone.member.common.dto.redeem;

import com.holderzone.member.common.qo.redeem.RespondEditActiveVO;
import com.holderzone.member.common.vo.card.CardBaseInfoVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 优惠兑换码活动
 */
@Data
@Accessors(chain = true)
public class RequestRedeemApplyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 兑换的卡片信息
     */
    private CardBaseInfoVO cardBaseInfoVO;

    /**
     * 会员卡guid
     */
    private String cardGuid;

    private String memberInfoCardGuid;


    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * orderGuid
     */
    private String orderGuid;

    /**
     * 兑换活动
     */
    private RespondEditActiveVO hsaRedeemCodeActive;

    private String dtlGuid;
}
