package com.holderzone.member.common.vo.nth;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 第N份优惠活动返回
 */
@Data
public class NthActivityVO implements Serializable {

    private static final long serialVersionUID = 5048507497440468615L;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String guid;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityCode;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 活动场景
     */
    @ApiModelProperty(value = "活动场景")
    private String activityBusiness;


    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    /**
     * 活动商品
     */
    @ApiModelProperty(value = "活动商品")
    private Long activityItem;

    /**
     * 活动订单
     */
    @ApiModelProperty(value = "活动订单")
    private Long activityOrder;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态")
    private Integer state;
}
