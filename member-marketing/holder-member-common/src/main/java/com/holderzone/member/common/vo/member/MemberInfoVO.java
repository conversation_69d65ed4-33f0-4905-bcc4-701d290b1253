package com.holderzone.member.common.vo.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ProjectName: member-marketing
 * @ClassName: MemberInfoVO
 * @Author: pantao
 * @Description: 会员列表返回参数
 * @Date: 2021/8/12 17:04
 * @Version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MemberInfoVO {

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 账号状态,0正常,1冻结
     */
    private Integer accountState;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员账号
     */
    private String memberAccount;

    /**
     * 会员姓名
     */
    private String userName;

    /**
     * 性别
     */
    private int sex;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 手机号区号
     */
    private String phoneCountryCode;

    /**
     * 头像
     */
    private String headImgUrl;

    /**
     * 生日
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime birthday;

    /**
     * 会员成长值
     */
    private Integer memberGrowthValue;


    /**
     * 会员等级
     */
    private String memberLevel;

    /**
     * 会员付费等级名称
     */
    private String memberPaidLevel;

    /**
     * 会员卡
     */
    private String memberCard;

    /**
     * 会员标签
     */
    private String memberLabel;

    /**
     * 最近消费时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private LocalDate lastConsumptionTime;

    /**
     * 累积消费金额
     */
    private BigDecimal consumptionMoney;

    /**
     * 累积消费次数
     */
    private Integer consumptionCount;

    /**
     * 累积充值次数
     */
    private Integer rechargeCount;

    /**
     * 累积充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 当前积分
     */
    private Integer memberIntegral;

    /**
     * 累计积分
     */
    private Integer totalMemberIntegral;

    /**
     * 账户余额
     */
    private BigDecimal accountMoney;

    /**
     * 累积账户余额
     */
    private BigDecimal totalAccountMoney;

    /**
     * 注册时间
     */
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 注册渠道
     */
    private Integer sourceType;

    /**
     * 注册渠道名称
     */
    private String sourceTypeName;

    /**
     * 归属门店
     */
    private String belongStore;

    /**
     * 角色类型
     *
     * @see com.holderzone.member.common.enums.member.RoleTypeEnum
     */
    private String roleType;
}
