package com.holderzone.member.common.qo.redeem;

import com.holderzone.member.common.vo.card.RedeemOwnCardVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠兑换码活动
 */
@Data
@Accessors(chain = true)
public class RequestCheckRedeemApplyQO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("兑换码")
    private String redeemCode;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    @ApiModelProperty("会员guid")
    private String memberGuid;

    @ApiModelProperty("memberName")
    private String memberName;

    @ApiModelProperty("memberPhone")
    private String memberPhone;

    @ApiModelProperty("会员等级信息")
    private String memberGradeGuid;

    @ApiModelProperty("会员卡信息")
    private List<RedeemOwnCardVO> redeemOwnCardList;

    @ApiModelProperty("会员标签信息")
    private List<String> memberLabelGuidList;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名称")
    private String storeName;

}
