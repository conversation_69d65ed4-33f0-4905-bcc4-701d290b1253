package com.holderzone.member.common.vo.card;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class LoginMemberCardVO implements Serializable {

    /**
     * 会员持卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 实体卡金额
     */
    private BigDecimal cardAmount;

    /**
     * 卡guid
     */
    private String cardGuid;

    /**
     * 顾客姓名
     */
    private String userName;

    /**
     * 顾客昵称
     */
    private String nickName;

    /**
     * 卡号
     */
    private String cardNum;

    /**
     * 卡类型 0实体卡 1电子卡
     */
    private Integer cardType;

    /**
     * 卡状态 -1 未激活  0已冻结 1 正常 2 已过期 3 未激活->已冻结 4 基础卡禁用
     */
    private Integer cardState;

    /**
     * 卡名
     */
    private String cardName;

    /**
     * 卡颜色
     */
    private String cardColor;

    /**
     * 是否是默认卡 1：是
     */
    private Integer defaultCard;

    private String memberPhoneNum;

    /**
     * 充值说明
     */
    private String cardRechargeExplain;

    /**
     * 是否需要密码 0 不校验 1 校验密码
     */
    private Integer isCheckPassword;
    /**
     * 是否开启充值功能
     */
    private Integer isPreStored;

    /**
     * 小程序充值功能
     */
    private Integer appletRecharge;


    /**
     * 是否开启任意充值
     */
    private Integer isRechargeAny;

    /**
     * 是否开启固定充值
     */
    private Integer isRechargeStipulate;

    /**
     * 固定充值金额列表(JSON)
     */
    private List<String> rechargeMoneys;

    /**
     * 充值限制（0不限制，1限制）
     */
    private Integer isRechargeUpperLimit;

    /**
     * 单次最少充值
     */
    private BigDecimal tinyRechargeMoney;

    /**
     * 单次最大充值
     */
    private BigDecimal maxRechargeMoney;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 是否支持超额
     */
    private Integer isExcess;

    /**
     * 超额类型 0 次数 1 金额
     */
    private Integer excessType;

    /**
     * 剩余可超金额
     */
    private BigDecimal excessAmount;

    /**
     * 剩余可超次数
     */
    private Integer excessTimes;

    /**
     * 范围
     */
    private List<StoreCardRuleVO> storeCardRuleVO = new ArrayList<>();

    /**
     * 充值提示
     */
    private String rechargeTips;

    private BigDecimal freezeAmount;

    /**
     *
     * 0永久有效1固定时间
     */
    private Integer cardValidity;

    /**
     * 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate cardValidityDate;

    /**
     * 是否选中
     */
    private Integer isOpt;

}
