package com.holderzone.member.common.module.settlement.rule.enums;

import cn.hutool.core.collection.CollUtil;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountVO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyOrderVO;

import cn.hutool.core.lang.Console;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算台优惠具体项
 * （最底层的类型，后端区分）
 *
 * <AUTHOR>
 * @create 2023-09-02
 * @description 结算台优惠类型
 * 优惠项大类 {@link SettlementDiscountItemEnum}
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountOptionEnum {

    NONE(0, "无", null, null, 0),
    /**
     * 商品会员价
     */
    MEMBER_PRICE(1, "商品会员价权益", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 会员折扣权益
     */
    MEMBER_DISCOUNT(2, "会员折扣权益", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 会员卡折扣权益
     */
    MEMBER_CARD_DISCOUNT(3, "会员卡折扣权益", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 代金券
     */
    COUPON_VOUCHER(10, "代金券", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.COUPON_VOUCHER, 0),
    /**
     * 折扣券
     */
    COUPON_DISCOUNT(11, "折扣券", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.COUPON_DISCOUNT, 0),
    /**
     * 兑换券
     */
    COUPON_EXCHANGE(12, "兑换券", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.COUPON_EXCHANGE, 0),
    /**
     * 满减满折
     */
    FULL_OFF(20, "满减满折", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.FULL_OFF, 1),
    /**
     * 限时特价
     */
    LIMITED_TIME_SPECIAL(22, "限时特价", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.LIMITED_TIME_SPECIAL, 1),
    /**
     * 第N份优惠
     */
    NTH_DISCOUNT(23, "第N份优惠", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.NTH_DISCOUNT, 1),
    /**
     * 积分兑换
     */
    INTEGRAL_EXPLAIN(91, "积分抵现", SettlementDiscountTypeEnum.PROPERTY, SettlementDiscountItemEnum.ASSET_PREFERENCE, 1);

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 类型
     */
    private final SettlementDiscountTypeEnum type;

    /**
     * 优惠项
     */
    private final SettlementDiscountItemEnum item;

    /**
     * 单笔限用数量
     */
    private final int limitNum;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */

    public static String getDesByCode(int code) {
        for (SettlementDiscountOptionEnum anEnum : SettlementDiscountOptionEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum.getDes();
            }
        }
        return null;
    }

    public static SettlementDiscountOptionEnum getEnum(Integer code) {
        for (SettlementDiscountOptionEnum anEnum : SettlementDiscountOptionEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        return NONE;
    }

    /**
     * 需要初始化的父级
     *
     * @return
     */
    public static List<SettlementDiscountOptionEnum> initValues() {
//        final List<SettlementDiscountOptionEnum> optionEnums = Arrays.stream(SettlementDiscountOptionEnum.values())
        //todo 暂时只初始化这三个
        final List<SettlementDiscountOptionEnum> optionEnums = Lists.newArrayList(
                        NONE,
                        MEMBER_DISCOUNT,
                        COUPON_VOUCHER,
                        COUPON_DISCOUNT,
                        COUPON_EXCHANGE,
                        INTEGRAL_EXPLAIN,
                        LIMITED_TIME_SPECIAL,
                        FULL_OFF,
                        NTH_DISCOUNT).stream()
                .filter(e -> !noChildren(e))
                .collect(Collectors.toList());
        //移除第一个none
        optionEnums.remove(0);
        return optionEnums;
    }


    public static boolean noShowDiscountGuid(Integer option) {
        return noShowDiscountGuid().contains(option);
    }

    /**
     * 前端不显示discountGuid
     * 商品会员价、会员折扣、积分抵现
     *
     * @return
     */
    public static List<Integer> noShowDiscountGuid() {
        return Lists.newArrayList(
                MEMBER_PRICE.code,
                MEMBER_CARD_DISCOUNT.code,
                INTEGRAL_EXPLAIN.code
        );
    }

    /**
     * 无子集的项
     *
     * @param optionEnum 类型
     * @return
     */
    public static boolean noChildren(SettlementDiscountOptionEnum optionEnum) {
        return noChildren(optionEnum.code);
    }

    /**
     * 老的实现方法，没有直接修改入参dto
     * 折扣、积分
     *
     * @return 是否
     */
    public static boolean oldImpl(int option) {
        return option == MEMBER_DISCOUNT.code || option == INTEGRAL_EXPLAIN.code;
    }

    /**
     * 老的实现方法类型
     *
     * @return 老的类型
     */
    public static List<Integer> oldImpl() {
        //积分只对接了商城，按新的来算, INTEGRAL_EXPLAIN.code
        return Lists.newArrayList(MEMBER_DISCOUNT.code, INTEGRAL_EXPLAIN.code);
    }

    /**
     * 限时特价类型
     *
     * @param option 类型
     * @return
     */
    public static boolean isCoupon(int option) {
        return COUPON_VOUCHER.code == option
                || COUPON_DISCOUNT.code == option
                || COUPON_EXCHANGE.code == option;
    }

    /**
     * 单品级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isSingleDiscount(int option) {
        return LIMITED_TIME_SPECIAL.code == option
                || MEMBER_PRICE.code == option
                || COUPON_EXCHANGE.code == option;
    }

    /**
     * 单品级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isSingleDiscount(List<Integer> option) {
        return option.contains(LIMITED_TIME_SPECIAL.code)
                || option.contains(MEMBER_PRICE.code)
                || option.contains(COUPON_EXCHANGE.code);
    }


    /**
     * 订单级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isOrderDiscount(List<Integer> option) {
        return option.contains(MEMBER_DISCOUNT.code)
                || option.contains(MEMBER_CARD_DISCOUNT.code)
                || option.contains(COUPON_VOUCHER.code)
                || option.contains(COUPON_DISCOUNT.code)
                || option.contains(FULL_OFF.code);
    }

    /**
     * 订单级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isOrderDiscount(int option) {
        return MEMBER_DISCOUNT.code == option
                || MEMBER_CARD_DISCOUNT.code == option
                || COUPON_VOUCHER.code == option
                || COUPON_DISCOUNT.code == option
                || FULL_OFF.code == option;
    }

    /**
     * 是否是折扣券优惠
     *
     * @param option 类型
     * @return
     */
    public static boolean isOrderCouponDiscount(int option) {
        return COUPON_DISCOUNT.code == option;
    }

    /**
     * 是否代金券优惠
     *
     * @param option 类型
     * @return
     */
    public static boolean isCouponVoucher(int option) {
        return COUPON_VOUCHER.code == option;
    }

    /**
     * 资产级类型
     *
     * @param option 类型
     * @return
     */
    public static boolean isPropertyDiscount(int option) {
        return INTEGRAL_EXPLAIN.code == option;
    }

    /**
     * 资产级类型
     *
     * @param option 类型
     * @return
     */
    public static boolean isPropertyDiscount(List<Integer> option) {
        return option.contains(INTEGRAL_EXPLAIN.code);
    }

    /**
     * 优惠券类型
     *
     * @return
     */
    public static List<Integer> getCoupon() {
        return Lists.newArrayList(
                COUPON_VOUCHER.code,
                COUPON_DISCOUNT.code,
                COUPON_EXCHANGE.code);
    }

    /**
     * 目前实现了的优惠
     *
     * @param option
     * @return
     */
    public static boolean isImplOption(int option) {
        return implOptions().contains(option);
    }

    /**
     * todo 目前实现了的优惠
     *
     * @return
     */
    public static List<Integer> implOptions() {
        return Lists.newArrayList(
                SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode(),
                SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode(),
                SettlementDiscountOptionEnum.MEMBER_CARD_DISCOUNT.getCode()
                , SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode()
                , SettlementDiscountOptionEnum.COUPON_DISCOUNT.getCode()
                , SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode()
                , SettlementDiscountOptionEnum.FULL_OFF.getCode()
        );
    }

    /**
     * 是否存在子级
     * 商品会员价、积分抵现只有第一层
     *
     * @param option 类型
     * @return
     */
    public static boolean noChildren(Integer option) {
        return option.equals(MEMBER_PRICE.code);
    }

    /**
     * 优惠类型key ，用来确定唯一
     *
     * @param discountOption 类型
     * @param discountGuid   类型guid
     * @return
     */
    public static String optionMapKey(Integer discountOption, String discountGuid) {
        return discountOption + StringConstant.COLON + discountGuid;
    }

    /**
     * 优惠类型key ，用来确定唯一
     *
     * @param discountOption 类型
     * @param discountGuid   类型guid
     * @param discountId     具体类型的数据（优惠券guid）
     * @return
     */
    public static String optionMapKey(Integer discountOption, String discountGuid, String discountId) {
        return discountOption + StringConstant.COLON + discountGuid + StringConstant.COLON + discountId;
    }

    public static List<Integer> getShareOptionCode(SettlementApplyOrderDTO dto,
                                                   Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                   SettlementDiscountOptionEnum optionEnum) {
        List<Integer> settlementDiscountOptionEnumList = Lists.newArrayList();

        switch (optionEnum) {
            case LIMITED_TIME_SPECIAL:
                Console.log("限时优惠");
                addIfShareable(dto, discountVoMap, FULL_OFF, settlementDiscountOptionEnumList);
                addIfShareable(dto, discountVoMap, INTEGRAL_EXPLAIN, settlementDiscountOptionEnumList);
                break;
                
            case MEMBER_DISCOUNT:
                Console.log("会员折扣");
                addIfShareable(dto, discountVoMap, INTEGRAL_EXPLAIN, settlementDiscountOptionEnumList);
                addIfShareable(dto, discountVoMap, FULL_OFF, settlementDiscountOptionEnumList);
                break;

            case FULL_OFF:
                Console.log("满减满折");
                addIfShareable(dto, discountVoMap, MEMBER_DISCOUNT, settlementDiscountOptionEnumList);
                addIfShareable(dto, discountVoMap, INTEGRAL_EXPLAIN, settlementDiscountOptionEnumList);
                break;
                
            default:
                // 其他类型暂不支持叠加
                break;
        }
        
        return settlementDiscountOptionEnumList;
    }

    /**
     * 如果指定优惠类型可以叠加，则添加到结果列表中
     */
    private static void addIfShareable(SettlementApplyOrderDTO dto,
                                      Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                      SettlementDiscountOptionEnum targetOption,
                                      List<Integer> resultList) {
        if (isaDiscountShare(dto, discountVoMap, targetOption)) {
            Console.log("优惠类型可以叠加：" + targetOption.getDes());
            resultList.add(targetOption.getCode());
        }
    }

    private static boolean isaDiscountShare(SettlementApplyOrderDTO dto,
                                            Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                            SettlementDiscountOptionEnum optionEnum) {
        return isDiscountOptionShare(dto, discountVoMap, optionEnum);
    }

    private static boolean isDiscountOptionShare(SettlementApplyOrderDTO dto,
                                                 Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                 SettlementDiscountOptionEnum optionEnum) {
        return isProperty(dto, discountVoMap, optionEnum);
    }

    private static boolean isProperty(SettlementApplyOrderDTO dto,
                                      Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                      SettlementDiscountOptionEnum optionEnum) {
        return discountVoMap.containsKey(optionEnum.getCode())
                && isCheckedByOption(dto, discountVoMap, optionEnum.getCode());
    }

    /**
     * 校验当前优惠项是否叠加
     */
    private static boolean isCheckedByOption(
            SettlementApplyOrderDTO dto,
            Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
            Integer code) {
        List<SettlementApplyOrderVO> selectOrderVOList = discountVoMap.get(code);

        if (CollUtil.isNotEmpty(selectOrderVOList)) {
            SettlementApplyOrderVO integralTempOrder = selectOrderVOList.get(NumberConstant.NUMBER_0);
            if (CollUtil.isNotEmpty(integralTempOrder.getDiscountList())) {
                SettlementApplyDiscountVO discountVO = integralTempOrder.getDiscountList().get(NumberConstant.NUMBER_0);
                String discountGuid = discountVO.getDiscountList().get(NumberConstant.NUMBER_0).getDiscountGuid();
                return isCheckShare(discountGuid, dto, code);
            }
        }
        return false;
    }

    public static boolean isCheckShare(String discountGuid, SettlementApplyOrderDTO dto, Integer code) {
        //判断当前优惠是否叠加
        final Map<Integer, Set<String>> appendMap = dto.getAppendMap();
        final Set<String> apendSet = appendMap.get(code);
        return CollUtil.isNotEmpty(apendSet) && apendSet.contains(discountGuid);
    }
}
