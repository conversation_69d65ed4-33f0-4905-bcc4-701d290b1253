package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.cloud.OperSubjectCloudQO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemConfigVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 会员通-系统终端配置Controller
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@RestController
@RequestMapping("/member_unilink_system_config")
public class HsaMemberUnilinkSystemConfigController {

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    /**
     * 查询系统配置（终端、渠道、业务）列表
     *
     * @return 系统终端列表
     */
    @GetMapping("/listConfig")
    public Result<MemberUnilinkSystemConfigVO> listConfig() {
        return memberMallToolFeign.listConfig();
    }


    /**
     * 餐饮云授权
     */
    @PostMapping("/cloudAuth")
    public Result<Void> cloudAuth(@RequestBody OperSubjectCloudQO operSubjectCloudQO) {
        return memberMallToolFeign.cloudAuth(operSubjectCloudQO);
    }

    /**
     * 餐饮云取消授权
     */
    @PostMapping("/cloudCancelAuth")
    public Result<Void> cloudCancelAuth(@RequestBody OperSubjectCloudQO operSubjectCloudQO) {
        return memberMallToolFeign.cloudCancelAuth(operSubjectCloudQO);
    }
} 