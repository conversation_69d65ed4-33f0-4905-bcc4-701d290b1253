<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper">


    <select id="findCardGuidByGuidList" resultType="com.holderzone.member.common.dto.card.MemberCardGuidDTO">
        SELECT card_guid as cardGuid,
        electronic_card_guid as electronicCardGuid,
        physical_card_guid as physicalCardGuid
        FROM hsa_member_info_card
        where 1=1
        <if test="cardGuidList != null and cardGuidList.size() > 0">
            and card_guid in
            <foreach collection="cardGuidList" item="guid" open="(" close=")" separator=",">
                #{guid}
            </foreach>
        </if>
        <if test="memberGuidList != null and memberGuidList.size() > 0">
            and member_info_guid in
            <foreach collection="memberGuidList" item="memberGuid" open="(" close=")" separator=",">
                #{memberGuid}
            </foreach>
        </if>
    </select>

    <select id="findEntityCardGuidByGuidList" resultType="String">
        SELECT member_info_guid as memberInfoGuid
        FROM hsa_member_info_card
        where 1=1
        <if test="cardGuidList != null and cardGuidList.size() > 0">
            and card_guid in
            <foreach collection="cardGuidList" item="guid" open="(" close=")" separator=",">
                #{guid}
            </foreach>
        </if>
        <if test="memberGuidList != null and memberGuidList.size() > 0">
            and member_info_guid in
            <foreach collection="memberGuidList" item="memberGuid" open="(" close=")" separator=",">
                #{memberGuid}
            </foreach>
        </if>
    </select>


    <update id="updatePhysicalCardBangding">
        update hsa_member_info_card
        set physical_card_guid = #{physicalCardGuid}
        where guid = #{guid}
    </update>

    <update id="updateByCardGuidList" parameterType="java.util.List">
        update hsa_member_info_card
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="physical_card_guid = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when card_guid = #{item.cardGuid} and member_info_guid = #{item.memberInfoGuid}
                    then #{item.physicalCardGuid}
                </foreach>
            </trim>
            <trim prefix="card_validity = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when card_guid = #{item.cardGuid} and member_info_guid = #{item.memberInfoGuid}
                    then #{item.cardValidity}
                </foreach>
            </trim>
            <trim prefix="card_validity_date = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when card_guid = #{item.cardGuid} and member_info_guid = #{item.memberInfoGuid}
                    then #{item.cardValidityDate}
                </foreach>
            </trim>
            <trim prefix="physical_card_num = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when card_guid = #{item.cardGuid} and member_info_guid = #{item.memberInfoGuid}
                    then #{item.physicalCardNum}
                </foreach>
            </trim>
            <trim prefix="physical_card_state = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when card_guid = #{item.cardGuid} and member_info_guid = #{item.memberInfoGuid}
                    then #{item.physicalCardState}
                </foreach>
            </trim>
            <trim prefix="card_employ_explain = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when card_guid = #{item.cardGuid} and member_info_guid = #{item.memberInfoGuid}
                    then #{item.cardEmployExplain}
                </foreach>
            </trim>
            <trim prefix="card_amount = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when card_guid = #{item.cardGuid} and member_info_guid = #{item.memberInfoGuid}
                    then (IFNULL(card_amount,0) + #{item.cardAmount})
                </foreach>
            </trim>
        </trim>
        where card_guid in
        <foreach collection="updateList" index="index" item="item" separator="," open="(" close=")">
            #{item.cardGuid}
        </foreach>
        and member_info_guid in
        <foreach collection="updateList" index="index" item="item" separator="," open="(" close=")">
            #{item.memberInfoGuid}
        </foreach>
    </update>


    <update id="updateCardAmountByCardGuidList">
        update hsa_member_info_card
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="card_amount = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when member_phone_num = #{item.phoneNum}
                    then #{item.rechargeAmount}
                </foreach>
            </trim>
        </trim>
        where member_phone_num in
        <foreach collection="updateList" index="index" item="item" separator="," open="(" close=")">
            #{item.phoneNum}
        </foreach>
        and oper_subject_guid = #{operSubjectGuid}

    </update>

    <update id="updateElectronicCardBind" parameterType="java.util.List">
        update hsa_member_info_card hc,hsa_card_base_info hi
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hc.electronic_card_guid = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when hc.card_guid = #{item.cardGuid} and hc.member_info_guid = #{item.memberInfoGuid}
                    then #{item.guid}
                </foreach>
            </trim>

            <trim prefix="hc.card_amount = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.isValue == false">
                        when hc.card_guid = #{item.cardGuid} and hc.member_info_guid = #{item.memberInfoGuid}
                        then (IFNULL(hi.card_value_money,0) + hc.card_amount)
                    </if>
                </foreach>
            </trim>
            <trim prefix="hc.card_validity = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when hc.card_guid = #{item.cardGuid} and hc.member_info_guid = #{item.memberInfoGuid}
                    then #{item.cardValidity}
                </foreach>
            </trim>
            <trim prefix="hc.card_validity_date = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when hc.card_guid = #{item.cardGuid} and hc.member_info_guid = #{item.memberInfoGuid}
                    then #{item.cardValidityDate}
                </foreach>
            </trim>
            <trim prefix="hc.electronic_card_num = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when hc.card_guid = #{item.cardGuid} and hc.member_info_guid = #{item.memberInfoGuid}
                    then #{item.cardNum}
                </foreach>
            </trim>
            <trim prefix="hc.electronic_card_state = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when hc.card_guid = #{item.cardGuid} and hc.member_info_guid = #{item.memberInfoGuid}
                    then #{item.cardState}
                </foreach>
            </trim>
            <trim prefix="hc.electronic_open_time = case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    when hc.card_guid = #{item.cardGuid} and hc.member_info_guid = #{item.memberInfoGuid}
                    then #{item.gmtCreate}
                </foreach>
            </trim>
        </trim>
        where
        hc.card_guid = hi.guid
        and hc.card_guid in
        <foreach collection="updateList" index="index" item="item" separator="," open="(" close=")">
            #{item.cardGuid}
        </foreach>
        and hc.member_info_guid in
        <foreach collection="updateList" index="index" item="item" separator="," open="(" close=")">
            #{item.memberInfoGuid}
        </foreach>
    </update>

    <select id="listAbleECard" resultType="com.holderzone.member.common.vo.card.AbleECardVO">
        select hi.guid as cardGuid,hi.card_name as cardName,
        CASE
        hi.card_validity
        WHEN 0 THEN
        '永久有效'
        WHEN 1 THEN
        concat( '领取后', hi.card_validity_time,
        CASE hi.validity_unit WHEN 1 THEN '日'
        WHEN 2 THEN '周'
        WHEN 3 THEN '月'
        WHEN 4 THEN '年'
        ELSE '日' END)
        WHEN 2 THEN
        CONCAT( DATE_FORMAT( NOW( ), '%Y-%m-%d' ), '至', DATE_FORMAT( hi.card_validity_date, '%Y-%m-%d' ) ) ELSE '永久有效'
        END AS periodOfValidity
        from hsa_card_base_info hi join hsa_card_open_rule hr on hi.guid =hr.card_guid
        where hi.card_status = 2
        AND (CASE hr.send_count_limit WHEN 0 THEN 1 = 1 ELSE hr.surplus_send_open_count_limit > 0 END)
        and hr.is_support_electronic_card = 1
        and hi.oper_subject_guid = #{operSubjectGuid}
        <if test="enterpriseGuid != null and enterpriseGuid != ''">
            and hi.enterprise_guid = #{enterpriseGuid}
        </if>
        <if test="cardName != null and cardName != ''">
            and hi.card_name like concat('%',#{cardName},'%')
        </if>
        and hi.guid not in (
        select card_guid from hsa_member_info_card
        where member_info_guid = #{memberInfoGuid}
        and electronic_card_guid is not null
        )
        order by hi.gmt_modified desc
    </select>

    <select id="findMiniProgramCardDetail" resultType="com.holderzone.member.common.vo.card.MiniProgramCardDetailVO">
        select CASE
                   hi.card_validity
                   WHEN 0 THEN
                       '永久有效'
                   WHEN 1 THEN
                       concat('领取后', hi.card_validity_time,
                              CASE hi.validity_unit
                                  WHEN 1 THEN '日'
                                  WHEN 2 THEN '周'
                                  WHEN 3 THEN '月'
                                  WHEN 4 THEN '年'
                                  ELSE '日' END)
                   WHEN 2 THEN
                       CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), '至', DATE_FORMAT(hi.card_validity_date, '%Y-%m-%d'))
                   ELSE '永久有效'
                   END AS periodOfValidity,
               hi.card_employ_explain,
               hi.card_name,
               hi.card_value_money,
               hr.self_type,
               hr.self_payment_money,
               hr.self_recharge_money,
               hr.electronic_open_way,
               hi.card_status
        FROM hsa_card_base_info hi
                 JOIN hsa_card_open_rule hr on hi.guid = hr.card_guid
        where hi.guid = #{guid}
    </select>

    <select id="findUnRelationMiniProgramCard" resultType="com.holderzone.member.common.qo.card.MiniProgramCardDTO">
        select CASE
                   hi.card_validity
                   WHEN 0 THEN
                       '永久有效'
                   WHEN 1 THEN
                       concat('领取后', hi.card_validity_time,
                              CASE hi.validity_unit
                                  WHEN 1 THEN '日'
                                  WHEN 2 THEN '周'
                                  WHEN 3 THEN '月'
                                  WHEN 4 THEN '年'
                                  ELSE '日' END)
                   WHEN 2 THEN
                       CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), '至', DATE_FORMAT(hi.card_validity_date, '%Y-%m-%d'))
                   ELSE '永久有效'
                   END AS periodOfValidity,
               hi.guid AS cardGuid,
               hi.card_name,
               hi.card_color,
               hi.card_image,
               hi.qr_code,
               hi.card_status,
               hr.open_card_time_type,
               hr.self_type,
               hr.self_payment_money,
               hr.self_recharge_money,
               hr.send_status,
               hi.card_value_money,
               hi.card_employ_explain,
               hr.electronic_open_way,
               hi.card_validity,
               hi.card_validity_time,
               hi.card_validity_date,
               hi.validity_unit,
               hi.applicable_all_store
        FROM hsa_card_base_info hi
                 JOIN hsa_card_open_rule hr ON hr.card_guid = hi.guid
        WHERE hi.oper_subject_guid = #{qo.operSubjectGuid}
          AND hr.is_support_electronic_card = 1
          AND (CASE hr.send_count_limit WHEN 0 THEN 1 = 1 ELSE hr.surplus_send_open_count_limit > 0 END)
          AND hi.card_status = 2
          AND hr.send_status = 2
          AND open_card_path LIKE #{qo.sendChannel}
          AND hr.electronic_open_way IN (0, 3)
          AND hi.guid NOT IN (SELECT card_guid
                              FROM hsa_member_info_card
                              WHERE oper_subject_guid = #{qo.operSubjectGuid}
                                AND member_info_guid = #{qo.memberInfoGuid}
                                AND electronic_card_guid IS NOT NULL)
        ORDER BY hi.gmt_create DESC
    </select>

    <select id="findUnOpenMaxFaceValue" resultType="java.math.BigDecimal">
        select max(hi.card_value_money)
        FROM hsa_card_base_info hi
                 JOIN hsa_card_open_rule hr ON hr.card_guid = hi.guid
        WHERE hi.oper_subject_guid = #{operSubjectGuid}
          AND hr.is_support_electronic_card = 1
          AND (CASE hr.send_count_limit WHEN 0 THEN 1 = 1 ELSE hr.surplus_send_open_count_limit > 0 END)
          AND hi.card_status = 2
          AND hr.send_status = 2
          AND open_card_path LIKE #{sendChannel}
          AND hr.electronic_open_way IN (0, 3)
    </select>


    <select id="findMiniProgramCard" resultType="com.holderzone.member.common.qo.card.MiniProgramCardDTO">
        select
        CASE
        hi.card_validity
        WHEN 0 THEN
        '永久有效'
        WHEN 1 THEN
        concat('领取后', hi.card_validity_time,
        CASE hi.validity_unit
        WHEN 1 THEN '日'
        WHEN 2 THEN '周'
        WHEN 3 THEN '月'
        WHEN 4 THEN '年'
        ELSE '日' END)
        WHEN 2 THEN
        CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), '至', DATE_FORMAT(hi.card_validity_date, '%Y-%m-%d'))
        ELSE '永久有效'
        END AS periodOfValidity,
        hc.card_name,
        hc.card_color,
        hc.card_image,
        hi.qr_code,
        hi.card_value_money,
        hi.card_status,
        hi.guid as cardGuid,
        hc.gmt_create,
        hc.card_validity,
        hc.default_card,
        hi.card_validity_time,
        hc.card_validity_date ,
        hi.validity_unit,
        hi.is_pre_stored isPreStored,
        hi.applet_recharge appletRecharge,
        (hc.subsidy_amount + hc.card_amount + hc.gift_amount ) as accountMoney,
        hr.open_card_time_type,
        hec.card_state,
        hpc.card_state as physicalCardState,
        hc.guid as memberInfoCardGuid,
        hr.self_type,
        hr.self_payment_money,
        hr.self_recharge_money,
        hr.send_status,
        hi.card_employ_explain,
        hec.open_card_way as electronicOpenWay,
        hc.guid as ownGuid,
        hc.electronic_card_num,
        hc.applicable_all_store,
        hi.excess_type,
        hi.is_excess,
        hc.excess_amount,
        hc.excess_times
        from hsa_card_open_rule hr
        JOIN hsa_card_base_info hi on hr.card_guid = hi.guid
        JOIN hsa_member_info_card hc on hi.guid = hc.card_guid
        LEFT JOIN hsa_physical_card hpc on hpc.guid = hc.physical_card_guid
        <if test="qo.type == 0">
            LEFT JOIN hsa_electronic_card hec on hec.guid = hc.electronic_card_guid
        </if>
        <if test="qo.type == 1">
            JOIN hsa_electronic_card hec on hec.guid = hc.electronic_card_guid
        </if>
        <if test="qo.type == 2">
            JOIN hsa_electronic_card hec on hec.guid = hc.electronic_card_guid
        </if>
        where hi.oper_subject_guid = #{qo.operSubjectGuid}
        and hc.member_info_guid = #{qo.memberInfoGuid}
        <if test=" qo.type == 0">
            and hi.card_status in (2,3)
            and ( hec.card_state in (0,1) or hpc.card_state in (-1,0,1,3) )
            order by hc.default_card desc ,hc.gmt_create DESC
        </if>
        <if test=" qo.type == 1">
            and hec.card_state = 2
            and hc.electronic_card_guid is not null
            order by hc.card_validity_date desc
        </if>
        <if test=" qo.type == 2">
            and hr.send_status = 2
            and hec.card_state in (0,1)
            and hr.electronic_open_way IN (0, 3)
            AND hr.open_card_path LIKE #{qo.sendChannel}
            and hc.electronic_card_guid is not null
            order by hc.default_card desc ,hc.gmt_create DESC
        </if>
    </select>

    <!-- 可用卡 -->
    <select id="queryAppletPaymentAccount" resultType="com.holderzone.member.common.vo.card.AppletPaymentAccountVO">
        select hc.card_name                                          as name,
               hc.card_image,
               hi.guid                                               as cardGuid,
               (hc.subsidy_amount + hc.card_amount + hc.gift_amount) as accountMoney,
               hc.guid                                               as ownGuid,
               hc.electronic_card_num,
               hc.default_card,
               hc.default_choose
        from hsa_card_open_rule hr
                 JOIN hsa_card_base_info hi on hr.card_guid = hi.guid
                 JOIN hsa_member_info_card hc on hi.guid = hc.card_guid
        where hi.oper_subject_guid = #{operSubjectGuid}
          and hc.member_info_guid = #{memberInfoGuid}
          and hc.electronic_card_guid is not null
          and hi.card_status = 2
          and hc.electronic_card_state = 1
        order by hc.default_card desc, hc.gmt_create DESC
    </select>

    <!-- 权益可用卡 -->
    <select id="findPayMiniProgramCard" resultType="com.holderzone.member.common.qo.card.MiniProgramCardDTO">
        select
        hc.card_name,
        hc.card_image,
        hi.guid                                               as cardGuid,
        hc.guid                                               as ownGuid,
        (hc.subsidy_amount + hc.card_amount + hc.gift_amount) as accountMoney,
        hc.electronic_card_num,
        hc.default_card,
        hc.default_choose,
        hc.card_color,
        hi.qr_code,
        hc.gmt_create,
        hi.card_value_money,
        hi.card_status,
        hc.card_validity,
        hi.card_validity_time,
        hi.validity_unit,
        hc.card_validity_date ,
        hi.is_pre_stored isPreStored,
        hi.applet_recharge appletRecharge,
        hr.open_card_time_type,
        hr.self_type,
        hr.self_payment_money,
        hr.self_recharge_money,
        hi.card_employ_explain,
        
        hr.send_status,
        hc.applicable_all_store,
        hi.excess_type,
        hi.is_excess,
        hc.excess_amount,
        hc.excess_times
        from hsa_card_open_rule hr
        JOIN hsa_card_base_info hi on hr.card_guid = hi.guid
        JOIN hsa_member_info_card hc on hi.guid = hc.card_guid
        where hi.oper_subject_guid = #{qo.operSubjectGuid}
        and hc.member_info_guid = #{qo.memberInfoGuid}
        and hc.electronic_card_guid is not null
        and hi.card_status = 2
        and hc.electronic_card_state = 1
        order by hc.default_card desc, hc.gmt_create DESC
    </select>


    <select id="queryValidElectronicCardNum" resultType="java.lang.Integer">
        select count(1)
        from hsa_card_base_info hi
        JOIN hsa_member_info_card hc on hc.card_guid = hi.guid
        <include refid="queryValidElectronicCard"/>
    </select>

    <sql id="queryValidElectronicCard">
        where
        hi.oper_subject_guid =
        #{qo.operSubjectGuid}
        and
        hc
        .
        member_info_guid
        =
        #{qo.memberInfoGuid}
        and
        hi
        .
        card_status
        =
        2
        and
        (
        hc
        .
        electronic_card_state
        in
        (
        0,
        1
        )
        or
        hc
        .
        physical_card_state
        in
        (
        -
        1,
        0,
        1,
        3
        )
        )
    </sql>

    <select id="findQrCodeOpenCardDetail" resultType="com.holderzone.member.common.qo.card.MiniProgramCardDTO">
        select CASE
                   hi.card_validity
                   WHEN 0 THEN
                       '永久有效'
                   WHEN 1 THEN
                       concat('领取后', hi.card_validity_time,
                              CASE hi.validity_unit
                                  WHEN 1 THEN '日'
                                  WHEN 2 THEN '周'
                                  WHEN 3 THEN '月'
                                  WHEN 4 THEN '年'
                                  ELSE '日' END)
                   WHEN 2 THEN
                       CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), '至', DATE_FORMAT(hi.card_validity_date, '%Y-%m-%d'))
                   ELSE '永久有效'
                   END AS periodOfValidity,
               hi.guid AS cardGuid,
               hi.card_name,
               hi.card_color,
               hi.card_image,
               hi.qr_code,
               hi.card_status,
               hr.open_card_time_type,
               hr.self_type,
               hr.self_payment_money,
               hr.self_recharge_money,
               hr.send_status,
               hi.card_value_money,
               hi.card_employ_explain,
               hr.electronic_open_way,
               hi.card_validity,
               hi.card_validity_time,
               hi.card_validity_date,
               hi.validity_unit
        FROM hsa_card_base_info hi
                 JOIN hsa_card_open_rule hr ON hr.card_guid = hi.guid
        WHERE hr.is_support_electronic_card = 1
          AND hi.guid = #{cardGuid}
    </select>


    <select id="findAllOwnGuid" resultType="java.lang.String">
        select hc.guid
        from hsa_member_info_card hc
        where hc.member_info_guid = #{memberInfoGuid}
          and hc.oper_subject_guid = #{operSubjectGuid}
          and (hc.electronic_card_state in (0, 1) or hc.physical_card_state in (-1, 0, 1, 3))
        order by hc.default_card desc, hc.gmt_create DESC
    </select>

    <select id="getValidDate" resultType="java.lang.String">
        select CASE
                   hi.card_validity
                   WHEN 0 THEN
                       '永久有效'
                   WHEN 1 THEN
                       concat('领取后', hi.card_validity_time,
                              CASE hi.validity_unit
                                  WHEN 1 THEN '日'
                                  WHEN 2 THEN '周'
                                  WHEN 3 THEN '月'
                                  WHEN 4 THEN '年'
                                  ELSE '日' END)
                   WHEN 2 THEN
                       CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), '至', DATE_FORMAT(hi.card_validity_date, '%Y-%m-%d'))
                   ELSE '永久有效'
                   END AS periodOfValidity
        from hsa_card_base_info hi
        where hi.guid = #{guid}

    </select>


    <select id="listOwnCardPage" resultMap="MemberInfoCardResultMap">
        select hi.guid
             , hi.card_name
             , hi.card_status
             , hi.card_image                                         as cardColor
             , hc.guid                                               as memberInfoCardGuid
             , hc.card_amount
             , hc.member_info_guid                                   as memberInfoGuid
             , hc.gift_amount
             , hc.subsidy_amount
             , (hc.card_amount + hc.gift_amount + hc.subsidy_amount) as cardBalance
             , hc.applicable_all_store
             , CASE
            hc.card_validity
                   WHEN 0 THEN
                       '永久有效'
                   ELSE
                       CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), '至', DATE_FORMAT(hc.card_validity_date, '%Y-%m-%d'))
            END                                                      AS periodOfValidity
             , hc.physical_card_guid
             , hc.electronic_card_guid
             , hc.guid                                               as ownCardGuid
             , hc.retreat_subsidy_amount
        from hsa_card_base_info hi,
             hsa_member_info_card hc
        where hi.guid = hc.card_guid
          and hc.member_info_guid = #{memberInfoGuid}
        order by hc.gmt_create desc
    </select>

    <resultMap id="MemberInfoCardResultMap" type="com.holderzone.member.common.vo.card.OwnCardVO">

        <result property="ownGuid" column="ownGuid"/>
        <result property="cardGuid" column="guid"/>
        <result property="cardName" column="card_name"/>
        <result property="cardStatus" column="card_status"/>
        <result property="cardBalance" column="cardBalance"/>
        <result property="applicableAllStore" column="applicable_all_store"/>
        <result property="periodOfValidity" column="periodOfValidity"/>
        <association property="ownPhysicalCard" column="physical_card_guid" select="getPhysicalCard"/>
        <association property="ownElectronicCard" column="electronic_card_guid" select="getElectronicCard"/>
    </resultMap>
    <select id="getPhysicalCard" resultType="com.holderzone.member.common.dto.card.OwnPhysicalCardDTO">
        select hpc.guid
             , hds.deposit_strategy_name     as depositStrategyName
             , hds.deposit_amount            as depositAmount
             , hds.is_refund_deposit         as isRefundDeposit
             , hds.refund_amount             as refundAmount
             , hpc.card_num                  as cardNum
             , hpc.card_state                as cardState
             , hcor.is_physical_card_retreat as isPhysicalCardRetreat
             , hpc.source
             , hpc.activation_time           as activationTime
        from hsa_physical_card hpc
                 left join hsa_card_open_rule hcor on hpc.card_guid = hcor.card_guid
                 left join hsa_deposit_strategy hds on hcor.open_physical_card_strategy_guid = hds.guid
        where hpc.guid = #{physical_card_guid}
          and hpc.is_delete = 0
    </select>

    <select id="getElectronicCard" resultType="com.holderzone.member.common.dto.card.OwnElectronicCardDTO">
        select hec.guid
             , hec.card_num      as cardNum
             , hec.card_state    as cardState
             , hec.gmt_create    as gmtCreate
             , hec.source
             , hec.open_card_way as openCardWay
        from hsa_electronic_card hec
        where hec.guid = #{electronic_card_guid}
    </select>


    <select id="listAllAbleECard" resultType="com.holderzone.member.common.vo.card.AbleECardVO">
        select hi.guid as cardGuid, hi.card_name as cardName
        from hsa_card_base_info hi
                 join hsa_card_open_rule hr on hi.guid = hr.card_guid
        where hi.card_status = 2
          and hr.send_status = 2
          and hi.oper_subject_guid = #{operSubjectGuid}
          and hr.is_support_electronic_card = 1
          and (case hr.send_count_limit when 0 then 1 = 1 else hr.surplus_send_open_count_limit > 0 end)
        order by hi.id desc
    </select>

    <select id="listAbleEntityCard" resultType="com.holderzone.member.common.vo.card.AbleEntityCardVO">
        SELECT hi.guid                             AS cardGuid,
               hi.card_name                        AS cardName,
               hi.card_image                       as cardImage,
               hi.card_value_money                 AS cardValueMoney,
               hr.open_physical_card_strategy_guid as openPhysicalCardStrategyGuid,
               hr.is_support_electronic_card       as isSupportElectronicCard,
               CASE
                   hi.card_validity
                   WHEN 0 THEN
                       '永久有效'
                   WHEN 1 THEN
                       concat(
                               '开通后',
                               hi.card_validity_time,
                               CASE
                                   hi.validity_unit
                                   WHEN 1 THEN
                                       '日'
                                   WHEN 2 THEN
                                       '周'
                                   WHEN 3 THEN
                                       '月'
                                   WHEN 4 THEN
                                       '年'
                                   ELSE '日'
                                   END
                           , '有效'
                           )
                   WHEN 2 THEN
                       CONCAT(DATE_FORMAT(hi.card_validity_date, '%Y-%m-%d'), '失效')
                   ELSE '永久有效'
                   END                             AS periodOfValidity
        FROM hsa_card_base_info hi
                 JOIN hsa_card_open_rule hr ON hi.guid = hr.card_guid
        WHERE hi.card_status = 2
          AND hr.is_support_physical_card = 1
          AND hi.oper_subject_guid = #{operSubjectGuid}
          and json_contains(hr.open_physical_card_channel, #{source})
        ORDER BY hi.gmt_modified DESC
    </select>

    <select id="queryMemberInfoCard" resultType="com.holderzone.member.common.vo.card.QueryMemberInfoCardVO">
        select
        hc.guid as memberCardGuid,
        hc.card_name as cardName,
        hc.card_guid as cardGuid,
        hc.member_info_guid as memberInfoGuid,
        hc.electronic_card_guid as electronicCardGuid,
        hc.physical_card_guid as physicalCardGuid,
        hc.electronic_card_num as electronicCardNum,
        hc.electronic_card_state as electronicCardState,
        hc.electronic_open_time as electronicOpenCardTime,
        hc.physical_card_num as physicalCardNum,
        hc.physical_card_state as physicalCardState,
        hc.physical_card_activation_time as physicalActivationTime,
        hc.applicable_all_store as applicableAllStore,
        hc.subsidy_amount as subsidyAmount,
        hc.retreat_subsidy_amount as retreatSubsidyAmount,
        hc.card_amount as cardAmount,
        hc.gift_amount as giftAmount,
        hc.card_validity as cardValidity,
        hc.card_validity_date as cardValidityDate,
        hc.gmt_create as cardCreateDate,
        hc.is_physical_card_retreat as isPhysicalCardRetreat
        from hsa_member_info_card hc
        where hc.oper_subject_guid = #{request.operSubjectGuid}
        <if test="request.memberInfoCardGuid != null and request.memberInfoCardGuid.size() > 0 ">
            and hc.guid in
            <foreach collection="request.memberInfoCardGuid" item="cardGuid" open="(" close=")" separator=",">
                #{cardGuid}
            </foreach>
        </if>
        <if test="request.keyword != null and request.keyword !='' and request.keywordType != null">
            <choose>
                <when test="request.keywordType ==1">
                    and hc.card_name LIKE CONCAT( '%',#{request.keyword}, '%' )
                </when>
                <when test="request.keywordType ==2">
                    and hc.electronic_card_num LIKE CONCAT( '%',#{request.keyword}, '%' )
                </when>
                <when test="request.keywordType ==3">
                    and hc.physical_card_num LIKE CONCAT( '%',#{request.keyword}, '%' )
                </when>
                <when test="request.keywordType ==4">
                    and hc.member_phone_num like CONCAT('%',#{request.keyword},'%')
                </when>
            </choose>
        </if>
        <if test="request.cardGuid != null and request.cardGuid != ''">
            and hc.card_guid = #{request.cardGuid}
        </if>

        <if test="request.bindingCardType != null and request.bindingCardType == 1 and request.isBinding == 1">
            and hc.electronic_card_guid is not null
        </if>
        <if test="request.bindingCardType != null and request.bindingCardType == 1 and request.isBinding == 0">
            and hc.electronic_card_guid is null
        </if>
        <if test="request.bindingCardType != null and request.bindingCardType == 2 and request.isBinding == 1">
            and hc.physical_card_guid is not null
        </if>
        <if test="request.bindingCardType != null and request.bindingCardType == 2 and request.isBinding == 0">
            and hc.physical_card_guid is null
        </if>

        <if test="request.cardStateType != null and request.cardStateType == 1 and request.cardState != null">
            and hc.electronic_card_state = #{request.cardState}
        </if>

        <if test="request.cardStateType != null and request.cardStateType == 2 and request.cardState != null">
            and hc.physical_card_state = #{request.cardState}
            <if test="request.cardState == 0">
                or hc.physical_card_state =3
            </if>
        </if>

        <if test="request.openCardType != null and request.openCardType == 1 ">
            <if test="request.openCardStartTime != null">
                <![CDATA[and hc.electronic_open_time>=DATE_FORMAT(#{request.openCardStartTime},'%Y-%m-%d 00:00:00')]]>
            </if>
            <if test="request.openCardEndTime != null">
                <![CDATA[and hc.electronic_open_time<=DATE_FORMAT(#{request.openCardEndTime},'%Y-%m-%d 23:59:59')]]>
            </if>
        </if>

        <if test="request.openCardType != null and request.openCardType == 2 ">
            <if test="request.openCardStartTime != null">
                <![CDATA[and hc.physical_card_activation_time>=DATE_FORMAT(#{request.openCardStartTime},'%Y-%m-%d 00:00:00')]]>
            </if>
            <if test="request.openCardEndTime != null">
                <![CDATA[and hc.physical_card_activation_time<=DATE_FORMAT(#{request.openCardEndTime},'%Y-%m-%d 23:59:59')]]>
            </if>
        </if>
        <if test="request.openCardType != null and request.openCardType == 3 ">
            <if test="request.openCardStartTime != null">
                and hc.id NOT IN (
                SELECT
                hi.id
                FROM
                hsa_member_info_card hi
                WHERE
                hi.card_validity in(1,2)
                AND (
                ( hi.gmt_create &lt;= DATE_FORMAT( #{request.openCardStartTime}, '%Y-%m-%d 00:00:00' ) AND
                hi.card_validity_date &lt; DATE_FORMAT( #{request.openCardStartTime}, '%Y-%m-%d 00:00:00' ) )
                OR ( hi.gmt_create &gt; DATE_FORMAT(#{request.openCardEndTime}, '%Y-%m-%d 23:59:59' ) )
                )
                )
            </if>
        </if>
        order by
        <choose>
            <when test="request.sortType == null">
                hc.gmt_create
            </when>
            <when test="request.sortType == 1">
                hc.electronic_open_time
            </when>
            <when test="request.sortType == 0">
                hc.physical_card_activation_time
            </when>
        </choose>
        <choose>
            <when test="request.orderType == null || request.orderType == 0">
                DESC
            </when>
            <when test="request.orderType == 1">
                ASC
            </when>
        </choose>
    </select>

    <select id="queryMemberInfoCardGuid" resultType="com.holderzone.member.common.vo.card.QueryMemberInfoCardVO">
        select hc.guid as memberInfoGuid,
        hc.card_name as cardName,
        hc.member_info_guid as memberInfoGuid,
        hec.card_num as electronicCardNum,
        hec.card_state as electronicCardState,
        hec.gmt_create as electronicOpenCardTime,
        hec.source as electronicSource,
        hec.open_card_way as electronicOpenCardWay,
        hpc.card_num as physicalCardNum,
        hpc.card_state as physicalCardState,
        hpc.source as physicalCardSource,
        hpc.activation_time as physicalActivationTime,
        hm.user_name as memberName,
        hm.phone_num as memberPhone,
        hc.subsidy_amount as subsidyAmount,
        hc.card_amount as cardAmount,
        hc.gift_amount as giftAmount,
        hc.card_validity as cardValidity,
        hc.card_validity_date as cardValidityDate,
        hc.gmt_create as cardCreateDate
        from hsa_member_info_card hc
        left join hsa_electronic_card hec on hc.electronic_card_guid = hec.guid
        left join hsa_physical_card hpc on hc.physical_card_guid = hpc.guid
        left join hsa_operation_member_info hm on hc.member_info_guid = hm.guid
        where 1=1
        <if test="request != null and request.size() > 0">
            and hc.guid in
            <foreach collection="request" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryMemberInfoCardCount" resultType="java.lang.Integer">
        select
        count(1)
        from hsa_member_info_card hc
        where hc.oper_subject_guid = #{request.operSubjectGuid}
        <if test="request.memberInfoCardGuid != null and request.memberInfoCardGuid.size() > 0 ">
            and hc.guid in
            <foreach collection="request.memberInfoCardGuid" item="cardGuid" open="(" close=")" separator=",">
                #{cardGuid}
            </foreach>
        </if>
        <if test="request.keyword != null and request.keyword !='' and request.keywordType != null">
            <choose>
                <when test="request.keywordType ==1">
                    and hc.card_name LIKE CONCAT( '%',#{request.keyword}, '%' )
                </when>
                <when test="request.keywordType ==2">
                    and hc.electronic_card_num LIKE CONCAT( '%',#{request.keyword}, '%' )
                </when>
                <when test="request.keywordType ==3">
                    and hc.physical_card_num LIKE CONCAT( '%',#{request.keyword}, '%' )
                </when>
                <when test="request.keywordType ==4">
                    and hc.member_phone_num like CONCAT('%',#{request.keyword},'%')
                </when>
            </choose>
        </if>
        <if test="request.cardGuid != null and request.cardGuid != ''">
            and hc.card_guid = #{request.cardGuid}
        </if>

        <if test="request.bindingCardType != null and request.bindingCardType == 1 and request.isBinding == 1">
            and hc.electronic_card_guid is not null
        </if>
        <if test="request.bindingCardType != null and request.bindingCardType == 1 and request.isBinding == 0">
            and hc.electronic_card_guid is null
        </if>
        <if test="request.bindingCardType != null and request.bindingCardType == 2 and request.isBinding == 1">
            and hc.physical_card_guid is not null
        </if>
        <if test="request.bindingCardType != null and request.bindingCardType == 2 and request.isBinding == 0">
            and hc.physical_card_guid is null
        </if>

        <if test="request.cardStateType != null and request.cardStateType == 1 and request.cardState != null">
            and hc.electronic_card_state = #{request.cardState}
        </if>

        <if test="request.cardStateType != null and request.cardStateType == 2 and request.cardState != null">
            and hc.physical_card_state = #{request.cardState}
            <if test="request.cardState == 0">
                or hc.physical_card_state =3
            </if>
        </if>

        <if test="request.openCardType != null and request.openCardType == 1 ">
            <if test="request.openCardStartTime != null">
                <![CDATA[and hc.electronic_open_time>=DATE_FORMAT(#{request.openCardStartTime},'%Y-%m-%d 00:00:00')]]>
            </if>
            <if test="request.openCardEndTime != null">
                <![CDATA[and hc.electronic_open_time<=DATE_FORMAT(#{request.openCardEndTime},'%Y-%m-%d 23:59:59')]]>
            </if>
        </if>

        <if test="request.openCardType != null and request.openCardType == 2 ">
            <if test="request.openCardStartTime != null">
                <![CDATA[and hc.physical_card_activation_time>=DATE_FORMAT(#{request.openCardStartTime},'%Y-%m-%d 00:00:00')]]>
            </if>
            <if test="request.openCardEndTime != null">
                <![CDATA[and hc.physical_card_activation_time<=DATE_FORMAT(#{request.openCardEndTime},'%Y-%m-%d 23:59:59')]]>
            </if>
        </if>
        <if test="request.openCardType != null and request.openCardType == 3 ">
            <if test="request.openCardStartTime != null">
                and hc.id NOT IN (
                SELECT
                hi.id
                FROM
                hsa_member_info_card hi
                WHERE
                hi.card_validity in(1,2)
                AND (
                ( hi.gmt_create &lt;= DATE_FORMAT( #{request.openCardStartTime}, '%Y-%m-%d 00:00:00' ) AND
                hi.card_validity_date &lt; DATE_FORMAT( #{request.openCardStartTime}, '%Y-%m-%d 00:00:00' ) )
                OR ( hi.gmt_create &gt; DATE_FORMAT(#{request.openCardEndTime}, '%Y-%m-%d 23:59:59' ) )
                )
                )
            </if>
        </if>
        ORDER BY
        <choose>
            <when test="request.sortType == null">
                hc.gmt_create
            </when>
            <when test="request.sortType == 1">
                hc.electronic_open_time
            </when>
            <when test="request.sortType == 0">
                hc.physical_card_activation_time
            </when>
        </choose>
        <choose>
            <when test="request.orderType == null || request.orderType == 0">
                DESC
            </when>
            <when test="request.orderType == 1">
                ASC
            </when>
        </choose>
    </select>


    <select id="findAllMemberCard" resultType="com.holderzone.member.common.dto.member.MemberCardDTO">
        select member_info_guid as memberGuid,
        GROUP_CONCAT(card_name SEPARATOR '、') as memberCard from
        hsa_member_info_card where member_info_guid in
        <foreach collection="memberGuid" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
        group by member_info_guid
    </select>

    <select id="findAmountMemberCard" resultType="com.holderzone.member.common.dto.member.MemberCardAmountDTO">
        SELECT member_info_guid as memberGuid,
        sum(subsidy_amount + card_amount + gift_amount) as memberCardAmount
        from hsa_member_info_card
        WHERE member_info_guid in
        <foreach collection="memberGuid" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
        GROUP by member_info_guid
    </select>

    <select id="queryValidElectronicCardGuidList" resultType="java.lang.String">
        select hi.guid
        from hsa_card_base_info hi
        JOIN hsa_member_info_card hc on hc.card_guid = hi.guid

        where hi.oper_subject_guid = #{qo.operSubjectGuid}
        and hc.member_info_guid = #{qo.memberInfoGuid}
        and hi.card_status = 2
        and (hc.electronic_card_state = 1 or hc.physical_card_state = 1)
    </select>

    <select id="queryByPhysicalCard" resultType="com.holderzone.member.base.entity.card.HsaMemberInfoCard">
        select *
        from hsa_member_info_card
        where physical_card_guid = #{physicalCardGuid} limit 1
    </select>

    <select id="queryByElectronicCard" resultType="com.holderzone.member.base.entity.card.HsaMemberInfoCard">
        select *
        from hsa_member_info_card
        where electronic_card_guid = #{electronicCardGuid} limit 1
    </select>

    <update id="batchUpdateBalance">
        <foreach collection="list" item="request">
            UPDATE hsa_member_info_card
            SET
            card_amount = #{request.cardAmount},
            gift_amount = #{request.giftAmount},
            subsidy_amount = #{request.subsidyAmount},
            excess_times = #{request.excessTimes},
            excess_amount = #{request.excessAmount}
            WHERE guid = #{request.guid};
        </foreach>
    </update>

    <update id="updateBalance">
        UPDATE hsa_member_info_card
        SET card_amount = #{cardAmount}
        WHERE
            guid = #{guid}
    </update>

    <update id="updatePhysicalCardState">
        UPDATE hsa_member_info_card
        SET physical_card_state = #{status}
        WHERE
        physical_card_state != 2
        and guid in
        <foreach collection="guids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updatePhysicalCardStateByGuid">
        UPDATE hsa_member_info_card
        SET physical_card_state = #{status}
        WHERE physical_card_state != 2
          and guid = #{guid}
    </update>

    <update id="updateElectronicCardState">
        UPDATE hsa_member_info_card
        SET electronic_card_state = #{status}
        WHERE
        electronic_card_state != 2
        and guid in
        <foreach collection="guids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateByGuid">
        UPDATE hsa_member_info_card
        SET card_pay_password = #{password}
        WHERE guid in
        <foreach collection="guids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="batchUpdatePhone">
        UPDATE hsa_member_info_card
        SET member_phone_num = #{phone}
        WHERE member_info_guid in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateBatchMemberInfoGuid">
        UPDATE hsa_member_info_card
        SET member_info_guid = #{memberInfoGuid}
        WHERE guid in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.guid}
        </foreach>
    </update>

    <update id="batchUpdateByApplicableAllStore">
        <foreach collection="list" item="request">
            UPDATE hsa_member_info_card
            SET
            applicable_all_store = #{request.applicableAllStore}
            WHERE guid = #{request.guid};
        </foreach>
    </update>

    <update id="batchUpdate">
        UPDATE hsa_member_info_card
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="subsidy_amount = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when guid = #{item.guid}
                    then #{item.subsidyAmount}
                </foreach>
            </trim>
            <trim prefix="retreat_subsidy_amount = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when guid = #{item.guid}
                    then #{item.retreatSubsidyAmount}
                </foreach>
            </trim>
            <trim prefix="card_amount = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when guid = #{item.guid}
                    then #{item.cardAmount}
                </foreach>
            </trim>
            <trim prefix="excess_times = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when guid = #{item.guid}
                    then #{item.excessTimes}
                </foreach>
            </trim>
            <trim prefix="excess_amount = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when guid = #{item.guid}
                    then #{item.excessAmount}
                </foreach>
            </trim>
        </trim>
        where guid in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.guid}
        </foreach>
    </update>

    <update id="batchUpdateValue">
        UPDATE hsa_member_info_card
        <trim prefix="set" suffixOverrides=",">

            <trim prefix="subsidy_amount = case" suffix="end,">
                <foreach collection="list" item="activity" index="index">
                    when guid = #{activity.guid}
                    then subsidy_amount + #{activity.subsidyAmount}
                </foreach>
            </trim>

            <trim prefix="retreat_subsidy_amount =  case" suffix="end,">
                <foreach collection="list" item="activity" index="index">
                    when guid = #{activity.guid}
                    then retreat_subsidy_amount + #{activity.retreatSubsidyAmount}
                </foreach>
            </trim>
            <trim prefix="excess_times = case" suffix="end,">
                <foreach collection="list" item="activity" index="index">
                    when guid = #{activity.guid}
                    then #{activity.excessTimes}
                </foreach>
            </trim>
            <trim prefix="card_amount = case" suffix="end,">
                <foreach collection="list" item="activity" index="index">
                    when guid = #{activity.guid}
                    then #{activity.cardAmount}
                </foreach>
            </trim>
            <trim prefix="excess_amount = case" suffix="end,">
                <foreach collection="list" item="activity" index="index">
                    when guid = #{activity.guid}
                    then #{activity.excessAmount}
                </foreach>
            </trim>

        </trim>
        WHERE guid in
        <foreach collection="list" item="activity" open="(" close=")" separator=",">
            #{activity.guid}
        </foreach>
    </update>

    <update id="subsidyRecycling" parameterType="com.holderzone.member.common.qo.card.SubsidyRecyclingQO">
        UPDATE hsa_member_info_card
        <trim prefix="set" suffixOverrides=",">

            <trim prefix="subsidy_amount = case" suffix="end,">
                <foreach collection="list" item="activity" index="index">
                    when guid = #{activity.guid}
                    then IF(subsidy_amount &lt; #{activity.subsidyAmount},0,subsidy_amount - #{activity.subsidyAmount})
                </foreach>
            </trim>

            <trim prefix="retreat_subsidy_amount =  case" suffix="end,">
                <foreach collection="list" item="activity" index="index">
                    when guid = #{activity.guid}
                    then IF(retreat_subsidy_amount &lt; #{activity.retreatSubsidyAmount},0,retreat_subsidy_amount -
                    #{activity.retreatSubsidyAmount})
                </foreach>
            </trim>
        </trim>
        WHERE guid in
        <foreach collection="list" item="activity" open="(" close=")" separator=",">
            #{activity.guid}
        </foreach>
    </update>

    <update id="updateDefaultCardByMemberGuid">
        UPDATE hsa_member_info_card
        SET default_card = null
        WHERE member_info_guid = #{memberGuid}
          and default_card = 1
    </update>

    <update id="updateDefaultChooseByMemberGuid">
        UPDATE hsa_member_info_card
        SET default_choose = null
        WHERE member_info_guid = #{memberGuid}
          and default_choose = 1
    </update>

    <update id="updateMemberInfoCardByGuid">
        UPDATE hsa_member_info_card
        SET card_amount   = #{cardAmount},
            excess_times  =#{excessTimes},
            excess_amount = #{excessAmount}
        WHERE guid = #{guid}
    </update>

    <update id="updateCardAmount">
        UPDATE hsa_member_info_card
        SET gmt_modified  = now(),
            card_amount   = #{card.cardAmount},
            excess_times  =#{card.excessTimes},
            excess_amount = #{card.excessAmount}
        WHERE guid = #{card.guid}
    </update>
    <update id="updateDefaultCardByGuid">
        UPDATE hsa_member_info_card
        SET default_card = 1
        WHERE guid = #{guid}
    </update>

    <select id="listCardAmountByDay" resultType="com.holderzone.member.base.entity.card.HsaMemberInfoCardTotal">
        select mc.oper_subject_guid      operSubjectGuid,
               sum(mc.subsidy_amount) as subsidyAmount,
               sum(mc.card_amount)    as cardAmount,
               sum(mc.gift_amount)    as giftAmount
        from hsm_member_marketing_platform_db.hsa_member_info_card mc
        group by oper_subject_guid
    </select>

    <select id="queryByGuidForUpdate" resultType="com.holderzone.member.base.entity.card.HsaMemberInfoCard">
        select *
        from hsa_member_info_card
        where guid = #{guid}
            for update
    </select>
    <select id="listAllAbleECardByChannel" resultType="com.holderzone.member.common.vo.card.AbleECardVO">
        select hi.guid as cardGuid, hi.card_name as cardName
        from hsa_card_base_info hi
                 join hsa_card_open_rule hr on hi.guid = hr.card_guid
        where hi.card_status = 2
          and hr.send_status = 2
          and hi.oper_subject_guid = #{operSubjectGuid}
          and hr.is_support_electronic_card = 1
          and (case hr.send_count_limit when 0 then 1 = 1 else hr.surplus_send_open_count_limit > 0 end)
        order by hi.id desc
    </select>

    <resultMap id="RedeemMemberCardResultMap" type="com.holderzone.member.common.vo.card.RedeemOwnCardVO">

        <result property="ownGuid" column="ownGuid"/>
        <result property="cardGuid" column="guid"/>
        <result property="cardName" column="card_name"/>
        <result property="cardStatus" column="card_status"/>
        <result property="defaultCard" column="default_card"/>
        <result property="cardBalance" column="cardBalance"/>
        <result property="electronicCardState" column="electronic_card_state"/>
        <result property="physicalCardState" column="physical_card_state"/>
        <result property="applicableAllStore" column="applicable_all_store"/>
        <result property="periodOfValidity" column="periodOfValidity"/>
        <association property="ownPhysicalCard" column="physical_card_guid" select="getPhysicalCard"/>
        <association property="ownElectronicCard" column="electronic_card_guid" select="getElectronicCard"/>
    </resultMap>

    <select id="getMemberCardList" resultMap="RedeemMemberCardResultMap">
        select hi.guid
             , hi.card_name
             , hc.default_card
             , hi.card_status
             , hi.card_image                                         as cardColor
             , hc.guid                                               as memberInfoCardGuid
             , hc.card_amount
             , hc.electronic_card_state
             , hc.physical_card_state
             , hc.member_info_guid                                   as memberInfoGuid
             , hc.gift_amount
             , hc.subsidy_amount
             , (hc.card_amount + hc.gift_amount + hc.subsidy_amount) as cardBalance
             , hc.applicable_all_store
             , CASE
            hc.card_validity
                   WHEN 0 THEN
                       '永久有效'
                   ELSE
                       CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), '至', DATE_FORMAT(hc.card_validity_date, '%Y-%m-%d'))
            END                                                      AS periodOfValidity
             , hc.physical_card_guid
             , hc.electronic_card_guid
             , hc.guid                                               as ownCardGuid
             , hc.retreat_subsidy_amount
        from hsa_card_base_info hi,
             hsa_member_info_card hc
        where hi.guid = hc.card_guid
          and hc.member_info_guid = #{guid}
        order by hc.gmt_create desc
    </select>

    <select id="listMemberCard" resultType="com.holderzone.member.common.vo.card.CardInfoBasicVO">
        SELECT
            hc.guid AS memberInfoCardGuid,
            ( hc.card_amount + hc.gift_amount + hc.subsidy_amount ) AS cardBalance
        FROM
            hsa_card_base_info hi,
            hsa_member_info_card hc
        WHERE
            hi.guid = hc.card_guid
          AND hc.member_info_guid = #{memberInfoGuid}
          AND hi.card_status = 2
        ORDER BY
            hc.gmt_create ASC
    </select>

    <select id="listPayMemberCard" resultType="com.holderzone.member.common.vo.card.PayMemberCardVO">
        SELECT
            mc.guid memberInfoCardGuid,
            mc.member_info_guid,
            c.card_image,
            c.card_name,
            ( mc.subsidy_amount + mc.card_amount + mc.gift_amount ) card_amount,
            mc.electronic_card_num,
            c.is_excess,
            c.excess_type,
            mc.excess_amount,
            mc.excess_times,
            mc.default_card,
            mc.applicable_all_store,
            CASE WHEN r.use_check = 1 AND r.applets_check_state = 1
            THEN 1 ELSE 0 END AS needCheckPwd
        FROM
            hsa_member_info_card mc
                JOIN hsa_card_base_info c ON mc.card_guid = c.guid
                JOIN hsa_card_balance_rule r ON c.oper_subject_guid = r.oper_subject_guid
        WHERE
          mc.member_info_guid = #{memberInfoGuid}
          AND c.card_status = 2
          AND mc.electronic_card_state= 1
          AND (mc.card_validity = 0
          OR (mc.card_validity = 1 AND NOW() &lt;= mc.card_validity_date))
        ORDER BY
            mc.default_card DESC,
            mc.gmt_create DESC
    </select>

</mapper>