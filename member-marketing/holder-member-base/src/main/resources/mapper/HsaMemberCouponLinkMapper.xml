<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper">


    <sql id="show_base_column">
        cl.id,
        cl.guid,
        cl.gmt_create,
        cl.gmt_modified,
        cl.oper_subject_guid,
        cl.code,
        cl.coupon_name,
        cl.coupon_type,
        cl.coupon_code,
        cl.threshold_amount,
        cl.threshold_type,
        cl.discount_amount,
        cl.user_name,
        cl.member_guid,
        cl.member_phone,
        cl.source,
        cl.coupon_package_type,
        cl.coupon_package_code,
        cl.store_guid,
        cl.store_name,
        cl.state,
        cl.coupon_effective_start_time,
        cl.coupon_effective_end_time,
        cl.reach_time,
        cl.apply_commodity,
        cl.discount_amount_limit,
        cl.single_order_used_limit,
        cl.share_relation,
        cl.exchange_times,
        cl.exchange_limit,
        cl.remark
    </sql>

    <sql id="show_all_column">
        <include refid="show_base_column"/>
        <!-- 规则：导出不查 -->
        <if test="qo.isExport != 1">
            <include refid="show_rule_column"/>
        </if>
    </sql>

    <sql id="show_rule_column">
        ,
        cl.apply_date_limited_json,
        cl.apply_business,
        cl.apply_terminal,
        cl.apply_terminal_json,
        cl.apply_label_guid_json,
        cl.applicable_all_store,
        cl.apply_date_limited,
        cl.apply_time_limited_json,
        cl.apply_business_json,
        cl.apply_time_limited_type,
        cl.applicable_all_store_json,
        cl.apply_commodity_json
    </sql>

    <update id="updateIsExpireRemind">
        update hsa_member_coupon_link set is_expire_remind = 1
        where guid in
        <foreach collection="couponGuidList" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </update>

    <update id="updateStateByGuids">
        update
        hsa_member_coupon_link
        set
        state = #{state}
        where
        is_delete = 0
        and guid in
        <foreach collection="guids" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </update>

    <update id="updateStateByUse">
        update
        hsa_member_coupon_link
        set
        state = #{state}
        where
        is_delete = 0
        and member_guid = #{memberInfoGuid}
        and coupon_code = #{couponCode}
        and code in
        <foreach collection="codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </update>

    <update id="updateStateByConsumptionGuid">
        update
            hsa_member_coupon_link
        set state = 6
        where oper_subject_guid = #{operSubjectGuid}
          and dtl_guid = #{consumptionGuid}
          and state != 5
    </update>

    <resultMap id="couponUseVOMap" type="com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseVO">
        <result column="code" property="code"/>
        <result column="coupon_name" property="couponName"/>
        <result column="coupon_type" property="couponType"/>
        <result column="coupon_code" property="couponCode"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="coupon_package_type" property="couponPackageType"/>
        <result column="coupon_package_code" property="couponPackageCode"/>
        <result column="member_phone" property="memberPhone"/>
        <result column="user_name" property="userName"/>
        <result column="member_guid" property="memberGuid"/>
        <result column="couponUseTime" property="couponUseTime"/>
        <result column="order_number" property="orderNumber"/>
        <result column="store_guid" property="storeGuid"/>
        <result column="store_name" property="storeName"/>
        <result column="source" property="source"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="order_paid_amount" property="orderPaidAmount"/>
        <result column="operator_account_name" property="operatorAccountName"/>
    </resultMap>

    <select id="listRule" resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        SELECT
        cl.guid,
        cl.apply_commodity,
        cl.remark
        <include refid="show_rule_column"/>
        FROM hsa_member_coupon_link cl
        where cl.guid in
        <foreach collection="guids" item="guid" open="(" separator="," close=")">
            #{guid}
        </foreach>
    </select>

    <select id="listUseDetail"
            resultMap="couponUseVOMap">
        SELECT
        cu.guid as couponGuid,
        cu.oper_subject_guid,
        cu.code,
        cu.coupon_name,
        cu.coupon_code,
        cu.member_phone,
        cu.coupon_package_code,
        cu.coupon_package_name,
        cu.user_name,
        cu.member_coupon_link_guid,
        cu.member_guid,
        <!-- 核销记录 -->
        cu.store_guid,
        cu.store_name,
        cu.source,
        cu.order_number,
        cu.gmt_create couponUseTime,
        cu.discount_amount,
        if(cu.pay_time is null,0,cu.order_paid_amount) order_paid_amount,
        cu.operator_account_name
        FROM hsa_member_coupon_use cu
        where
        <include refid="sql_use_query"/>
        order by cu.gmt_create desc

    </select>

    <sql id="sql_use_query">
        cu.oper_subject_guid = #{qo.operSubjectGuid}
        and cu.is_delete = 0

        <if test="qo.couponPackageKeywords != null and qo.couponPackageKeywords != ''">
            and
            (
            cu.coupon_package_code like concat('%',#{qo.couponPackageKeywords},'%')
            or
            cu.coupon_package_name like concat('%',#{qo.couponPackageKeywords},'%')
            )
        </if>

        <if test="qo.storeGuids != null and qo.storeGuids.size() > 0">
            and cu.store_guid in
            <foreach collection="qo.storeGuids" item="store" open="(" separator="," close=")">
                #{store}
            </foreach>
        </if>

        <if test="qo.startTime != null and qo.endTime != null">
            and cu.gmt_create between #{qo.startTime} and #{qo.endTime}
        </if>

        <if test="qo.orderNumber != null and qo.orderNumber != ''">
            and cu.order_number like concat('%', #{qo.orderNumber},'%')
        </if>

        <if test="qo.source != null">
            and cu.source = #{qo.source}
        </if>

        <!-- 模糊查询最后 -->
        <if test="qo.keywords != null and qo.keywords != ''">
            and (
            cu.coupon_code like concat('%',#{qo.keywords},'%')
            or
            cu.coupon_name like concat('%',#{qo.keywords},'%')
            or
            cu.code like concat('%',#{qo.keywords},'%')
            or
            cu.user_name like concat('%',#{qo.keywords},'%')
            or
            cu.member_phone like concat('%',#{qo.keywords},'%')
            )
        </if>
    </sql>

    <select id="countUse"
            resultType="com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseCountVO">
        SELECT
        count(*) useNum,
        sum(cu.discount_amount) discountAmount
        FROM hsa_member_coupon_link cl
        left join hsa_member_coupon_use cu on cl.guid = cu.member_coupon_link_guid
        where
        <include refid="sql_use_query"/>
    </select>


    <select id="queryTopStoreByUse"
            resultType="string">
        SELECT temp.store_name FROM (
            SELECT
            cu.store_name,
            COUNT(1) AS useNum
            FROM hsa_member_coupon_use cu
            WHERE
            <include refid="sql_use_query"/>
            and cu.store_guid is not null
            GROUP BY cu.store_guid, cu.store_name
            ORDER BY useNum DESC
            LIMIT 1 ) temp
    </select>

    <select id="listDetail" resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        SELECT
        <include refid="show_all_column"/>
        FROM hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        <include refid="sql_give_query"/>
        order by cl.reach_time desc,cl.id desc
    </select>

    <select id="detail" resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        SELECT
        <include refid="show_base_column"/>
        <include refid="show_rule_column"/>
        FROM hsa_member_coupon_link cl
        where
        cl.guid = #{guid}
        and cl.is_delete = 0
    </select>

    <sql id="sql_give_query">
        and cl.is_delete = 0

        <if test="qo.keywords != null and qo.keywords != ''">
            and (
            cl.coupon_code like concat('%',#{qo.keywords},'%')
            or
            cl.coupon_name like concat('%',#{qo.keywords},'%')
            or
            cl.code like concat('%',#{qo.keywords},'%')
            or
            cl.user_name like concat('%',#{qo.keywords},'%')
            or
            cl.member_phone like concat('%',#{qo.keywords},'%')
            )
        </if>

        <if test="qo.storeGuids != null and qo.storeGuids.size() > 0">
            and cl.store_guid in
            <foreach collection="qo.storeGuids" item="store" open="(" separator="," close=")">
                #{store}
            </foreach>
        </if>

        <if test="qo.startTime != null and qo.endTime != null">
            and cl.reach_time between #{qo.startTime} and #{qo.endTime}
        </if>

        <if test="qo.couponPackageType != null and qo.couponPackageType >= 0">
            and cl.coupon_package_type = #{qo.couponPackageType}
        </if>

        <if test="qo.couponPackageKeywords != null and qo.couponPackageKeywords != ''">
            and
            (
            cl.coupon_package_code like concat('%',#{qo.couponPackageKeywords},'%')
            or
            cl.coupon_package_name like concat('%',#{qo.couponPackageKeywords},'%')
            )
        </if>

        <include refid="sql_state_query"/>
    </sql>

    <select id="countGiveNum" resultType="int">
        select
        count(*) giveCouponNum
        from
        hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        <include refid="sql_give_query"/>
    </select>

    <select id="countOnlyInvalidNum" resultType="int">
        select
        count(*)
        from
        hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        <include refid="sql_give_query"/>
        and cl.state = 6
    </select>

    <select id="countExpireNum" resultType="int">
        select
        count(*)
        from
        hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        <include refid="sql_give_query"/>
        and cl.coupon_effective_end_time &lt; sysdate()
        and cl.state not in (5,6)
    </select>


    <sql id="sql_member_query">
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        and cl.is_delete = 0
        and cl.member_guid = #{qo.memberGuid}
        <if test="qo.keywords != null and qo.keywords != ''">
            and (
            cl.coupon_code like concat('%',#{qo.keywords},'%')
            or
            cl.coupon_name like concat('%',#{qo.keywords},'%')
            or
            cl.code like concat('%',#{qo.keywords},'%')
            )
        </if>

        <include refid="sql_state_query"/>
    </sql>

    <sql id="sql_state_query">
        <if test="qo.state != null and qo.state != ''">
            <!-- 已使用、已失效 -->
            <if test="qo.state > 4">
                and cl.state = #{qo.state}
            </if>

            <!-- 未过期 -->
            <if test="qo.state == 3">
                and cl.state not in (5,6)
                and cl.coupon_effective_end_time > sysdate()
            </if>
            <!-- 已过期 -->
            <if test="qo.state == 4">
                and cl.state not in (5,6)
                and cl.coupon_effective_end_time &lt; sysdate()
            </if>
        </if>
    </sql>

    <select id="memberPageCouponDetail"
            resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        SELECT
        <include refid="show_all_column"/>
        FROM hsa_member_coupon_link cl
        where
        <include refid="sql_member_query"/>
        order by cl.reach_time desc,cl.id desc
    </select>

    <select id="countUnwrittenNumByCoupon" resultType="int">
        select
        count(*)
        from
        hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        <include refid="sql_give_query"/>
        <!-- 已过期的根据时间来判断 -->
        and cl.state not in (5,6)
        and coupon_effective_end_time > sysdate()
    </select>

    <select id="countWrittenNumByCoupon" resultType="int">
        select
        count(*)
        from
        hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        <include refid="sql_give_query"/>
        <!-- 已过期的根据时间来判断 -->
        and cl.state = 5
    </select>

    <select id="countUnwrittenNum" resultType="int">
        select
        count(*) unwrittenNum
        from
        hsa_member_coupon_link cl
        where
        <include refid="sql_member_query"/>
        and cl.state not in (5,6)
        and coupon_effective_end_time > sysdate()
    </select>

    <select id="countWrittenNum" resultType="int">
        select
        count(*) writtenNum
        from
        hsa_member_coupon_link cl
        where
        <include refid="sql_member_query"/>
        and cl.state = 5
    </select>

    <select id="countInvalidNum" resultType="int">
        select
        count(*)
        from
        hsa_member_coupon_link cl
        where
        <include refid="sql_member_query"/>
        and
        (
        (cl.coupon_effective_end_time &lt; sysdate() and cl.state != 5)
        or
        cl.state = 6
        )
    </select>

    <select id="countCouponPackage" resultType="com.holderzone.member.common.dto.ItemNum">
        SELECT
        coupon_package_code item,
        COUNT(*) num
        FROM (
        select cl.coupon_package_code
        from hsa_member_coupon_link cl
        where cl.oper_subject_guid = #{qo.operSubjectGuid}
        and cl.is_delete = 0
        and cl.coupon_package_code in
        <foreach collection="qo.couponPackageCodes" item="cp" open="(" separator="," close=")">
            #{cp}
        </foreach>
        ) t
        GROUP BY coupon_package_code
    </select>

    <select id="countUseCouponPackage" resultType="com.holderzone.member.common.dto.ItemNum">
        SELECT
        coupon_package_code item,
        COUNT(*) num
        FROM (
        select
        cl.coupon_package_code
        from hsa_member_coupon_link cl
        where cl.oper_subject_guid = #{qo.operSubjectGuid}
        and cl.is_delete = 0
        and cl.state = 5
        and cl.coupon_package_code in
        <foreach collection="qo.couponPackageCodes" item="cp" open="(" separator="," close=")">
            #{cp}
        </foreach>
        ) t
        GROUP BY coupon_package_code
    </select>

    <select id="countCoupon" resultType="com.holderzone.member.common.dto.ItemNum">
        SELECT
        coupon_code item,
        COUNT(*) num
        FROM (
        select
        cl.coupon_code
        from
        hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        and cl.is_delete = 0
        and cl.coupon_code in
        <foreach collection="qo.couponCodes" item="cp" open="(" separator="," close=")">
            #{cp}
        </foreach>
        ) t
        GROUP BY coupon_code
    </select>

    <select id="countUseCoupon" resultType="com.holderzone.member.common.dto.ItemNum">
        SELECT
        coupon_code item,
        COUNT(*) num
        FROM (
        select
        cl.coupon_code
        from
        hsa_member_coupon_link cl
        left join hsa_member_coupon_use cu on cl.guid = cu.member_coupon_link_guid
        where
        cu.oper_subject_guid = #{qo.operSubjectGuid}
        and cl.is_delete = 0
        and cu.is_delete = 0
        and cl.coupon_code in
        <foreach collection="qo.couponCodes" item="cp" open="(" separator="," close=")">
            #{cp}
        </foreach>
        ) t
        GROUP BY coupon_code
    </select>

    <sql id="sql_member_coupon_query">
        cl.oper_subject_guid = #{qo.operSubjectGuid}
        and cl.is_delete = 0
        <if test="qo.memberGuid != null and qo.memberGuid != ''">
            and cl.member_guid = #{qo.memberGuid}
        </if>
        <!-- 未过期 -->
        <if test="qo.useType == 0">
            and cl.state not in (5,6,4)
            and cl.coupon_effective_end_time > sysdate()
            <!-- 过期时间 -->
            <if test="qo.expireDate != null">
                and cl.coupon_effective_end_time &lt; #{qo.expireDate}
            </if>
        </if>
        <!-- 历史 -->
        <if test="qo.useType != 0">
            and
            (
            cl.state in (5,6)
            or cl.coupon_effective_end_time &lt; sysdate()
            )
        </if>
    </sql>

    <select id="countMemberNum" resultType="int">
        select
        count(*)
        from
        hsa_member_coupon_link cl
        where
        <include refid="sql_member_coupon_query"/>
    </select>

    <select id="listByUseSettlement"
            resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        SELECT
        <include refid="show_base_column"/>
        <include refid="show_rule_column"/>
        FROM hsa_member_coupon_link cl
        where
        <include refid="sql_member_coupon_query"/>

        <if test="qo.discountOptionId != null and qo.discountOptionId.size() > 0">
            and cl.code in
            <foreach collection="qo.discountOptionId" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="qo.couponType != null">
            and cl.coupon_type = #{qo.couponType}
        </if>
        
        <if test="qo.orderGuid != null and qo.orderGuid != ''">
            and cl.order_guid = #{qo.orderGuid}
        </if>

        <!-- 未过期 -->
        <if test="qo.pageTime != null and qo.pageId != null and qo.pageId != ''">
            and (
            cl.reach_time <![CDATA[ < ]]> #{qo.pageTime}
            or
            (
            cl.reach_time <![CDATA[ = ]]> #{qo.pageTime}
            and cl.id &lt; #{qo.pageId}
            )
            )
        </if>
        order by cl.discount_amount desc,cl.id desc
    </select>

    <select id="pageMemberCouponByTime"
            resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        SELECT
        <include refid="show_base_column"/>
        FROM hsa_member_coupon_link cl
        where
        <include refid="sq_member_coupon_list_query"/>
    </select>

    <select id="pageableMemberCouponByTime"
            resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        SELECT
        <include refid="show_base_column"/>
        FROM hsa_member_coupon_link cl
        where
        <include refid="sq_member_coupon_list_query_pageable"/>
    </select>



    <select id="listGuid" resultType="java.lang.String">
        select
        guid
        <include refid="query_coupon_code"/>
    </select>

    <select id="listAllByCode" resultType="com.holderzone.member.common.entity.coupon.HsaMemberCouponLink">
        select
        <include refid="show_base_column"/>
        <include refid="query_coupon_code"/>
    </select>

    <select id="sumOrderPaidAmount"
            resultType="java.math.BigDecimal">
        select
        sum(order_paid_amount) orderPaidAmount
        from(
        SELECT
        cu.order_paid_amount
        FROM hsa_member_coupon_link cl
        left join hsa_member_coupon_use cu on cl.guid = cu.member_coupon_link_guid
        where
        <include refid="sql_use_query"/>
        <!-- 支付后才统计带动消费 -->
        and cu.pay_time is not null
        group by
        cu.order_number
        ) as tmp
    </select>

    <select id="queryTopStoreNameByUse" resultType="java.lang.String">
        select
        store_name
        from
        (
        SELECT
        cu.store_name,
        count(*) useNum
        FROM hsa_member_coupon_link cl
        left join hsa_member_coupon_use cu on cl.guid = cu.member_coupon_link_guid
        where
        <include refid="sql_use_query"/>
        and cu.store_guid is not null
        group by cu.store_guid
        ) as tmp
        order by useNum desc
        limit 1
    </select>
    <sql id="query_coupon_code">
        from
        hsa_member_coupon_link cl
        where
        cl.oper_subject_guid = #{operSubjectGuid}
        and cl.is_delete = 0
        and cl.coupon_code = #{couponCode}
        and cl.code in
        <foreach collection="codes" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </sql>

    <sql id="sq_member_coupon_list_query">
        <include refid="sql_member_coupon_query"/>
        <!-- 未过期 -->
        <if test="qo.useType == 0">

            <if test="qo.pageTime != null and qo.pageId != null and qo.pageId != ''">
                and (
                cl.reach_time <![CDATA[ < ]]> #{qo.pageTime}
                or
                (
                cl.reach_time <![CDATA[ = ]]> #{qo.pageTime}
                and
                cl.id &lt; #{qo.pageId}
                )
                )
            </if>
            order by cl.reach_time desc,cl.id desc
        </if>
        <!-- 历史：分页 -->
        <if test="qo.useType != 0">

            <if test="qo.pageTime != null and qo.pageId != null and qo.pageId != ''">
                and (
                cl.gmt_modified <![CDATA[ < ]]> #{qo.pageTime}
                or
                (
                cl.gmt_modified <![CDATA[ = ]]> #{qo.pageTime}
                and
                cl.id &lt; #{qo.pageId}
                )
                )
            </if>
            order by cl.gmt_modified desc,cl.id desc
        </if>
        <!-- 倒序分页 -->
        limit #{qo.pageSize}
    </sql>

    <sql id="sq_member_coupon_list_query_pageable">
        <include refid="sql_member_coupon_query"/>
        <!-- 未过期 -->
        <if test="qo.useType == 0">

            <if test="qo.pageTime != null and qo.pageId != null and qo.pageId != ''">
                and (
                cl.reach_time <![CDATA[ < ]]> #{qo.pageTime}
                or
                (
                cl.reach_time <![CDATA[ = ]]> #{qo.pageTime}
                and
                cl.id &lt; #{qo.pageId}
                )
                )
            </if>
            order by cl.reach_time desc,cl.id desc
        </if>
        <!-- 历史：分页 -->
        <if test="qo.useType != 0">

            <if test="qo.pageTime != null and qo.pageId != null and qo.pageId != ''">
                and (
                cl.gmt_modified <![CDATA[ < ]]> #{qo.pageTime}
                or
                (
                cl.gmt_modified <![CDATA[ = ]]> #{qo.pageTime}
                and
                cl.id &lt; #{qo.pageId}
                )
                )
            </if>
            order by cl.gmt_modified desc,cl.id desc
        </if>
    </sql>
</mapper>