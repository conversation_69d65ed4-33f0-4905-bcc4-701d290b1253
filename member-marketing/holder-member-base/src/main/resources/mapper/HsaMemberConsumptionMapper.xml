<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.base.mapper.member.HsaMemberConsumptionMapper">

    <select id="getAllConsumption" resultType="java.math.BigDecimal">
        select sum(recharge_amount + gift_amount + subsidy_amount)
        from hsa_member_funding_detail
        where amount_source_type = 3
          and member_info_guid = #{memberGuid}
    </select>

    <select id="getRechargeDetail" resultType="com.holderzone.member.common.vo.card.RechargeDetailVO">
        SELECT
        hmc.gmt_create,
        hmc.order_number,
        hd.card_name,
        hmc.order_source,
        hmc.store_name,
        hd.recharge_amount,
        hd.card_recharge_residual_balance,
        hd.gift_amount,
        hd.card_gift_residual_balance,
        hw.pay_name,
        hmc.is_cancel as orderStatus ,
        hd.card_gift_residual_balance as cardGiftResidualBalance
        FROM
        hsa_member_consumption hmc
        left JOIN hsa_member_consumption_pay_way hw ON hmc.guid = hw.consumption_guid
        left JOIN hsa_member_funding_detail hd ON hd.member_consumption_guid = hmc.guid
        WHERE hmc.consumption_type = 0
        and hmc.is_complete = 1
        AND hmc.member_info_guid = #{request.memberGuid}
        <if test="request.keywords != null and request.keywords != ''">
            and (hd.card_name like CONCAT('%',#{request.keywords},'%')
            or hmc.order_number like CONCAT('%',#{request.keywords},'%')
            or hmc.store_name like CONCAT('%',#{request.keywords},'%'))
        </if>
        <if test="request.sourceType != null ">
            and hmc.order_source = #{request.sourceType}
        </if>
        <if test="request.startRechargeTime != null">
            <![CDATA[and hmc.gmt_create>=DATE_FORMAT(#{request.startRechargeTime},'%Y-%m-%d 00:00:00')]]>
        </if>
        <if test="request.endRechargeTime != null">
            <![CDATA[and hmc.gmt_create<=DATE_FORMAT(#{request.endRechargeTime},'%Y-%m-%d 23:59:59')]]>
        </if>
        order by hmc.gmt_create desc
    </select>


    <select id="getConsumptionDetail" resultType="com.holderzone.member.common.vo.card.ConsumptionDetailVO">
        SELECT
            hmc.gmt_create,
            hmc.consumption_time,
            hmc.order_number,
            hmc.order_type,
            hmc.is_cancel as orderStatus,
            case when mic.electronic_card_num is null then mic.physical_card_num else mic.electronic_card_num end as card_num,
            hmc.order_source,
            hmc.store_guid,
            hmc.store_name,
            hmc.order_amount,
            hmc.order_discount_amount,
            hmc.order_paid_amount,
            GROUP_CONCAT(CONCAT(hw.pay_name,hw.pay_amount) SEPARATOR ',') as payName
        FROM
            hsa_member_consumption hmc
        left JOIN hsa_member_consumption_pay_way hw ON hmc.guid = hw.consumption_guid
        left JOIN hsa_member_info_card mic ON hmc.member_info_card_guid = mic.guid
        WHERE
            hmc.consumption_type = 1
        AND hmc.member_info_guid = #{request.memberGuid}
        <if test="request.keywords != null and request.keywords != ''">
            <choose>
                <when test="request.system != null and request.system == 1">
                    and hmc.order_number like CONCAT('%',#{request.keywords},'%')
                </when>
                <otherwise>
                    and ( hmc.order_number like CONCAT('%',#{request.keywords},'%')
                        or mic.physical_card_num like CONCAT('%',#{request.keywords},'%')
                        or mic.electronic_card_num like CONCAT('%',#{request.keywords},'%') )
                </otherwise>
            </choose>
        </if>
        <if test="request.orderType != null ">
            and hmc.order_type = #{request.orderType}
        </if>
        <if test="request.startRechargeTime != null">
            <![CDATA[and hmc.gmt_create>=DATE_FORMAT(#{request.startRechargeTime},'%Y-%m-%d 00:00:00')]]>
        </if>
        <if test="request.endRechargeTime != null">
            <![CDATA[and hmc.gmt_create<=DATE_FORMAT(#{request.endRechargeTime},'%Y-%m-%d 23:59:59')]]>
        </if>
        <if test="request.startConsumptionTime != null">
            <![CDATA[and hmc.consumption_time>=DATE_FORMAT(#{request.startConsumptionTime},'%Y-%m-%d 00:00:00')]]>
        </if>
        <if test="request.endConsumptionTime != null">
            <![CDATA[and hmc.consumption_time<=DATE_FORMAT(#{request.endConsumptionTime},'%Y-%m-%d 23:59:59')]]>
        </if>
        <if test="request.orderStatus != null ">
            and hmc.is_cancel = #{request.orderStatus}
        </if>
        <if test="request.sourceType != null ">
            and hmc.order_source = #{request.sourceType}
        </if>
        <if test="request.sourceTypes != null and request.sourceTypes.size() > 0">
            and hmc.order_source in
            <foreach collection="request.sourceTypes" item="sourceType" open="(" separator="," close=")">
                #{sourceType}
            </foreach>
        </if>
        GROUP BY hmc.guid
        order by hmc.gmt_create desc
    </select>


    <select id="getTotalConsumptionDetail" resultType="com.holderzone.member.common.vo.member.ConsumptionOrderTotalVO">
        SELECT SUM(order_discount_amount) as allDiscountsAmount,
               SUM(order_paid_amount)     as allPaidAmount,
               COUNT(1)                   as allConsumptionCount
        FROM hsa_member_consumption
        WHERE consumption_type = 1
          and is_cancel = 0
          AND member_info_guid = #{memberGuid}
    </select>

    <select id="getTotalRefundDetail" resultType="com.holderzone.member.common.vo.member.ConsumptionOrderTotalVO">
        SELECT SUM(order_paid_amount) as allAfterSaleAmount,
               COUNT(1)               as allAfterSaleCount
        FROM hsa_member_consumption
        WHERE consumption_type = 1
          and is_cancel = 1
          AND member_info_guid = #{memberGuid}
    </select>

    <select id="getConsumptionDetailByMemberGuid"
            resultType="com.holderzone.member.common.dto.member.MemberConsumptionInfoDTO">
        SELECT
        member_info_guid,
        SUM(order_paid_amount- add_refund_amount) as consumptionAmount,
        MAX(gmt_modified) as consumptionTime
        FROM hsa_member_consumption
        WHERE consumption_type = 1
        and is_cancel = 0
        AND member_info_guid IN
        <foreach collection="memberGuidList" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
        GROUP BY member_info_guid
    </select>

    <select id="getRechargeDetailByMemberGuid"
            resultType="com.holderzone.member.common.dto.member.MemberConsumptionInfoDTO">
        SELECT
        member_info_guid,
        COUNT(1) AS rechargeCount,
        IFNULL(SUM(order_paid_amount),0) AS rechargeAmount
        FROM hsa_member_consumption
        WHERE consumption_type = 0
        and member_info_guid in
        <foreach collection="memberGuidList" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
        and is_complete = 1
        and is_cancel = 0
        and refund_time is null
        GROUP BY member_info_guid
    </select>


    <select id="getAllRecharge" resultType="java.math.BigDecimal">
        select
        sum(
        case
        amount_recharge_funding_type
        when 0 then recharge_amount
        else -recharge_amount end
        )
        from hsa_member_funding_detail
        where member_info_guid = #{memberGuid}
        <if test="type != null ">
            and amount_source_type in (1,0)
        </if>
    </select>

    <select id="totalPeriodAmount" resultType="java.math.BigDecimal">
        select sum(IFNULL(order_paid_amount,0))
        from hsa_member_consumption
        where member_info_guid = #{memberInfoGuid}
        and consumption_type = #{consumptionType}
        and is_cancel = 0 and is_complete = 1
        and refund_time is null
        <if test="gmtCreate!=null">
            and consumption_time > DATE_FORMAT(#{gmtCreate},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="periodType != null and periodType == 0">
            and consumption_time > DATE_FORMAT(#{beginTime},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="periodType != null and periodType != 0 and periodType != -1">
            and consumption_time > DATE_FORMAT(#{beginTime},'%Y-%m-%d 00:00:00')
        </if>
    </select>
    <select id="totalPeriodCount" resultType="java.lang.Integer">
        select count(1)
        from hsa_member_consumption
        where member_info_guid = #{memberInfoGuid}
        and consumption_type = 1
        and is_cancel = 0 and is_complete = 1
        and order_paid_amount >= #{consumptionIgnoreAmount}
        and refund_time is null
        <if test="gmtCreate!=null">
            and consumption_time > DATE_FORMAT(#{gmtCreate},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="periodType != null and periodType == 0">
            and consumption_time > DATE_FORMAT(#{beginTime},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="periodType != null and periodType != 0 and periodType != -1">
            and consumption_time > DATE_FORMAT(#{beginTime},'%Y-%m-%d 00:00:00')
        </if>
    </select>

    <select id="findRelationLabelMemberGuid"
            resultType="com.holderzone.member.common.vo.member.MemberConsumptionInfoVO">
        select
        temp.member_info_guid,
        temp.order_paid_amount
        FROM (
        SELECT
        member_info_guid,
        sum(case when is_cancel = 0 then order_paid_amount else - order_paid_amount end) as "order_paid_amount",
        sum(is_cancel) as "is_cancel"
        FROM
        hsa_member_consumption
        WHERE
        consumption_type = 1
        and oper_subject_guid = #{query.operSubjectGuid}
        and member_info_guid IS NOT NULL
        <if test="query.consumptionStoreJson != null and query.consumptionStoreJson.size > 0 ">
            and store_guid in
            <foreach collection="query.consumptionStoreJson" item="store" open="(" close=")" separator=",">
                #{store.id}
            </foreach>
        </if>
        <if test="query.memberGuid != null and query.memberGuid.size > 0 ">
            and member_info_guid in
            <foreach collection="query.memberGuid" item="guid" open="(" close=")" separator=",">
                #{guid}
            </foreach>
        </if>
        <!-- 累计消费金额类型 1：注册至今 2：最近一段时间 3：固定时间 -->
        <if test="query.consumptionAmountType != null ">
            <choose>
                <!-- 最近一段时间 -->
                <when test="query.consumptionAmountType == 2 and null != query.consumptionAmountRecentDay">
                    and TIMESTAMPDIFF(DAY,gmt_create,NOW()) &lt;= #{query.consumptionAmountRecentDay}
                </when>
                <!-- 固定时间 -->
                <when test="query.consumptionAmountType == 3 and null != query.consumptionAmountFixedDateJson">
                    and gmt_create between #{query.consumptionAmountFixedDateJson[0]} and
                    CONCAT(#{query.consumptionAmountFixedDateJson[1]},' 23:59:59')
                </when>
            </choose>
        </if>
        <!-- 累计消费次数类型 1：注册至今 2：最近一段时间 3：固定时间 -->
        <if test="query.consumptionCountType != null ">
            <choose>
                <!-- 最近一段时间 -->
                <when test="query.consumptionCountType == 2 and null != query.consumptionCountRecentDay">
                    and TIMESTAMPDIFF(DAY,gmt_create,NOW()) &lt;= #{query.consumptionCountRecentDay}
                </when>
                <!-- 固定时间 -->
                <when test="query.consumptionCountType == 3 and null != query.consumptionCountFixedDateJson">
                    and gmt_create between #{query.consumptionCountFixedDateJson[0]} and
                    CONCAT(#{query.consumptionCountFixedDateJson[1]},' 23:59:59')
                </when>
            </choose>
        </if>

        <!-- 累计消费均价类型 1：注册至今 2：最近一段时间 3：固定时间 -->
        <if test="query.consumptionAvgType != null ">
            <choose>
                <!-- 最近一段时间 -->
                <when test="query.consumptionAvgType == 2 and null != query.consumptionAvgRecentDay">
                    and TIMESTAMPDIFF(DAY,gmt_create,NOW()) &lt;= #{query.consumptionAvgRecentDay}
                </when>
                <!-- 固定时间 -->
                <when test="query.consumptionAvgType == 3 and null != query.consumptionAvgFixedDateJson">
                    and gmt_create between #{query.consumptionAvgFixedDateJson[0]} and
                    CONCAT(#{query.consumptionAvgFixedDateJson[1]},' 23:59:59')
                </when>
            </choose>
        </if>
        GROUP BY order_number
        ) temp
        where temp.is_cancel = 0
    </select>

    <select id="getOneOrder" resultType="com.holderzone.member.base.entity.member.HsaMemberConsumption">
        SELECT *
        FROM hsa_member_consumption
        WHERE card_num = #{cardNum}
          and guid != #{memberConsumptionGuid}
          and oper_subject_guid = #{operSubjectGuid}
          and excess = 1
          and excess_type = 1
        GROUP BY order_number
        HAVING COUNT (order_number) > 1
    </select>

    <select id="selectRechargeAmountMemberGuid" resultType="java.math.BigDecimal">
        SELECT sum(order_paid_amount) FROM hsa_member_consumption
        WHERE member_info_guid = #{memberInfoGuid}
        and consumption_type = 0
        and guid &lt;&gt; #{guid}
        and gmt_create &gt;= #{beginTime}
        and refund_time is null
        and is_cancel = 0
        <if test="suspendTaskTime != null and suspendTaskTime.size > 0 ">
            <foreach collection="suspendTaskTime" item="item">
                and gmt_create not BETWEEN #{item.suspendStartTime} and #{item.suspendEndTime}
            </foreach>
        </if>
    </select>

    <select id="selectByMemberGuid" resultType="java.math.BigDecimal">
        SELECT SUM(t.amount) FROM
        (SELECT order_paid_amount as amount FROM hsa_member_consumption
        WHERE member_info_guid = #{memberInfoGuid}
        and consumption_type = 1
        and guid &lt;&gt; #{guid}
        and gmt_create &gt;= #{beginTime}
        and is_cancel = 0
        and is_complete = 1
        and refund_time is null
        and order_type in
        <foreach collection="apply" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="suspendTaskTime != null and suspendTaskTime.size > 0 ">
            <foreach collection="suspendTaskTime" item="item">
                and gmt_create not BETWEEN #{item.suspendStartTime} and #{item.suspendEndTime}
            </foreach>
        </if>
        GROUP BY order_number HAVING COUNT(order_number) = 1) as t
    </select>

    <select id="getConsumptionCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
        SELECT 1 FROM hsa_member_consumption
        WHERE member_info_guid = #{memberInfoGuid}
        AND consumption_type = 1
        and guid &lt;&gt; #{guid}
        and order_paid_amount &gt;= #{consumptionAmount}
        and gmt_create &gt;= #{beginTime}
        and is_complete = 1
        and refund_time is null
        and is_cancel = 0
        and order_type in
        <foreach collection="apply" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="suspendTaskTime != null and suspendTaskTime.size > 0 ">
            <foreach collection="suspendTaskTime" item="item">
                and gmt_create not BETWEEN #{item.suspendStartTime} and #{item.suspendEndTime}
            </foreach>
        </if>
        GROUP BY order_number HAVING COUNT(order_number) = 1) t
    </select>

    <select id="selectByMemberGuidBackAmount" resultType="java.math.BigDecimal">
        SELECT SUM(t.amount) FROM
        (SELECT order_paid_amount as amount FROM hsa_member_consumption
        WHERE member_info_guid = #{memberInfoGuid}
        and guid &lt;&gt; #{guid}
        and consumption_type = 1
        and is_cancel = 0
        and is_complete = 1
        and refund_time is null
        and order_type in
        <foreach collection="apply" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="suspendTaskTime != null and suspendTaskTime.size > 0 ">
            <foreach collection="suspendTaskTime" item="item">
                and gmt_create not BETWEEN #{item.suspendStartTime} and #{item.suspendEndTime}
            </foreach>
        </if>

        <if test="beginTime != null">
            and gmt_create &gt; #{beginTime}
        </if>
        <if test="endTime != null">
            and gmt_create &lt;= #{endTime}
        </if>

        GROUP BY order_number HAVING COUNT(order_number) = 1) as t
    </select>

    <select id="getBackConsumptionCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
        SELECT 1 FROM hsa_member_consumption
        WHERE member_info_guid = #{memberInfoGuid}
        AND consumption_type = 1
        and guid &lt;&gt; #{guid}
        and order_paid_amount &gt;= #{consumptionAmount}
        and is_complete = 1
        and is_cancel = 0
        and refund_time is null
        and order_type in
        <foreach collection="apply" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="suspendTaskTime != null and suspendTaskTime.size > 0 ">
            <foreach collection="suspendTaskTime" item="item">
                and gmt_create not BETWEEN #{item.suspendStartTime} and #{item.suspendEndTime}
            </foreach>
        </if>

        <if test="beginTime != null">
            and gmt_create &gt; #{beginTime}
        </if>
        <if test="endTime != null">
            and gmt_create &lt;= #{endTime}
        </if>
        GROUP BY order_number HAVING COUNT(order_number) = 1) t
    </select>

    <select id="selectRechargeBackAmountMemberGuid" resultType="java.math.BigDecimal">
        SELECT sum(order_paid_amount) FROM hsa_member_consumption
        WHERE member_info_guid = #{memberInfoGuid}
        and consumption_type = 0
        and guid &lt;&gt; #{guid}
        and is_complete = 1
        and refund_time is null
        and is_cancel = 0
        <if test="suspendTaskTime != null and suspendTaskTime.size > 0 ">
            <foreach collection="suspendTaskTime" item="item">
                and gmt_create not BETWEEN #{item.suspendStartTime} and #{item.suspendEndTime}
            </foreach>
        </if>

        <if test="beginTime != null">
            and gmt_create &gt; #{beginTime}
        </if>
        <if test="endTime != null">
            and gmt_create &lt;= #{endTime}
        </if>
    </select>

    <select id="getTotalRefundAmount" resultType="java.math.BigDecimal">
        SELECT SUM(add_refund_amount)
        FROM hsa_member_consumption
        WHERE consumption_type = 1
          and is_cancel = 0
          AND member_info_guid = #{memberGuid}
    </select>

    <select id="getTotalRefundNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM hsa_member_consumption
        WHERE consumption_type = 1
          and is_cancel = 1
          AND member_info_guid = #{memberGuid}
    </select>

    <select id="getMemberTotalRefundAmount" resultType="java.math.BigDecimal">
        select sum(recharge_amount + gift_amount + subsidy_amount)
        from hsa_member_funding_detail
        where amount_source_type = 2
          and member_info_guid = #{memberGuid}
    </select>


    <select id="getMemberAllRecharge" resultType="java.math.BigDecimal">
        select
        sum(
        case
        amount_recharge_funding_type
        when 0 then recharge_amount
        else -recharge_amount end
        )
        from hsa_member_funding_detail
        where member_info_guid = #{memberGuid}
            and amount_source_type in (1,0,21,10)
    </select>

    <select id="getMemberAllGift" resultType="java.math.BigDecimal">
        select sum(
                       case
                           amount_gift_funding_type
                           when 0 then gift_amount
                           else -gift_amount end
               )
        from hsa_member_funding_detail
        where member_info_guid = #{memberGuid}
          and amount_source_type in (1, 0, 21)
    </select>

    <select id="getMemberTotalConsumptionDiscounts" resultType="java.math.BigDecimal">
        SELECT SUM(order_discount_amount)
        FROM hsa_member_consumption
        WHERE consumption_type = 1
          and is_cancel = 0
          and refund_type != 1
          AND member_info_guid = #{memberGuid}
    </select>

    <select id="getMemberTotalConsumptionPaid" resultType="java.math.BigDecimal">
        SELECT SUM(order_paid_amount- add_refund_amount)
        FROM hsa_member_consumption
        WHERE consumption_type = 1
          and is_cancel = 0
          AND member_info_guid = #{memberGuid}
    </select>

    <select id="getMemberTotalConsumptionCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM hsa_member_consumption
        WHERE consumption_type = 1
          and is_cancel = 0
          and refund_type != 1
          AND member_info_guid = #{memberGuid}
    </select>

    <select id="getAllConsumptionRecharge" resultType="java.math.BigDecimal">
        select sum(
                       case
                           is_cancel
                           when 0 then order_paid_amount
                           else -order_paid_amount end
               )

        from hsa_member_consumption
        where consumption_type = 0
          and member_info_guid = #{memberGuid}
          and is_complete = 1
    </select>

    <select id="getAllConsumptionRechargeCount" resultType="java.lang.Integer">

    </select>

    <select id="getMemberConsumptionGift" resultType="java.math.BigDecimal">
        select sum(
                       case
                           amount_gift_funding_type
                           when 0 then gift_amount
                           else -gift_amount end
               )
        from hsa_member_funding_detail
        where member_info_guid = #{memberGuid}
          and amount_source_type in (1, 21)
    </select>

    <select id="getConsumptionCountByMemberGuid"
            resultType="com.holderzone.member.common.dto.member.MemberConsumptionInfoDTO">
        SELECT
        member_info_guid,
        count(1) as consumptionCount
        FROM hsa_member_consumption
        WHERE consumption_type = 1
        and is_cancel = 0
        and refund_type != 1
        AND member_info_guid IN
        <foreach collection="memberGuidList" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
        GROUP BY member_info_guid
    </select>
    <select id="getConsumptionCountNew" resultType="java.lang.String">
        SELECT t.guid FROM (
        SELECT guid FROM hsa_member_consumption
        WHERE member_info_guid = #{memberInfoGuid}
        AND consumption_type = 1
        and guid &lt;&gt; #{guid}
        and order_paid_amount &gt;= #{consumptionAmount}
        and gmt_create &gt;= #{beginTime}
        and is_complete = 1
        and refund_time is null
        and is_cancel = 0
        and order_type in
        <foreach collection="apply" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="suspendTaskTime != null and suspendTaskTime.size > 0 ">
            <foreach collection="suspendTaskTime" item="item">
                and gmt_create not BETWEEN #{item.suspendStartTime} and #{item.suspendEndTime}
            </foreach>
        </if>
        GROUP BY order_number HAVING COUNT(order_number) = 1) t
    </select>
    
    
    <select id="listByMemberGuid"
            resultType="com.holderzone.member.base.entity.member.HsaMemberConsumption">
        SELECT
            *
        FROM
            `hsa_member_consumption`
        WHERE
            member_info_guid = #{memberInfoGuid}
            AND oper_subject_guid = #{operSubjectGuid}
            AND is_cancel = #{orderStatus}
            <if test="consumptionType != null">
                AND consumption_type = #{consumptionType}
            </if>
    </select>

    <update id="updateIsNull">
        update
            hsa_member_consumption
        set refund_time = #{memberConsumption.refundTime}
        where guid = #{memberConsumption.guid}
    </update>

    <update id="updateCompany">
        UPDATE hsa_member_consumption
        SET work_name       = #{memberInfo.workName},
            department_name = #{memberInfo.departmentName}
        WHERE member_info_guid = #{memberInfo.guid};
    </update>
</mapper>