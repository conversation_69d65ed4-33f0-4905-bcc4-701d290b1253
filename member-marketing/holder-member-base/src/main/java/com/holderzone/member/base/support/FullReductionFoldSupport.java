package com.holderzone.member.base.support;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.dto.SettlementBackDiscountDTO;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.base.mapper.member.HsaMemberOrderDiscountMapper;
import com.holderzone.member.base.service.member.HsaMemberLabelService;
import com.holderzone.member.base.service.settlement.assembler.SettlementFullReductionFoldAssembler;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.activity.FullReductionFoldActivityDTO;
import com.holderzone.member.common.enums.ApplyCommodityEnum;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.activity.ConsumptionTypeEnum;
import com.holderzone.member.common.enums.activity.StrategyTypeEnum;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyCommodityDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderInfoDTO;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.constant.SettlementConstant;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRecordQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRunQO;
import com.holderzone.member.common.qo.member.AddMemberLabelCorrelationQO;
import com.holderzone.member.common.qo.member.UpdateLabelCorrelationStatusQO;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityItemRunVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRecordVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRunVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 折扣优惠券工具类
 *
 * <AUTHOR>
 * @date 2024/12/04
 * @since 1.8
 */
@Slf4j
@Component
public class FullReductionFoldSupport {

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private HsaMemberLabelService hsaMemberLabelService;

    @Resource
    private HsaMemberOrderDiscountMapper orderDiscountMapper;

    // 使用时的推荐配置：
    private final BigDecimal MIN_DISCOUNT_THRESHOLD = BigDecimal.valueOf(0.01);

    public SettlementApplyOrderVO getSettlementApplyOrderVO(SettlementApplyOrderDTO dto,
                                                            List<FullReductionFoldActivityRunVO> fullReductionFoldActivityRunVOS,
                                                            SettlementApplyOrderInfoDTO orderInfo) {
        SettlementApplyOrderVO settlementApplyOrderVO = new SettlementApplyOrderVO();
        //初始化优惠项
        List<SettlementApplyDiscountVO> settlementApplyDiscountVOS = initApplyDiscount();

        SettlementApplyDiscountVO settlementApplyDiscountVO = settlementApplyDiscountVOS.get(0);

        //返回活动优惠列表
        List<SettlementApplyDiscountDetailVO> discountList = Lists.newArrayList();

        //最优活动商品列表
        Map<String, List<SettlementApplyCommodityVO>> optimalAmountMap = new HashMap<>();

        optimalCalculate(dto, fullReductionFoldActivityRunVOS, optimalAmountMap, discountList);

        //设置最优活动以及最优商品列表
        setOptimalDiscount(discountList, optimalAmountMap, settlementApplyOrderVO, settlementApplyDiscountVO, orderInfo);

        settlementApplyDiscountVO.setDiscountList(discountList);

        settlementApplyOrderVO.setDiscountList(settlementApplyDiscountVOS);
        return settlementApplyOrderVO;
    }

    private static List<SettlementApplyDiscountVO> initApplyDiscount() {
        List<SettlementApplyDiscountVO> discountList = Lists.newArrayList();
        discountList.add(SettlementApplyDiscountVO.buildByDiscountType(SettlementDiscountOptionEnum.FULL_OFF));
        return discountList;
    }


    private void optimalCalculate(SettlementApplyOrderDTO dto,
                                  List<FullReductionFoldActivityRunVO> fullReductionFoldActivityRunVOS,
                                  Map<String, List<SettlementApplyCommodityVO>> optimalAmountMap,
                                  List<SettlementApplyDiscountDetailVO> discountList) {
        //活动最优总额
        BigDecimal discountAmount = BigDecimal.ZERO;

        //需要计算的商品列表
        List<SettlementApplyCommodityDTO> orderCommodityList = dto.getOrderCommodityList();
        Collections.reverse(orderCommodityList);

        //此操作为  若活动商品都不匹配 则提前准备好返回商品列表
        List<SettlementApplyCommodityVO> notCommodityList = SettlementFullReductionFoldAssembler.getNotApplyCommodityVOS(orderCommodityList);

        //满折满折使用记录
        Map<String, List<HsaMemberOrderDiscount>> activityRecordMap = getActivityRecordMap(fullReductionFoldActivityRunVOS, dto);

        //分别计算活动优惠
        for (FullReductionFoldActivityRunVO activityRunVO : fullReductionFoldActivityRunVOS) {
            SettlementApplyDiscountDetailVO discountDetailVO = SettlementFullReductionFoldAssembler.getSettlementApplyDiscountDetailVO(activityRunVO);
            //0 按阶梯门槛，满足条件后优惠  满 1 单门槛可叠加优惠 每满
            Integer fullReductionTacticsType = activityRunVO.getFullReductionTacticsType();
            //优惠门槛 0 消费门槛 1 购买件数
            Integer discountSillType = activityRunVO.getDiscountSillType();
            List<SettlementApplyCommodityVO> commodityList = Lists.newArrayList();

            //限制次数校验
            checkLimitCount(activityRunVO, activityRecordMap, discountDetailVO);
            if (discountDetailVO.getIsEnabled() == BooleanEnum.TRUE.getCode()) {
                //优惠门槛
                List<FullReductionFoldActivityDTO> fullReductionFoldActivityDTOS =
                        JSON.parseArray(activityRunVO.getFullReductionFoldJson(), FullReductionFoldActivityDTO.class);

                //当前活动满足商品
                List<SettlementApplyCommodityDTO> settlementApplyCommodityDTOS = getSettlementApplyCommodityDTOS(activityRunVO, orderCommodityList);
                
                // 如果门店筛选后没有符合条件的商品，跳过当前活动
				if (CollUtil.isEmpty(settlementApplyCommodityDTOS)) {
					discountDetailVO.setIsEnabled(BooleanEnum.FALSE.getCode());
				} else {
                //商品剩余总额
                BigDecimal commodityResidualFee = getCommodityResidualFee(settlementApplyCommodityDTOS);

                //获取当前订单门槛条件
                BigDecimal commodityResidualValue = getCommodityResidualValue(discountSillType, settlementApplyCommodityDTOS);

                //获取当前参数满足门槛
                FullReductionFoldActivityDTO fullReductionFoldActivityDTO = getFullReductionFoldActivityDTO(
                        fullReductionFoldActivityDTOS,
                        commodityResidualValue);

                log.info("满减满折活动优惠计算门槛信息：{}", JSON.toJSONString(fullReductionFoldActivityDTO));

                if (Objects.nonNull(fullReductionFoldActivityDTO)) {

                    //获取优惠金额
                    BigDecimal singleDiscountAmount = getDiscountAmount(
                            fullReductionTacticsType,
                            commodityResidualValue,
                            fullReductionFoldActivityDTO,
                            commodityResidualFee);
                    log.info("优惠金额：{}", JSON.toJSONString(singleDiscountAmount));

                    //计算商品分摊金额 并组装商品列表
                    calculateCommodityDiscountFee(settlementApplyCommodityDTOS, commodityResidualFee, singleDiscountAmount, commodityList);

                    discountAmount = getOptimizeDiscountAmount(optimalAmountMap, discountAmount, commodityList, discountDetailVO);
					}
				}
            } else {
                //置换最优项
                discountAmount = getOptimizeDiscountAmount(optimalAmountMap, discountAmount, notCommodityList, discountDetailVO);
            }
            discountDetailVO.setIsEnabled(activityRunVO.getIsEnabled());
            discountList.add(discountDetailVO);
        }
    }

    private Map<String, List<HsaMemberOrderDiscount>> getActivityRecordMap(List<FullReductionFoldActivityRunVO> fullReductionFoldActivityRunVOS,
                                                                           SettlementApplyOrderDTO dto) {
        //限制次数校验
        Map<String, List<HsaMemberOrderDiscount>> activityRecordMap = new HashMap<>();
        if (StringUtils.isEmpty(dto.getOrderInfo().getMemberInfoGuid())) {
            return activityRecordMap;
        }
        List<String> activityCodeList = fullReductionFoldActivityRunVOS.stream().map(FullReductionFoldActivityRunVO::getActivityCode)
                .collect(Collectors.toList());
        List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = orderDiscountMapper.selectList(new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                .in(HsaMemberOrderDiscount::getDiscountGuid, activityCodeList)
                .eq(HsaMemberOrderDiscount::getDiscountOption, SettlementDiscountOptionEnum.FULL_OFF.getCode())
                .eq(HsaMemberOrderDiscount::getDiscountState, BooleanEnum.FALSE.getCode())
                .eq(HsaMemberOrderDiscount::getMemberGuid, dto.getOrderInfo().getMemberInfoGuid()));
        if (CollUtil.isNotEmpty(hsaMemberOrderDiscounts)) {
            activityRecordMap = hsaMemberOrderDiscounts.stream()
                    .collect(Collectors.groupingBy(HsaMemberOrderDiscount::getDiscountGuid));
        }
        return activityRecordMap;
    }

    private static void checkLimitCount(FullReductionFoldActivityRunVO activityRunVO,
                                        Map<String, List<HsaMemberOrderDiscount>> activityRecordMap,
                                        SettlementApplyDiscountDetailVO discountDetailVO) {
        if (CollUtil.isNotEmpty(activityRecordMap) && activityRecordMap.containsKey(activityRunVO.getActivityCode())) {
            List<HsaMemberOrderDiscount> fullReductionFoldActivityRecords = activityRecordMap.get(activityRunVO.getActivityCode());
            if (fullReductionFoldActivityRecords.size() >= activityRunVO.getLimitCount() && activityRunVO.getLimitCount() > 0) {
                discountDetailVO.setIsEnabled(BooleanEnum.FALSE.getCode());
            }
        }
    }

    private static BigDecimal getOptimizeDiscountAmount(Map<String, List<SettlementApplyCommodityVO>> optimalAmountMap,
                                                        BigDecimal discountAmount,
                                                        List<SettlementApplyCommodityVO> commodityList,
                                                        SettlementApplyDiscountDetailVO discountDetailVO) {
        //置换最优项
        discountAmount = exchangeOptimizeDiscount(optimalAmountMap, commodityList, discountDetailVO, discountAmount);

        discountDetailVO.setCommodityList(commodityList);
        return discountAmount;
    }

    private void calculateCommodityDiscountFee(List<SettlementApplyCommodityDTO> settlementApplyCommodityDTOS,
                                               BigDecimal commodityResidualFee,
                                               BigDecimal discountAmount,
                                               List<SettlementApplyCommodityVO> commodityList) {
        //除去最后一个的总优惠 用于兜底
        BigDecimal sumDiscountFee = BigDecimal.ZERO;

        final int endSize = settlementApplyCommodityDTOS.size() - 1;
        for (int i = 0; i < endSize; i++) {
            SettlementApplyCommodityVO commodityDTO = new SettlementApplyCommodityVO();
            final SettlementApplyCommodityDTO applyCommodityDTO = settlementApplyCommodityDTOS.get(i);
            commodityDTO.setCommodityId(applyCommodityDTO.getCommodityId());
            commodityDTO.setCommodityName(applyCommodityDTO.getCommodityName());
            commodityDTO.setSkuId(applyCommodityDTO.getSkuId());
            commodityDTO.setRid(applyCommodityDTO.getRid());

            //单行商品分摊金额计算
            BigDecimal currentCommodityDiscount = getCommodityDiscountFee(applyCommodityDTO, commodityResidualFee, discountAmount);

            if (currentCommodityDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("商品{}优惠金额{}小于等于0，不参与优惠", applyCommodityDTO.getCommodityName(), currentCommodityDiscount);
                continue;
            }
            commodityDTO.setDiscountFee(currentCommodityDiscount);
            commodityDTO.setShareDiscountFee(currentCommodityDiscount);
            //累加
            sumDiscountFee = sumDiscountFee.add(currentCommodityDiscount);
            commodityList.add(commodityDTO);
        }
        //最后一个优惠
        setAfterCommodity(settlementApplyCommodityDTOS, endSize, discountAmount, sumDiscountFee, commodityList);
    }


    private static BigDecimal exchangeOptimizeDiscount(Map<String, List<SettlementApplyCommodityVO>> optimalAmountMap,
                                                       List<SettlementApplyCommodityVO> commodityList,
                                                       SettlementApplyDiscountDetailVO discountDetailVO,
                                                       BigDecimal discountAmount) {
        //累计活动总优惠金额
        BigDecimal activityDiscountFee =
                commodityList
                        .stream()
                        .map(SettlementApplyCommodityVO::getDiscountFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

        discountDetailVO.setDiscountAmount(activityDiscountFee);

        if (activityDiscountFee.compareTo(discountAmount) > 0 || CollUtil.isEmpty(optimalAmountMap)) {
            optimalAmountMap.clear();
            optimalAmountMap.put(discountDetailVO.getDiscountGuid(), commodityList);
            discountAmount = activityDiscountFee;
        }
        return discountAmount;
    }

    private static void setAfterCommodity(List<SettlementApplyCommodityDTO> commodityRespList,
                                          int endSize, BigDecimal totalDiscountFee,
                                          BigDecimal sumDiscountFee,
                                          List<SettlementApplyCommodityVO> commodityList) {

        final SettlementApplyCommodityDTO endCommodityDto = commodityRespList.get(endSize);
        BigDecimal afterDiscountAmount = totalDiscountFee.subtract(sumDiscountFee);
        SettlementApplyCommodityVO commodityDTO = new SettlementApplyCommodityVO();
        commodityDTO.setCommodityId(endCommodityDto.getCommodityId());
        commodityDTO.setCommodityName(endCommodityDto.getCommodityName());
        commodityDTO.setSkuId(endCommodityDto.getSkuId());
        commodityDTO.setRid(endCommodityDto.getRid());
        commodityDTO.setDiscountFee(afterDiscountAmount);
        commodityDTO.setShareDiscountFee(afterDiscountAmount);
        commodityList.add(commodityDTO);
    }

    /**
     * 优惠金额
     *
     * @param applyCommodityDTO    商品
     * @param commodityResidualFee 当前商品剩余总额
     * @param realityDiscountFee 当前商品优惠金额
     * @return 优惠
     */
    private BigDecimal getCommodityDiscountFee(SettlementApplyCommodityDTO applyCommodityDTO,
                                               BigDecimal commodityResidualFee,
                                               BigDecimal realityDiscountFee) {
        //当前商品剩余可用
        final BigDecimal currentCommodityResidualFee = Objects.nonNull(applyCommodityDTO.getAfterDiscountTotalPrice()) ?
                applyCommodityDTO.getAfterDiscountTotalPrice() :
                applyCommodityDTO.getDiscountTotalPriceInShopCar();

        //比列：当前商品剩余总额/ 所有商品剩余总额
        final BigDecimal scale = currentCommodityResidualFee.divide(commodityResidualFee, SettlementConstant.SCALE_LENGTH, RoundingMode.DOWN);
        //最大优惠金额 * 比例
        final BigDecimal discountFee = realityDiscountFee.multiply(scale).setScale(2, RoundingMode.DOWN);
        //不超过商品剩余金额
        return BigDecimalUtil.min(discountFee, currentCommodityResidualFee);
    }

    public static BigDecimal getDiscountAmount(Integer fullReductionTacticsType,
                                               BigDecimal commodityResidualValue,
                                               FullReductionFoldActivityDTO fullReductionFoldActivityDTO,
                                               BigDecimal commodityResidualFee) {
        //优惠总额
        BigDecimal totalDiscount;

        BigDecimal discountAmount = BigDecimal.ZERO;

        if (fullReductionTacticsType == StrategyTypeEnum.MULTIPLE.getCode()) {
            //满足倍数
            BigDecimal numDiscount = commodityResidualValue
                    .divide(fullReductionFoldActivityDTO.getConsumptionMoney())
                    .setScale(0, RoundingMode.DOWN);
            if (fullReductionFoldActivityDTO.getConsumptionType() == ConsumptionTypeEnum.PERCENTAGE.getCode()){
                totalDiscount = numDiscount.multiply(fullReductionFoldActivityDTO.getConsumptionMoney());
            }else {
                totalDiscount = numDiscount.multiply(fullReductionFoldActivityDTO.getConsumptionNumber());
            }

        } else {
            if (fullReductionFoldActivityDTO.getConsumptionType() == ConsumptionTypeEnum.PERCENTAGE.getCode()){
                totalDiscount = commodityResidualFee;
            }else {
                totalDiscount = fullReductionFoldActivityDTO.getConsumptionNumber();
            }

        }

        //计算实际优惠金额
        //优惠金额/折扣
        if (fullReductionFoldActivityDTO.getConsumptionType() == ConsumptionTypeEnum.FIXED_AMOUNT.getCode()) {
            discountAmount = commodityResidualFee.subtract(totalDiscount);
            discountAmount = discountAmount.compareTo(BigDecimal.ZERO) < 0 ? commodityResidualFee : totalDiscount;
        }
        //折扣优惠
        else {
            BigDecimal discountDynamics = fullReductionFoldActivityDTO.getConsumptionNumber().divide(BigDecimal.TEN);
            //若小于等于0.01就无优惠
            if (totalDiscount.compareTo(NumberConstant.DECIMAL_POINT_001) >= 0) {
                //商品优惠价格
                BigDecimal commodityDiscount = totalDiscount.multiply(discountDynamics);

                //优惠金额
                discountAmount = checkDiscountAmount(commodityDiscount, totalDiscount);
            }
        }
        return discountAmount;
    }

    private static BigDecimal getCommodityResidualValue(Integer discountSillType, List<SettlementApplyCommodityDTO> settlementApplyCommodityDTOS) {
        BigDecimal commodityResidualValue;
        if (discountSillType == 0) {
            //商品剩余总额
            commodityResidualValue = getCommodityResidualFee(settlementApplyCommodityDTOS);
        } else {
            //商品剩余总数量
            commodityResidualValue = getCommodityResidualNum(settlementApplyCommodityDTOS);
        }
        return commodityResidualValue;
    }

    private static FullReductionFoldActivityDTO getFullReductionFoldActivityDTO(List<FullReductionFoldActivityDTO> fullReductionFoldActivityDTOS,
                                                                                BigDecimal commodityResidualValue) {
        FullReductionFoldActivityDTO fullReductionFoldActivityDTO = null;

        //满=阶梯优惠  选出满足最高金额的门槛
        for (FullReductionFoldActivityDTO reductionFoldActivityDTO : fullReductionFoldActivityDTOS) {
            if (commodityResidualValue.compareTo(reductionFoldActivityDTO.getConsumptionMoney()) >= 0) {
                fullReductionFoldActivityDTO = reductionFoldActivityDTO;
            }
        }
        return fullReductionFoldActivityDTO;
    }

    private static void setOptimalDiscount(List<SettlementApplyDiscountDetailVO> discountList,
                                           Map<String, List<SettlementApplyCommodityVO>> optimalAmountMap,
                                           SettlementApplyOrderVO settlementApplyOrderVO,
                                           SettlementApplyDiscountVO settlementApplyDiscountVO,
                                           SettlementApplyOrderInfoDTO orderInfo) {
        for (SettlementApplyDiscountDetailVO discount : discountList) {
            if (optimalAmountMap.containsKey(discount.getDiscountGuid())) {

                //可能会存在优惠金额为0 此时不设置最优标识
                if (discount.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                    discount.setOptimal(BooleanEnum.TRUE.getCode());
                }

                settlementApplyOrderVO.setCommodityList(optimalAmountMap.get(discount.getDiscountGuid()));

                settlementApplyDiscountVO.setDiscountOption(SettlementDiscountOptionEnum.FULL_OFF.getCode());
                settlementApplyDiscountVO.setDiscountName("满减满折活动");
                settlementApplyDiscountVO.setDiscountAmount(discount.getDiscountAmount());

                //订单优惠金额
                SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO()
                        .setDiscountAmount(discount.getDiscountAmount())
                        .setDiscountOption(SettlementDiscountOptionEnum.FULL_OFF.getCode())
                        .setCommodityTotalAmount(orderInfo.getCommodityTotalAmount());
                settlementApplyOrderVO.setOderInfo(oderInfo);
                break;
            }
        }

        //优惠排序
        discountList = discountList
                .stream()
                .sorted(Comparator.comparing(SettlementApplyDiscountDetailVO::getDiscountAmount).reversed())
                .collect(Collectors.toList());

        log.info("满减满折活动优惠排序结果：{}", JSON.toJSONString(discountList));
    }


    private static BigDecimal getCommodityResidualFee(List<SettlementApplyCommodityDTO> settlementApplyCommodityDTOS) {
        return settlementApplyCommodityDTOS.stream()
                //数量 x 单价
                .map(c -> Objects.nonNull(c.getAfterDiscountTotalPrice()) ?
                        c.getAfterDiscountTotalPrice() : c.getDiscountTotalPriceInShopCar().subtract(c.getDiscountFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static BigDecimal getCommodityResidualNum(List<SettlementApplyCommodityDTO> settlementApplyCommodityDTOS) {
        BigDecimal commodityResidualNum = BigDecimal.ZERO;
        for (SettlementApplyCommodityDTO commodityDTO : settlementApplyCommodityDTOS) {
            BigDecimal totalPrice = Objects.nonNull(commodityDTO.getAfterDiscountTotalPrice()) ?
                    commodityDTO.getAfterDiscountTotalPrice() : commodityDTO.getDiscountTotalPriceInShopCar();
            //可优惠金额为0或兑换券数量等于商品数量，则不满足门槛
            if (totalPrice.compareTo(BigDecimal.ZERO) < 0
                    || commodityDTO.getCommodityNum().compareTo(commodityDTO.getExchangeDiscountNum()) == 0) {
                continue;
            }
            commodityResidualNum = commodityResidualNum.add(commodityDTO.getCommodityNum().subtract(commodityDTO.getExchangeDiscountNum()));
        }
        //去除小数 预防称重商品
        commodityResidualNum = commodityResidualNum.setScale(0, RoundingMode.DOWN);
        return commodityResidualNum;
    }

    private static BigDecimal checkDiscountAmount(BigDecimal discountCommodityTotalAmount, BigDecimal cumulativeUnitPrice) {
        if (discountCommodityTotalAmount.compareTo(NumberConstant.DECIMAL_POINT_001) <= 0) {
            discountCommodityTotalAmount = BigDecimal.ZERO;
        } else {
            //折后商品总价
            discountCommodityTotalAmount = cumulativeUnitPrice.subtract(discountCommodityTotalAmount);
        }
        return discountCommodityTotalAmount.setScale(SettlementConstant.FINAL_LENGTH, RoundingMode.HALF_UP);
    }

    private static List<SettlementApplyCommodityDTO> getSettlementApplyCommodityDTOS(FullReductionFoldActivityRunVO activityRunVO,
                                                                                     List<SettlementApplyCommodityDTO> orderCommodityList) {
        // 获取活动适用门店集合
        Set<String> activityStoreGuids = getActivityStoreGuids(activityRunVO);
        
        // 商品范围筛选的映射表
        Map<String, FullReductionFoldActivityItemRunVO> applyCommodityMap = new HashMap<>();
        if (CollUtil.isNotEmpty(activityRunVO.getFoldActivityItemRunVOList())) {
            applyCommodityMap = activityRunVO.getFoldActivityItemRunVOList().stream()
                    .collect(Collectors.toMap(FullReductionFoldActivityItemRunVO::getCommodityCode, Function.identity(), (entity1, entity2) -> entity1));
        }
        
        List<SettlementApplyCommodityDTO> settlementApplyCommodityDTOS = new ArrayList<>();
        for (SettlementApplyCommodityDTO commodityDTO : orderCommodityList) {
            // 门店筛选
            if (!isStoreApplicable(activityStoreGuids, commodityDTO.getStoreGuid())) {
                continue;
            }
            
            if (activityRunVO.getApplyCommodity() == ApplyCommodityEnum.ALL.getCode()
                    || Objects.nonNull(applyCommodityMap.get(commodityDTO.getCommodityCode()))) {
                //如果此商品售价已经优惠为0 则不再参与分摊
                if (commodityDTO.checkApplyCommodity()) {
                    continue;
                }
                settlementApplyCommodityDTOS.add(commodityDTO);
            }
        }
        return settlementApplyCommodityDTOS;
    }

    /**
     * 获取活动适用的门店GUID集合
     */
    private static Set<String> getActivityStoreGuids(FullReductionFoldActivityRunVO activityRunVO) {
        // 如果是部分门店活动，返回门店GUID集合
        if (CollUtil.isNotEmpty(activityRunVO.getStoreGuidList())) {
            return new HashSet<>(activityRunVO.getStoreGuidList());
        }
        
        // 如果门店列表为空，表示全门店活动，返回空Set
        return Collections.emptySet();
    }

    /**
     * 判断门店是否适用于当前活动
     */
    private static boolean isStoreApplicable(Set<String> activityStoreGuids, String storeGuid) {
        // 如果是全门店活动，直接返回true
        if (CollUtil.isEmpty(activityStoreGuids)) {
            return true;
        }
        
        // 检查商品门店是否在活动门店列表中
        return activityStoreGuids.contains(storeGuid);
    }

    /**
     * 取消标签
     */
    public void cancelMarkLabel(SettlementBackDiscountDTO discountDTO,
                                List<HsaMemberOrderDiscount> discountList) {
        List<String> activityCodeList = discountList.stream()
                .map(HsaMemberOrderDiscount::getDiscountGuid)
                .distinct()
                .collect(Collectors.toList());

        // 查询满减满折
        FullReductionFoldActivityRunQO activityRunQO = new FullReductionFoldActivityRunQO();
        activityRunQO.setOperSubjectGuid(discountDTO.getOperSubjectGuid());
        activityRunQO.setDiscountGuidList(activityCodeList);
        List<FullReductionFoldActivityRunVO> limitSpecialsActivityRunVOList = memberMarketingFeign.queryRunInfo(activityRunQO);
        if (CollectionUtils.isEmpty(limitSpecialsActivityRunVOList)) {
            log.warn("没有满减满折活动");
            return;
        }
        Map<String, FullReductionFoldActivityRunVO> activityRunVOMap = limitSpecialsActivityRunVOList.stream()
                .collect(Collectors.toMap(FullReductionFoldActivityRunVO::getActivityCode, Function.identity(), (v1, v2) -> v1));

        // 查询满减满折记录
        FullReductionFoldActivityRecordQO activityRecordQO = new FullReductionFoldActivityRecordQO();
        activityRecordQO.setActivityCodeList(activityCodeList);
        String memberGuid = discountList.get(0).getMemberGuid();
        activityRecordQO.setMemberGuid(memberGuid);
        log.info("[查询满减满折记录][参数]activityRecordQO={}", JacksonUtils.writeValueAsString(activityRecordQO));
        List<FullReductionFoldActivityRecordVO> activityRecordVOList = memberMarketingFeign.queryRecord(activityRecordQO);
        log.info("[查询满减满折记录][返回]activityRecordVOList={}", JacksonUtils.writeValueAsString(activityRecordVOList));
        Map<String, List<FullReductionFoldActivityRecordVO>> activityRecordVOGroupMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(activityRecordVOList)) {
            activityRecordVOList.removeIf(ar -> ar.getOrderNo().equals(discountDTO.getOrderNo()));
            if (!CollectionUtils.isEmpty(activityRecordVOList)) {
                activityRecordVOGroupMap = activityRecordVOList.stream()
                        .collect(Collectors.groupingBy(FullReductionFoldActivityRecordVO::getActivityCode));
            }
        }

        List<String> labelGuid = new ArrayList<>();
        for (HsaMemberOrderDiscount discount : discountList) {
            FullReductionFoldActivityRunVO activityRunVO = activityRunVOMap.get(discount.getDiscountGuid());
            if (ObjectUtils.isEmpty(activityRunVO)) {
                continue;
            }
            List<FullReductionFoldActivityRecordVO> recordVOList = activityRecordVOGroupMap.get(discount.getDiscountGuid());
            if (CollectionUtils.isEmpty(recordVOList) && !CollectionUtils.isEmpty(activityRunVO.getLabelGuidList())) {
                labelGuid.addAll(activityRunVO.getLabelGuidList());
            }
        }
        log.info("[需要取消的标签]labelGuid={}", labelGuid);
        if (CollectionUtils.isEmpty(labelGuid)) {
            return;
        }

        // 取消标签
        UpdateLabelCorrelationStatusQO statusQo = new UpdateLabelCorrelationStatusQO();
        statusQo.setLabelGuid(labelGuid);
        statusQo.setMemberGuid(memberGuid);
        log.info("[取消标签]statusQo={}", JacksonUtils.writeValueAsString(statusQo));
        hsaMemberLabelService.autoCancelConnection(statusQo);
    }

    /**
     * 批量打标签
     */
    public void markLabel(SettlementPayAfterDiscountDTO discountDTO,
                          List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts) {
        AddMemberLabelCorrelationQO memberLabelQO = new AddMemberLabelCorrelationQO();
        List<String> discountGuidList = hsaMemberOrderDiscounts.stream()
                .map(HsaMemberOrderDiscount::getDiscountGuid)
                .distinct()
                .collect(Collectors.toList());
        FullReductionFoldActivityRunQO activityRunQO = new FullReductionFoldActivityRunQO();
        activityRunQO.setOperSubjectGuid(discountDTO.getOperSubjectGuid());
        activityRunQO.setDiscountGuidList(discountGuidList);
        List<FullReductionFoldActivityRunVO> limitSpecialsActivityRunVOList = memberMarketingFeign.queryRunInfo(activityRunQO);
        if (CollectionUtils.isEmpty(limitSpecialsActivityRunVOList)) {
            log.warn("没有满减满折活动");
            return;
        }
        List<String> labelGuidList = limitSpecialsActivityRunVOList.stream()
                .filter(a -> !CollectionUtils.isEmpty(a.getLabelGuidList()))
                .flatMap(a -> a.getLabelGuidList().stream())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(labelGuidList)) {
            log.warn("活动无需打标签");
            return;
        }
        memberLabelQO.setLabelGuid(labelGuidList);
        List<String> operationMemberInfoGuidList = hsaMemberOrderDiscounts.stream()
                .map(HsaMemberOrderDiscount::getMemberGuid)
                .distinct()
                .collect(Collectors.toList());
        memberLabelQO.setMemberInfoGuid(operationMemberInfoGuidList);
        String batchId = String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        memberLabelQO.setBatchId(batchId);
        log.info("[批量打标签]memberLabelQO={}", JacksonUtils.writeValueAsString(memberLabelQO));
        hsaMemberLabelService.addMemberInfoLabel(memberLabelQO);
    }


    /**
     * 计算商品折扣金额（优化版）
     *
     * @param strategyType 策略类型（1-倍数优惠 0-固定优惠）
     * @param commodityValue 商品剩余价值（参与优惠计算的部分）
     * @param activity 满减活动配置信息
     * @param residualFee 商品剩余可优惠金额
     * @return 实际可应用的折扣金额
     *
     * 主要流程：
     * 1. 根据策略类型计算总优惠基数
     * 2. 根据优惠类型（定额/百分比）应用实际折扣
     * 3. 确保金额计算安全性和业务合理性
     */
    public BigDecimal calculateDiscountAmount(Integer strategyType,
                                              BigDecimal commodityValue,
                                              FullReductionFoldActivityDTO activity,
                                              BigDecimal residualFee) {
        // 提前获取DTO数据避免重复调用（提升性能）
        final BigDecimal consumptionMoney = activity.getConsumptionMoney();
        final BigDecimal consumptionNumber = activity.getConsumptionNumber();
        final int consumptionType = activity.getConsumptionType();

        // 阶段1：计算优惠基数（倍数策略需要计算倍数）
        BigDecimal totalDiscount = calculateTotalDiscount(strategyType, commodityValue, consumptionMoney, consumptionNumber);

        // 阶段2：根据优惠类型应用折扣
        return consumptionType == ConsumptionTypeEnum.FIXED_AMOUNT.getCode() ?
                handleFixedAmountDiscount(totalDiscount, consumptionNumber) :
                handlePercentageDiscount(consumptionNumber, totalDiscount);
    }

    /**
     * 计算总优惠基数
     *
     * @param strategyType 策略类型（MULTIPLE-倍数策略/FIXED-固定策略）
     * @param commodityValue 商品剩余价值
     * @param consumptionMoney 消费金额要求（用于倍数计算）
     * @param consumptionNumber 优惠基数（倍数策略时为每倍优惠值）
     *
     * @return 总优惠基数（未与商品实际金额比较的原始优惠值）
     *
     * 示例：
     * - 策略类型1（倍数）：商品价值100，消费要求50，每倍优惠10 → 100/50=2倍 → 2 * 10=20
     * - 策略类型2（固定）：直接返回配置的优惠数
     */
    private static BigDecimal calculateTotalDiscount(Integer strategyType,
                                                     BigDecimal commodityValue,
                                                     BigDecimal consumptionMoney,
                                                     BigDecimal consumptionNumber) {
        if (strategyType == StrategyTypeEnum.MULTIPLE.getCode()) {
            //满足倍数
            return commodityValue
                    .divide(consumptionMoney)
                    .setScale(0, RoundingMode.DOWN).multiply(consumptionMoney);
        } else {
            return consumptionNumber;
        }
    }

    /**
     * 处理定额优惠逻辑
     * 安全控制：
     * - 确保不会出现负优惠
     * - 不会超过商品剩余金额
     */
    private static BigDecimal handleFixedAmountDiscount(BigDecimal totalDiscount,
                                                        BigDecimal consumptionNumber) {
        BigDecimal subAmount = totalDiscount.subtract(consumptionNumber);
        return subAmount.compareTo(BigDecimal.ZERO) < 0 ? consumptionNumber : totalDiscount;
    }

    /**
     * 处理百分比折扣逻辑
     * 业务规则：
     * - 当剩余金额<0.01时不应用任何折扣
     * - 折扣率需要除以10转换为小数（如配置85→实际0.85）
     */
    private BigDecimal handlePercentageDiscount(BigDecimal consumptionNumber,
                                                BigDecimal totalDiscount) {
        if (isDiscountApplicable(totalDiscount)) {
            // 转换折扣率（85 → 0.85）并保留两位小数
            BigDecimal discountFactor = consumptionNumber.divide(BigDecimal.TEN, SettlementConstant.FINAL_LENGTH, RoundingMode.HALF_UP);
            // 计算折扣金额（商品金额 × 折扣率）
            BigDecimal calculatedDiscount = totalDiscount.multiply(discountFactor);
            //优惠金额
            return checkDiscountAmount(calculatedDiscount, totalDiscount);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 判断是否满足折扣应用条件
     *
     * @param residualFee 剩余可优惠金额
     * @return 当金额≥0.01时可应用折扣
     *
     * 注意：使用BigDecimal.compareTo避免浮点精度问题
     */
    private boolean isDiscountApplicable(BigDecimal residualFee) {
        return residualFee.compareTo(MIN_DISCOUNT_THRESHOLD) >= 0;
    }
}