package com.holderzone.member.base.controller.wechat;


import com.holderzone.member.base.manage.MemberCouponQueryManage;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.coupon.MemberCouponListQO;
import com.holderzone.member.common.qo.coupon.MemberCouponNumQO;
import com.holderzone.member.common.qo.coupon.MemberCouponQO;
import com.holderzone.member.common.vo.coupon.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 小程序：会员优惠劵明细
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@RestController
@RequestMapping("/applets/coupon")
public class WeChatMemberCouponController {

    @Resource
    private MemberCouponQueryManage couponQueryManage;

    @Resource
    private IHsaMemberCouponLinkService memberCouponLinkService;

    @Resource
    private ShortMessageSendService sendService;

    /**
     * 按时间分页查询已领券
     *
     * @param qo 查询参数
     * @return 券列表
     */
    @PostMapping(value = "/pageMemberCouponByTime")
    Result<List<MemberCouponWxVO>> pageMemberCouponByTime(@RequestBody MemberCouponListQO qo) {
        return Result.success(couponQueryManage.pageMemberCouponByTime(qo));
    }

    /**
     * 分页查询会员优惠券（按时间排序）
     *
     * @param qo 查询条件
     * @return 分页结果
     */
    @PostMapping(value = "/pageableMemberCouponByTime")
    Result<PageResult<MemberCouponWxVO>> pageableMemberCouponByTime(@RequestBody MemberCouponQO qo) {
        return Result.success(couponQueryManage.pageableMemberCouponByTime(qo));
    }

    /**
     * 优惠券详情
     *
     * @return 优惠券
     */
    @GetMapping(value = "/detail/{memberCouponGuid}")
    Result<MemberCouponWxDetailVO> detail(@PathVariable String memberCouponGuid) {
        return Result.success(couponQueryManage.detail(memberCouponGuid));
    }

    /**
     * 会员账户详情: 发放数量统计
     *
     * @param qo 查询参数
     * @return 券统计
     */
    @PostMapping(value = "/countMemberNum")
    Result<MemberCouponWxCountVO> countMemberNum(@RequestBody MemberCouponNumQO qo) {
        return Result.success(couponQueryManage.countMemberNum(qo));
    }

    /**
     * 获取二维码：60s过期
     *
     * @param memberCouponGuid 会员优惠券guid
     * @return 二维码
     */
    @ApiOperation("获取二维码")
    @GetMapping(value = "/getQrcode", produces = "application/json;charset=utf-8")
    Result<CouponQrCodeVO> getQrCode(String memberCouponGuid) {
        return Result.success(memberCouponLinkService.getQrCode(memberCouponGuid));
    }


    /**
     * 发送会员优惠券通知
     *
     * @param memberCouponLinks 优惠券列表
     */
    @PostMapping(value = "/sendMemberCouponNotice", produces = "application/json;charset=utf-8")
    void sendMemberCouponNotice(@RequestBody List<MemberCouponPackageVO> memberCouponLinks) {
        memberCouponLinkService.sendMemberCouponNotice(memberCouponLinks);
        sendService.sendMemberCouponNotice(memberCouponLinks);
    }

    /**
     * 发送会员优惠券过期通知
     */
    @GetMapping(value = "/sendMemberCouponExpireNotice", produces = "application/json;charset=utf-8")
    void sendMemberCouponExpireNotice() {
        memberCouponLinkService.sendMemberCouponExpireNotice();
    }
}

