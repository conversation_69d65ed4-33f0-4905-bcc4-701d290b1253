package com.holderzone.member.base.controller.base;

import com.holderzone.framework.util.Page;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.ShopBaseService;
import com.holderzone.member.base.client.StoreBaseService;
import com.holderzone.member.base.service.base.HsaApplyDictionariesService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.base.InitSubjectDataDTO;
import com.holderzone.member.common.dto.base.StoreCountDTO;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.qo.base.CommodityBasePageQO;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import com.holderzone.member.common.vo.equities.ApplyModuleVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import com.holderzone.member.common.vo.ipass.TeamInfoVO;
import com.holderzone.member.common.dto.s2b2c.StorePageQueryDTO;
import com.holderzone.member.common.dto.base.StoreBaseInfo;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Arrays;

import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础数据
 * @date 2021/8/26 14:18
 */
@RestController
@RequestMapping("/hsa-base")
@Slf4j
public class OperatingSubjectController {

    @Resource
    private RequestGoalgoService operatingSubjectService;

    @Resource
    private StoreBaseService storeBaseService;

    @Resource
    private ShopBaseService shopBaseService;

    @Resource
    private HsaApplyDictionariesService hsaApplyDictionariesService;


    /**
     * 获取基础数据
     * @return Result
     */
    @ApiOperation("获取基础数据")
    @GetMapping(value = "/getOperatingSubjectInfo", produces = "application/json;charset=utf-8")
    public Result getOperatingSubjectInfo() {
        return Result.success(operatingSubjectService.getOperatingSubjectInfo());
    }

    /**
     * 查询企业部门树
     *
     * @return 企业部门树
     */
    @ApiOperation("查询企业部门树")
    @GetMapping(value = "/getTeamTree")
    public Result<TeamInfoVO> getTeamTree() {
        return Result.success(operatingSubjectService.getTeamTree());
    }

    /**
     * 获取门店基础数据
     * @param keyword request model
     * @return Result
     */
    @ApiOperation("获取门店基础数据")
    @GetMapping(value = "/getQueryStoreInfo", produces = "application/json;charset=utf-8")
    public Result queryStoreInfo(@RequestParam(value = "keyword") String keyword,
                                 @RequestParam(value = "status", required = false, defaultValue = "0") Integer status) {
        return Result.success(storeBaseService.queryStore(keyword, status));
    }


    @ApiOperation("获取门店系统Tab列表")
    @GetMapping(value = "/store_tabs", produces = "application/json;charset=utf-8")
    public Result<List<Map<String, Object>>> getStoreTabs() {
        return Result.success(wrapTabs());
    }

    private static List<Map<String, Object>>  wrapTabs () {
        List<Map<String, Object>> tabs = new ArrayList<>();
        SystemEnum[] enums = {SystemEnum.RETAIL, SystemEnum.MALL, SystemEnum.REPAST};
        for (SystemEnum systemEnum : enums) {
            Map<String, Object> tab = new HashMap<>();
            tab.put("label", systemEnum.getDes());
            tab.put("system", systemEnum.name());
            tab.put("type", systemEnum.getCode());
            tabs.add(tab);
        }
        return tabs;
    }

    @SuppressWarnings("java:S4144")
    @ApiOperation("获取商品Tab列表")
    @GetMapping(value = "/goods_tabs", produces = "application/json;charset=utf-8")
    public Result<List<Map<String, Object>>> getGoodsTabs() {
        return Result.success(wrapTabs());
    }

    /**
     * 获取AppId
     *
     * @param operSubjectGuid
     * @return
     */
    @ApiOperation("获取AppId")
    @GetMapping(value = "/getWeChatAppId", produces = "application/json;charset=utf-8")
    public Result getWeChatAppId(Integer operSubjectGuid) {
        return Result.success(storeBaseService.getWeChatAppId(operSubjectGuid));
    }

    @ApiOperation("获取业务订单类型")
    @GetMapping(value = "/getBusinessType", produces = "application/json;charset=utf-8")
    public Result getBusinessType() {

        return Result.success(storeBaseService.getBusinessType());
    }

    @PostMapping(value = "/autoInit", produces = "application/json;charset=utf-8")
    public Result<Void> autoInitSubjectData(@RequestBody @Validated InitSubjectDataDTO initSubjectData) {
        operatingSubjectService.autoInitSubjectData(initSubjectData);
        return Result.success();
    }

    @PostMapping("/get_apply_module")
    @ApiOperation(value = "获取适用模块")
    public Result<ApplyModuleVO> getApplyModule() {
        return Result.success(hsaApplyDictionariesService.getApplyModule());
    }
    /**
     * 获取适用业务
     * @return 操作结果
     */
    @GetMapping("/get_system_business_type")
    public Result<List<ApplyTypeVO>> getSystemApplyBusiness(){
        List<ApplyTypeVO> applyTypeVOS = hsaApplyDictionariesService.getApplyBusiness(false);
        return Result.success(applyTypeVOS);
    }

    /**
     * 分页查询门店列表
     * todo 这个分页对象居然没有总页数？？
     */
    @ApiOperation("分页查询门店列表 ")
    @PostMapping("/query_store_page")
    public Result<Page<StoreBaseInfo>> queryStorePage(@RequestBody StorePageQueryDTO queryDTO) {
        return Result.success(storeBaseService.queryStorePage(queryDTO));
    }

    /**
     * 分页查询商品列表
     * todo 这个分页对象居然没有总页数？？
     */
    @ApiOperation("分页查询门店列表")
    @PostMapping("/query_goods_page")
    public Result<Page<CommodityBaseVO>> queryGoodsPage(@RequestBody CommodityBasePageQO queryDTO) {
        return Result.success(shopBaseService.pageCommodityBase(queryDTO));
    }


    /**
     * 统计各渠道店铺数量
     *
     * @param systemNames 系统枚举名称列表，如：["RETAIL", "MALL"]，不传时查询默认系统
     */
    @ApiOperation("统计各渠道店铺数量")
    @GetMapping(value = "/countStores", produces = "application/json;charset=utf-8")
    public Result<StoreCountDTO> countStores(@RequestParam(value = "systemNames", required = false) List<String> systemNames) {
        // 确定需要查询的系统列表
        List<SystemEnum> systemsToQuery = determineSystemsToQuery(systemNames);

        // 统计各系统的店铺数量
        StoreCountResult countResult = calculateStoreCount(systemsToQuery);

        // 构建返回结果
        StoreCountDTO totalCount = buildStoreCountDTO(countResult);

        return Result.success(totalCount);
    }

    /**
     * 确定需要查询的系统列表
     *
     * @param systemNames 传入的系统名称列表
     * @return 需要查询的系统枚举列表
     */
    private List<SystemEnum> determineSystemsToQuery(List<String> systemNames) {
        // 如果没有传系统参数，查询全部系统
        if (systemNames == null || systemNames.isEmpty()) {
            return Arrays.asList(SystemEnum.values());
        }

        // 传了参数，解析指定的系统
        List<SystemEnum> systemsToQuery = new ArrayList<>();
        for (String systemName : systemNames) {
            SystemEnum systemEnum = parseSystemEnum(systemName);
            if (systemEnum != null) {
                systemsToQuery.add(systemEnum);
            }
        }

        return systemsToQuery;
    }

    /**
     * 解析系统枚举名称
     *
     * @param systemName 系统名称
     * @return 解析后的系统枚举，解析失败返回null
     */
    private SystemEnum parseSystemEnum(String systemName) {
        if (systemName == null || systemName.trim().isEmpty()) {
            return null;
        }

        try {
            return SystemEnum.valueOf(systemName.toUpperCase().trim());
        } catch (IllegalArgumentException e) {
            log.warn("无效的系统枚举名称: {}", systemName);
            return null;
        }
    }

    /**
     * 计算各系统的店铺数量
     *
     * @param systemsToQuery 需要查询的系统列表
     * @return 统计结果
     */
    private StoreCountResult calculateStoreCount(List<SystemEnum> systemsToQuery) {
        int retailCount = NumberConstant.NUMBER_0;
        int mallCount = NumberConstant.NUMBER_0;
		int repastCount = NumberConstant.NUMBER_0;

        for (SystemEnum systemEnum : systemsToQuery) {
            StoreCountDTO storeCount = storeBaseService.getStoreCount(systemEnum);
            if (storeCount != null) {
                StoreCountResult systemResult = categorizeStoreCount(systemEnum, storeCount);
                retailCount += systemResult.getRetailCount();
                mallCount += systemResult.getMallCount();
                repastCount += systemResult.getRepastCount();
            }
        }

        return new StoreCountResult(retailCount, mallCount,repastCount);
    }

    /**
     * 根据系统类型分类店铺数量
     *
     * @param systemEnum 系统枚举
     * @param storeCount 店铺统计数据
     * @return 分类后的统计结果
     */
    private StoreCountResult categorizeStoreCount(SystemEnum systemEnum, StoreCountDTO storeCount) {
        int retailCount = NumberConstant.NUMBER_0;
        int mallCount = NumberConstant.NUMBER_0;
		int repastCount = NumberConstant.NUMBER_0;

        switch (systemEnum) {
            case SALE:
            case REPAST:
				repastCount += (storeCount.getRepastStoreCount() !=null ? storeCount.getRepastStoreCount(): NumberConstant.NUMBER_0);
				break;
            case PARTNER:
            case RETAIL:
                // 零售类系统
                retailCount = storeCount.getRetailStoreCount() != null ? storeCount.getRetailStoreCount() : NumberConstant.NUMBER_0;
                break;
            case MALL:
                // 商城系统
                mallCount = storeCount.getMallStoreCount() != null ? storeCount.getMallStoreCount() : NumberConstant.NUMBER_0;
                break;
            default:
                // 其他系统类型，不计入统计
                break;
        }

        return new StoreCountResult(retailCount, mallCount, repastCount);
    }

    /**
     * 构建店铺统计DTO
     *
     * @param countResult 统计结果
     * @return 店铺统计DTO
     */
    private StoreCountDTO buildStoreCountDTO(StoreCountResult countResult) {
        return new StoreCountDTO()
                .setRetailStoreCount(countResult.getRetailCount())
                .setMallStoreCount(countResult.getMallCount())
                .setRepastStoreCount(countResult.getRepastCount())
                .setTotalCount(countResult.getRetailCount() + countResult.getMallCount() + countResult.getRepastCount());
    }

    /**
     * 店铺统计结果内部类
     */
    private static class StoreCountResult {
        private final int retailCount;
        private final int mallCount;
		private final int repastCount;

        public StoreCountResult(int retailCount, int mallCount,int repastCount) {
            this.retailCount = retailCount;
            this.mallCount = mallCount;
			this.repastCount = repastCount;
        }

        public int getRetailCount() {
            return retailCount;
        }

        public int getMallCount() {
            return mallCount;
        }
		public int getRepastCount(){
			return repastCount;
		}
    }
}
