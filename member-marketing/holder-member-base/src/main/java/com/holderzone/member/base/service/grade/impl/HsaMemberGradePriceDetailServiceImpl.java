package com.holderzone.member.base.service.grade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.member.base.entity.card.HsaElectronicCard;
import com.holderzone.member.common.enums.member.GradeTypeEnum;
import com.holderzone.member.common.enums.member.MarketConsumptionOrderTypeEnum;
import com.holderzone.member.common.vo.equities.EquitiesStoreRuleVO;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.dto.MembershipBenefitsDTO;
import com.holderzone.member.base.entity.grade.HsaBusinessEquities;
import com.holderzone.member.base.entity.grade.HsaCommodityMemberPrice;
import com.holderzone.member.base.entity.grade.HsaGradeRightsCommodityRule;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.member.HsaMemberGradePriceDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.event.ConsumptionOrderPublisher;
import com.holderzone.member.base.event.domain.ConsumptionOrderEventEnum;
import com.holderzone.member.base.mapper.grade.HsaBusinessEquitiesMapper;
import com.holderzone.member.base.mapper.grade.HsaCommodityMemberPriceMapper;
import com.holderzone.member.base.mapper.grade.HsaGradeRightsCommodityRuleMapper;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.mapper.member.HsaMemberGradePriceDetailMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.equities.HsaEquitiesStoreRuleService;
import com.holderzone.member.base.service.grade.HsaCommodityMemberPriceService;
import com.holderzone.member.base.service.grade.HsaControlledGradeStateService;
import com.holderzone.member.base.service.grade.HsaMemberGradePriceDetailService;
import com.holderzone.member.base.service.integral.HsaIntegralDeductService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.grade.EquitiesLimitedDTO;
import com.holderzone.member.common.dto.grade.EquitiesTimeLimitedDTO;
import com.holderzone.member.common.dto.grade.LimitCycleDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.*;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.module.base.purchase.dto.PurchaseOrderCancelDto;
import com.holderzone.member.common.module.base.purchase.dto.PurchaseOrderStateDto;
import com.holderzone.member.common.module.base.purchase.enums.ConsumptionOrderStateEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedDiscountReqDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.common.module.settlement.constant.SettlementConstant;
import com.holderzone.member.common.qo.equities.*;
import com.holderzone.member.common.qo.grade.GradeCommodityBaseQO;
import com.holderzone.member.common.qo.integral.ReleaseOrderDeductDetailQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.equities.CalculateMemberPriceCommodityVO;
import com.holderzone.member.common.vo.equities.MemberPriceApplyCommodityVO;
import com.holderzone.member.common.vo.equities.MembershipBenefitsVO;
import com.holderzone.member.common.vo.grade.CommodityInfoVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员等级会员价周期累计明细接口服务
 * @date 2022/2/8 15:16
 */
@Slf4j
@Service
public class HsaMemberGradePriceDetailServiceImpl extends HolderBaseServiceImpl<HsaMemberGradePriceDetailMapper, HsaMemberGradePriceDetail> implements HsaMemberGradePriceDetailService {

    @Resource
    private HsaMemberGradePriceDetailMapper hsaMemberGradePriceDetailMapper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaCommodityMemberPriceMapper hsaCommodityMemberPriceMapper;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaBusinessEquitiesMapper hsaBusinessEquitiesMapper;

    @Resource
    private HsaGradeRightsCommodityRuleMapper hsaGradeRightsCommodityRuleMapper;

    @Resource
    private HsaControlledGradeStateService gradeStateService;

    @Resource
    private HsaMemberGradePriceDetailService hsaMemberGradePriceDetailService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Lazy
    @Autowired
    private HsaCommodityMemberPriceService hsaCommodityMemberPriceService;

    @Resource
    private HsaIntegralDeductService hsaIntegralDeductService;

    @Resource
    private ConsumptionOrderPublisher consumptionOrderPublisher;

    @Resource
    private HsaEquitiesStoreRuleService hsaEquitiesStoreRuleService;


    /**
     * 时段校验
     *
     * @param hsaBusinessEquities hsaBusinessEquities
     * @return boolean
     */
    private boolean isCheckTime(HsaBusinessEquities hsaBusinessEquities) {
        if (hsaBusinessEquities.getEquitiesEffectiveDateLimited() == NumberConstant.NUMBER_0 ||
                StringUtils.isEmpty(hsaBusinessEquities.getEquitiesTimeLimitedJson())) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        Date date = new Date();
        List<EquitiesTimeLimitedDTO> equitiesTimeLimitedDTOS = JSONArray.parseArray(hsaBusinessEquities.getEquitiesTimeLimitedJson(), EquitiesTimeLimitedDTO.class);
        if (hsaBusinessEquities.getEquitiesTimeLimitedType() == DataUnitEnum.DAY.getCode()) {
            return !checkDayTime(equitiesTimeLimitedDTOS, now, date);
        } else if (hsaBusinessEquities.getEquitiesTimeLimitedType() == DataUnitEnum.WEEK.getCode()) {
            //当前星期
            String current = DateUtil.dateToWeek(DateUtil.getTmpDate(date, StringConstant.FORMAT_YYYY_MM_DD));
            return isCheckCycleTime(now, date, equitiesTimeLimitedDTOS, current);
        } else if (hsaBusinessEquities.getEquitiesTimeLimitedType() == DataUnitEnum.MONTH.getCode()) {
            //当前日期
            String current = DateUtil.getTmpDate(date, StringConstant.FORMAT_D);
            return isCheckCycleTime(now, date, equitiesTimeLimitedDTOS, current);
        } else if (hsaBusinessEquities.getEquitiesTimeLimitedType() == DataUnitEnum.YEAR.getCode()) {
            //当前月日
            String current = DateUtil.getTmpDate(date, StringConstant.FORMAT_M_D);
            return isCheckCycleTime(now, date, equitiesTimeLimitedDTOS, current);
        } else {
            //自定义
            return !checkDefinitionTime(now, equitiesTimeLimitedDTOS);
        }
    }

    /**
     * 渠道校验
     *
     * @param hsaBusinessEquities hsaBusinessEquities
     * @return boolean
     */
    private boolean isCheckApplyChannel(HsaBusinessEquities hsaBusinessEquities, String channel) {
        if (hsaBusinessEquities.getApplyChannel() == NumberConstant.NUMBER_0 || StringUtils.isEmpty(hsaBusinessEquities.getApplyChannelJson())) {
            return false;
        }
        String[] channelJson = hsaBusinessEquities.getApplyChannelJson().split(",");
        List<String> list = Arrays.asList(channelJson);
        return !list.contains(channel);
    }

    /**
     * 业务校验
     *
     * @param hsaBusinessEquities hsaBusinessEquities
     * @return boolean
     */
    private boolean isCheckApplyBusiness(HsaBusinessEquities hsaBusinessEquities, String business) {
        if (hsaBusinessEquities.getApplyBusiness() == NumberConstant.NUMBER_0 || StringUtils.isEmpty(hsaBusinessEquities.getApplyBusinessJson())) {
            return false;
        }
        String[] businessJson = hsaBusinessEquities.getApplyBusinessJson().split(",");
        List<String> list = Arrays.asList(businessJson);
        return !list.contains(business);
    }

    /**
     * 终端校验
     *
     * @param hsaBusinessEquities hsaBusinessEquities
     * @return boolean
     */
    private boolean isCheckApplyTerminal(HsaBusinessEquities hsaBusinessEquities, String terminal) {
        if (hsaBusinessEquities.getApplyTerminal() == NumberConstant.NUMBER_0 || StringUtils.isEmpty(hsaBusinessEquities.getApplyTerminalJson())) {
            return false;
        }
        String[] terminalJson = hsaBusinessEquities.getApplyTerminalJson().split(",");
        List<String> list = Arrays.asList(terminalJson);
        return !list.contains(terminal);
    }

    /**
     * 自定义时间校验
     *
     * @param now                     now
     * @param equitiesTimeLimitedDTOS equitiesTimeLimitedDTOS
     * @return boolean
     */
    private boolean checkDefinitionTime(LocalDateTime now, List<EquitiesTimeLimitedDTO> equitiesTimeLimitedDTOS) {
        boolean isPass = false;
        for (EquitiesTimeLimitedDTO equitiesTimeLimitedDTO : equitiesTimeLimitedDTOS) {
            List<String> strings = equitiesTimeLimitedDTO.getValue();
            LocalDateTime startDayTime = DateUtil.date2LocalDate(DateUtil.parse(strings.get(NumberConstant.NUMBER_0), StringConstant.FORMAT));
            LocalDateTime endDayTime = DateUtil.date2LocalDate(DateUtil.parse(strings.get(NumberConstant.NUMBER_1), StringConstant.FORMAT));
            //是否满足事件单
            if (now.isAfter(Objects.requireNonNull(startDayTime)) && now.isBefore(Objects.requireNonNull(endDayTime))) {
                isPass = true;
                break;
            }
        }
        return isPass;
    }

    /**
     * 周期 周、月 校验
     *
     * @param equitiesTimeLimitedDTOS equitiesTimeLimitedDTOS
     * @param now                     now
     * @param date                    date
     * @return boolean
     */
    private boolean isCheckCycleTime(LocalDateTime now, Date date, List<EquitiesTimeLimitedDTO> equitiesTimeLimitedDTOS, String current) {
        boolean isPass = false;
        for (EquitiesTimeLimitedDTO equitiesTimeLimitedDTO : equitiesTimeLimitedDTOS) {
            List<String> types = equitiesTimeLimitedDTO.getType();
            if (types.contains(current)) {
                List<String> dayTime = equitiesTimeLimitedDTO.getValue();
                LocalDateTime startDayTime = DateUtil.getDayLocalDateTime(date, dayTime.get(NumberConstant.NUMBER_0));
                LocalDateTime endDayTime = DateUtil.getDayLocalDateTime(date, dayTime.get(NumberConstant.NUMBER_1));
                //是否满足事件单
                if (now.isAfter(startDayTime) && now.isBefore(endDayTime)) {
                    isPass = true;
                    break;
                }
            }
        }
        return !isPass;
    }

    /**
     * 周期 天 校验
     *
     * @param equitiesTimeLimitedDTOS equitiesTimeLimitedDTOS
     * @param now                     now
     * @param date                    date
     * @return boolean
     */
    private boolean checkDayTime(List<EquitiesTimeLimitedDTO> equitiesTimeLimitedDTOS, LocalDateTime now, Date date) {
        boolean isPass = false;
        for (EquitiesTimeLimitedDTO equitiesTimeLimitedDTO : equitiesTimeLimitedDTOS) {
            List<String> dayTime = equitiesTimeLimitedDTO.getValue();
            LocalDateTime startDayTime = DateUtil.getDayLocalDateTime(date, dayTime.get(NumberConstant.NUMBER_0));
            LocalDateTime endDayTime = DateUtil.getDayLocalDateTime(date, dayTime.get(NumberConstant.NUMBER_1));
            //是否满足事件单
            if (now.isAfter(startDayTime) && now.isBefore(endDayTime)) {
                isPass = true;
                break;
            }
        }
        return isPass;
    }

    /**
     * 获取会员等级tryReleaseShared商品
     *
     * @param qo MemberPriceApplyCommodityQO
     * @return MemberPriceApplyCommodityVO
     */
    @Override
    public MemberPriceApplyCommodityVO getMemberPriceApplyCommodity(MemberPriceApplyCommodityQO qo) {
        log.info("获取会员等级折扣商品请求参数：{}", JSON.toJSONString(qo));
        MemberPriceApplyCommodityVO vo = new MemberPriceApplyCommodityVO();
        if (gradeStateService.isNotEnable()) {
            return vo;
        }
        HsaBusinessEquities hsaBusinessEquities = getHsaGradeEquities(qo.getMemberInfoGuid(), null);
        //返回折扣对象
        return getMemberPriceApplyCommodityVO(qo, hsaBusinessEquities);
    }

    /**
     * 返回折扣对象
     *
     * @param qo                  条件入参：渠道等
     * @param hsaBusinessEquities 权益实体
     * @return 折扣对象
     */
    @Override
    public MemberPriceApplyCommodityVO getMemberPriceApplyCommodityVO(MemberPriceApplyCommodityQO qo,
                                                                      HsaBusinessEquities hsaBusinessEquities) {
        // 1. 创建返回对象
        MemberPriceApplyCommodityVO resultVO = new MemberPriceApplyCommodityVO();

        // 2. 校验参数和权限
        if (checkAll(qo, hsaBusinessEquities)) {
            // 校验不通过，返回空对象
            return resultVO;
        }
        //业务
        if (StringUtils.isEmpty(hsaBusinessEquities.getApplyBusinessJson())) {
            resultVO.setBusinessList(MarketConsumptionOrderTypeEnum.getCodeList());
        } else {
            String[] businessJson = hsaBusinessEquities.getApplyBusinessJson().split(",");
            List<String> list = Arrays.asList(businessJson);
            resultVO.setBusinessList(list);
        }

        // 3. 设置适用门店信息
        setStoreRuleInfo(resultVO, hsaBusinessEquities);

        // 4. 获取和设置会员等级类型
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(qo.getMemberInfoGuid());
        int gradeType = StringUtil.isNotEmpty(memberInfo.getMemberPaidGradeInfoGuid()) ?
                GradeTypeEnum.PAID.getCode() : GradeTypeEnum.FREE.getCode();
        resultVO.setGradeType(gradeType);

        // 5. 设置折扣力度
        resultVO.setDiscountDynamics(hsaBusinessEquities.getDiscountDynamics());

        // 6. 获取适用商品信息
        List<GradeCommodityBaseQO> commodityList = getApplicableCommodities(hsaBusinessEquities);

        // 7. 设置商品应用类型和商品列表
        resultVO.setApplyGoodsType(hsaBusinessEquities.getApplyGoodsType());
        resultVO.setCommodityCode(commodityList.stream()
                .map(GradeCommodityBaseQO::getCommodityCode)
                .collect(Collectors.toList()));
        resultVO.setCommodityId(commodityList.stream()
                .map(GradeCommodityBaseQO::getCommodityId)
                .collect(Collectors.toList()));
        resultVO.setEquitiesRuleType(hsaBusinessEquities.getEquitiesRuleType());

        log.info("获取会员等级折扣商品列表：{}", JSON.toJSONString(resultVO));
        return resultVO;
    }

    /**
     * 设置门店规则信息
     *
     * @param resultVO 结果对象
     * @param equities 权益实体
     */
    private void setStoreRuleInfo(MemberPriceApplyCommodityVO resultVO, HsaBusinessEquities equities) {
        // 非全部适用门店时，获取适用门店列表
        if (equities.getApplicableAllStore() == BooleanEnum.FALSE.getCode()) {
            List<EquitiesStoreRuleVO> storeRuleList = hsaEquitiesStoreRuleService.getEquitiesStoreRule(
                    equities.getGuid(), BooleanEnum.TRUE.getCode());
            resultVO.setEquitiesStoreRuleVOList(storeRuleList);
        }
        resultVO.setApplicableAllStore(equities.getApplicableAllStore());
    }

    /**
     * 获取适用商品列表
     *
     * @param equities 权益实体
     * @return 商品列表
     */
    private List<GradeCommodityBaseQO> getApplicableCommodities(HsaBusinessEquities equities) {
        // 查询适用商品规则
        List<HsaGradeRightsCommodityRule> commodityRules = hsaGradeRightsCommodityRuleMapper.selectList(
                new LambdaQueryWrapper<HsaGradeRightsCommodityRule>()
                        .eq(HsaGradeRightsCommodityRule::getGradeEquitiesGuid, equities.getGuid())
                        .eq(HsaGradeRightsCommodityRule::getBusinessType, ThreadLocalCache.getSystem())
                        .in(HsaGradeRightsCommodityRule::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2)
                        .eq(HsaGradeRightsCommodityRule::getEffective, NumberConstant.NUMBER_1));

        // 如果是全部适用商品或没有规则，返回空列表
        if (equities.getApplyGoodsType() == ApplyCommodityTypeEnum.ALL_APPLY_COMMODITY.getCode() ||
                CollUtil.isEmpty(commodityRules)) {
            return Lists.newArrayList();
        }

        // 将规则转换为商品基础信息
        return commodityRules.stream()
                .map(rule -> getCommodityBase(
                        rule.getCommodityId(),
                        rule.getCommodityCode(),
                        rule.getCommodityName()))
                .collect(Collectors.toList());
    }

    /**
     * 获取此会员对应等级会员价权益
     *
     * @param memberInfoGuid 会员信息GUID
     * @param orderNum       订单号(可选)
     * @return 会员等级权益
     */
    public HsaBusinessEquities getHsaGradeEquities(String memberInfoGuid, String orderNum) {
        // 1. 优先从订单记录获取权益信息
        if (StringUtils.isNotEmpty(orderNum)) {
            HsaMemberGradePriceDetail priceDetail = hsaMemberGradePriceDetailMapper.selectOne(
                    new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                            .eq(HsaMemberGradePriceDetail::getOrderNumber, orderNum)
                            .eq(HsaMemberGradePriceDetail::getMemberInfoGuid, memberInfoGuid));

            if (Objects.nonNull(priceDetail)) {
                return hsaBusinessEquitiesMapper.selectOne(
                        new LambdaQueryWrapper<HsaBusinessEquities>()
                                .eq(HsaBusinessEquities::getGuid, priceDetail.getEquitiesGuid()));
            }
        }

        // 2. 从会员等级获取权益信息
        // 2.1 获取会员信息
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberInfoGuid);
        if (memberInfo == null ||
                (StringUtils.isEmpty(memberInfo.getMemberGradeInfoGuid()) &&
                        StringUtils.isEmpty(memberInfo.getMemberPaidGradeInfoGuid()))) {
            log.info("会员无等级信息，会员guid:{}", memberInfoGuid);
            return null;
        }

        // 2.2 优先使用付费等级，其次使用免费等级
        String gradeInfoGuid = StringUtil.isNotEmpty(memberInfo.getMemberPaidGradeInfoGuid()) ?
                memberInfo.getMemberPaidGradeInfoGuid() :
                memberInfo.getMemberGradeInfoGuid();

        // 2.3 获取等级信息
        HsaMemberGradeInfo gradeInfo = hsaMemberGradeInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaMemberGradeInfo>()
                        .eq(HsaMemberGradeInfo::getGuid, gradeInfoGuid)
                        .eq(HsaMemberGradeInfo::getEffective, NumberConstant.NUMBER_1)
                        .in(HsaMemberGradeInfo::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2));

        if (Objects.isNull(gradeInfo)) {
            log.info("未查询到等级信息，等级guid:{}", gradeInfoGuid);
            return null;
        }

        // 2.4 获取等级对应的权益信息
        return hsaBusinessEquitiesMapper.selectOne(
                new LambdaQueryWrapper<HsaBusinessEquities>()
                        .eq(HsaBusinessEquities::getMemberGradeInfoGuid, gradeInfo.getGuid())
                        .eq(HsaBusinessEquities::getEffective, NumberConstant.NUMBER_1)
                        .ne(HsaBusinessEquities::getBusinessType, BusinessTypeEnum.CARD_EQUITIES.getCode())
                        .in(HsaBusinessEquities::getEquitiesRuleType,
                                EquitiesRuleTypeEnum.MEMBER_PRICE.getCode(),
                                EquitiesRuleTypeEnum.GOODS_MEMBER.getCode())
                        .in(HsaBusinessEquities::getIsDelete,
                                BooleanEnum.FALSE.getCode(),
                                NumberConstant.NUMBER_2));
    }

    /**
     * 会员优惠通用获取
     *
     * @param memberPriceCommodityQO memberPriceCommodityQO
     * @return CalculateMemberPriceCommodityVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CalculateMemberPriceCommodityVO calculateMemberPriceCommodity(CalculateMemberPriceCommodityQO memberPriceCommodityQO,
                                                                         CalculateCumulativeDiscountQO cumulativeDiscountQO,
                                                                         HsaBusinessEquities gradeEquities) {
        CalculateMemberPriceCommodityVO memberPriceCommodityVO = new CalculateMemberPriceCommodityVO();
        Optional.ofNullable(gradeEquities)
                .ifPresent(e -> memberPriceCommodityQO.setBusinessType(e.getBusinessType()));
        //限额记录
        HsaMemberGradePriceDetail hsaMemberGradePriceDetail = getHsaMemberGradePriceDetail(memberPriceCommodityQO);

        if (checkCondition(memberPriceCommodityQO, memberPriceCommodityVO, gradeEquities)) {
            log.info("计算商品订单折扣：{}，不满足条件", JSON.toJSONString(memberPriceCommodityQO));
            return memberPriceCommodityVO;
        }
        log.info("计算商品订单折扣：{}", JSON.toJSONString(memberPriceCommodityQO));
        List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList = new ArrayList<>();
        List<MemberPriceCommodityQO> memberPriceCommodityList = Lists.newArrayList();
        //获取会员折扣优惠列表
        List<MembershipBenefitsVO> membershipBenefitsVOS = Lists.newArrayList();
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberPriceCommodityQO.getMemberInfoGuid());
        memberPriceCommodityQO.setChannel(hsaOperationMemberInfo.getSourceType().toString());

        //折扣权益为空处理
        if (Objects.isNull(gradeEquities)) {
            log.info("会员等级折扣权益为空，会员信息：{}", JSON.toJSONString(memberPriceCommodityQO));
            MembershipBenefitsDTO membershipBenefitsDTO =
                    getMembershipBenefitsDTO(memberPriceCommodityQO, cumulativeDiscountQO, gradeEquities, memberPriceCommodityVO, membershipBenefitsVOS, hsaOperationMemberInfo);
            return getCalculateMemberPriceCommodityVO(membershipBenefitsDTO);
        }

        //获取折扣类型
        Integer discountType = getDiscountType(memberPriceCommodityQO, gradeEquities);
        memberPriceCommodityVO.setBusinessType(gradeEquities.getBusinessType());

        //不使用折扣处理
        if (discountType == DiscountTypeEnum.DISCOUNT_NOT.getCode()) {
            MembershipBenefitsDTO membershipBenefitsDTO =
                    getMembershipBenefitsDTO(memberPriceCommodityQO, cumulativeDiscountQO, gradeEquities, memberPriceCommodityVO, membershipBenefitsVOS, hsaOperationMemberInfo);
            log.info("会员等级折扣权益不使用折扣，会员信息：{}", JSON.toJSONString(memberPriceCommodityQO));
            return getCalculateMemberPriceCommodityVO(membershipBenefitsDTO);
        }

        //计算折扣     校验商品
        memberPriceCommodityVOList = getCalculateMemberPriceCommodityVOS(memberPriceCommodityQO, cumulativeDiscountQO, gradeEquities, hsaMemberGradePriceDetail, memberPriceCommodityVOList, memberPriceCommodityList, discountType);

        //比对最优方案
        memberPriceCommodityVO = getCalculateMemberPriceCommodityVO(memberPriceCommodityQO, memberPriceCommodityVO, hsaMemberGradePriceDetail, memberPriceCommodityVOList, hsaOperationMemberInfo);

        memberPriceCommodityVO.setDiscountDynamicsAmount(Objects.isNull(memberPriceCommodityVO.getDiscountDynamicsAmount()) ?
                BigDecimal.ZERO : memberPriceCommodityVO.getDiscountDynamicsAmount());
        memberPriceCommodityVO.setDiscountDynamics(
                Optional.ofNullable(gradeEquities.getDiscountDynamics())
                        .map(dynamics -> dynamics.divide(BigDecimal.TEN, SettlementConstant.FINAL_LENGTH, RoundingMode.DOWN))
                        .orElse(BigDecimal.ZERO)
        );
        //获取会员折扣优惠列表
        MembershipBenefitsDTO membershipBenefitsDTO =
                getMembershipBenefitsDTO(
                        memberPriceCommodityQO,
                        cumulativeDiscountQO,
                        gradeEquities,
                        memberPriceCommodityVO,
                        membershipBenefitsVOS,
                        hsaOperationMemberInfo);
        getMembershipBenefits(membershipBenefitsDTO);
        memberPriceCommodityVO.setDiscountDynamics(gradeEquities.getDiscountDynamics());
        log.info("计算商品订单折扣返参：{}", JSON.toJSONString(memberPriceCommodityVO));
        return memberPriceCommodityVO;
    }

    public void checkMembershipBenefitsVOS(CalculateMemberPriceCommodityQO memberPriceCommodityQO,
                                           CalculateCumulativeDiscountQO cumulativeDiscountQO,
                                           HsaBusinessEquities gradeEquities,
                                           CalculateMemberPriceCommodityVO memberPriceCommodityVO,
                                           List<MembershipBenefitsVO> membershipBenefitsVOS,
                                           HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (memberPriceCommodityVO.getDiscountDynamicsAmount().compareTo(BigDecimal.ZERO) > 0) {
            MembershipBenefitsDTO membershipBenefitsDTO =
                    getMembershipBenefitsDTO(memberPriceCommodityQO, cumulativeDiscountQO, gradeEquities, memberPriceCommodityVO, membershipBenefitsVOS, hsaOperationMemberInfo);
            getMembershipBenefits(membershipBenefitsDTO);
            memberPriceCommodityVO.setDiscountDynamics(gradeEquities.getDiscountDynamics());
        } else {
            MembershipBenefitsVO membershipBenefitsVO = getMembershipBenefitsVO(cumulativeDiscountQO, gradeEquities);
            membershipBenefitsVOS.add(membershipBenefitsVO);
            memberPriceCommodityVO.setMembershipBenefitsVOS(membershipBenefitsVOS);
        }
    }

    public static MembershipBenefitsVO getMembershipBenefitsVO(CalculateCumulativeDiscountQO cumulativeDiscountQO,
                                                               HsaBusinessEquities gradeEquities) {
        MembershipBenefitsVO membershipBenefitsVO = new MembershipBenefitsVO();
        membershipBenefitsVO.setEquitiesId(String.valueOf(gradeEquities.getId()));
        membershipBenefitsVO.setEquitiesGuid(gradeEquities.getGuid());
        membershipBenefitsVO.setDiscountType(gradeEquities.getEquitiesRuleType());
        membershipBenefitsVO.setDiscountDynamics(gradeEquities.getDiscountDynamics());
        //周期限额
        membershipBenefitsVO.setPeriodDiscountLimited(gradeEquities.getPeriodDiscountLimited())
                .setEquitiesTimeLimitedType(gradeEquities.getEquitiesTimeLimitedType())
                .setMinimumThisMonthAmount(BigDecimal.ZERO);
        membershipBenefitsVO.setSingleDiscountsLimited(gradeEquities.getSingleDiscountsLimited());
        membershipBenefitsVO.setSingleDiscountsLimitedAmount(gradeEquities.getSingleDiscountsLimitedAmount());

        //累计限制
        Optional.ofNullable(cumulativeDiscountQO).map(CalculateCumulativeDiscountQO::getTotalDiscount).ifPresent(total -> {
            membershipBenefitsVO.setTotalDiscountLimited(BooleanEnum.TRUE.getCode());
            membershipBenefitsVO.setTotalDiscountLimitedAmount(total);
        });
        //当前使用周期
        Optional.ofNullable(cumulativeDiscountQO)
                .map(CalculateCumulativeDiscountQO::getCurrentPeriod)
                .ifPresent(membershipBenefitsVO::setEquitiesTimeLimitedType);
        return membershipBenefitsVO;
    }


    private MembershipBenefitsDTO getMembershipBenefitsDTO(CalculateMemberPriceCommodityQO memberPriceCommodityQO, CalculateCumulativeDiscountQO cumulativeDiscountQO, HsaBusinessEquities gradeEquities, CalculateMemberPriceCommodityVO memberPriceCommodityVO, List<MembershipBenefitsVO> membershipBenefitsVOS, HsaOperationMemberInfo hsaOperationMemberInfo) {
        MembershipBenefitsDTO membership = new MembershipBenefitsDTO();
        membership.setCumulativeDiscountQO(cumulativeDiscountQO);
        membership.setHsaOperationMemberInfo(hsaOperationMemberInfo);
        membership.setMemberPriceCommodityQO(memberPriceCommodityQO);
        membership.setMembershipBenefitsVOS(membershipBenefitsVOS);
        membership.setGradeEquities(gradeEquities);
        membership.setMemberPriceCommodityVO(memberPriceCommodityVO);
        return membership;
    }

    private static Integer getDiscountType(CalculateMemberPriceCommodityQO memberPriceCommodityQO, HsaBusinessEquities gradeEquities) {
        Integer discountType = memberPriceCommodityQO.getDiscountType();
        if (Objects.isNull(discountType)) {
            discountType = gradeEquities.getEquitiesRuleType();
            memberPriceCommodityQO.setDiscountType(discountType);
        }
        return discountType;
    }

    private List<CalculateMemberPriceCommodityVO> getCalculateMemberPriceCommodityVOS(CalculateMemberPriceCommodityQO memberPriceCommodityQO,
                                                                                      CalculateCumulativeDiscountQO cumulativeDiscountQO,
                                                                                      HsaBusinessEquities gradeEquities,
                                                                                      HsaMemberGradePriceDetail hsaMemberGradePriceDetail,
                                                                                      List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList,
                                                                                      List<MemberPriceCommodityQO> memberPriceCommodityList,
                                                                                      Integer discountType) {
        //折扣
        if (discountType == DiscountTypeEnum.DISCOUNT_GRADE.getCode()) {
            CalculateMemberQO calculateMemberQO = getCalculateMemberQO(memberPriceCommodityList, memberPriceCommodityQO, cumulativeDiscountQO, memberPriceCommodityVOList);
            //根据会员等级权益计算商品折扣  校验商品
            getCalculateGradeMemberPrice(gradeEquities, hsaMemberGradePriceDetail, calculateMemberQO);
            memberPriceCommodityVOList = calculateMemberQO.getMemberPriceCommodityVOList();
        } else if (CollUtil.isNotEmpty(memberPriceCommodityQO.getMemberPriceCommodityQOS()) && discountType == DiscountTypeEnum.GOODS_MEMBER.getCode()) {
            //商品会员价
            getCalculateGradeMemberPrice(memberPriceCommodityQO, memberPriceCommodityVOList, memberPriceCommodityList, gradeEquities);
        }
        return memberPriceCommodityVOList;
    }

    private CalculateMemberPriceCommodityVO getCalculateMemberPriceCommodityVO(CalculateMemberPriceCommodityQO memberPriceCommodityQO,
                                                                               CalculateMemberPriceCommodityVO memberPriceCommodityVO,
                                                                               HsaMemberGradePriceDetail hsaMemberGradePriceDetail,
                                                                               List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList,
                                                                               HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (CollUtil.isNotEmpty(memberPriceCommodityVOList)) {
            memberPriceCommodityVO = getCalculateMemberPriceCommodityVO(memberPriceCommodityVOList);
            //折扣锁定
            lockedAccumulation(memberPriceCommodityQO, hsaOperationMemberInfo, memberPriceCommodityVO, hsaMemberGradePriceDetail);
        } else {
            BeanUtils.copyProperties(memberPriceCommodityQO, memberPriceCommodityVO);
        }
        return memberPriceCommodityVO;
    }

    private static CalculateMemberQO getCalculateMemberQO(List<MemberPriceCommodityQO> memberPriceCommodityList, CalculateMemberPriceCommodityQO memberPriceCommodityQO, CalculateCumulativeDiscountQO cumulativeDiscountQO, List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList) {
        CalculateMemberQO calculateMemberQO = new CalculateMemberQO();
        calculateMemberQO.setQo(memberPriceCommodityQO);
        calculateMemberQO.setMemberPriceCommodityVOList(memberPriceCommodityVOList);
        calculateMemberQO.setCumulativeDiscountQO(cumulativeDiscountQO);
        calculateMemberQO.setMemberPriceCommodityList(memberPriceCommodityList);
        return calculateMemberQO;
    }

    /**
     * 计算会员商品价格
     *
     * @param memberPriceCommodityQO     memberPriceCommodityQO
     * @param memberPriceCommodityVOList memberPriceCommodityVOList
     * @param memberPriceCommodityList   memberPriceCommodityList
     * @param gradeEquities              gradeEquities
     */
    private void getCalculateGradeMemberPrice(CalculateMemberPriceCommodityQO memberPriceCommodityQO,
                                              List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList,
                                              List<MemberPriceCommodityQO> memberPriceCommodityList,
                                              HsaBusinessEquities gradeEquities) {
        //校验集合（终端、时段等）
        HsaBusinessEquities hsaBusinessEquities = checkHsaGradeEquities(memberPriceCommodityQO, gradeEquities);
        if (Objects.nonNull(hsaBusinessEquities)) {
            CalculateMemberPriceCommodityVO vo = new CalculateMemberPriceCommodityVO();
            //折扣额度
            BigDecimal discountCommodityTotalAmount = BigDecimal.ZERO;
            //获取会员价商品
            List<HsaCommodityMemberPrice> hsaCommodityMemberPrices = hsaCommodityMemberPriceMapper.selectList(new LambdaQueryWrapper<HsaCommodityMemberPrice>().eq(HsaCommodityMemberPrice::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
            List<CommodityInfoVO> commodityInfoDTOS = hsaCommodityMemberPriceService.queryCustomCommodityPrice();
            if (CollectionUtil.isEmpty(commodityInfoDTOS)) {
                return;
            }
            Map<Long, HsaCommodityMemberPrice> hsaCommodityInfoDTOMap = hsaCommodityMemberPrices.stream().collect(Collectors.toMap(HsaCommodityMemberPrice::getCommodityId, Function.identity(), (entity1, entity2) -> entity1));
            //限购校验
            Map<Long, Integer> commodityIntMap = new HashMap<>();
            //整合商品
            Map<String, CommodityMemberPriceQO> commodityMemberPriceQOMap = new HashMap<>();
            //整合商品数据
            for (CommodityInfoVO commodityInfoDTO : commodityInfoDTOS) {
                if (hsaCommodityInfoDTOMap.containsKey(commodityInfoDTO.getCommodity_id())) {
                    CommodityMemberPriceQO commodityMemberPriceQO = new CommodityMemberPriceQO();
                    HsaCommodityMemberPrice hsaCommodityMemberPrice = hsaCommodityInfoDTOMap.get(commodityInfoDTO.getCommodity_id());
                    BeanUtils.copyProperties(hsaCommodityMemberPrice, commodityMemberPriceQO);
                    commodityMemberPriceQO.setCommodityPrice(commodityInfoDTO.getCommodity_price());
                    commodityMemberPriceQOMap.put(String.valueOf(commodityInfoDTO.getCommodity_id()), commodityMemberPriceQO);
                }
            }
            //参与折扣商品 用于计算分摊比例
            List<MemberPriceCommodityQO> memberPriceCommodityQOS = Lists.newArrayList();
            //不参与折扣商品
            List<MemberPriceCommodityQO> notMemberPriceCommodityQOS = Lists.newArrayList();

            //会员价计算
            memberPriceToCalculate(memberPriceCommodityQO, commodityIntMap, commodityMemberPriceQOMap, memberPriceCommodityQOS, notMemberPriceCommodityQOS);

            //无优惠
            if (discountCommodityTotalAmount.compareTo(BigDecimal.ZERO) <= NumberConstant.NUMBER_0) {
                return;
            }

            //保留两位
            discountCommodityTotalAmount = discountCommodityTotalAmount.setScale(NumberConstant.NUMBER_2, BigDecimal.ROUND_DOWN);

            //订单实际支付金额
            BigDecimal orderRealPaymentAmount = memberPriceCommodityQO.getCommodityTotalAmount().subtract(discountCommodityTotalAmount);

            //商品优惠比例价格
            memberPriceCommodityList.addAll(memberPriceCommodityQOS);
            memberPriceCommodityList.addAll(notMemberPriceCommodityQOS);
            memberPriceCommodityVOList.add(vo.setMemberPriceCommodityQOS(memberPriceCommodityList)
                    .setCommodityTotalAmount(memberPriceCommodityQO.getCommodityTotalAmount())
                    .setOrderRealPaymentAmount(orderRealPaymentAmount)
                    .setDiscountDynamicsAmount(discountCommodityTotalAmount)
                    .setDiscountType(DiscountTypeEnum.GOODS_MEMBER.getCode())
                    .setEquitiesGuid(hsaBusinessEquities.getGuid())
                    .setEquitiesId(hsaBusinessEquities.getEquitiesId()));
        }
    }

    private void memberPriceToCalculate(CalculateMemberPriceCommodityQO memberPriceCommodityQO,
                                        Map<Long, Integer> commodityIntMap,
                                        Map<String, CommodityMemberPriceQO> commodityMemberPriceQOMap,
                                        List<MemberPriceCommodityQO> memberPriceCommodityQOS,
                                        List<MemberPriceCommodityQO> notMemberPriceCommodityQOS) {
        for (MemberPriceCommodityQO commodityQO : memberPriceCommodityQO.getMemberPriceCommodityQOS()) {
            if (commodityMemberPriceQOMap.containsKey(commodityQO.getCommodityId())) {
                CommodityMemberPriceQO commodityInfoDTO = commodityMemberPriceQOMap.get(commodityQO.getCommodityId());
                //商品单价
                BigDecimal commodityPrice = commodityQO.getCommodityPrice();
                //商品数量
                int commodityNum = Integer.parseInt(commodityQO.getCommodityNum() + "");
                //折扣
                BigDecimal discountValue = commodityInfoDTO.getDiscountValue();
                dealCommodityMinistryPrice(commodityIntMap, commodityQO, commodityInfoDTO, commodityPrice, commodityNum, discountValue);
                memberPriceCommodityQOS.add(commodityQO);
            }
            notMemberPriceCommodityQOS.add(commodityQO);
        }
    }

    private static void dealCommodityMinistryPrice(Map<Long, Integer> commodityIntMap, MemberPriceCommodityQO commodityQO, CommodityMemberPriceQO commodityInfoDTO, BigDecimal commodityPrice, int commodityNum, BigDecimal discountValue) {
        for (int i = NumberConstant.NUMBER_0; i < commodityNum; i++) {
            if (CollectionUtil.isNotEmpty(commodityIntMap)
                    && commodityIntMap.containsKey(commodityInfoDTO.getCommodityId())
                    && commodityInfoDTO.getLimitedType() == NumberConstant.NUMBER_1
                    && commodityIntMap.get(commodityInfoDTO.getCommodityId()) < commodityInfoDTO.getLimitedNumber()) {
                continue;
            }
            checkScale(commodityQO, commodityInfoDTO, commodityPrice, discountValue);
        }
    }

    private static void checkScale(MemberPriceCommodityQO commodityQO, CommodityMemberPriceQO commodityInfoDTO, BigDecimal commodityPrice, BigDecimal discountValue) {
        if (commodityInfoDTO.getDiscountType() == NumberConstant.NUMBER_0) {
            BigDecimal commodityDiscount = commodityPrice.multiply(discountValue).setScale(NumberConstant.NUMBER_2, BigDecimal.ROUND_HALF_UP);
            commodityQO.setCommodityMinistryPrice(commodityQO.getCommodityMinistryPrice().add(commodityDiscount));
        } else if (commodityInfoDTO.getDiscountType() == NumberConstant.NUMBER_1) {
            setReducePrice(commodityQO, commodityPrice, discountValue);
        } else {
            setAppointPrice(commodityQO, commodityPrice, discountValue);
        }
    }

    private static void setAppointPrice(MemberPriceCommodityQO commodityQO, BigDecimal commodityPrice, BigDecimal discountValue) {
        if (commodityPrice.compareTo(discountValue) > NumberConstant.NUMBER_0) {
            commodityQO.setCommodityMinistryPrice(commodityQO.getCommodityMinistryPrice().add(commodityPrice.subtract(discountValue)));
        }
    }

    private static void setReducePrice(MemberPriceCommodityQO commodityQO, BigDecimal commodityPrice, BigDecimal discountValue) {
        BigDecimal subtract = commodityPrice.subtract(discountValue);
        if (subtract.compareTo(BigDecimal.ZERO) >= NumberConstant.NUMBER_0) {
            commodityQO.setCommodityMinistryPrice(subtract.compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0 ? commodityQO.getCommodityMinistryPrice().add(commodityPrice) : commodityQO.getCommodityMinistryPrice().add(subtract));
        }
    }

    private CalculateMemberPriceCommodityVO getCalculateMemberPriceCommodityVO(MembershipBenefitsDTO membershipBenefitsDTO) {
        CalculateMemberPriceCommodityQO memberPriceCommodityQO = membershipBenefitsDTO.getMemberPriceCommodityQO();

        CalculateMemberPriceCommodityVO memberPriceCommodityVO = membershipBenefitsDTO.getMemberPriceCommodityVO();
        BeanUtils.copyProperties(memberPriceCommodityQO, memberPriceCommodityVO);
        //权益展示
        getMembershipBenefits(membershipBenefitsDTO);
        return memberPriceCommodityVO;
    }

    private boolean checkCondition(CalculateMemberPriceCommodityQO priceCommodityQo,
                                   CalculateMemberPriceCommodityVO memberPriceCommodityVO,
                                   HsaBusinessEquities gradeEquities) {
        if (StringUtil.isEmpty(priceCommodityQo.getMemberInfoGuid())) {
            BeanUtils.copyProperties(priceCommodityQo, memberPriceCommodityVO);
            log.info("会员信息guid为空，无法计算会员价：{}", JSON.toJSONString(priceCommodityQo));
            return true;
        }
        //排除卡折扣
        final boolean isMemberGrade = Optional.ofNullable(gradeEquities)
                .map(b -> !Objects.equals(b.getBusinessType(), BusinessTypeEnum.CARD_EQUITIES.getCode()))
                .orElse(true);
        //等级开关
        if (isMemberGrade && gradeStateService.isNotEnable()) {
            BeanUtils.copyProperties(priceCommodityQo, memberPriceCommodityVO);
            log.info("会员等级折扣未开启，无法计算会员价：{}", JSON.toJSONString(priceCommodityQo));
            return true;
        }
        if (Objects.isNull(gradeEquities)) {
            log.info("会员 {} 不存在权益规则", priceCommodityQo.getMemberInfoGuid());
            return true;
        }
        //门店非空、不满足
        List<String> storeList = validateEquitiesStoreNew(Objects.requireNonNull(gradeEquities), priceCommodityQo.getStoreGuidList());
        log.info("权益规则 {} 校验门店： {}", gradeEquities.getGuid(), storeList);
        //校验门店，为空默认所有
        if (CollUtil.isEmpty(storeList)) {
            //门店不满足
            log.info("权益规则 {} 不适用门店： {}", gradeEquities.getGuid(), priceCommodityQo.getStoreGuidList());
            return true;
        }
        priceCommodityQo.setStoreGuidList(storeList);
        return false;
    }

    /**
     * 校验权益门店
     *
     * @param gradeEquities 权益
     * @param storeGuidList     门店
     * @return 是否
     */
    private List<String> validateEquitiesStoreNew(HsaBusinessEquities gradeEquities, List<String> storeGuidList) {
        //部分门店，且不存在
        log.info("权益规则 {} 是否全部门店：{}", gradeEquities.getGuid(), gradeEquities.getApplicableAllStore());
        if (Objects.equals(gradeEquities.getApplicableAllStore(), BooleanEnum.TRUE.getCode())) {
            log.info("权益规则 {} 适用所有门店", gradeEquities.getGuid());
            return storeGuidList;
        } else {
            return hsaEquitiesStoreRuleService.getExistsStoreList(gradeEquities.getGuid(), gradeEquities.getBusinessType(), gradeEquities.getVersionId(), storeGuidList);

        }

    }


    private HsaMemberGradePriceDetail getHsaMemberGradePriceDetail(CalculateMemberPriceCommodityQO memberPriceCommodityQO) {
        HsaMemberGradePriceDetail hsaMemberGradePriceDetail = null;
        if (StringUtil.isNotEmpty(memberPriceCommodityQO.getOrderNum())) {
            hsaMemberGradePriceDetail = hsaMemberGradePriceDetailMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                    .eq(HsaMemberGradePriceDetail::getOrderNumber, memberPriceCommodityQO.getOrderNum())
                    .eq(HsaMemberGradePriceDetail::getBusinessType, memberPriceCommodityQO.getBusinessType())
                    .eq(HsaMemberGradePriceDetail::getMemberInfoGuid, memberPriceCommodityQO.getMemberInfoGuid()));
        }
        log.info("获取订单锁定累计：{}", JSON.toJSONString(hsaMemberGradePriceDetail));
        return hsaMemberGradePriceDetail;
    }

    private CalculateMemberPriceCommodityVO getCalculateMemberPriceCommodityVO(List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList) {
        CalculateMemberPriceCommodityVO memberPriceCommodityVO;
        memberPriceCommodityVO = memberPriceCommodityVOList
                .stream()
                .sorted(Comparator.comparing(CalculateMemberPriceCommodityVO::getDiscountDynamicsAmount).reversed())
                .collect(Collectors.toList())
                .get(0);
        return memberPriceCommodityVO;
    }

    /**
     * 获取所有会员价相关权益
     */
    private void getMembershipBenefits(MembershipBenefitsDTO membershipBenefitsDTO) {
        HsaBusinessEquities gradeEquities = membershipBenefitsDTO.getGradeEquities();
        List<MembershipBenefitsVO> membershipBenefitsVOS = membershipBenefitsDTO.getMembershipBenefitsVOS();
        CalculateMemberPriceCommodityVO memberPriceCommodityVO = membershipBenefitsDTO.getMemberPriceCommodityVO();
        if (Objects.nonNull(gradeEquities)) {
            getMembershipVo(membershipBenefitsDTO);
            log.info("获取会员折扣优惠列表：{}", JSON.toJSONString(membershipBenefitsVOS));
        }
        memberPriceCommodityVO.setMembershipBenefitsVOS(membershipBenefitsVOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int accumulationDiscountReleaseKey(List<AccumulationReleaseKeyQO> qo) {
        log.info("会员折扣累计权益锁定释放={}", JSON.toJSONString(qo));
        final String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        //取消限购订单
        qo.forEach(q -> {
            q.setOperSubjectGuid(operSubjectGuid);
            cancelPurchaseOrder(q);
        });

        List<HsaMemberGradePriceDetail> hsaMemberGradePriceDetail = hsaMemberGradePriceDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberGradePriceDetail>().in(HsaMemberGradePriceDetail::getOrderNumber, qo.stream().map(AccumulationReleaseKeyQO::getOrderNum).collect(Collectors.toList())));
        log.info("hsaMemberGradePriceDetail={}", JSON.toJSONString(hsaMemberGradePriceDetail));
        if (CollectionUtil.isNotEmpty(hsaMemberGradePriceDetail)) {
            for (HsaMemberGradePriceDetail memberGradePriceDetail : hsaMemberGradePriceDetail) {
                hsaMemberGradePriceDetailMapper.removeByGuid(memberGradePriceDetail.getGuid());
            }
            return hsaMemberGradePriceDetail.get(0).getDiscountType();
        } else {
            return -1;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accumulationDiscountRelease(AccumulationReleaseKeyQO accumulationReleaseKeyQO) {
        log.info("权益折扣回退参数：{}", JacksonUtils.writeValueAsString(accumulationReleaseKeyQO));
        //权益回退
        accumulationDiscountReleaseKey(Collections.singletonList(new AccumulationReleaseKeyQO().setMemberInfoGuid(accumulationReleaseKeyQO.getMemberInfoGuid()).setOrderNum(accumulationReleaseKeyQO.getOrderNum())));
        //释放会员价优惠额度（支付后）
        HsaMemberGradePriceDetail gradePriceDetail = hsaMemberGradePriceDetailMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                .eq(HsaMemberGradePriceDetail::getOrderNumber, accumulationReleaseKeyQO.getOrderNum())
                .eq(HsaMemberGradePriceDetail::getMemberInfoGuid, accumulationReleaseKeyQO.getMemberInfoGuid()));
        if (Objects.nonNull(gradePriceDetail)) {
            boolean isSuccess = hsaMemberGradePriceDetailMapper.removeByGuid(gradePriceDetail.getGuid());
            log.info("权益折扣回退结果:{}", isSuccess);
        }

        //积分抵扣回退
        ReleaseOrderDeductDetailQO releaseOrderDeductDetailVO = new ReleaseOrderDeductDetailQO();
        releaseOrderDeductDetailVO.setMemberInfoGuid(accumulationReleaseKeyQO.getMemberInfoGuid())
                .setOrderNumber(accumulationReleaseKeyQO.getOrderNum());
        hsaIntegralDeductService.releaseIntegralDeductDetail(releaseOrderDeductDetailVO);

        //取消限购订单
        cancelPurchaseOrder(accumulationReleaseKeyQO);
    }

    /**
     * 取消限购订单
     *
     * @param accumulationReleaseKeyQo 订单参数
     */
    private void cancelPurchaseOrder(AccumulationReleaseKeyQO accumulationReleaseKeyQo) {
        //取消
        if (accumulationReleaseKeyQo.getConsumptionGuid() == null) {
            PurchaseOrderCancelDto cancelDto = new PurchaseOrderCancelDto()
                    .setOrderNumber(accumulationReleaseKeyQo.getOrderNum())
                    .setOperSubjectGuid(accumulationReleaseKeyQo.getOperSubjectGuid());
            consumptionOrderPublisher.publish(ConsumptionOrderEventEnum.PURCHASE_ORDER_CANCEL, JacksonUtils.writeValueAsString(cancelDto));
            return;

        }
        //退款
        final PurchaseOrderStateDto orderStateDto = PurchaseOrderStateDto.builder()
                .orderState(ConsumptionOrderStateEnum.REFUND.getCode())
                .orderNumber(accumulationReleaseKeyQo.getOrderNum())
                .operSubjectGuid(accumulationReleaseKeyQo.getOperSubjectGuid()).build();
        consumptionOrderPublisher.publish(ConsumptionOrderEventEnum.PURCHASE_ORDER_STATE, JacksonUtils.writeValueAsString(orderStateDto));
    }

    @Override
    public boolean unLockedDiscount(SettlementUnLockedDiscountDTO discountDTO) {
        log.info("会员折扣累计权益锁定释放={}", JSON.toJSONString(discountDTO));
        final LambdaQueryWrapper<HsaMemberGradePriceDetail> wrapper = new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                .eq(HsaMemberGradePriceDetail::getOperSubjectGuid, discountDTO.getOperSubjectGuid())
                .eq(HsaMemberGradePriceDetail::getOrderNumber, discountDTO.getOrderNo());
        List<HsaMemberGradePriceDetail> gradePriceDetails = hsaMemberGradePriceDetailMapper.selectList(wrapper);
        log.info("hsaMemberGradePriceDetail={}", JSON.toJSONString(gradePriceDetails));
        if (CollectionUtil.isEmpty(gradePriceDetails)) {
            return false;
        }
        //删除
        final List<String> guidList = gradePriceDetails.stream().map(HsaMemberGradePriceDetail::getGuid).collect(Collectors.toList());
        hsaMemberGradePriceDetailMapper.removeByGuids(guidList);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderGenerateCallbackProcessing(List<OrderGenerateCallbackQO> qo) {
        for (OrderGenerateCallbackQO orderGenerateCallbackQO : qo) {
            HsaMemberGradePriceDetail hsaMemberGradePriceDetail = hsaMemberGradePriceDetailMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradePriceDetail>().eq(HsaMemberGradePriceDetail::getGuid, orderGenerateCallbackQO.getMemberGradePriceDetailGuid()));
            if (Objects.nonNull(hsaMemberGradePriceDetail)) {
                hsaMemberGradePriceDetail.setOrderNumber(orderGenerateCallbackQO.getOrderNum());
                hsaMemberGradePriceDetailMapper.updateByGuid(hsaMemberGradePriceDetail);
            }
        }
    }

    @Override
    public boolean saveHsaMemberGradePriceDetail(MemberGradePriceDetailQO memberGradePriceDetail) {
        HsaMemberGradePriceDetail qo = new HsaMemberGradePriceDetail();
        BeanUtils.copyProperties(memberGradePriceDetail, qo);
        qo.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradePriceDetail.class.getSimpleName())).setIsCancel(NumberConstant.NUMBER_0).setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        return hsaMemberGradePriceDetailMapper.insert(qo) == NumberConstant.NUMBER_1;
    }

    private void lockedAccumulation(CalculateMemberPriceCommodityQO qo, HsaOperationMemberInfo hsaOperationMemberInfo, CalculateMemberPriceCommodityVO vo, HsaMemberGradePriceDetail hsaMemberGradePriceDetail) {
        //新的计算接口不走这
        if (qo.getIsOfferLocked() == BooleanEnum.TRUE.getCode()) {
            if (Objects.isNull(hsaMemberGradePriceDetail)) {
                hsaMemberGradePriceDetail = new HsaMemberGradePriceDetail();
                hsaMemberGradePriceDetail.setBusinessType(qo.getBusinessType());
                hsaMemberGradePriceDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradePriceDetail.class.getSimpleName()));
                setMemberGradePriceDetail(qo, hsaOperationMemberInfo, vo, hsaMemberGradePriceDetail);
                hsaMemberGradePriceDetailMapper.insert(hsaMemberGradePriceDetail);
            } else {
                setMemberGradePriceDetail(qo, hsaOperationMemberInfo, vo, hsaMemberGradePriceDetail);
                hsaMemberGradePriceDetailMapper.updateByGuid(hsaMemberGradePriceDetail);
            }
            vo.setMemberGradePriceDetailGuid(hsaMemberGradePriceDetail.getGuid());
            log.info("折扣锁定：{}", JSON.toJSONString(hsaMemberGradePriceDetail));
        }
    }

    /**
     * 锁定折扣
     *
     * @param orderNumber    订单号
     * @param businessType   0等级 1会员卡 BusinessTypeEnum
     * @param memberInfoGuid 会员guid
     * @param vo             优惠结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockedDiscount(String orderNumber, Integer businessType, String memberInfoGuid,
                               SettlementLockedDiscountReqDTO vo) {
        log.info("锁定折扣开始，订单号：{}，业务类型：{}，会员ID：{}", orderNumber, businessType, memberInfoGuid);

        // 参数校验
        if (StringUtils.isBlank(orderNumber) || Objects.isNull(businessType) ||
                StringUtils.isBlank(memberInfoGuid) || Objects.isNull(vo)) {
            log.error("锁定折扣参数错误，orderNumber：{}，businessType：{}，memberInfoGuid：{}",
                    orderNumber, businessType, memberInfoGuid);
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            // 查询现有记录
            HsaMemberGradePriceDetail priceDetail = hsaMemberGradePriceDetailMapper.selectOne(
                    new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                            .eq(HsaMemberGradePriceDetail::getOrderNumber, orderNumber)
                            .eq(HsaMemberGradePriceDetail::getBusinessType, businessType)
                            .eq(HsaMemberGradePriceDetail::getMemberInfoGuid, memberInfoGuid));

            // 构建折扣详情
            if (Objects.isNull(priceDetail)) {
                priceDetail = new HsaMemberGradePriceDetail();
                priceDetail.setBusinessType(businessType);
            }

            // 设置公共属性
            priceDetail.setDiscountedPrice(vo.getDiscountAmount())
                    .setDiscountType(EquitiesRuleTypeEnum.MEMBER_PRICE.getCode())
                    .setEquitiesGuid(vo.getDiscountGuid())
                    .setEquitiesId(vo.getDiscountOptionId())
                    .setOrderNumber(orderNumber)
                    .setDiscountDynamics(new BigDecimal(vo.getDiscountDynamic()))
                    .setIsCancel(BooleanEnum.FALSE.getCode())
                    .setMemberInfoGuid(memberInfoGuid)
                    .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());

            // 保存或更新记录
            if (Objects.isNull(priceDetail.getGuid())) {
                priceDetail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberGradePriceDetail.class.getSimpleName()));
                hsaMemberGradePriceDetailMapper.insert(priceDetail);
            } else {
                hsaMemberGradePriceDetailMapper.updateByGuid(priceDetail);
            }

            log.info("锁定折扣成功，订单号：{}", orderNumber);
        } catch (Exception e) {
            log.error("锁定折扣异常，订单号：{}，异常：{}", orderNumber, e.getMessage(), e);
            throw e;
        }
    }

    private void setMemberGradePriceDetail(CalculateMemberPriceCommodityQO qo, HsaOperationMemberInfo hsaOperationMemberInfo, CalculateMemberPriceCommodityVO vo, HsaMemberGradePriceDetail hsaMemberGradePriceDetail) {
        hsaMemberGradePriceDetail.setDiscountedPrice(vo.getDiscountDynamicsAmount())
                .setDiscountType(vo.getDiscountType())
                .setEquitiesGuid(vo.getEquitiesGuid())
                .setEquitiesId(vo.getEquitiesId())
                .setOrderNumber(qo.getOrderNum())
                .setDiscountDynamics(vo.getDiscountDynamics())
                .setIsCancel(NumberConstant.NUMBER_0).setMemberInfoGuid(hsaOperationMemberInfo.getGuid())
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
    }

    /**
     * 根据会员等级权益计算商品折扣
     *
     * @param gradeEquities             gradeEquities
     * @param hsaMemberGradePriceDetail hsaMemberGradePriceDetail
     */
    private void getCalculateGradeMemberPrice(HsaBusinessEquities gradeEquities,
                                              HsaMemberGradePriceDetail hsaMemberGradePriceDetail,
                                              CalculateMemberQO calculateMemberQO) {
        log.info("获取会员等级权益计算商品折扣，gradeEquities：{}, hsaMemberGradePriceDetail：{}", JSON.toJSONString(gradeEquities), JSON.toJSONString(hsaMemberGradePriceDetail));
        CalculateMemberPriceCommodityQO qo = calculateMemberQO.getQo();

        List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList = calculateMemberQO.getMemberPriceCommodityVOList();

        //商品入参
        List<MemberPriceCommodityQO> memberPriceCommodityList = calculateMemberQO.getMemberPriceCommodityList();

        CalculateCumulativeDiscountQO cumulativeDiscountQO = calculateMemberQO.getCumulativeDiscountQO();

        CalculateMemberPriceCommodityVO vo = new CalculateMemberPriceCommodityVO();
        //条件校验(终端等)
        HsaBusinessEquities hsaBusinessEquities = checkHsaGradeEquities(qo, gradeEquities);
        if (Objects.nonNull(hsaBusinessEquities) && CollUtil.isNotEmpty(qo.getMemberPriceCommodityQOS())) {
            MemberCalculatePriceQO memberCalculatePriceQO = new MemberCalculatePriceQO();
            //折扣力度
            BigDecimal discountDynamics;
            discountDynamics = getDiscountDynamics(hsaMemberGradePriceDetail, hsaBusinessEquities);
            Long startTime = System.currentTimeMillis();

            //数据库：获取参与折扣商品
            Map<String, GradeCommodityBaseQO> dbGradeCommodityMap = getGradeCommodityBase(hsaBusinessEquities, qo);
            log.info("获取参与折扣商品：{}", JSON.toJSONString(dbGradeCommodityMap));

            Long endTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>>>>>>>>>>>获取参与折扣商品逻辑耗时：    " + (endTime - startTime));
            //参与折扣商品 用于计算分摊比例
            List<MemberPriceCommodityQO> memberPriceCommodityQOS = Lists.newArrayList();
            //不参与折扣商品
            List<MemberPriceCommodityQO> notMemberPriceCommodityQOS = Lists.newArrayList();


            //计算对应折扣商品总额
            Long startTime1 = System.currentTimeMillis();

            //计算商品总金额
            dealOrderDiscountDynamics(qo,
                    memberCalculatePriceQO,
                    discountDynamics,
                    dbGradeCommodityMap,
                    memberPriceCommodityQOS,
                    notMemberPriceCommodityQOS);

            Long endTime1 = System.currentTimeMillis();
            log.info(">>>>>>>>>>>>>>>>>>>>>计算对应折扣商品总额商品逻辑耗时：    " + (endTime1 - startTime1));

            //最大优惠总额
            BigDecimal discountCommodityTotalAmount = memberCalculatePriceQO.getDiscountCommodityTotalAmount();

            BigDecimal cumulativeUnitPrice = memberCalculatePriceQO.getCumulativeUnitPrice();
            //无优惠
            if (discountCommodityTotalAmount.compareTo(BigDecimal.ZERO) <= NumberConstant.NUMBER_0) {
                return;
            }

            //单次优惠限制
            discountCommodityTotalAmount = getCheckSingleDiscountsLimited(hsaBusinessEquities, discountCommodityTotalAmount);

            //周期优惠限制
            discountCommodityTotalAmount = getCheckLimitCycle(qo, hsaBusinessEquities, discountCommodityTotalAmount, cumulativeDiscountQO);

            //累计优惠限额
            discountCommodityTotalAmount = getCheckTotalDiscountLimited(qo, hsaBusinessEquities, discountCommodityTotalAmount, cumulativeDiscountQO);

            //保留两位
            discountCommodityTotalAmount = discountCommodityTotalAmount.setScale(NumberConstant.NUMBER_2, RoundingMode.DOWN);


            //可用商品优惠分摊
            if (CollUtil.isNotEmpty(dbGradeCommodityMap)) {
                //优惠分摊
                calculateCommodityDiscountRate(memberPriceCommodityQOS, cumulativeUnitPrice, discountCommodityTotalAmount, qo.getDiscountType());

                log.info("商品优惠比例价格：{}", JSON.toJSONString(memberPriceCommodityQOS));
                memberPriceCommodityList.addAll(memberPriceCommodityQOS);
                memberPriceCommodityList.addAll(notMemberPriceCommodityQOS);
            }
            //订单实际支付金额
            BigDecimal orderRealPaymentAmount = qo.getCommodityTotalAmount().subtract(discountCommodityTotalAmount);

            memberPriceCommodityVOList.add(
                    vo.setMemberPriceCommodityQOS(memberPriceCommodityList)
                            .setSlotTime(qo.getSlotTime())
                            .setOrderNum(qo.getOrderNum())
                            .setOrderNumRedundant(qo.getOrderNumRedundant())
                            .setCommodityTotalAmount(qo.getCommodityTotalAmount())
                            .setOrderRealPaymentAmount(orderRealPaymentAmount)
                            .setDiscountDynamicsAmount(discountCommodityTotalAmount)
                            .setDiscountDynamics(discountDynamics)
                            .setDiscountType(DiscountTypeEnum.DISCOUNT_GRADE.getCode())
                            .setEquitiesGuid(hsaBusinessEquities.getGuid())
                            .setEquitiesId(hsaBusinessEquities.getEquitiesId())
            );
        }
    }

    private void dealOrderDiscountDynamics(CalculateMemberPriceCommodityQO qo,
                                           MemberCalculatePriceQO memberCalculatePriceQO,
                                           BigDecimal discountDynamics,
                                           Map<String, GradeCommodityBaseQO> dbGradeCommodityMap,
                                           List<MemberPriceCommodityQO> memberPriceCommodityQOS,
                                           List<MemberPriceCommodityQO> notMemberPriceCommodityQOS) {
        //以商品价格合计来算
        dealCommodityOrder(qo, memberCalculatePriceQO, discountDynamics, dbGradeCommodityMap, memberPriceCommodityQOS, notMemberPriceCommodityQOS);
    }

    private void dealCommodityOrder(CalculateMemberPriceCommodityQO qo,
                                    MemberCalculatePriceQO memberCalculatePriceQO,
                                    BigDecimal discountDynamics,
                                    Map<String, GradeCommodityBaseQO> dbGradeCommodityMap,
                                    List<MemberPriceCommodityQO> memberPriceCommodityQOS,
                                    List<MemberPriceCommodityQO> notMemberPriceCommodityQOS) {

        BigDecimal total = null;
        //折扣额度
        BigDecimal discountCommodityTotalAmount;
        //商品累计单价
        BigDecimal cumulativeUnitPrice = BigDecimal.ZERO;
        //遍历商品
        for (MemberPriceCommodityQO memberPriceCommodityQO : qo.getMemberPriceCommodityQOS()) {
            log.info("计算对应折扣商品总额：{}", JSON.toJSONString(qo.getMemberPriceCommodityQOS()));
            if (dbGradeCommodityMap.containsKey(memberPriceCommodityQO.getCommodityId())) {
                //商品总价
                BigDecimal totalNum = memberPriceCommodityQO.getCommodityPrice();
                //0.01单价不参与折扣
                if (totalNum.compareTo(NumberConstant.DECIMAL_POINT_001) <= 0) {
                    //商品要返回去
                    memberPriceCommodityQOS.add(memberPriceCommodityQO);
                    continue;
                }
//                //减去其他优惠项 已优惠金额
                if (Objects.nonNull(memberPriceCommodityQO.getCommodityMinistryPrice())
                        && memberPriceCommodityQO.getCommodityMinistryPrice().compareTo(BigDecimal.ZERO) > 0) {

                    totalNum = totalNum.subtract(memberPriceCommodityQO.getCommodityMinistryPrice());
                }
                //减去其他优惠
                total = geTotal(total, totalNum);
                //减去其他优惠金额
                cumulativeUnitPrice = getAdd(cumulativeUnitPrice, totalNum);

                memberPriceCommodityQOS.add(memberPriceCommodityQO);

                memberCalculatePriceQO.setCumulativeUnitPrice(cumulativeUnitPrice);
            } else {
                notMemberPriceCommodityQOS.add(memberPriceCommodityQO);
            }
        }
        //商品优惠价格
        if (Objects.nonNull(total) && total.compareTo(new BigDecimal("0.01")) > 0) {
            //算比例 todo 最大优惠金额，还要看限额
            discountCommodityTotalAmount = getDiscountCommodityTotalAmount(discountDynamics, total);
            memberCalculatePriceQO.setDiscountCommodityTotalAmount(discountCommodityTotalAmount);
        }
    }


    private BigDecimal geTotal(BigDecimal total, BigDecimal totalNum) {
        if (Objects.isNull(total)) {
            total = BigDecimal.ZERO;
        }
        //适用商品总价
        total = getAdd(total, totalNum);
        return total;
    }

    private static BigDecimal getDiscountCommodityTotalAmount(BigDecimal discountDynamics, BigDecimal total) {
        BigDecimal discountCommodityTotalAmount;

        if (Objects.isNull(total)) {
            return BigDecimal.ZERO;
        }
        //优惠价格
        discountCommodityTotalAmount = total.multiply(discountDynamics).setScale(SettlementConstant.SCALE_LENGTH, RoundingMode.DOWN);

        //无优惠不给
        if (discountCommodityTotalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            //需求调整，不给优惠
            return BigDecimal.ZERO;
        }

        if (discountCommodityTotalAmount.compareTo(NumberConstant.DECIMAL_POINT_001) < 0 || discountDynamics.compareTo(NumberConstant.DECIMAL_POINT_1) < 0) {
            discountCommodityTotalAmount = total.subtract(discountCommodityTotalAmount);
        }
        return discountCommodityTotalAmount.setScale(SettlementConstant.FINAL_LENGTH, RoundingMode.DOWN);
    }


    //以前端传入总价来算（原dealOrderDiscountDynamics方法调用 弃）
    public void dealPriceOrder(CalculateMemberPriceCommodityQO qo, MemberCalculatePriceQO memberCalculatePriceQO, BigDecimal discountDynamics) {
        BigDecimal total;
        //商品累计单价
        BigDecimal cumulativeUnitPrice = BigDecimal.ZERO;
        //折扣额度
        BigDecimal discountCommodityTotalAmount;
        //商品总价
        total = qo.getCommodityTotalAmount();

        //若小于等于0.01就无优惠
        if (total.compareTo(NumberConstant.DECIMAL_POINT_001) <= 0) {
            return;
        }
        //商品优惠价格
        BigDecimal commodityDiscount = total.multiply(discountDynamics).setScale(SettlementConstant.SCALE_LENGTH, RoundingMode.DOWN);

        cumulativeUnitPrice = getAdd(cumulativeUnitPrice, total);

        memberCalculatePriceQO.setCumulativeUnitPrice(cumulativeUnitPrice);

        discountCommodityTotalAmount = checkDiscountAmount(commodityDiscount, cumulativeUnitPrice);
        memberCalculatePriceQO.setDiscountCommodityTotalAmount(discountCommodityTotalAmount);
    }

    private static BigDecimal getDiscountDynamics(HsaMemberGradePriceDetail hsaMemberGradePriceDetail, HsaBusinessEquities hsaBusinessEquities) {
        BigDecimal discountDynamics;
        if (Objects.nonNull(hsaMemberGradePriceDetail) && Objects.nonNull(hsaMemberGradePriceDetail.getDiscountDynamics())) {
            discountDynamics = hsaMemberGradePriceDetail.getDiscountDynamics();
        } else {
            discountDynamics = hsaBusinessEquities.getDiscountDynamics().divide(BigDecimal.TEN);
        }
        return discountDynamics;
    }

    private static BigDecimal checkDiscountAmount(BigDecimal discountCommodityTotalAmount, BigDecimal cumulativeUnitPrice) {
        if (discountCommodityTotalAmount.compareTo(NumberConstant.DECIMAL_POINT_001) < 0) {
            discountCommodityTotalAmount = BigDecimal.ZERO;
        } else {
            //折后商品总价
            discountCommodityTotalAmount = cumulativeUnitPrice.subtract(discountCommodityTotalAmount);
        }
        return discountCommodityTotalAmount.setScale(SettlementConstant.FINAL_LENGTH, RoundingMode.DOWN);
    }

    private BigDecimal getAdd(BigDecimal discountCommodityTotalAmount, BigDecimal decimal) {
        return discountCommodityTotalAmount.add(decimal);
    }

    /**
     * 计算商品分摊
     *
     * @param memberPriceCommodityQOS 可用商品
     * @param cumulativeUnitPrice     计算总额
     * @param discountPrice           优惠价
     * @param discountType            优惠类型
     */
    private void calculateCommodityDiscountRate(List<MemberPriceCommodityQO> memberPriceCommodityQOS,
                                                BigDecimal cumulativeUnitPrice,
                                                BigDecimal discountPrice,
                                                Integer discountType) {
        log.info("计算分摊的商品：{}", JSON.toJSONString(memberPriceCommodityQOS));
        if (Objects.nonNull(discountType) && discountType == DiscountTypeEnum.DISCOUNT_NOT.getCode()) {
            return;
        }
        int endSize = memberPriceCommodityQOS.size() - 1;
        BigDecimal endCommodityMinistryPrice = discountPrice;
        for (int i = 0; i < endSize; i++) {
            final MemberPriceCommodityQO priceCommodityQo = memberPriceCommodityQOS.get(i);
            //0.01单价不参与折扣
            if (priceCommodityQo.getCommodityPrice().compareTo(NumberConstant.DECIMAL_POINT_001) <= 0) {
                continue;
            }
            //剩余金额
            BigDecimal commodityPrice = (priceCommodityQo.getCommodityPrice()
                    .subtract(priceCommodityQo.getCommodityMinistryPrice()));
            //比例
            final BigDecimal scale = commodityPrice.divide(cumulativeUnitPrice, SettlementConstant.SCALE_LENGTH, RoundingMode.DOWN);
            //优惠金额
            BigDecimal discountRatio = discountPrice.multiply(scale);
            final BigDecimal commodityMinistryPrice = getCommodityMinistryPrice(commodityPrice, discountRatio);
            endCommodityMinistryPrice = endCommodityMinistryPrice.subtract(commodityMinistryPrice);
            //累计优惠
            priceCommodityQo.addCommodityMinistryPrice(commodityMinistryPrice);
        }

        //最后一个商品
        if (endCommodityMinistryPrice.compareTo(BigDecimal.ZERO) > 0) {

            final MemberPriceCommodityQO endCommodity = memberPriceCommodityQOS.get(endSize);
            //0.01单价不参与折扣
            if (endCommodity.getCommodityPrice().compareTo(NumberConstant.DECIMAL_POINT_001) <= 0) {
                return;
            }
            //剩余金额
            BigDecimal endCommodityPrice = (endCommodity.getCommodityPrice()
                    .subtract(endCommodity.getCommodityMinistryPrice()));
            //剩下的都给他
            final BigDecimal min = BigDecimalUtil.min(endCommodityPrice, endCommodityMinistryPrice);
            //累加
            endCommodity.addCommodityMinistryPrice(min);
        }

    }

    private static BigDecimal getCommodityMinistryPrice(BigDecimal commodityPrice, BigDecimal discountRatio) {
        if (discountRatio.compareTo(BigDecimal.ZERO) == 0) {
            //至少给他0.01
            return commodityPrice.compareTo(BigDecimal.ZERO) > 0 ? new BigDecimal("0.01") : BigDecimal.ZERO;
        } else {
            return discountRatio.setScale(NumberConstant.NUMBER_2, BigDecimal.ROUND_DOWN);
        }
    }

    private BigDecimal getCheckSingleDiscountsLimited(HsaBusinessEquities hsaBusinessEquities, BigDecimal discountPrice) {
        if (hsaBusinessEquities.getSingleDiscountsLimited() == EquitiesLimitedTypeEnum.LIMITED.getCode() && discountPrice.compareTo(hsaBusinessEquities.getSingleDiscountsLimitedAmount()) > 0) {
            discountPrice = hsaBusinessEquities.getSingleDiscountsLimitedAmount();
        }

        return discountPrice;
    }

    private BigDecimal getCheckTotalDiscountLimited(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, BigDecimal discountPrice, CalculateCumulativeDiscountQO cumulativeDiscountQO) {
        if (hsaBusinessEquities.getTotalDiscountLimited() != EquitiesLimitedTypeEnum.LIMITED.getCode()) {
            return discountPrice;
        }
        //获取累计优惠限额
        LocalDateTime start = null;
        LocalDateTime end = null;
        //已使用
        BigDecimal consumptionLimit = getConsumptionLimit(qo, hsaBusinessEquities, start, end);
        //剩余可用
        BigDecimal limitedAmount = hsaBusinessEquities.getTotalDiscountLimitedAmount().subtract(consumptionLimit);
        if (discountPrice.compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
            setTotalDiscount(cumulativeDiscountQO, limitedAmount);
            return discountPrice;
        }
        if (Objects.nonNull(cumulativeDiscountQO)) {
            discountPrice = getDiscountPrice(qo, discountPrice, cumulativeDiscountQO, limitedAmount, 99);
        } else {
            if (limitedAmount.compareTo(BigDecimal.ZERO) < 0) {
                limitedAmount = BigDecimal.ZERO;
            }
            if (discountPrice.compareTo(limitedAmount) > 0) {
                discountPrice = limitedAmount;
            }
        }
        return discountPrice;
    }

    private static void setTotalDiscount(CalculateCumulativeDiscountQO cumulativeDiscountQO, BigDecimal limitedAmount) {
        if (cumulativeDiscountQO != null) {
            //累计剩余
            cumulativeDiscountQO.setTotalDiscount(limitedAmount);
        }
    }

    private void getMembershipVo(MembershipBenefitsDTO membershipBenefitsDTO) {
        CalculateMemberPriceCommodityQO qo = membershipBenefitsDTO.getMemberPriceCommodityQO();
        CalculateMemberPriceCommodityVO vo = membershipBenefitsDTO.getMemberPriceCommodityVO();
        HsaBusinessEquities hsaBusinessEquities = membershipBenefitsDTO.getGradeEquities();
        HsaOperationMemberInfo hsaOperationMemberInfo = membershipBenefitsDTO.getHsaOperationMemberInfo();
        List<MembershipBenefitsVO> membershipBenefitsVOS = membershipBenefitsDTO.getMembershipBenefitsVOS();
        if ((Objects.isNull(qo.getDiscountType()) || qo.getDiscountType() != DiscountTypeEnum.DISCOUNT_NOT.getCode()) && Objects.isNull(vo.getDiscountDynamicsAmount())) {
            return;
        }
        List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList = new ArrayList<>();
        List<MemberPriceCommodityQO> memberPriceCommodityList = Lists.newArrayList();
        if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.MEMBER_PRICE.getCode()) {
            dealMemberPrice(membershipBenefitsDTO, memberPriceCommodityVOList, memberPriceCommodityList);
        } else if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GOODS_MEMBER.getCode()) {
            dealGoodMember(qo, hsaOperationMemberInfo, membershipBenefitsVOS, hsaBusinessEquities, vo, memberPriceCommodityVOList, memberPriceCommodityList);
        }
    }

    private void dealGoodMember(CalculateMemberPriceCommodityQO qo,
                                HsaOperationMemberInfo hsaOperationMemberInfo,
                                List<MembershipBenefitsVO> membershipBenefitsVOS,
                                HsaBusinessEquities hsaBusinessEquities,
                                CalculateMemberPriceCommodityVO vo,
                                List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList,
                                List<MemberPriceCommodityQO> memberPriceCommodityList) {
        MembershipBenefitsVO membershipBenefitsVO;
        membershipBenefitsVO = getMembershipBenefitsVO(hsaOperationMemberInfo, hsaBusinessEquities, vo, new LimitCycleDTO(), qo);
        isChecked(vo, membershipBenefitsVO, DiscountTypeEnum.GOODS_MEMBER);
        if (Objects.nonNull(qo.getDiscountType()) && qo.getDiscountType() == DiscountTypeEnum.DISCOUNT_NOT.getCode()) {
            getCalculateGradeMemberPrice(qo, memberPriceCommodityVOList, memberPriceCommodityList, hsaBusinessEquities);
            if (CollUtil.isNotEmpty(memberPriceCommodityVOList)) {
                membershipBenefitsVO.setDiscountDynamicsAmount(memberPriceCommodityVOList.get(0).getDiscountDynamicsAmount());
            }
        }
        membershipBenefitsVOS.add(membershipBenefitsVO);
    }

    private void dealMemberPrice(MembershipBenefitsDTO membershipBenefitsDTO,
                                 List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList,
                                 List<MemberPriceCommodityQO> memberPriceCommodityList) {
        CalculateMemberPriceCommodityQO qo = membershipBenefitsDTO.getMemberPriceCommodityQO();
        CalculateMemberPriceCommodityVO vo = membershipBenefitsDTO.getMemberPriceCommodityVO();
        HsaBusinessEquities hsaBusinessEquities = membershipBenefitsDTO.getGradeEquities();
        HsaOperationMemberInfo hsaOperationMemberInfo = membershipBenefitsDTO.getHsaOperationMemberInfo();
        List<MembershipBenefitsVO> membershipBenefitsVOS = membershipBenefitsDTO.getMembershipBenefitsVOS();
        CalculateCumulativeDiscountQO cumulativeDiscountQO = membershipBenefitsDTO.getCumulativeDiscountQO();

        MembershipBenefitsVO membershipBenefitsVO;
        LimitCycleDTO limitCycleDTO = calculateCheckQuota(qo, hsaBusinessEquities, vo, cumulativeDiscountQO);
        if (Objects.isNull(limitCycleDTO) && hsaBusinessEquities.getPeriodDiscountLimited() == EquitiesLimitedTypeEnum.UN_LIMITED.getCode()) {
            membershipBenefitsVO = getMembershipBenefitsVO(hsaOperationMemberInfo, hsaBusinessEquities, vo, new LimitCycleDTO(), qo);
        } else {
            membershipBenefitsVO = getMembershipBenefitsVO(qo, hsaOperationMemberInfo, hsaBusinessEquities, vo, limitCycleDTO);
        }
        getDiscountDynamicsAmount(qo, hsaBusinessEquities, memberPriceCommodityVOList, memberPriceCommodityList, membershipBenefitsVO, cumulativeDiscountQO);
        isChecked(vo, membershipBenefitsVO, DiscountTypeEnum.DISCOUNT_GRADE);
        membershipBenefitsVOS.add(membershipBenefitsVO);
    }

    private MembershipBenefitsVO getMembershipBenefitsVO(CalculateMemberPriceCommodityQO qo, HsaOperationMemberInfo hsaOperationMemberInfo, HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityVO vo, LimitCycleDTO limitCycleDTO) {
        MembershipBenefitsVO membershipBenefitsVO;
        membershipBenefitsVO = getMembershipBenefitsVO(hsaOperationMemberInfo, hsaBusinessEquities, vo, limitCycleDTO, qo);
        if (Objects.nonNull(vo.getDiscountType()) && vo.getDiscountType() == DiscountTypeEnum.DISCOUNT_GRADE.getCode()) {
            membershipBenefitsVO.setIsChecked(NumberConstant.NUMBER_1);
        } else {
            membershipBenefitsVO.setIsChecked(NumberConstant.NUMBER_0);
        }
        return membershipBenefitsVO;
    }

    private void isChecked(CalculateMemberPriceCommodityVO vo, MembershipBenefitsVO membershipBenefitsVO, DiscountTypeEnum goodsMember) {
        if (Objects.nonNull(vo.getDiscountType()) && vo.getDiscountType() == goodsMember.getCode()) {
            membershipBenefitsVO.setIsChecked(NumberConstant.NUMBER_1);
        } else {
            membershipBenefitsVO.setIsChecked(NumberConstant.NUMBER_0);
        }
    }

    private void getDiscountDynamicsAmount(CalculateMemberPriceCommodityQO qo,
                                           HsaBusinessEquities hsaBusinessEquities,
                                           List<CalculateMemberPriceCommodityVO> memberPriceCommodityVOList,
                                           List<MemberPriceCommodityQO> memberPriceCommodityList,
                                           MembershipBenefitsVO membershipBenefitsVO,
                                           CalculateCumulativeDiscountQO cumulativeDiscountQO) {
        if (Objects.nonNull(qo.getDiscountType()) && qo.getDiscountType() == DiscountTypeEnum.DISCOUNT_NOT.getCode()) {
            HsaMemberGradePriceDetail hsaMemberGradePriceDetail = hsaMemberGradePriceDetailMapper
                    .selectOne(new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                            .eq(HsaMemberGradePriceDetail::getOrderNumber, qo.getOrderNum())
                            .eq(HsaMemberGradePriceDetail::getMemberInfoGuid,
                                    qo.getMemberInfoGuid()));
            CalculateMemberQO calculateMemberQO = getCalculateMemberQO(memberPriceCommodityList, qo, cumulativeDiscountQO, memberPriceCommodityVOList);
            getCalculateGradeMemberPrice(hsaBusinessEquities, hsaMemberGradePriceDetail, calculateMemberQO);
            if (CollectionUtil.isNotEmpty(memberPriceCommodityVOList)) {
                membershipBenefitsVO.setDiscountDynamicsAmount(memberPriceCommodityVOList.get(0).getDiscountDynamicsAmount());
            }
        }
    }

    private MembershipBenefitsVO getMembershipBenefitsVO(HsaOperationMemberInfo hsaOperationMemberInfo, HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityVO vo, LimitCycleDTO limitCycleDTO, CalculateMemberPriceCommodityQO qo) {
        MembershipBenefitsVO membershipBenefitsVO = new MembershipBenefitsVO();
        dealEquitiesTimeLimited(hsaBusinessEquities, qo, membershipBenefitsVO);

        membershipBenefitsVO.setMemberGradeInfoName(hsaOperationMemberInfo.getMemberGradeInfoName())
                .setDiscountType(hsaBusinessEquities.getEquitiesRuleType())
                .setEquitiesName(hsaBusinessEquities.getEquitiesName())
                .setDiscountDynamicsAmount(Objects.nonNull(vo) ? vo.getDiscountDynamicsAmount() : null)
                .setEquitiesGuid(hsaBusinessEquities.getGuid())
                .setPeriodDiscountLimited(hsaBusinessEquities.getPeriodDiscountLimited());
        dealDiscountDynamics(hsaBusinessEquities, vo, qo, membershipBenefitsVO);
        dealDiscountLimited(limitCycleDTO, membershipBenefitsVO);
        dealIsCheck(vo, membershipBenefitsVO);
        return membershipBenefitsVO;
    }

    private static void dealIsCheck(CalculateMemberPriceCommodityVO vo, MembershipBenefitsVO membershipBenefitsVO) {
        if (Objects.nonNull(vo) && Objects.nonNull(vo.getDiscountType()) && vo.getDiscountType().equals(membershipBenefitsVO.getDiscountType())) {
            membershipBenefitsVO.setIsChecked(NumberConstant.NUMBER_1);
        }
    }

    private static void dealDiscountLimited(LimitCycleDTO limitCycleDTO, MembershipBenefitsVO membershipBenefitsVO) {
        if (Objects.isNull(limitCycleDTO)) {
            return;
        }
        //累计
        if (Objects.equals(limitCycleDTO.getType(), DataUnitEnum.TOTAL.getCode())) {
            membershipBenefitsVO.setTotalDiscountLimited(NumberConstant.NUMBER_1);
            membershipBenefitsVO.setTotalDiscountLimitedAmount(limitCycleDTO.getValue());
            return;
        }
        //周期
        if (Objects.nonNull(limitCycleDTO.getType())) {
            membershipBenefitsVO.setEquitiesTimeLimitedType(limitCycleDTO.getType())
                    .setMinimumThisMonthAmount(limitCycleDTO.getValue())
                    .setPeriodDiscountLimited(EquitiesLimitedTypeEnum.LIMITED.getCode());
        }
    }

    private static void dealDiscountDynamics(HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityVO vo, CalculateMemberPriceCommodityQO qo, MembershipBenefitsVO membershipBenefitsVO) {
        if (Objects.nonNull(qo.getDiscountType()) && qo.getDiscountType() == DiscountTypeEnum.DISCOUNT_NOT.getCode()) {
            membershipBenefitsVO.setDiscountDynamics(hsaBusinessEquities.getDiscountDynamics());
        } else if (Objects.nonNull(vo)) {
            membershipBenefitsVO.setDiscountDynamics(vo.getDiscountDynamics().multiply(BigDecimal.TEN));
        }
    }

    private void dealEquitiesTimeLimited(HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityQO qo, MembershipBenefitsVO membershipBenefitsVO) {
        if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.MEMBER_PRICE.getCode()) {
            membershipBenefitsVO.setSingleDiscountsLimitedAmount(hsaBusinessEquities.getSingleDiscountsLimited() == EquitiesLimitedTypeEnum.LIMITED.getCode() ? hsaBusinessEquities.getSingleDiscountsLimitedAmount() : null).setSingleDiscountsLimited(hsaBusinessEquities.getSingleDiscountsLimited());
        } else {
            if (hsaBusinessEquities.getPeriodDiscountLimited() == NumberConstant.NUMBER_1) {
                EquitiesLimitedDTO equitiesLimitedDTOS = JSONArray.parseArray(hsaBusinessEquities.getPeriodDiscountType(), EquitiesLimitedDTO.class).get(0);
                membershipBenefitsVO.setEquitiesTimeLimitedType(Integer.parseInt(equitiesLimitedDTOS.getType()));
                membershipBenefitsVO.setMemberPriceLimited(getCheckGoodsLimitCycle(qo, hsaBusinessEquities));
            }
        }
    }

    private BigDecimal getCheckLimitCycle(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, BigDecimal discountPrice, CalculateCumulativeDiscountQO cumulativeDiscountQO) {
        if (hsaBusinessEquities.getPeriodDiscountLimited() == EquitiesLimitedTypeEnum.LIMITED.getCode()) {
            List<EquitiesLimitedDTO> equitiesLimitedDTOS = JSON.parseArray(hsaBusinessEquities.getPeriodDiscountType(), EquitiesLimitedDTO.class);
            //日限制
            EquitiesLimitedDTO equitiesLimitedDay = getEquitiesLimitedDTOS(equitiesLimitedDTOS, DataUnitEnum.DAY.getCode());
            discountPrice = calculateCheckQuota(DataUnitEnum.DAY.getCode(), qo, hsaBusinessEquities, discountPrice, equitiesLimitedDay, cumulativeDiscountQO);

            //周限制
            EquitiesLimitedDTO equitiesLimitedWeek = getEquitiesLimitedDTOS(equitiesLimitedDTOS, DataUnitEnum.WEEK.getCode());
            discountPrice = calculateCheckQuota(DataUnitEnum.WEEK.getCode(), qo, hsaBusinessEquities, discountPrice, equitiesLimitedWeek, cumulativeDiscountQO);

            //月限制
            EquitiesLimitedDTO equitiesLimitedMonth = getEquitiesLimitedDTOS(equitiesLimitedDTOS, DataUnitEnum.MONTH.getCode());
            discountPrice = calculateCheckQuota(DataUnitEnum.MONTH.getCode(), qo, hsaBusinessEquities, discountPrice, equitiesLimitedMonth, cumulativeDiscountQO);

            //年限制
            EquitiesLimitedDTO equitiesLimitedYear = getEquitiesLimitedDTOS(equitiesLimitedDTOS, DataUnitEnum.YEAR.getCode());
            discountPrice = calculateCheckQuota(DataUnitEnum.YEAR.getCode(), qo, hsaBusinessEquities, discountPrice, equitiesLimitedYear, cumulativeDiscountQO);
        }
        return discountPrice;
    }

    /**
     * 计算会员价周期剩余次数
     *
     * @param qo                  qo
     * @param hsaBusinessEquities hsaBusinessEquities
     * @return
     */
    private int getCheckGoodsLimitCycle(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities) {
        if (hsaBusinessEquities.getPeriodDiscountLimited() == EquitiesLimitedTypeEnum.LIMITED.getCode()) {
            EquitiesLimitedDTO equitiesLimitedDTOS = JSONArray.parseArray(hsaBusinessEquities.getPeriodDiscountType(), EquitiesLimitedDTO.class).get(0);
            if (Objects.nonNull(equitiesLimitedDTOS)) {
                //日限制
                if (equitiesLimitedDTOS.getType().equals(DataUnitEnum.DAY.getCode() + "")) {
                    return calculateCheckFrequency(qo, hsaBusinessEquities, equitiesLimitedDTOS, DateUtil.getDayStartTime(), DateUtil.getDayEndTime());
                }
                //周限制
                if (equitiesLimitedDTOS.getType().equals(DataUnitEnum.WEEK.getCode() + "")) {
                    return calculateCheckFrequency(qo, hsaBusinessEquities, equitiesLimitedDTOS, DateUtil.getWeekStartTime(), DateUtil.getWeekEndTime());
                }
                //月限制
                if (equitiesLimitedDTOS.getType().equals(DataUnitEnum.MONTH.getCode() + "")) {
                    return calculateCheckFrequency(qo, hsaBusinessEquities, equitiesLimitedDTOS, DateUtil.getMonthStartTime(), DateUtil.getMonthEndTime());
                }
                //年限制
                if (equitiesLimitedDTOS.getType().equals(DataUnitEnum.YEAR.getCode() + "")) {
                    return calculateCheckFrequency(qo, hsaBusinessEquities, equitiesLimitedDTOS, DateUtil.getYearStartTime(), DateUtil.getYearEndTime());
                }
            }
        }
        return NumberConstant.NUMBER_0;
    }

    private EquitiesLimitedDTO getEquitiesLimitedDTOS(List<EquitiesLimitedDTO> equitiesLimitedDTOS, Integer code) {
        List<EquitiesLimitedDTO> equitiesLimitedDTO = Objects.requireNonNull(equitiesLimitedDTOS).stream().filter(in -> Integer.parseInt(in.getType()) == code).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(equitiesLimitedDTO)) {
            return null;
        }
        return equitiesLimitedDTO.get(0);
    }

    /**
     * 计算周期最小限额
     *
     * @param qo                  CalculateMemberPriceCommodityQO
     * @param hsaBusinessEquities hsaBusinessEquities
     */
    private LimitCycleDTO calculateCheckQuota(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityVO vo, CalculateCumulativeDiscountQO cumulativeDiscountQO) {
        List<LimitCycleDTO> limitCycleDTOS = Lists.newArrayList();
        List<EquitiesLimitedDTO> equitiesLimitedDTOS = JSONArray.parseArray(hsaBusinessEquities.getPeriodDiscountType(), EquitiesLimitedDTO.class);
        if (hsaBusinessEquities.getPeriodDiscountLimited() == EquitiesLimitedTypeEnum.LIMITED.getCode() && CollectionUtil.isNotEmpty(equitiesLimitedDTOS)) {
            dealEquitiesLimited(qo, hsaBusinessEquities, vo, cumulativeDiscountQO, limitCycleDTOS, equitiesLimitedDTOS);
        }
        //累计优惠限额
        if (hsaBusinessEquities.getTotalDiscountLimited() == EquitiesLimitedTypeEnum.LIMITED.getCode()) {
            dealTotalDiscountLimited(qo, hsaBusinessEquities, vo, cumulativeDiscountQO, limitCycleDTOS);
        }
        limitCycleDTOS = limitCycleDTOS.stream().filter(in -> in.getValue().compareTo(BigDecimal.ZERO) >= 0).sorted(Comparator.comparing(LimitCycleDTO::getValue)).collect(Collectors.toList());
        return checkLimitCycleDTO(limitCycleDTOS);
    }

    private static LimitCycleDTO checkLimitCycleDTO(List<LimitCycleDTO> limitCycleDTOS) {
        if (CollectionUtil.isNotEmpty(limitCycleDTOS)) {
            return limitCycleDTOS.get(0);
        } else {
            return null;
        }
    }

    private void dealTotalDiscountLimited(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityVO vo, CalculateCumulativeDiscountQO cumulativeDiscountQO, List<LimitCycleDTO> limitCycleDTOS) {
        BigDecimal discountDynamicsAmount;
        if (Objects.isNull(vo.getDiscountDynamicsAmount())) {
            discountDynamicsAmount = BigDecimal.ZERO;
        } else {
            discountDynamicsAmount = vo.getDiscountDynamicsAmount();
        }

        //获取累计优惠限额
        BigDecimal consumptionLimit = getConsumptionDateLimit(qo, hsaBusinessEquities, null, null);

        if (Objects.nonNull(cumulativeDiscountQO) && Objects.isNull(qo.getHeadData()) && Objects.nonNull(cumulativeDiscountQO.getTotalDiscount())) {
            BigDecimal suDecimal = hsaBusinessEquities.getTotalDiscountLimitedAmount().subtract(consumptionLimit);
            consumptionLimit = cumulativeDiscountQO.getTotalDiscount().add(discountDynamicsAmount).subtract(suDecimal).abs().add(consumptionLimit);
        }

        BigDecimal limitedAmount = hsaBusinessEquities.getTotalDiscountLimitedAmount().subtract(consumptionLimit);
        if (StringUtil.isNotEmpty(qo.getOrderNum())) {
            limitedAmount = getAdd(limitedAmount, discountDynamicsAmount);
            if (limitedAmount.compareTo(hsaBusinessEquities.getTotalDiscountLimitedAmount()) >= 0) {
                limitedAmount = hsaBusinessEquities.getTotalDiscountLimitedAmount();
            }
        }
        LimitCycleDTO limitCycleDTO = new LimitCycleDTO();
        limitCycleDTO.setType(DataUnitEnum.TOTAL.getCode());
        limitCycleDTO.setValue(limitedAmount.setScale(2, BigDecimal.ROUND_DOWN));
        limitCycleDTOS.add(limitCycleDTO);
    }

    private void dealEquitiesLimited(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityVO vo, CalculateCumulativeDiscountQO cumulativeDiscountQO, List<LimitCycleDTO> limitCycleDTOS, List<EquitiesLimitedDTO> equitiesLimitedDTOS) {
        CheckEquitiesLimitedQO checkEquitiesLimitedQO = new CheckEquitiesLimitedQO();
        checkEquitiesLimitedQO.setQo(qo);
        checkEquitiesLimitedQO.setVo(vo);
        checkEquitiesLimitedQO.setCumulativeDiscountQO(cumulativeDiscountQO);
        checkEquitiesLimitedQO.setLimitCycleDTOS(limitCycleDTOS);
        checkEquitiesLimitedQO.setEquitiesLimitedDTOS(equitiesLimitedDTOS);

        for (EquitiesLimitedDTO equitiesLimitedDTO : equitiesLimitedDTOS) {
            checkEquitiesLimitedQO.setEquitiesLimitedDTO(equitiesLimitedDTO);
            if (Integer.parseInt(equitiesLimitedDTO.getType()) == DataUnitEnum.DAY.getCode()) {
                checkEquitiesLimitedQO.setStartTime(DateUtil.getDayStartTime());
                checkEquitiesLimitedQO.setEndTime(DateUtil.getDayEndTime());
                checkEquitiesLimitedQO.setDayDiscount(cumulativeDiscountQO.getDayDiscount());
                checkLimited(checkEquitiesLimitedQO, hsaBusinessEquities, DataUnitEnum.DAY);
            } else if (Integer.parseInt(equitiesLimitedDTO.getType()) == DataUnitEnum.WEEK.getCode()) {
                //获取当周已消耗优惠额度
                checkEquitiesLimitedQO.setStartTime(DateUtil.getWeekStartTime());
                checkEquitiesLimitedQO.setEndTime(DateUtil.getWeekEndTime());
                checkEquitiesLimitedQO.setDayDiscount(cumulativeDiscountQO.getWeekDiscount());
                checkLimited(checkEquitiesLimitedQO, hsaBusinessEquities, DataUnitEnum.WEEK);
            } else if (Integer.parseInt(equitiesLimitedDTO.getType()) == DataUnitEnum.MONTH.getCode()) {
                //获取当月已消耗优惠额度
                checkEquitiesLimitedQO.setStartTime(DateUtil.getMonthStartTime());
                checkEquitiesLimitedQO.setEndTime(DateUtil.getMonthEndTime());
                checkEquitiesLimitedQO.setDayDiscount(cumulativeDiscountQO.getMonthDiscount());
                checkLimited(checkEquitiesLimitedQO, hsaBusinessEquities, DataUnitEnum.MONTH);
            } else {
                //获取当年已消耗优惠额度
                checkEquitiesLimitedQO.setStartTime(DateUtil.getYearStartTime());
                checkEquitiesLimitedQO.setEndTime(DateUtil.getYearEndTime());
                checkEquitiesLimitedQO.setDayDiscount(cumulativeDiscountQO.getYearDiscount());
                checkLimited(checkEquitiesLimitedQO, hsaBusinessEquities, DataUnitEnum.YEAR);
            }
        }
    }

    private void checkLimited(CheckEquitiesLimitedQO checkEquitiesLimitedQO, HsaBusinessEquities hsaBusinessEquities, DataUnitEnum day) {
        CalculateMemberPriceCommodityVO vo = checkEquitiesLimitedQO.getVo();
        CalculateMemberPriceCommodityQO qo = checkEquitiesLimitedQO.getQo();
        LocalDateTime startTime = checkEquitiesLimitedQO.getStartTime();
        LocalDateTime endTime = checkEquitiesLimitedQO.getEndTime();
        EquitiesLimitedDTO equitiesLimitedDTO = checkEquitiesLimitedQO.getEquitiesLimitedDTO();
        List<LimitCycleDTO> limitCycleDTOS = checkEquitiesLimitedQO.getLimitCycleDTOS();
        BigDecimal discount = checkEquitiesLimitedQO.getDayDiscount();
        CalculateCumulativeDiscountQO cumulativeDiscountQO = checkEquitiesLimitedQO.getCumulativeDiscountQO();
        //周期消耗优惠额度
        BigDecimal consumptionLimit;
        consumptionLimit = getConsumptionDateLimit(qo, hsaBusinessEquities, startTime, endTime);

        if (Objects.nonNull(cumulativeDiscountQO) && Objects.isNull(qo.getHeadData())) {
            consumptionLimit = getConsumptionLimit(vo.getDiscountDynamicsAmount(), discount, equitiesLimitedDTO, consumptionLimit);
        }
        //日限制剩余金额
        addLimitCycle(limitCycleDTOS, day.getCode(), equitiesLimitedDTO, consumptionLimit, vo.getDiscountDynamicsAmount(), qo);
    }

    private static BigDecimal getConsumptionLimit(BigDecimal discountDynamicsAmount, BigDecimal dayDiscount, EquitiesLimitedDTO equitiesLimitedDTO, BigDecimal consumptionLimit) {
        if (Objects.nonNull(dayDiscount)) {
            BigDecimal suDecimal = new BigDecimal(equitiesLimitedDTO.getValue()).subtract(consumptionLimit);

            consumptionLimit = dayDiscount.add(Objects.nonNull(discountDynamicsAmount) ? discountDynamicsAmount : BigDecimal.ZERO).subtract(suDecimal).abs().add(consumptionLimit);
        }
        return consumptionLimit;
    }

    private void addLimitCycle(List<LimitCycleDTO> limitCycleDTOS, Integer code, EquitiesLimitedDTO equitiesLimitedDTO, BigDecimal consumptionLimit, BigDecimal discountDynamicsAmount, CalculateMemberPriceCommodityQO qo) {
        if (Objects.isNull(discountDynamicsAmount)) {
            discountDynamicsAmount = BigDecimal.ZERO;
        }
        BigDecimal value = new BigDecimal(equitiesLimitedDTO.getValue());
        LimitCycleDTO limitCycleDTO = new LimitCycleDTO();
        limitCycleDTO.setType(code);
        BigDecimal usedLimit = value.subtract(consumptionLimit);
        if (StringUtil.isNotEmpty(qo.getOrderNum())) {
            usedLimit = getAdd(usedLimit, discountDynamicsAmount);
            if (usedLimit.compareTo(value) >= 0) {
                usedLimit = value;
            }
        }
        limitCycleDTO.setValue(usedLimit.setScale(2, BigDecimal.ROUND_DOWN));
        limitCycleDTOS.add(limitCycleDTO);
    }

    /**
     * 计算周期限额
     *
     * @param qo                  CalculateMemberPriceCommodityQO
     * @param hsaBusinessEquities hsaBusinessEquities
     * @param discountPrice       discountPrice
     */
    private BigDecimal calculateCheckQuota(int unit,
                                           CalculateMemberPriceCommodityQO qo,
                                           HsaBusinessEquities hsaBusinessEquities,
                                           BigDecimal discountPrice,
                                           EquitiesLimitedDTO equitiesLimitedDTO,
                                           CalculateCumulativeDiscountQO cumulativeDiscountQO) {
        CalculateMemberPriceUnitQO calculateCum = getCalculateMemberPriceUnitQO(unit);

        LocalDateTime startTime = calculateCum.getStartTime();
        LocalDateTime endTime = calculateCum.getEndTime();
        if (discountPrice.compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
            return discountPrice;
        }
        if (Objects.nonNull(equitiesLimitedDTO)) {
            //获取当日已消耗优惠额度
            BigDecimal consumptionLimit = getConsumptionLimit(qo, hsaBusinessEquities, startTime, endTime);
            //日限制剩余金额
            BigDecimal limitedAmount = new BigDecimal(equitiesLimitedDTO.getValue()).subtract(consumptionLimit);
            //当前使用周期
            Optional.ofNullable(cumulativeDiscountQO).ifPresent(cq -> {
                cq.setCurrentPeriod(unit);
            });
            if (limitedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return BigDecimal.ZERO;
            }
            //处理批量折扣
            if (Objects.nonNull(cumulativeDiscountQO)) {
                discountPrice = getDiscountPrice(qo, discountPrice, cumulativeDiscountQO, limitedAmount, unit);
            } else {
                if (discountPrice.compareTo(limitedAmount) > 0) {
                    discountPrice = limitedAmount;
                }
            }
        }
        return discountPrice;
    }

    private static CalculateMemberPriceUnitQO getCalculateMemberPriceUnitQO(int unit) {

        CalculateMemberPriceUnitQO calculateCum = new CalculateMemberPriceUnitQO();

        if (unit == DataUnitEnum.DAY.getCode()) {

            calculateCum.setStartTime(DateUtil.getDayStartTime());
            calculateCum.setEndTime(DateUtil.getDayEndTime());

        } else if (unit == DataUnitEnum.WEEK.getCode()) {

            calculateCum.setStartTime(DateUtil.getWeekStartTime());
            calculateCum.setEndTime(DateUtil.getWeekEndTime());

        } else if (unit == DataUnitEnum.MONTH.getCode()) {

            calculateCum.setStartTime(DateUtil.getMonthStartTime());
            calculateCum.setEndTime(DateUtil.getMonthEndTime());

        } else {

            calculateCum.setStartTime(DateUtil.getYearStartTime());
            calculateCum.setEndTime(DateUtil.getYearEndTime());

        }
        return calculateCum;
    }

    private static BigDecimal getDiscountPrice(CalculateMemberPriceCommodityQO qo, BigDecimal discountPrice, CalculateCumulativeDiscountQO cumulativeDiscountQO, BigDecimal consumptionLimit, int unit) {

        BigDecimal discount = getDiscount(cumulativeDiscountQO, unit);

        if (Objects.isNull(discount)) {
            if (discountPrice.compareTo(consumptionLimit) >= 0) {
                discountPrice = consumptionLimit;
                discount = BigDecimal.ZERO;
            } else {
                discount = consumptionLimit.subtract(discountPrice);
            }
        } else {
            if (discountPrice.compareTo(discount) >= 0) {
                discountPrice = discount;
                discount = BigDecimal.ZERO;
            } else {
                discount = discount.subtract(discountPrice);
            }
        }

        setTotalDiscount(qo, cumulativeDiscountQO, unit, discount);
        return discountPrice;
    }

    private static void setTotalDiscount(CalculateMemberPriceCommodityQO qo, CalculateCumulativeDiscountQO cumulativeDiscountQO, int unit, BigDecimal discount) {
        if (qo.getDiscountType() != DiscountTypeEnum.DISCOUNT_NOT.getCode()) {
            if (unit == DataUnitEnum.DAY.getCode()) {
                cumulativeDiscountQO.setDayDiscount(discount);
            } else if (unit == DataUnitEnum.WEEK.getCode()) {
                cumulativeDiscountQO.setWeekDiscount(discount);
            } else if (unit == DataUnitEnum.MONTH.getCode()) {
                cumulativeDiscountQO.setMonthDiscount(discount);
            } else if (unit == DataUnitEnum.YEAR.getCode()) {
                cumulativeDiscountQO.setYearDiscount(discount);
            } else {
                cumulativeDiscountQO.setTotalDiscount(discount);
            }

        }
    }

    private static BigDecimal getDiscount(CalculateCumulativeDiscountQO cumulativeDiscountQO, int unit) {
        BigDecimal discount;
        if (unit == DataUnitEnum.DAY.getCode()) {
            discount = cumulativeDiscountQO.getDayDiscount();
        } else if (unit == DataUnitEnum.WEEK.getCode()) {
            discount = cumulativeDiscountQO.getWeekDiscount();
        } else if (unit == DataUnitEnum.MONTH.getCode()) {
            discount = cumulativeDiscountQO.getMonthDiscount();
        } else if (unit == DataUnitEnum.YEAR.getCode()) {
            discount = cumulativeDiscountQO.getYearDiscount();
        } else {
            discount = cumulativeDiscountQO.getTotalDiscount();
        }
        return discount;
    }


    /**
     * 计算剩余次数
     *
     * @param qo                  CalculateMemberPriceCommodityQO
     * @param hsaBusinessEquities hsaBusinessEquities
     */
    private int calculateCheckFrequency(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, EquitiesLimitedDTO equitiesLimitedDTO, LocalDateTime startTime, LocalDateTime endTime) {
        //获取周期已消耗次数
        int consumptionLimit = getConsumptionGoodsLimit(qo, hsaBusinessEquities, startTime, endTime);
        //限制次数
        int limitedAmount = Integer.parseInt(equitiesLimitedDTO.getValue());
        return limitedAmount - consumptionLimit;
    }

    /**
     * 计算周期内已使用优惠额度
     *
     * @param qo                  CalculateMemberPriceCommodityQO
     * @param hsaBusinessEquities hsaBusinessEquities
     * @param startTime           startTime
     * @param endTime             endTime
     * @return BigDecimal
     */
    private BigDecimal getConsumptionLimit(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, LocalDateTime startTime, LocalDateTime endTime) {
        BigDecimal dayConsumptionLimit = BigDecimal.ZERO;
        List<HsaMemberGradePriceDetail> hsaMemberGradePriceDetails = getHsaMemberGradePriceDetails(qo, hsaBusinessEquities, startTime, endTime);
        if (CollectionUtil.isNotEmpty(hsaMemberGradePriceDetails)) {
            for (HsaMemberGradePriceDetail hsaMemberGradePriceDetail : hsaMemberGradePriceDetails) {
                dayConsumptionLimit = getAdd(hsaMemberGradePriceDetail.getDiscountedPrice(), dayConsumptionLimit);
            }
        }
        return dayConsumptionLimit;
    }

    private int getConsumptionGoodsLimit(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, LocalDateTime startTime, LocalDateTime endTime) {
        List<HsaMemberGradePriceDetail> hsaMemberGradePriceDetails = getHsaMemberGradePriceDetails(qo, hsaBusinessEquities, startTime, endTime);
        if (CollectionUtil.isNotEmpty(hsaMemberGradePriceDetails)) {
            return hsaMemberGradePriceDetails.size();
        }
        return NumberConstant.NUMBER_0;
    }

    private List<HsaMemberGradePriceDetail> getHsaMemberGradePriceDetails(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, LocalDateTime startTime, LocalDateTime endTime) {
        return hsaMemberGradePriceDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                .eq(HsaMemberGradePriceDetail::getMemberInfoGuid, qo.getMemberInfoGuid())
                .eq(HsaMemberGradePriceDetail::getEquitiesGuid, hsaBusinessEquities.getGuid())
                .eq(HsaMemberGradePriceDetail::getDiscountType, hsaBusinessEquities.getEquitiesRuleType())
                .notIn(StringUtil.isNotEmpty(qo.getOrderNum()), HsaMemberGradePriceDetail::getOrderNumber, qo.getOrderNum())
                .gt(Objects.nonNull(startTime), HsaMemberGradePriceDetail::getGmtCreate, startTime)
                .lt(Objects.nonNull(endTime), HsaMemberGradePriceDetail::getGmtCreate, endTime));
    }

    private BigDecimal getConsumptionDateLimit(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities, LocalDateTime startTime, LocalDateTime endTime) {
        BigDecimal dayConsumptionLimit = BigDecimal.ZERO;
        List<HsaMemberGradePriceDetail> hsaMemberGradePriceDetails = hsaMemberGradePriceDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberGradePriceDetail>()
                .eq(HsaMemberGradePriceDetail::getMemberInfoGuid, qo.getMemberInfoGuid())
                .eq(HsaMemberGradePriceDetail::getEquitiesGuid, hsaBusinessEquities.getGuid())
                .gt(Objects.nonNull(startTime), HsaMemberGradePriceDetail::getGmtCreate, startTime)
                .lt(Objects.nonNull(endTime), HsaMemberGradePriceDetail::getGmtCreate, endTime));
        if (CollectionUtil.isNotEmpty(hsaMemberGradePriceDetails)) {
            for (HsaMemberGradePriceDetail hsaMemberGradePriceDetail : hsaMemberGradePriceDetails) {
                dayConsumptionLimit = getAdd(hsaMemberGradePriceDetail.getDiscountedPrice(), dayConsumptionLimit);
            }
        }
        return dayConsumptionLimit;
    }

    /**
     * 获取适用于会员等级折扣的商品基础信息
     *
     * @param hsaBusinessEquities 会员等级权益
     * @param qo 会员价格计算请求参数
     * @return 商品ID到商品基础信息的映射
     */
    private Map<String, GradeCommodityBaseQO> getGradeCommodityBase(HsaBusinessEquities hsaBusinessEquities, CalculateMemberPriceCommodityQO qo) {
        // 如果没有商品列表，直接返回空映射
        if (CollUtil.isEmpty(qo.getMemberPriceCommodityQOS())) {
            return new HashMap<>();
        }

        // 查询权益对应的商品规则
        List<HsaGradeRightsCommodityRule> commodityRules = hsaGradeRightsCommodityRuleMapper
                .selectList(new LambdaQueryWrapper<HsaGradeRightsCommodityRule>()
                        .eq(HsaGradeRightsCommodityRule::getGradeEquitiesGuid, hsaBusinessEquities.getGuid())
                        // 按业务类型查询：商城0 pos 1
                        .eq(HsaGradeRightsCommodityRule::getBusinessType, ThreadLocalCache.getSystem())
                        // 老数据，默认为0
                        .in(StringConstant.ZERO.equals(hsaBusinessEquities.getVersionId()),
                                HsaGradeRightsCommodityRule::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2)
                        // 指定版本查询
                        .eq(StringUtil.isNotBlank(hsaBusinessEquities.getVersionId()),
                                HsaGradeRightsCommodityRule::getGradeEquitiesVersionId, hsaBusinessEquities.getVersionId())
                        // 会员卡权益，默认生效
                        .eq(HsaGradeRightsCommodityRule::getEffective, NumberConstant.NUMBER_1));

        // 根据适用商品类型获取商品列表
        List<GradeCommodityBaseQO> eligibleCommodities = getEligibleCommodities(
                hsaBusinessEquities, qo, commodityRules);

        // 如果没有符合条件的商品，返回空映射
        if (CollUtil.isEmpty(eligibleCommodities)) {
            return new HashMap<>();
        }

        // 添加门店信息并过滤不适用的门店
        eligibleCommodities = addStoreInfoAndFilter(eligibleCommodities, qo);

        log.info("通过门店校验的权益商品信息：{}", eligibleCommodities);

        // 转换为Map结构，便于快速查找
        return eligibleCommodities.stream()
                .collect(Collectors.toMap(
                        GradeCommodityBaseQO::getCommodityId,
                        Function.identity(),
                        (entity1, entity2) -> entity1));
    }

    /**
     * 根据适用商品类型获取符合条件的商品列表
     *
     * @param hsaBusinessEquities 会员等级权益
     * @param qo 会员价格计算请求参数
     * @param commodityRules 商品规则列表
     * @return 符合条件的商品列表
     */
    private List<GradeCommodityBaseQO> getEligibleCommodities(
            HsaBusinessEquities hsaBusinessEquities,
            CalculateMemberPriceCommodityQO qo,
            List<HsaGradeRightsCommodityRule> commodityRules) {

        // 适用所有商品
        if (hsaBusinessEquities.getApplyGoodsType() == ApplyCommodityTypeEnum.ALL_APPLY_COMMODITY.getCode()) {
            return qo.getMemberPriceCommodityQOS().stream()
                    .map(item -> new GradeCommodityBaseQO()
                            .setCommodityCode(item.getCommodityCode())
                            .setCommodityId(item.getCommodityId())
                            .setCommodityName(item.getCommodityName()))
                    .collect(Collectors.toList());
        }
        // 适用指定商品
        else if (hsaBusinessEquities.getApplyGoodsType() == ApplyCommodityTypeEnum.APPLY_COMMODITY.getCode()) {
            return commodityRules.stream()
                    .map(rule -> getCommodityBase(
                            rule.getCommodityId(),
                            rule.getCommodityCode(),
                            rule.getCommodityName()))
                    .collect(Collectors.toList());
        }
        // 不适用指定商品
        else {
            // 将规则列表转换为Map，便于快速查找
            Map<String, HsaGradeRightsCommodityRule> ruleMap = commodityRules.stream()
                    .collect(Collectors.toMap(
                            HsaGradeRightsCommodityRule::getCommodityId,
                            Function.identity(),
                            (entity1, entity2) -> entity1));

            // 筛选不在排除列表中的商品
            return qo.getMemberPriceCommodityQOS().stream()
                    .filter(item -> !ruleMap.containsKey(item.getCommodityId()))
                    .map(item -> getCommodityBase(
                            item.getCommodityId(),
                            item.getCommodityCode(),
                            item.getCommodityName()))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 添加门店信息并过滤不适用的门店
     *
     * @param commodities 商品列表
     * @param qo 会员价格计算请求参数
     * @return 添加门店信息并过滤后的商品列表
     */
    private List<GradeCommodityBaseQO> addStoreInfoAndFilter(
            List<GradeCommodityBaseQO> commodities,
            CalculateMemberPriceCommodityQO qo) {

        // 创建商品ID到商品的映射，便于快速查找
        Map<String, MemberPriceCommodityQO> commodityMap = qo.getMemberPriceCommodityQOS().stream()
                .collect(Collectors.toMap(
                        MemberPriceCommodityQO::getCommodityId,
                        Function.identity(),
                        (obj1, obj2) -> obj1));

        // 为每个商品添加门店信息
        commodities.forEach(commodity -> {
            MemberPriceCommodityQO memberPriceCommodityQO = commodityMap.get(commodity.getCommodityId());
            if (Objects.nonNull(memberPriceCommodityQO)) {
                commodity.setStoreGuid(memberPriceCommodityQO.getStoreGuid());
            }
        });

        // 过滤出适用门店的商品
        return commodities.stream()
                .filter(commodity -> qo.getStoreGuidList().contains(commodity.getStoreGuid()))
                .collect(Collectors.toList());
    }

    /**
     * 校验整合
     *
     * @param qo qo
     * @return boolean
     */
    private HsaBusinessEquities checkHsaGradeEquities(CalculateMemberPriceCommodityQO qo, HsaBusinessEquities hsaBusinessEquities) {
        if (Objects.isNull(hsaBusinessEquities)) {
            log.info("权益不存在：{}", qo);
            return null;
        }
        if (isCheckApplyBusiness(hsaBusinessEquities, qo.getBusiness())) {
            log.info("业务不适用：{}", qo.getBusiness());
            return null;
        }
        if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.MEMBER_PRICE.getCode() && isCheckApplyChannel(hsaBusinessEquities, qo.getChannel())) {
            log.info("渠道不适用：{}", qo.getChannel());
            return null;
        }
        if (isCheckApplyTerminal(hsaBusinessEquities, qo.getTerminal())) {
            log.info("终端不适用：{}", qo.getTerminal());
            return null;
        }
        if (isCheckTime(hsaBusinessEquities)) {
            log.info("时段不适用：{}", hsaBusinessEquities);
            return null;
        }
        //周期优惠限制
        if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GOODS_MEMBER.getCode()
                && hsaBusinessEquities.getPeriodDiscountLimited() == EquitiesLimitedTypeEnum.LIMITED.getCode()
                && getCheckGoodsLimitCycle(qo, hsaBusinessEquities) >= Integer.parseInt(JSON.parseArray(hsaBusinessEquities.getPeriodDiscountType(),
                EquitiesLimitedDTO.class).get(0).getValue())) {
            log.info("已到达限制次数：{}", hsaBusinessEquities);
            return null;
        }
        return hsaBusinessEquities;
    }

    /**
     * 校验整合
     *
     * @param qo                  qo
     * @param hsaBusinessEquities hsaBusinessEquities
     * @return boolean
     */
    private boolean checkAll(MemberPriceApplyCommodityQO qo, HsaBusinessEquities hsaBusinessEquities) {
        if (Objects.isNull(hsaBusinessEquities)) {
            log.info("会员价权益不存在：{}", qo);
            return true;
        }
        if (Objects.nonNull(qo.getBusiness()) && isCheckApplyBusiness(hsaBusinessEquities, qo.getBusiness())) {
            log.info("业务不适用：{}", qo.getBusiness());
            return true;
        }
        if (hsaBusinessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.MEMBER_PRICE.getCode() && isCheckApplyChannel(hsaBusinessEquities, qo.getChannel())) {
            log.info("渠道不适用：{}", qo.getChannel());
            return true;
        }
        if (isCheckApplyTerminal(hsaBusinessEquities, qo.getTerminal())) {
            log.info("终端不适用：{}", qo.getTerminal());
            return true;
        }
        if (isCheckTime(hsaBusinessEquities)) {
            log.info("时段不适用：{}", hsaBusinessEquities);
            return true;
        }
        return false;
    }

    /**
     * 创建商品基础信息对象
     *
     * @param productTemplateId 商品模板ID
     * @param commodityCode 商品编码
     * @param commodityName 商品名称
     * @return 商品基础信息对象
     */
    private GradeCommodityBaseQO getCommodityBase(String productTemplateId, String commodityCode, String commodityName) {
        return new GradeCommodityBaseQO()
                .setCommodityId(productTemplateId)
                .setCommodityCode(commodityCode)
                .setCommodityName(commodityName);
    }
}
