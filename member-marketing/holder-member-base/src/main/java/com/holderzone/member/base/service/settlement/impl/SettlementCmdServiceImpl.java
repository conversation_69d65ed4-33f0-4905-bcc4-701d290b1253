package com.holderzone.member.base.service.settlement.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.dto.SettlementBackDiscountDTO;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.base.service.member.HsaMemberConsumptionService;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.base.service.settlement.SettlementCmdService;
import com.holderzone.member.base.service.settlement.assembler.SettlementApplyLockedAssembler;
import com.holderzone.member.base.service.settlement.assembler.SettlementLimitSpecialsAssembler;
import com.holderzone.member.base.service.settlement.chain.SettleHandler;
import com.holderzone.member.base.service.settlement.chain.SettleHandlerConfig;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.activity.ApplyRecordCommodity;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.exception.MemberSettlementException;
import com.holderzone.member.common.module.settlement.apply.dto.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.card.BarkOrderDiscountCallbackQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsCommodityRecordQO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算规则处理
 *
 * <AUTHOR>
 * @date 2023/11/17
 * @since 1.8
 */
@Slf4j
@Service
public class SettlementCmdServiceImpl implements SettlementCmdService {

    /**
     * 优惠计算类型配置
     */
    @Resource
    SettleHandlerConfig settleHandlerConfig;

    @Resource
    private HsaMemberConsumptionService hsaMemberConsumptionService;

    /**
     * 事务处理
     */
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 锁定优惠
     */
    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;

    /**
     * redis
     */
    @Resource
    private RedissonClient redissonClient;

    @Override
    public boolean lockedDiscount(SettlementOrderLockDTO dto) {
        log.info("优惠锁定入参：{}", JSON.toJSONString(dto));
        //校验计算参数
        dto.validateCheck();
        //填充主体
        final SettlementLockedOrderInfoDTO orderInfo = dto.getOrderInfo();
        //填充登录用户信息
        fillLoginUserInfo(orderInfo);
        //优惠锁定数据操作
        final String lockKey = RedisKeyConstant.SETTLEMENT_LOCKED + orderInfo.getOperSubjectGuid()
                + RedisKeyConstant.COLON
                + orderInfo.getOrderNumber();
        //锁定数据操作
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.isLocked()) {
                throw new MemberSettlementException("订单优惠锁定中，请勿重复操作");
            }
            lock.lock();

            //锁定优惠
            return Boolean.TRUE.equals(transactionTemplate.execute((status) -> {
                //锁定优惠操作
                lockDiscountData(dto);
                return true;
            }));
        } catch (Exception e) {
            log.error("优惠锁定异常：", e);
            throw new MemberSettlementException("优惠锁定异常");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 填充登录用户信息
     *
     * @param orderInfo 订单
     */
    private void fillLoginUserInfo(SettlementLockedOrderInfoDTO orderInfo) {
        final HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        orderInfo.setOperSubjectGuid(userInfo.getOperSubjectGuid());
        orderInfo.setSource(userInfo.getSource());
        //操作人 todo bug: 为空则查库
        orderInfo.setOperatorAccountName(userInfo.getOperatorAccountName());
    }

    /**
     * 锁定优惠数据操作
     *
     * @param dto 优惠数据
     */
    private void lockDiscountData(SettlementOrderLockDTO dto) {
        //释放已有优惠
        unLockOldDiscount(dto);
        if (CollUtil.isEmpty(dto.getCheckDiscountList())) {
            log.error("无新增优惠");
            return;
        }
        //【公共】保存优惠明细
        hsaMemberOrderDiscountService.saveBatchByLock(dto);

        //优惠项单独的操作
        final List<Integer> sortOptions = dto.getCheckDiscountList().stream()
                .map(SettlementLockedDiscountReqDTO::getDiscountOption).distinct()
                .collect(Collectors.toList());
        log.info("订单：{}，锁定优惠：{},", dto.getOrderInfo().getOrderNumber(), JacksonUtils.writeValueAsString(sortOptions));
        //选中的规则、按规则排序
        final List<SettleHandler> settleHandlers = settleHandlerConfig.sorted(sortOptions);
        final Map<Integer, List<SettlementLockedDiscountReqDTO>> groupOptionMap = dto.getCheckDiscountList()
                .stream().collect(Collectors.groupingBy(SettlementLockedDiscountReqDTO::getDiscountOption));
        //锁定优惠 更新优惠、释放 -> 锁定
        for (SettleHandler handler : settleHandlers) {
            final List<SettlementLockedDiscountReqDTO> checkList = groupOptionMap.get(handler.option());
            if (CollUtil.isEmpty(checkList)) {
                continue;
            }
            //当前类型
            dto.setCheckDiscountList(checkList);
            //如果异常，释放优惠
            handler.lockDiscount(dto);
        }
    }

    /**
     * 释放已有优惠
     *
     * @param dto 订单入参
     */
    private void unLockOldDiscount(SettlementOrderLockDTO dto) {
        final SettlementLockedOrderInfoDTO orderInfo = dto.getOrderInfo();
        //优惠列表 
        final List<HsaMemberOrderDiscount> discountList = hsaMemberOrderDiscountService.list(orderInfo.getOperSubjectGuid(), orderInfo.getOrderNumber());
        //优惠更新
        if (CollUtil.isEmpty(discountList)) {
            log.info("订单信息{}：无优惠", JacksonUtils.writeValueAsString(orderInfo));
            return;
        }
        //todo 会员更新,先锁定A，再释放B，防止A不够

        //目前只是删除（卡折扣）
        final List<SettlementLockedDiscountReqDTO> checkDiscountList = Optional.ofNullable(dto.getCheckDiscountList())
                .orElse(Collections.emptyList());
        //todo（暂不支持更新【更新需要先释放一部分、再锁定一部分】）
        List<HsaMemberOrderDiscount> existsDiscount = new ArrayList<>();
        for (HsaMemberOrderDiscount orderDiscount : discountList) {
            //已存在的（剩下的新增）
            final boolean exists = checkDiscountList.removeIf(c ->
                    c.getDiscountOption().equals(orderDiscount.getDiscountOption())
                            && c.getDiscountGuid().equals(orderDiscount.getDiscountGuid())
            );
            //存在
            if (exists) {
                existsDiscount.add(orderDiscount);
            }
        }
        //剩下的删除
        discountList.removeAll(existsDiscount);
        if (CollUtil.isEmpty(discountList)) {
            return;
        }
        SettlementUnLockedDiscountDTO unLockedDiscountDTO = SettlementApplyLockedAssembler.toSettlementUnLockedDiscountDTO(orderInfo);

        //删除会员优惠
        removeMemberOrderDiscount(discountList);
        //删除、释放(卡折扣)
        handlerUnLockedDiscount(unLockedDiscountDTO, discountList);
    }

    /**
     * 删除会员优惠： 更新过
     *
     * @param discountList 优惠记录
     */
    private void removeMemberOrderDiscount(List<HsaMemberOrderDiscount> discountList) {
        final List<String> guidList = discountList.stream().map(HsaMemberOrderDiscount::getGuid).collect(Collectors.toList());

        //释放，假删
        hsaMemberOrderDiscountService.removeByGuids(guidList);
    }

    @Override
    public boolean unLockedDiscount(SettlementUnLockedDiscountDTO discountDTO) {
        log.info("优惠释放入参：{}", JSON.toJSONString(discountDTO));
        discountDTO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //优惠释放数据操作
        final String unLockKey = RedisKeyConstant.SETTLEMENT_UNLOCKED + discountDTO.getOperSubjectGuid()
                + RedisKeyConstant.COLON
                + discountDTO.getOrderNo();
        //释放数据操作
        RLock lock = redissonClient.getLock(unLockKey);
        try {
            if (lock.isLocked()) {
                throw new MemberSettlementException("订单优惠释放中，请勿重复操作");
            }
            lock.lock();

            //优惠释放数据操作
            return unLockDiscountHandler(discountDTO);
        } catch (Exception e) {
            log.error("优惠释放异常：{}", e.getMessage());
            return false;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 优惠释放数据操作
     *
     * @param discountDTO 优惠释放数据
     * @return 是否成功
     */
    private boolean unLockDiscountHandler(SettlementUnLockedDiscountDTO discountDTO) {
        //优惠列表  
        List<HsaMemberOrderDiscount> discountList = listMemberOrderDiscountList(discountDTO.getOperSubjectGuid(), discountDTO.getOrderNo());

        //无优惠
        if (CollUtil.isEmpty(discountList)) {
            //兼容老代码（业务对接完了，就不要了），释放折扣
            final List<Integer> oldOptions = SettlementDiscountOptionEnum.oldImpl();
            log.info("订单：{}，老接口 释放优惠折扣：{},", discountDTO.getOrderNo(), JacksonUtils.writeValueAsString(oldOptions));
            settleHandlerConfig.getSettleHandlers(oldOptions)
                    .forEach(settleHandler -> settleHandler.unlockDiscount(discountDTO));
            return false;
        }

        //去掉反结账
        discountList.removeIf(d -> d.getDiscountState().equals(BooleanEnum.TRUE.getCode()));
        if (!CollectionUtils.isEmpty(discountDTO.getUnLockedDiscountIdList())) {
            discountList = discountList.stream()
                    .filter(e -> discountDTO.getUnLockedDiscountIdList().contains(e.getDiscountId()))
                    .collect(Collectors.toList());
            discountDTO.setDiscountIdList(discountDTO.getUnLockedDiscountIdList());
        }
        if (CollUtil.isEmpty(discountList)) {
            log.warn("订单：{}，优惠已释放过，重复操作 ", discountDTO.getOrderNo());
            return true;
        }
        List<HsaMemberOrderDiscount> finalDiscountList = discountList;
        //释放优惠
        return Boolean.TRUE.equals(transactionTemplate.execute((status) -> {
            //优惠记录反结账
            updateMemberOrderDiscountState(finalDiscountList);

            //处理优惠释放
            handlerUnLockedDiscount(discountDTO, finalDiscountList);
            return true;
        }));
    }

    /**
     * 查询会员优惠
     *
     * @param operSubjectGuid 主体guid
     * @param orderNumber     订单号
     * @return 优惠记录
     */
    public List<HsaMemberOrderDiscount> listMemberOrderDiscountList(String operSubjectGuid, String orderNumber) {
        final LambdaQueryWrapper<HsaMemberOrderDiscount> wrapper = new LambdaQueryWrapper<HsaMemberOrderDiscount>()
                .eq(HsaMemberOrderDiscount::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberOrderDiscount::getOrderNumber, orderNumber);
        return hsaMemberOrderDiscountService.list(wrapper);
    }

    /**
     * 处理优惠释放
     *
     * @param discountDTO  释放对象
     * @param discountList 存在优惠
     */
    private void handlerUnLockedDiscount(SettlementUnLockedDiscountDTO discountDTO,
                                         List<HsaMemberOrderDiscount> discountList) {
        //释放优惠细节
        final List<Integer> options = discountList.stream()
                .map(HsaMemberOrderDiscount::getDiscountOption)
                .distinct()
                .collect(Collectors.toList());
        //选中的规则、按规则排序
        final List<SettleHandler> settleHandlers = settleHandlerConfig.getSettleHandlers(options);
        log.info("订单：{}，开始释放优惠，类型：{},", discountDTO.getOrderNo(), JacksonUtils.writeValueAsString(options));
        for (SettleHandler settleHandler : settleHandlers) {
            //解锁，释放  具体释放内容
            settleHandler.unlockDiscount(discountDTO);
        }
    }


    /**
     * 优惠反结账
     *
     * @param discountList 优惠记录
     */
    private void updateMemberOrderDiscountState(List<HsaMemberOrderDiscount> discountList) {

        //反结账
        discountList.forEach(d -> d.setDiscountState(BooleanEnum.TRUE.getCode()));

        hsaMemberOrderDiscountService.updateBatchById(discountList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterOrderDiscount(OrderPayDiscountDTO orderPayDiscountDTO) {
        //判断是否是私域商城
        if (ThreadLocalCache.getSystem() == SystemEnum.MALL.getCode() && orderPayDiscountDTO.getOrderNo().contains("-")) {
            //私域商城不处理优惠
            orderPayDiscountDTO.setOrderNo(orderPayDiscountDTO.getOrderNo().substring(0, orderPayDiscountDTO.getOrderNo().lastIndexOf("-")));
        }

        List<HsaMemberOrderDiscount> discountList = hsaMemberOrderDiscountService.list(orderPayDiscountDTO.getOperSubjectGuid(), orderPayDiscountDTO.getOrderNo());

        if (CollUtil.isEmpty(discountList)) {
            log.info("订单完成：{}，无优惠记录，不处理", orderPayDiscountDTO.getOrderNo());
            return;
        }

        Map<Integer, List<HsaMemberOrderDiscount>> orderDiscountByOptionMap = discountList.
                stream()
                .collect(Collectors.groupingBy(HsaMemberOrderDiscount::getDiscountOption));

        List<Integer> discountOptionList = discountList.stream()
                .map(HsaMemberOrderDiscount::getDiscountOption)
                .distinct()
                .collect(Collectors.toList());

        //选中的规则、按规则排序
        final List<SettleHandler> settleHandlers = settleHandlerConfig.getSettleHandlers(discountOptionList);
        log.info("订单：{}，开始处理优惠，类型：{},", orderPayDiscountDTO.getOrderNo(), JacksonUtils.writeValueAsString(discountOptionList));
        SettlementPayAfterDiscountDTO afterDiscount = new SettlementPayAfterDiscountDTO();
        BeanUtils.copyProperties(orderPayDiscountDTO, afterDiscount);
        afterDiscount.setOrderDiscountByOptionMap(orderDiscountByOptionMap);

        for (SettleHandler settleHandler : settleHandlers) {
            //支付后优惠处理
            settleHandler.afterPayDiscount(afterDiscount);
        }

        discountList.forEach(in -> {
            in.setIsPay(BooleanEnum.TRUE.getCode());
            hsaMemberOrderDiscountService.updateByGuid(in);
        });
    }


    @Override
    public void barkOrderDiscount(BarkOrderDiscountCallbackQO orderPayDiscountDTO) {
        List<HsaMemberOrderDiscount> discountList = hsaMemberOrderDiscountService
                .listByOrderNumber(orderPayDiscountDTO.getOperSubjectGuid(), orderPayDiscountDTO.getOrderNo());

        if (CollUtil.isEmpty(discountList)) {
            log.info("订单回退业务：{}，无优惠记录，不处理", orderPayDiscountDTO.getOrderNo());
            return;
        }

        Map<Integer, List<HsaMemberOrderDiscount>> orderDiscountByOptionMap = discountList.
                stream()
                .collect(Collectors.groupingBy(HsaMemberOrderDiscount::getDiscountOption));

        List<Integer> discountOptionList = discountList.stream()
                .map(HsaMemberOrderDiscount::getDiscountOption)
                .distinct()
                .collect(Collectors.toList());

        //选中的规则、按规则排序
        final List<SettleHandler> settleHandlers = settleHandlerConfig.getSettleHandlers(discountOptionList);
        log.info("订单：{}，开始处理优惠回退，类型：{},", orderPayDiscountDTO.getOrderNo(), JacksonUtils.writeValueAsString(discountOptionList));
        SettlementBackDiscountDTO discountDTO = new SettlementBackDiscountDTO();
        BeanUtils.copyProperties(orderPayDiscountDTO, discountDTO);
        discountDTO.setOrderDiscountByOptionMap(orderDiscountByOptionMap);
        for (SettleHandler settleHandler : settleHandlers) {
            //解锁，释放  具体释放内容
            settleHandler.backDiscount(discountDTO);
        }

        if (Objects.nonNull(orderPayDiscountDTO.getDiscountAmount())) {
            hsaMemberConsumptionService.synConsumptionDiscount(orderPayDiscountDTO.getOrderNo(), orderPayDiscountDTO.getDiscountAmount());
        }
    }

    @Override
    public Map<String, List<ApplyRecordCommodity>> getLimitSpecialsCommodityRecord(LimitSpecialsCommodityRecordQO limitSpecialsCommodityRecordQO) {
        List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = hsaMemberOrderDiscountService.getCommodityRecord(limitSpecialsCommodityRecordQO);

        Map<String, List<ApplyRecordCommodity>> listMap = new HashMap<>();
        if (CollUtil.isNotEmpty(hsaMemberOrderDiscounts)) {

            List<ApplyRecordCommodity> limitSpecialsRecordCommodities = new ArrayList<>();

            Map<String, List<HsaMemberOrderDiscount>> orderDiscountMap =
                    hsaMemberOrderDiscounts
                            .stream()
                            .collect(Collectors.groupingBy(HsaMemberOrderDiscount::getDiscountGuid));

            //分组清洗商品数据 累加数量
            for (Map.Entry<String, List<HsaMemberOrderDiscount>> entry : orderDiscountMap.entrySet()) {

                for (HsaMemberOrderDiscount hsaMemberOrderDiscount : entry.getValue()) {
                    limitSpecialsRecordCommodities.addAll(JSON.parseArray(hsaMemberOrderDiscount.getCommodityJson(), ApplyRecordCommodity.class));
                }
                Map<String, ApplyRecordCommodity> map = SettlementLimitSpecialsAssembler.getLimitSpecialsRecordCommodityMap(limitSpecialsRecordCommodities);
                listMap.put(entry.getKey(), new ArrayList<>(map.values()));
            }
        }
        return listMap;
    }


}
