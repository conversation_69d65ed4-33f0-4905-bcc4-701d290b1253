package com.holderzone.member.base.service.assembler;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.MemberPhoneDTO;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.dto.redeem.RequestRedeemApplyDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.qo.redeem.RespondEditActiveVO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
public class RedeemApplyAssembler {

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    private RedeemApplyAssembler() {

    }


    public List<HsaMemberCouponLink> formMemberCouponLink(RequestRedeemApplyDTO dto,
                                                          RespondEditActiveVO activityVO,
                                                          HsaOperationMemberInfo memberPhoneDTO,
                                                          Map<String, String> codeMap,
                                                          Set<String> hsaMemberLabelSet) {

        LocalDateTime now = LocalDateTime.now();
        List<HsaMemberCouponLink> hsaMemberCouponLinkList = Lists.newArrayList();
        for (ResponseCouponDTO responseCouponDTO : activityVO.getCouponGuidList()) {
            for (int i = 0; i < responseCouponDTO.getNum(); i++) {
                HsaMemberCouponLink hsaMemberCouponLink = new HsaMemberCouponLink();
                hsaMemberCouponLink.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberCouponLink.class.getSimpleName()));
                hsaMemberCouponLink.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
                hsaMemberCouponLink.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
                hsaMemberCouponLink.setCouponName(responseCouponDTO.getCouponName())
                        .setSource(ThreadLocalCache.getSource())
                        .setReachTime(now)
                        .setCouponPackageType(CouponPackageTypeEnum.COUPON_REDEEM_ACTIVITY.getCode())
                        .setCouponPackageCode(activityVO.getActiveCode())
                        .setCouponPackageName(activityVO.getActiveName())
                        .setCode(createCouponCode(codeMap, hsaMemberCouponLink.getGuid()))
                        .setCouponCode(responseCouponDTO.getCouponCode())
                        .setCouponType(responseCouponDTO.getCouponType())
                        .setThresholdType(responseCouponDTO.getThresholdType())
                        .setThresholdAmount(responseCouponDTO.getThresholdAmount())
                        .setDiscountAmount(responseCouponDTO.getDiscountAmount())
                        .setRemark(responseCouponDTO.getRemark())
                        .setApplyDateLimited(responseCouponDTO.getApplyDateLimited())
                        .setApplyTimeLimitedType(responseCouponDTO.getApplyTimeLimitedType())
                        .setApplyTimeLimitedJson(responseCouponDTO.getApplyTimeLimitedJson())
                        .setApplyBusiness(responseCouponDTO.getApplyBusiness())
                        .setApplyTerminal(responseCouponDTO.getApplyTerminal())
                        .setApplyTerminalJson(JSON.toJSONString(responseCouponDTO.getApplyTerminalList()))
                        .setApplicableAllStore(responseCouponDTO.getApplicableAllStore())

                        .setExchangeTimes(responseCouponDTO.getExchangeTimes())
                        .setExchangeLimit(responseCouponDTO.getExchangeLimit())
                        .setDtlGuid(dto.getDtlGuid())
                        .setApplyCommodity(responseCouponDTO.getApplyCommodity())
                        .setDiscountAmountLimit(responseCouponDTO.getDiscountAmountLimit())
                        .setSingleOrderUsedLimit(responseCouponDTO.getSingleOrderUsedLimit())
                        .setShareRelation(responseCouponDTO.getShareRelation());

                if (Objects.nonNull(memberPhoneDTO)) {
                    // 绑定会员
                    hsaMemberCouponLink.setUserName(memberPhoneDTO.getUserName())
                            .setMemberGuid(memberPhoneDTO.getGuid())
                            .setMemberPhone(memberPhoneDTO.getPhoneNum());
                } else {
                    // 绑定订单
                    hsaMemberCouponLink.setOrderGuid(dto.getOrderGuid());
                }

                HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
                if (StringUtils.isNotEmpty(headerUserInfo.getStoreGuid())) {
                    hsaMemberCouponLink.setStoreGuid(Integer.valueOf(headerUserInfo.getStoreGuid()))
                            .setStoreName(headerUserInfo.getStoreName());
                }

                // 时间判断
                checkEffectiveTime(now, responseCouponDTO, hsaMemberCouponLink);

                setApplyBusiness(hsaMemberLabelSet, responseCouponDTO, hsaMemberCouponLink);
                hsaMemberCouponLinkList.add(hsaMemberCouponLink);
            }
        }

        return hsaMemberCouponLinkList;
    }

    private static void setApplyBusiness(Set<String> hsaMemberLabelSet, ResponseCouponDTO responseCouponDTO, HsaMemberCouponLink hsaMemberCouponLink) {
        if (CollUtil.isNotEmpty(responseCouponDTO.getApplyLabelGuidList())) {
            hsaMemberCouponLink.setApplyLabelGuidJson(JSON.toJSONString(responseCouponDTO.getApplyLabelGuidList()));

            hsaMemberLabelSet.addAll(responseCouponDTO.getApplyLabelGuidList());
        }

        if (CollUtil.isNotEmpty(responseCouponDTO.getApplyBusinessList())) {
            hsaMemberCouponLink.setApplyBusinessJson(JSON.toJSONString(responseCouponDTO.getApplyBusinessList()));
        }

        //门店
        if (CollUtil.isNotEmpty(responseCouponDTO.getRequestCouponStoreQOList())) {
            hsaMemberCouponLink.setApplicableAllStoreJson(JSON.toJSONString(responseCouponDTO.getRequestCouponStoreQOList()));
        }

        //商品
        if (CollUtil.isNotEmpty(responseCouponDTO.getRequestCouponCommodityQOList())) {
            hsaMemberCouponLink.setApplyCommodityJson(JSON.toJSONString(responseCouponDTO.getRequestCouponCommodityQOList()));
        }
    }

    private static void checkEffectiveTime(LocalDateTime now,
                                           ResponseCouponDTO responseCouponDTO,
                                           HsaMemberCouponLink hsaMemberCouponLink) {
        if (responseCouponDTO.getEffectiveType() == 0) {
            hsaMemberCouponLink.setCouponEffectiveStartTime(responseCouponDTO.getCouponEffectiveStartTime());
            hsaMemberCouponLink.setCouponEffectiveEndTime(responseCouponDTO.getCouponEffectiveEndTime());
        } else if (responseCouponDTO.getEffectiveType() == 1) {
            if (responseCouponDTO.getAfterUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusHours(responseCouponDTO.getAfterValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusDays(responseCouponDTO.getAfterValue()));
            }

            if (responseCouponDTO.getEffectiveUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveStartTime()
                        .plusHours(responseCouponDTO.getEffectiveValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveStartTime()
                        .plusDays(responseCouponDTO.getEffectiveValue()));
            }

        } else {
            if (responseCouponDTO.getAfterUnit() == 0) {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusHours(responseCouponDTO.getAfterValue()));
            } else {
                hsaMemberCouponLink.setCouponEffectiveStartTime(now.plusDays(responseCouponDTO.getAfterValue()));
            }

            hsaMemberCouponLink.setCouponEffectiveEndTime(responseCouponDTO.getCouponEffectiveEndTime());
        }

        //判断是否过期
        if (hsaMemberCouponLink.getCouponEffectiveEndTime().isAfter(now)) {
            hsaMemberCouponLink.setState(CouponMemberStateEnum.UN_EXPIRE.getCode());
        } else {
            hsaMemberCouponLink.setState(CouponMemberStateEnum.EXPIRE.getCode());
        }
    }

    /**
     * 生成编码
     *
     * @return String
     */
    public String createCouponCode(Map<String, String> codeMap, String guid) {
        String code = guid.substring(7, 19);
        if (codeMap.containsKey(code)) {
            code = NumberUtil.buildNumToStr(12);
        }
        codeMap.put(code, code);
        return code;
    }
}
