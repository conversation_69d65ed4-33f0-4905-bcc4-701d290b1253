package com.holderzone.member.base.mapper.coupon;

import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import com.holderzone.member.common.dto.ItemNum;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponUseQO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseCountVO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseVO;
import com.holderzone.member.common.qo.coupon.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 会员优惠券 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface HsaMemberCouponLinkMapper extends HolderBaseMapper<HsaMemberCouponLink> {

    /**
     * 使用规则
     *
     * @param guids 参数
     * @return
     */
    List<HsaMemberCouponLink> listRule(@Param("guids") List<String> guids);

    /**
     * 使用明细
     *
     * @param qo 参数
     * @return
     */
    List<CouponUseVO> listUseDetail(@Param("qo") CouponUseQO qo);

    /**
     * 核销统计
     *
     * @param qo 查询参数
     * @return
     */
    CouponUseCountVO countUse(@Param("qo") CouponUseQO qo);

    /**
     * 累加订单实付金额
     *
     * @param qo 查询参数
     * @return
     */
    BigDecimal sumOrderPaidAmount(@Param("qo") CouponUseQO qo);

    /**
     * 核销订单数最多的门店
     *
     * @param qo 查询参数
     * @return
     */
    String queryTopStoreByUse(@Param("qo") CouponUseQO qo);

    /**
     * 核销订单数最多的门店
     *
     * @param qo 查询参数
     * @return
     */
    String queryTopStoreNameByUse(@Param("qo") CouponUseQO qo);

    /**
     * 列表查询
     *
     * @param qo
     * @return
     */
    List<HsaMemberCouponLink> listDetail(@Param("qo") CouponGiveQO qo);

    /**
     * 优惠券详情
     *
     * @param guid 券guid
     * @return
     */
    HsaMemberCouponLink detail(@Param("guid") String guid);

    /**
     * 数据统计
     *
     * @param qo
     * @return
     */
    Integer countGiveNum(@Param("qo") CouponGiveQO qo);

    /**
     * 数据统计
     *
     * @param qo
     * @return
     */
    Integer countOnlyInvalidNum(@Param("qo") CouponGiveQO qo);

    /**
     * 数据统计
     *
     * @param qo
     * @return
     */
    Integer countExpireNum(@Param("qo") CouponGiveQO qo);

    /**
     * 会员：列表查询
     *
     * @param qo
     * @return
     */
    List<HsaMemberCouponLink> memberPageCouponDetail(@Param("qo") MemberCouponQO qo);

    /**
     * 会员：统计可用
     *
     * @param qo
     * @return
     */
    Integer countUnwrittenNum(@Param("qo") MemberCouponQO qo);

    /**
     * 会员：统计核销数量
     *
     * @param qo
     * @return
     */
    Integer countWrittenNum(@Param("qo") MemberCouponQO qo);

    /**
     * 会员：统计可用
     *
     * @param qo
     * @return
     */
    Integer countUnwrittenNumByCoupon(@Param("qo") CouponGiveQO qo);

    /**
     * 会员：统计已核销
     *
     * @param qo
     * @return
     */
    Integer countWrittenNumByCoupon(@Param("qo") CouponGiveQO qo);

    /**
     * 会员：统计失效
     *
     * @param qo
     * @return
     */
    Integer countInvalidNum(@Param("qo") MemberCouponQO qo);

    /**
     * 发放成功记录数
     *
     * @param qo
     * @return
     */
    List<ItemNum> countCouponPackage(@Param("qo") PackageListCountQO qo);

    /**
     * 核销成功记录数
     *
     * @param qo
     * @return
     */
    List<ItemNum> countUseCouponPackage(@Param("qo") PackageListCountQO qo);

    /**
     * 发放成功记录数
     *
     * @param qo
     * @return
     */
    List<ItemNum> countCoupon(@Param("qo") CouponListCountQO qo);

    /**
     * 核销成功记录数
     *
     * @param qo
     * @return
     */
    List<ItemNum> countUseCoupon(@Param("qo") CouponListCountQO qo);

    /**
     * 发放成功记录数
     *
     * @param qo
     * @return
     */
    Integer countMemberNum(@Param("qo") MemberCouponNumQO qo);

    /**
     * 分页查询优惠券
     * 所有字段：包含条件
     *
     * @param qo
     * @return
     */
    List<HsaMemberCouponLink> listByUseSettlement(@Param("qo") MemberCouponListQO qo);

    /**
     * 分页查询优惠券
     *
     * @param qo
     * @return
     */
    List<HsaMemberCouponLink> pageMemberCouponByTime(@Param("qo") MemberCouponListQO qo);

    /**
     * 分页查询优惠券
     * @param qo
     * @return
     */
    List<HsaMemberCouponLink> pageableMemberCouponByTime(@Param("qo") MemberCouponListQO qo);

    void updateIsExpireRemind(@Param("couponGuidList") List<String> couponGuidList);

    /**
     * 更新优惠券状态: 最后一步了，不用判断券状态
     *
     * @param guids 券guid
     * @param state 状态
     * @return
     */
    int updateStateByGuids(@Param("guids") List<String> guids,
                           @Param("state") int state);

    /**
     * 更新优惠券状态: 最后一步了，不用判断券状态
     *
     * @param couponCode 券类型code
     * @param codes      已领券code
     * @param state      状态
     * @return
     */
    int updateStateByUse(@Param("memberInfoGuid") String memberInfoGuid,
                         @Param("couponCode") String couponCode,
                         @Param("codes") List<String> codes,
                         @Param("state") int state);

    /**
     * 查询优惠券guid
     *
     * @param operSubjectGuid 主体
     * @param couponCode      券类型
     * @param codes           券码
     * @return guid
     */
    List<String> listGuid(@Param("operSubjectGuid") String operSubjectGuid,
                          @Param("couponCode") String couponCode,
                          @Param("codes") List<String> codes);

    /**
     * 查询优惠券
     *
     * @param operSubjectGuid 主体
     * @param couponCode      券类型
     * @param codes           券码
     * @return guid
     */
    List<HsaMemberCouponLink> listAllByCode(@Param("operSubjectGuid") String operSubjectGuid,
                                            @Param("couponCode") String couponCode,
                                            @Param("codes") List<String> codes);

    /**
     * 作废该订单的优惠券
     */
    int updateStateByConsumptionGuid(@Param("operSubjectGuid") String operSubjectGuid,
                                     @Param("consumptionGuid") String consumptionGuid);
}
