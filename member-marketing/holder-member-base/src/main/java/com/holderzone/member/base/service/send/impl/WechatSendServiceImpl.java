package com.holderzone.member.base.service.send.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.service.send.WechatSendService;
import com.holderzone.member.base.util.WeChatSendMessagesUtil;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.wechat.WechatMsgSendType;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.evaluation.OrderEvaluationListQO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.vo.evaluation.OrderEvaluateVO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;


@Service
@Slf4j
public class WechatSendServiceImpl implements WechatSendService {

    @Resource
    private WeChatSendMessagesUtil wechatSendMessagesUtil;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    @Qualifier("memberWxMsgThreadExecutor")
    private ExecutorService memberWxMsgThreadExecutor;

    public static final int BATCH_SEND_PARTITION = 10;

    @Override
    public void send(MessagesSendQO messagesSend) {
        log.info("消息接收：{}", JacksonUtils.writeValueAsString(messagesSend));
        SendMessagesConfigVO sendMessagesConfig = getMessagesConfigByName(messagesSend.getOperSubjectGuid(), messagesSend.getTemplateName());
        if (Objects.isNull(sendMessagesConfig)) {
            log.error("模板不存在！名称：{}", messagesSend.getTemplateName());
            return;
        }
        log.info("模板配置信息：{}", JacksonUtils.writeValueAsString(sendMessagesConfig));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        // 小程序发送
        memberWxMsgThreadExecutor.execute(() -> {
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            sendAppletMsg(sendMessagesConfig, messagesSend);
        });
        // 公众号发送
        memberWxMsgThreadExecutor.execute(() -> {
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            sendMpMsg(sendMessagesConfig, messagesSend);
        });
    }

    @Override
    public void sendBatch(List<MessagesSendQO> messagesSendList) {
        if (CollectionUtils.isEmpty(messagesSendList)) {
            return;
        }
        log.info("批量发送消息接收：{}", JacksonUtils.writeValueAsString(messagesSendList));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("header user info :{}", JacksonUtils.writeValueAsString(headerUserInfo));
        // 防止存在不同运营主体
        Map<String, List<MessagesSendQO>> messagesSendMap = messagesSendList.stream().collect(Collectors.groupingBy(MessagesSendQO::getOperSubjectGuid));
        for (Map.Entry<String, List<MessagesSendQO>> entry : messagesSendMap.entrySet()) {
            List<MessagesSendQO> innerMessagesSendList = entry.getValue();
            MessagesSendQO innerMessagesSend = innerMessagesSendList.get(0);
            // 查询模板信息
            SendMessagesConfigVO sendMessagesConfig = getMessagesConfigByName(innerMessagesSend.getOperSubjectGuid(), innerMessagesSend.getTemplateName());
            if (Objects.isNull(sendMessagesConfig)) {
                log.error("模板不存在！名称：{}", innerMessagesSend.getTemplateName());
                return;
            }
            // 分批线程处理
            List<List<MessagesSendQO>> groupByMessageSendList = Lists.partition(innerMessagesSendList, BATCH_SEND_PARTITION);
            for (List<MessagesSendQO> messagesSendPartition : groupByMessageSendList) {
                memberWxMsgThreadExecutor.execute(() -> {
                    ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
                    // 小程序发送
                    sendBatchAppletMsg(sendMessagesConfig, messagesSendPartition);
                    // 公众号发送
                    sendBatchMpMsg(sendMessagesConfig, messagesSendPartition);
                });
            }
        }
    }

    /**
     * 小程序发送消息
     */
    private void sendAppletMsg(SendMessagesConfigVO sendMessagesConfig, MessagesSendQO innerMessagesSend) {
        if (StringUtil.isEmpty(sendMessagesConfig.getAppletTemplateId()) || sendMessagesConfig.getAppletStatus() == 0) {
            return;
        }
        // 校验跳转
        dealPageParams(innerMessagesSend, sendMessagesConfig);
        // 校验触发条件
        if (checkOrderEvaluation(innerMessagesSend, sendMessagesConfig)) {
            return;
        }
        wechatSendMessagesUtil.sendAppletMsg(innerMessagesSend);
    }

    /**
     * 小程序批量发送消息
     */
    private void sendBatchAppletMsg(SendMessagesConfigVO sendMessagesConfig, List<MessagesSendQO> messagesSendList) {
        if (StringUtil.isEmpty(sendMessagesConfig.getAppletTemplateId()) || sendMessagesConfig.getAppletStatus() == 0) {
            return;
        }
        Iterator<MessagesSendQO> iterator = messagesSendList.iterator();
        while (iterator.hasNext()) {
            MessagesSendQO innerMessagesSend = iterator.next();
            // 校验跳转
            dealPageParams(innerMessagesSend, sendMessagesConfig);
            // 校验触发条件
            if (checkOrderEvaluation(innerMessagesSend, sendMessagesConfig)) {
                iterator.remove();
                return;
            }
        }
        if (CollectionUtils.isEmpty(messagesSendList)) {
            return;
        }
        wechatSendMessagesUtil.sendBatchAppletMsg(messagesSendList);
    }


    /**
     * 公众号发送消息
     */
    private void sendMpMsg(SendMessagesConfigVO sendMessagesConfig, MessagesSendQO innerMessagesSend) {
        if (StringUtil.isEmpty(sendMessagesConfig.getMsgTemplateId()) || sendMessagesConfig.getStatus() == 0) {
            return;
        }
        // 校验跳转
        dealPageParams(innerMessagesSend, sendMessagesConfig);
        wechatSendMessagesUtil.sendWxMpMsg(innerMessagesSend);
    }

    /**
     * 公众号批量发送消息
     */
    private void sendBatchMpMsg(SendMessagesConfigVO sendMessagesConfig, List<MessagesSendQO> messagesSendList) {
        if (StringUtil.isEmpty(sendMessagesConfig.getMsgTemplateId()) || sendMessagesConfig.getStatus() == 0) {
            return;
        }
        for (MessagesSendQO messagesSend : messagesSendList) {
            // 校验跳转
            dealPageParams(messagesSend, sendMessagesConfig);
        }
        wechatSendMessagesUtil.sendBatchWxMpMsg(messagesSendList);
    }


    private boolean checkOrderEvaluation(MessagesSendQO messagesSendQO, SendMessagesConfigVO sendMessagesConfigVO) {
        if (sendMessagesConfigVO.getMsgTitle().equals("用餐点评提醒")) {
            OrderEvaluationListQO orderEvaluationListQO = new OrderEvaluationListQO();
            orderEvaluationListQO.setOrderNum(Collections.singletonList(messagesSendQO.getOrderNum()));
            orderEvaluationListQO.setOperSubjectGuid(messagesSendQO.getOperSubjectGuid());
            orderEvaluationListQO.setEnterpriseGuid(messagesSendQO.getEnterpriseGuid());
            List<OrderEvaluateVO> result = (List<OrderEvaluateVO>) memberMarketingFeign.queryEvaluationByOrderNum(orderEvaluationListQO).getData();
            if (CollUtil.isEmpty(result)) {
                log.error("评价业务未开启,订单号：{}", messagesSendQO.getOrderNum());
                return true;
            }
        }
        return false;
    }

    private static void dealPageParams(MessagesSendQO messagesSendQO, SendMessagesConfigVO sendMessagesConfigVO) {
        messagesSendQO.setAppletTemplateId(sendMessagesConfigVO.getAppletTemplateId());
        messagesSendQO.setMsgTemplateId(sendMessagesConfigVO.getMsgTemplateId());
        if (StringUtils.isNotBlank(sendMessagesConfigVO.getPagePath())) {
            messagesSendQO.setPageParams(sendMessagesConfigVO.getPagePath());
            if (sendMessagesConfigVO.getMsgTitle().equals(WechatMsgSendType.AMOUNT_CHANGE.getMsgTitle())
                    && StringUtils.isNotBlank(messagesSendQO.getMemberInfoCardGuid())) {

                messagesSendQO.setPageParams(messagesSendQO.getPageParams()
                        + "memberInfoCardGuid=" + messagesSendQO.getMemberInfoCardGuid());
            } else if (sendMessagesConfigVO.getMsgTitle().equals(WechatMsgSendType.ORDER_REFUND.getMsgTitle())
                    || sendMessagesConfigVO.getMsgTitle().equals(WechatMsgSendType.ORDER_COMMENT.getMsgTitle())) {
                StringBuilder params = new StringBuilder(messagesSendQO.getPageParams())
                        .append("orderNo=").append(messagesSendQO.getOrderNo());
                if (StringUtils.isNotEmpty(messagesSendQO.getPackageId())) {
                    params.append("&packageId=").append(messagesSendQO.getPackageId());
                }
                params.append("&shopId=").append(messagesSendQO.getShopId());
                messagesSendQO.setPageParams(params.toString());
            } else if (sendMessagesConfigVO.getMsgTitle().equals(WechatMsgSendType.ACTIVITY_AUDIT.getMsgTitle())) {
                if (StringUtils.isNotEmpty(messagesSendQO.getActivityGuid())) {
                    String params = String.format(messagesSendQO.getPageParams(),
                            messagesSendQO.getActivityGuid(),
                            messagesSendQO.getActivityRecordGuid());
                    messagesSendQO.setPageParams(params);
                } else {
                    messagesSendQO.setPageParams(null);
                }
            }
        }
    }


    @Override
    public SendMessagesConfigVO getMessagesConfigByName(String operSubjectGuid, String name) {
        return memberMallToolFeign.
                getMessagesConfigByName(operSubjectGuid, name);
    }
}
