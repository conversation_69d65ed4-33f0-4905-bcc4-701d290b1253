package com.holderzone.member.base.service.settlement.chain;

import com.holderzone.member.base.dto.SettlementBackDiscountDTO;
import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.base.support.VoucherCouponSupport;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyOrderVO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 优惠券:代金券
 *
 * <AUTHOR>
 * @date 2023/10/8
 * @since 1.8
 */
@Component
@Slf4j
public class CouponVoucherSettleHandler extends SettleHandler {

    @Resource
    private VoucherCouponSupport voucherCouponSupport;

    /**
     * 优惠券处理
     */
    @Resource
    private IHsaMemberCouponLinkService hsaMemberCouponLinkService;

    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;


    @Override
    public int option() {
        return SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode();
    }

    @Override
    public SettlementApplyOrderVO listDiscount(SettlementApplyOrderDTO dto) {

        if (StringUtils.isEmpty(dto.getOrderInfo().getMemberInfoGuid())) {
            return null;
        }
        //获取可用的代金券列表并计算优惠金额（不进行自动选择）
        return voucherCouponSupport.calculateDiscount(dto);
    }

    @Override
    public SettlementApplyOrderVO calculateDiscount(SettleHandlerCalculateChain chain) {
        final SettlementApplyOrderDTO orderDTO = chain.getReq();
        return voucherCouponSupport.calculateDiscount(orderDTO);
    }

    @Override
    public void lockDiscount(SettlementOrderLockDTO lockedDiscount) {
        lockedDiscount.setCodeType(SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode());
        //优惠券锁定
        hsaMemberCouponLinkService.locked(lockedDiscount);
    }

    @Override
    public void unlockDiscount(SettlementUnLockedDiscountDTO discountDTO) {
        //优惠券状态回退，按最新的来判断
        hsaMemberCouponLinkService.unlocked(discountDTO);
    }

    @Override
    public void afterPayDiscount(SettlementPayAfterDiscountDTO discountDTO) {
        discountDTO.setCodeType(SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode());
        hsaMemberCouponLinkService.afterPayDiscount(discountDTO);
    }

    /**
     * 回退优惠
     */
    @Override
    public void backDiscount(SettlementBackDiscountDTO discountDTO) {
        if (discountDTO.getIsIntegralBack() == BooleanEnum.TRUE.getCode()) {

            List<HsaMemberOrderDiscount> discountList = discountDTO.getOrderDiscountByOptionMap()
                                                                .get(SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode());

            //过滤可退款的优惠
            discountList = discountList.stream().filter(in -> in.getDiscountState() == BooleanEnum.FALSE.getCode()
                                                                      && in.getCouponRollback() == BooleanEnum.TRUE.getCode())
                                   .collect(Collectors.toList());

            if (CollUtil.isEmpty(discountList)) {
                log.info("订单：{}，无可退代金券优惠记录，不处理", discountDTO.getOrderNo());
                return;
            }

            //取消锁定优惠
            discountList.forEach(in -> {
                in.setDiscountState(BooleanEnum.TRUE.getCode());
                in.setCommodityJson(null);
                hsaMemberOrderDiscountService.updateByGuid(in);
            });

            unlockDiscount(new SettlementUnLockedDiscountDTO()
                                   .setDiscountIdList(discountList.stream().map(HsaMemberOrderDiscount::getDiscountId).collect(Collectors.toList()))
                                   .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                                   .setMemberGuid(discountDTO.getMemberGuid())
                                   .setOrderNo(discountDTO.getOrderNo())
                                   .setCodeType(SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode()));
        }
    }
}
