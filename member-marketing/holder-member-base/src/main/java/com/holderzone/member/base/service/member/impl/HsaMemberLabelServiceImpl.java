package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.member.HsaLabelSetting;
import com.holderzone.member.base.entity.member.HsaMemberLabel;
import com.holderzone.member.base.mapper.card.HsaMemberInfoCardMapper;
import com.holderzone.member.base.mapper.member.HsaLabelSettingMapper;
import com.holderzone.member.base.mapper.member.HsaMemberLabelMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.grade.IHsaMemberGradeRelationService;
import com.holderzone.member.base.service.member.HsaDepositStrategyService;
import com.holderzone.member.base.service.member.HsaMemberLabelRecordService;
import com.holderzone.member.base.service.member.HsaMemberLabelService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.label.MemberLabelRecordDTO;
import com.holderzone.member.common.dto.member.MemberCardDTO;
import com.holderzone.member.common.dto.member.MemberRelationLabelDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.exception.MemberLabelExceptionEnum;
import com.holderzone.member.common.enums.member.ConnectionLabelTypeEnum;
import com.holderzone.member.common.enums.member.LabelCancelConnectTypeEnum;
import com.holderzone.member.common.enums.member.LabelConnectTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.grade.MemberNewGradeDTO;
import com.holderzone.member.common.vo.member.MemberLabelVO;
import com.holderzone.member.common.vo.member.RelationLabelListVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class HsaMemberLabelServiceImpl extends HolderBaseServiceImpl<HsaMemberLabelMapper, HsaMemberLabel> implements HsaMemberLabelService {

    /**
     * 生成guid工具类
     */
    private final GuidGeneratorUtil guidGeneratorUtil;

    /**
     * 标签mapper
     */
    private final HsaMemberLabelMapper hsaMemberLabelMapper;

    /**
     * 标签设置mapper
     */
    private final HsaLabelSettingMapper hsaLabelSettingMapper;

    private final HsaMemberLabelRecordService hsaMemberLabelRecordService;

    private final HsaDepositStrategyService hsaDepositStrategyService;

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    private final HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    private final IHsaMemberGradeRelationService memberGradeRelationService;

    private final RedissonClient redissonClient;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRelationLabel(AddHsaMemberLabelQO addHsaMemberLabelQO) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        //避免要是运营主体为空，存下错误数据 这里还是再校验一下
        if (ObjectUtil.checkParam(headerUserInfo, headerUserInfo.getOperSubjectGuid())) {
            throw new MemberBaseException("运营主体guid不能为空");
        }
        List<HsaMemberLabel> list = new ArrayList<>();
        //标签信息
        HsaLabelSetting hsaLabelSetting = hsaLabelSettingMapper.queryByGuid(addHsaMemberLabelQO.getLabelSettingGuid());
        LocalDateTime now = LocalDateTime.now();
        List<MemberLabelRecordDTO> memberLabelRecordDTOList = Lists.newArrayList();
        dealForMemberLabel(addHsaMemberLabelQO, headerUserInfo, now, list, hsaLabelSetting, memberLabelRecordDTOList);
        hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOList, headerUserInfo);
        return this.saveBatch(list);
    }

    private void dealForMemberLabel(AddHsaMemberLabelQO addHsaMemberLabelQO, HeaderUserInfo headerUserInfo, LocalDateTime now, List<HsaMemberLabel> list, HsaLabelSetting hsaLabelSetting, List<MemberLabelRecordDTO> memberLabelRecordDTOList) {
        for (String memberGuid : addHsaMemberLabelQO.getOperationMemberInfoGuid()) {
            HsaMemberLabel hsaMemberLabel = addMemberLabel(addHsaMemberLabelQO, headerUserInfo, now, list, memberGuid);
            //记录
            addLabelRecord(now, hsaLabelSetting, memberLabelRecordDTOList, hsaMemberLabel);
        }
    }

    private HsaMemberLabel addMemberLabel(AddHsaMemberLabelQO addHsaMemberLabelQO, HeaderUserInfo headerUserInfo, LocalDateTime now, List<HsaMemberLabel> list, String memberGuid) {
        HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid(ObjectUtil.objToString(guidGeneratorUtil.getGuid(HsaMemberLabel.class.getSimpleName())))
                .setLabelSettingGuid(addHsaMemberLabelQO.getLabelSettingGuid())
                .setOperationMemberInfoGuid(memberGuid)
                .setOperatorGuid(headerUserInfo.getUserGuid())
                .setOperatorName(headerUserInfo.getUserName())
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setConnectionType(LabelConnectTypeEnum.MANUAL.getCode())
                .setGmtCreate(now)
                .setGmtModified(now);
        list.add(hsaMemberLabel);
        return hsaMemberLabel;
    }

    private static void addLabelRecord(LocalDateTime now, HsaLabelSetting hsaLabelSetting, List<MemberLabelRecordDTO> memberLabelRecordDTOList, HsaMemberLabel hsaMemberLabel) {
        MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
        labelRecordQO.setLabelType(hsaLabelSetting.getLabelType())
                .setLabelName(hsaLabelSetting.getLabelName());
        labelRecordQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setIsConnection(ConnectionLabelTypeEnum.AUTOMATION.getCode())
                .setMemberLabelGuid(hsaMemberLabel.getGuid())
                .setConnectionTime(now)
                .setConnectionType(LabelConnectTypeEnum.MANUAL.getCode())
                .setMemberInfoGuid(hsaMemberLabel.getOperationMemberInfoGuid())
                .setLabelSettingGuid(hsaLabelSetting.getGuid());
        memberLabelRecordDTOList.add(labelRecordQO);
    }

    @Override
    public PageResult listRelationLabel(RelationLabelListQO relationLabelListQO) {
        //查询数量
        PageHelper.startPage(relationLabelListQO.getCurrentPage(), relationLabelListQO.getPageSize());
        List<RelationLabelListVO> list = hsaMemberLabelMapper.listRelationLabel(relationLabelListQO);
        if (CollectionUtils.isEmpty(list)) {
            return PageUtil.getPageResult(new PageInfo<>(list));
        }
        List<String> memberGuids = list.stream().map(RelationLabelListVO::getMemberGuid).collect(Collectors.toList());
        List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
        Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid,
                MemberRelationLabelDTO::getLabelName));
        //所有会员卡
        List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
        Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid,
                MemberCardDTO::getMemberCard));
        for (RelationLabelListVO relationLabelListVO : list) {
            relationLabelListVO.setMemberLabel(allLabelMap.get(relationLabelListVO.getMemberGuid()));
            relationLabelListVO.setMemberCard(allMemberMap.get(relationLabelListVO.getMemberGuid()));
        }
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public PageResult listUnRelationLabelMember(UnRelationLabelListQO unRelationLabelListQO) {

        unRelationLabelListQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageHelper.startPage(unRelationLabelListQO.getCurrentPage(), unRelationLabelListQO.getPageSize());
        List<RelationLabelListVO> list = hsaMemberLabelMapper.findUnRelationLabelMember(unRelationLabelListQO);
        if (CollectionUtils.isEmpty(list)) {
            return PageUtil.getPageResult(new PageInfo<>(list));
        }
        List<String> memberGuids = list.stream().map(RelationLabelListVO::getMemberGuid).collect(Collectors.toList());
        List<MemberRelationLabelDTO> allLabels = hsaMemberLabelMapper.findMemberAllLabel(memberGuids);
        Map<String, String> allLabelMap = allLabels.stream().collect(Collectors.toMap(MemberRelationLabelDTO::getMemberGuid,
                MemberRelationLabelDTO::getLabelName));
        //所有会员卡
        List<MemberCardDTO> allMembers = hsaMemberInfoCardMapper.findAllMemberCard(memberGuids);
        Map<String, String> allMemberMap = allMembers.stream().collect(Collectors.toMap(MemberCardDTO::getMemberGuid,
                MemberCardDTO::getMemberCard));

        for (RelationLabelListVO relationLabelListVO : list) {
            relationLabelListVO.setMemberLabel(allLabelMap.get(relationLabelListVO.getMemberGuid()));
            relationLabelListVO.setMemberCard(allMemberMap.get(relationLabelListVO.getMemberGuid()));
        }

        if (ThreadLocalCache.isPartner()) {
            List<MemberNewGradeDTO> currentGradeList = memberGradeRelationService.getNewCurrentGradeList(memberGuids);
            if (CollectionUtil.isNotEmpty(currentGradeList)) {
                Map<String, MemberNewGradeDTO> memberNewGradeMap = currentGradeList.stream()
                        .collect(Collectors.toMap(MemberNewGradeDTO::getMemberInfoGuid, Function.identity(), (obj1, obj2) -> obj1));
                list.forEach(l -> {
                    MemberNewGradeDTO newGradeDTO = memberNewGradeMap.get(l.getMemberGuid());
                    if (Objects.nonNull(newGradeDTO)) {
                        //会员等级 + 商家等级
                        l.setMemberGradeInfoName(StringUtils.isEmpty(newGradeDTO.getBusinessGradeName()) ? newGradeDTO.getMemberInfoGradeName() :
                                newGradeDTO.getMemberInfoGradeName() + "、" + newGradeDTO.getBusinessGradeName());
                    }
                });
            }
        }
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public void importRelationLabelMember(ImportRelationLabelMemberQO qo) {

        if (ObjectUtil.checkParam(qo.getLabelGuid()) || CollectionUtils.isEmpty(qo.getMemberGuids())) {
            throw new MemberBaseException(MemberLabelExceptionEnum.ERROR_PARAM);
        }
        List<HsaMemberLabel> labels = new ArrayList<>();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        for (String memberGuid : qo.getMemberGuids()) {
            HsaMemberLabel label = new HsaMemberLabel()
                    .setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabel.class.getSimpleName()))
                    .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                    .setOperatorName(headerUserInfo.getUserName())
                    .setOperatorGuid(headerUserInfo.getUserGuid())
                    .setLabelSettingGuid(qo.getLabelGuid())
                    .setOperationMemberInfoGuid(memberGuid)
                    .setGmtModified(LocalDateTime.now())
                    .setGmtCreate(LocalDateTime.now());
            labels.add(label);
        }
        saveBatch(labels);
    }


    @Override
    public PageResult listMemberLabel(Integer currentPage, Integer pageSize, String memberInfoGuid) {
        PageHelper.startPage(currentPage, pageSize);
        List<MemberLabelVO> list = hsaMemberLabelMapper.listMemberLabel(memberInfoGuid);
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public List<MemberLabelVO> listMemberLabelByMemberInfoGuid(String memberInfoGuid) {
        return hsaMemberLabelMapper.listMemberLabel(memberInfoGuid);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCorrelationStatus(UpdateLabelStatusQO qo) {
        final List<HsaMemberLabel> hsaMemberLabels = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                .eq(HsaMemberLabel::getOperSubjectGuid, qo.getOperSubjectGuid())
                .eq(HsaMemberLabel::getOperationMemberInfoGuid, qo.getMemberGuid())
                .in(HsaMemberLabel::getLabelSettingGuid, qo.getLabelGuids())
        );
        if (CollectionUtils.isEmpty(hsaMemberLabels)) {
            return false;
        }
        UpdateLabelCorrelationStatusQO statusQo = new UpdateLabelCorrelationStatusQO();
        statusQo.setIsConnection(0);
        statusQo.setMemberLabelGuid(hsaMemberLabels.stream()
                .map(HsaMemberLabel::getGuid)
                .collect(Collectors.toList()));
        return updateCorrelationStatus(statusQo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCorrelationStatus(UpdateLabelCorrelationStatusQO qo) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<HsaMemberLabel> hsaMemberLabelList;
        if (!CollectionUtils.isEmpty(qo.getMemberLabelGuid())) {
            hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                    .in(HsaMemberLabel::getGuid, qo.getMemberLabelGuid()));
        } else {
            hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                    .in(HsaMemberLabel::getLabelSettingGuid, qo.getLabelGuid())
                    .eq(HsaMemberLabel::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid())
                    .eq(HsaMemberLabel::getOperationMemberInfoGuid, qo.getMemberGuid()));
        }
        if (qo.getIsConnection() == BooleanEnum.TRUE.getCode()) {
            //只修改自动标签
            List<String> labelGuid = Lists.newArrayList();
            List<HsaMemberLabel> hsaMemberLabels = Lists.newArrayList();
            Map<String, HsaLabelSetting> hsaLabelSettingMap = hsaLabelSettingMapper.selectList(new LambdaQueryWrapper<HsaLabelSetting>()
                            .in(HsaLabelSetting::getGuid, hsaMemberLabelList.stream().map(HsaMemberLabel::getLabelSettingGuid).collect(Collectors.toList())))
                    .stream()
                    .collect(Collectors.toMap(HsaLabelSetting::getGuid, Function.identity(), (entity1, entity2) -> entity1));
            hsaMemberLabelList.forEach(in -> {
                if (CollectionUtil.isNotEmpty(hsaLabelSettingMap) && hsaLabelSettingMap.containsKey(in.getLabelSettingGuid())) {
                    HsaLabelSetting hsaLabelSetting = hsaLabelSettingMap.get(in.getLabelSettingGuid());
                    if (hsaLabelSetting.getLabelType() == 1) {
                        labelGuid.add(in.getGuid());
                        hsaMemberLabels.add(in);
                    }
                }
            });
            if (CollectionUtil.isEmpty(labelGuid)) {
                return false;
            }
            hsaMemberLabelMapper.updateCorrelationStatus(labelGuid, BooleanEnum.TRUE.getCode());
            List<String> operationMemberInfoGuid = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                            .in(HsaMemberLabel::getGuid, labelGuid))
                    .stream()
                    .map(HsaMemberLabel::getOperationMemberInfoGuid)
                    .collect(Collectors.toList());
            List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
            hsaMemberLabels.forEach(in -> {
                MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
                labelRecordQO.setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode());
                labelRecordQO.setMemberLabelGuid(in.getGuid());
                labelRecordQO.setIsConnection(ConnectionLabelTypeEnum.AUTOMATION.getCode());
                memberLabelRecordDTOArrayList.add(labelRecordQO);
            });
            hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
            hsaDepositStrategyService.refreshLabel(operationMemberInfoGuid, null, BooleanEnum.FALSE.getCode());

        } else {
            //数量
//            List<HsaLabelSetting> hsaLabelSettingList = Lists.newArrayList();
            List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
            hsaMemberLabelList.forEach(in -> {
                MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
                labelRecordQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                        .setIsConnection(ConnectionLabelTypeEnum.MANUAL.getCode())
                        .setMemberLabelGuid(in.getGuid())
                        .setCancelConnectionTime(LocalDateTime.now())
                        .setCancelConnectionType(LabelCancelConnectTypeEnum.MANUAL.getCode());
                memberLabelRecordDTOArrayList.add(labelRecordQO);
//                if (CollectionUtil.isNotEmpty(hsaLabelSettingMap) && hsaLabelSettingMap.containsKey(in.getLabelSettingGuid())) {
//                    HsaLabelSetting hsaLabelSetting = hsaLabelSettingMap.get(in.getLabelSettingGuid());
//                    hsaLabelSetting.setMemberNum(hsaLabelSetting.getMemberNum() - 1);
//                    hsaLabelSettingList.add(hsaLabelSetting);
//                }
            });
            hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
            hsaMemberLabelMapper.deleteCorrelation(qo.getMemberLabelGuid());
//            if (CollectionUtil.isNotEmpty(hsaLabelSettingList)) {
//                hsaLabelSettingMapper.updateMemberSettingNum(hsaLabelSettingList);
//            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addMemberInfoLabel(AddMemberLabelCorrelationQO qo) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        if (CollectionUtils.isEmpty(qo.getLabelGuid()) || CollectionUtils.isEmpty(qo.getMemberInfoGuid())) {
            return false;
        }

        //未加锁
        if (StringUtils.isEmpty(qo.getBatchId())) {
            return handlerAddMemberLabel(qo, headerUserInfo);
        }

        //加锁
        RLock lock = redissonClient.getLock(RedisKeyConstant.LABEL_BATCH + headerUserInfo.getOperSubjectGuid() + ":" + qo.getBatchId());
        try {
            if (!lock.tryLock(30, 30, TimeUnit.SECONDS)) {
                return false;
            }
            return handlerAddMemberLabel(qo, headerUserInfo);
        } catch (Exception exception) {
            log.error("{}，批量添加标签异常：{}", qo.getBatchId(), exception.getMessage());
            throw new MemberBaseException(exception.getMessage());
        } finally {
            //释放锁资源
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理添加标签
     *
     * @param qo             参数
     * @param headerUserInfo 操作人
     * @return 是否成功
     */
    private boolean handlerAddMemberLabel(AddMemberLabelCorrelationQO qo,
                                          HeaderUserInfo headerUserInfo) {
        //标签信息
        Map<String, HsaLabelSetting> hsaLabelSettingMap = hsaLabelSettingMapper.selectList(new LambdaQueryWrapper<HsaLabelSetting>()
                        .in(HsaLabelSetting::getGuid, qo.getLabelGuid()))
                .stream()
                .collect(Collectors.toMap(HsaLabelSetting::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        //已关联的标签
        Map<String, List<HsaMemberLabel>> hsaMemberLabelMap = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                        .in(HsaMemberLabel::getOperationMemberInfoGuid, qo.getMemberInfoGuid()))
                .stream()
                .collect(Collectors.groupingBy(HsaMemberLabel::getOperationMemberInfoGuid));
        //数量记录
        List<HsaLabelSetting> hsaLabelSettingList = Lists.newArrayList();
        //修改数据
        List<HsaMemberLabel> updateMemberLabels = Lists.newArrayList();
        //组装全新数据
        List<HsaMemberLabel> newMemberLabels = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();

        //处理关联关系
        dealMemberLabel(qo, hsaMemberLabelMap, hsaLabelSettingMap, updateMemberLabels, headerUserInfo, newMemberLabels, now);

        //修改数据
        dealUpdateMemberLabels(updateMemberLabels, headerUserInfo);
        //关联数据
        if (CollUtil.isNotEmpty(newMemberLabels)) {
            List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = getMemberLabelRecordDTOS(hsaLabelSettingMap, newMemberLabels, now, hsaLabelSettingList);
            hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
            saveBatch(newMemberLabels);
        }
        //关联状态更新
        if (CollUtil.isNotEmpty(updateMemberLabels)) {
            List<String> guid = updateMemberLabels.stream().map(HsaMemberLabel::getGuid).collect(Collectors.toList());
            hsaMemberLabelMapper.updateCorrelationStatus(guid, LabelConnectTypeEnum.MANUAL.getCode());
        }
        return true;
    }

    private void dealUpdateMemberLabels(List<HsaMemberLabel> updateMemberLabels, HeaderUserInfo headerUserInfo) {
        if (CollUtil.isNotEmpty(updateMemberLabels)) {
            List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
            updateMemberLabels.forEach(in -> {
                MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
                labelRecordQO.setConnectionType(LabelConnectTypeEnum.MANUAL.getCode());
                labelRecordQO.setMemberLabelGuid(in.getGuid());
                labelRecordQO.setIsConnection(ConnectionLabelTypeEnum.AUTOMATION.getCode());
                in.setConnectionType(LabelConnectTypeEnum.MANUAL.getCode());
                memberLabelRecordDTOArrayList.add(labelRecordQO);
            });
            hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
        }
    }

    private void dealMemberLabel(AddMemberLabelCorrelationQO qo, Map<String, List<HsaMemberLabel>> hsaMemberLabelMap, Map<String, HsaLabelSetting> hsaLabelSettingMap, List<HsaMemberLabel> updateMemberLabels, HeaderUserInfo headerUserInfo, List<HsaMemberLabel> newMemberLabels, LocalDateTime now) {
        qo.getMemberInfoGuid().forEach(in -> {
            Map<String, HsaMemberLabel> memberLabelMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(hsaMemberLabelMap) && hsaMemberLabelMap.containsKey(in)) {
                memberLabelMap = hsaMemberLabelMap.get(in)
                        .stream()
                        .collect(Collectors.toMap(HsaMemberLabel::getLabelSettingGuid, Function.identity(), (entity1, entity2) -> entity1));
            }

            Integer connectionType = qo.getConnectionType();
            for (String labelGuid : qo.getLabelGuid()) {
                //更新为手动关联
                if (CollectionUtil.isNotEmpty(memberLabelMap) && memberLabelMap.containsKey(labelGuid)) {
                    if (hsaLabelSettingMap.containsKey(labelGuid) && memberLabelMap.get(labelGuid).getConnectionType() == 1) {
                        updateMemberLabels.add(memberLabelMap.get(labelGuid));
                    }
                } else {
                    getHsaMemberLabel(headerUserInfo, newMemberLabels, now, in, labelGuid, connectionType);
                }
            }
        });
    }

    private void getHsaMemberLabel(HeaderUserInfo headerUserInfo, List<HsaMemberLabel> newMemberLabels,
                                   LocalDateTime now, String in, String labelGuid, Integer connectionType) {
        HsaMemberLabel hsaMemberLabel = new HsaMemberLabel();
        hsaMemberLabel.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabel.class.getSimpleName()))
                .setLabelSettingGuid(labelGuid)
                .setOperationMemberInfoGuid(in)
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setConnectionType(Optional.ofNullable(connectionType)
                        .orElse(LabelConnectTypeEnum.MANUAL.getCode()))
                .setGmtCreate(now)
                .setGmtModified(now);
        if (Objects.equals(LabelConnectTypeEnum.MANUAL.getCode(), hsaMemberLabel.getConnectionType())) {
            hsaMemberLabel
                    .setOperatorGuid(headerUserInfo.getTel())
                    .setOperatorName(headerUserInfo.getUserName());
        }
        newMemberLabels.add(hsaMemberLabel);
    }

    private List<MemberLabelRecordDTO> getMemberLabelRecordDTOS(Map<String, HsaLabelSetting> hsaLabelSettingMap, List<HsaMemberLabel> newMemberLabels, LocalDateTime now, List<HsaLabelSetting> hsaLabelSettingList) {
        List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
        newMemberLabels.forEach(in -> {
            MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
            if (CollectionUtil.isNotEmpty(hsaLabelSettingMap) && hsaLabelSettingMap.containsKey(in.getLabelSettingGuid())) {
                HsaLabelSetting hsaLabelSetting = hsaLabelSettingMap.get(in.getLabelSettingGuid());
                hsaLabelSetting.setMemberNum(hsaLabelSetting.getMemberNum() + 1);
                labelRecordQO.setLabelType(hsaLabelSetting.getLabelType())
                        .setLabelName(hsaLabelSetting.getLabelName());
                hsaLabelSettingList.add(hsaLabelSetting);
            }
            labelRecordQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                    .setIsConnection(ConnectionLabelTypeEnum.AUTOMATION.getCode())
                    .setMemberLabelGuid(in.getGuid())
                    .setOperSubjectGuid(in.getOperSubjectGuid())
                    .setConnectionTime(now)
                    .setConnectionType(in.getConnectionType())
                    .setMemberInfoGuid(in.getOperationMemberInfoGuid())
                    .setLabelSettingGuid(in.getLabelSettingGuid());
            memberLabelRecordDTOArrayList.add(labelRecordQO);
        });
        return memberLabelRecordDTOArrayList;
    }

    @Override
    public PageResult getNotMemberInfoLabel(MemberLabelCorrelationPageQO memberLabelCorrelationPageQO) {
        PageHelper.startPage(memberLabelCorrelationPageQO.getCurrentPage(), memberLabelCorrelationPageQO.getPageSize());
        memberLabelCorrelationPageQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<MemberLabelVO> list = hsaLabelSettingMapper.getNotMemberInfoLabel(memberLabelCorrelationPageQO);
        return PageUtil.getPageResult(new PageInfo<>(list));
    }

    @Override
    public List<String> findCompanyOrDept(int type, String keywords) {
        List<String> list = null;
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        switch (type) {
            //公司
            case 0:
                list = hsaOperationMemberInfoMapper.findAllCompanyName(operSubjectGuid, keywords);
                break;
            //部門
            case 1:
                list = hsaOperationMemberInfoMapper.findAllDepartmentName(operSubjectGuid, keywords);
                break;
            case 2:
                list = hsaOperationMemberInfoMapper.findAllJobTitle(operSubjectGuid, keywords);
                break;
            default:
        }
        return list;
    }

    @Override
    public List<HsaMemberLabel> getMemberLabelList(List<String> labelSettingGuids) {
        return hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                .in(HsaMemberLabel::getLabelSettingGuid, labelSettingGuids));
    }

    @Override
    public void autoCancelConnection(UpdateLabelCorrelationStatusQO qo) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        List<HsaMemberLabel> hsaMemberLabelList = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                .in(HsaMemberLabel::getLabelSettingGuid, qo.getLabelGuid())
                .eq(HsaMemberLabel::getOperationMemberInfoGuid, qo.getMemberGuid())
        );
        if (CollectionUtils.isEmpty(hsaMemberLabelList)) {
            log.warn("没有查询到标签绑定,MemberGuid={}",qo.getMemberGuid());
            return;
        }
        List<MemberLabelRecordDTO> memberLabelRecordDTOArrayList = Lists.newArrayList();
        hsaMemberLabelList.forEach(memberLabel -> {
            MemberLabelRecordDTO labelRecordQO = new MemberLabelRecordDTO();
            labelRecordQO.setCancelConnectionTime(LocalDateTime.now());
            labelRecordQO.setIsConnection(ConnectionLabelTypeEnum.MANUAL.getCode());
            labelRecordQO.setCancelConnectionType(LabelCancelConnectTypeEnum.AUTOMATION.getCode());
            labelRecordQO.setMemberLabelGuid(memberLabel.getGuid());
            labelRecordQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            memberLabelRecordDTOArrayList.add(labelRecordQO);
        });
        hsaMemberLabelRecordService.batchSaveMemberLabelRecord(memberLabelRecordDTOArrayList, headerUserInfo);
        List<String> guids = hsaMemberLabelList.stream()
                .map(HsaMemberLabel::getGuid)
                .distinct()
                .collect(Collectors.toList());
        hsaMemberLabelMapper.deleteCorrelation(guids);
    }

    @Override
    public void saveHsaMemberLabel(List<String> memberGuidList, List<HsaLabelSetting> hsaLabelSettings,
                                   HeaderUserInfo headerUserInfo) {
        List<HsaMemberLabel> labels = new ArrayList<>();
        for (HsaLabelSetting hsaLabelSetting : hsaLabelSettings) {
            for (String memberGuid : memberGuidList) {
                HsaMemberLabel label = new HsaMemberLabel()
                        .setGuid(guidGeneratorUtil.getStringGuid(HsaMemberLabel.class.getSimpleName()))
                        .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                        .setOperatorName(headerUserInfo.getUserName())
                        .setOperatorGuid(headerUserInfo.getUserGuid())
                        .setLabelSettingGuid(hsaLabelSetting.getGuid())
                        .setOperationMemberInfoGuid(memberGuid)
                        .setConnectionType(LabelConnectTypeEnum.MANUAL.getCode())
                        .setGmtModified(LocalDateTime.now())
                        .setGmtCreate(LocalDateTime.now());
                labels.add(label);
            }
        }
        if (!CollectionUtils.isEmpty(labels)) {
            this.saveBatch(labels);
        }
    }
}
