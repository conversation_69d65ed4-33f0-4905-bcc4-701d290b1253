package com.holderzone.member.base.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponUseMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponUseService;
import com.holderzone.member.base.service.coupon.assembler.MemberCouponAssembler;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedDiscountReqDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementLockedOrderInfoDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;
import com.holderzone.member.common.qo.crm.CrmOrderDetailQo;
import com.holderzone.member.common.qo.crm.CrmOrderDetailVo;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 优惠券使用记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Slf4j
@Service
public class HsaMemberCouponUseServiceImpl extends HolderBaseServiceImpl<HsaMemberCouponUseMapper, HsaMemberCouponUse> implements IHsaMemberCouponUseService {

    @Resource
    private HsaMemberCouponUseMapper hsaMemberCouponUseMapper;

    /**
     * 生成guid
     */
    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    /**
     * crm服务
     */
    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Override
    public void lockedCoupon(SettlementOrderLockDTO lockedDiscount, List<HsaMemberCouponLink> memberCoupons) {
        final SettlementLockedOrderInfoDTO orderInfo = lockedDiscount.getOrderInfo();
        //优惠券guid
        final List<String> memberCouponGuids = memberCoupons.stream().map(HsaBaseEntity::getGuid).collect(Collectors.toList());
        //查询条件
        final LambdaQueryWrapper<HsaMemberCouponUse> wrapper = new LambdaQueryWrapper<HsaMemberCouponUse>()
                .eq(HsaMemberCouponUse::getOperSubjectGuid, orderInfo.getOperSubjectGuid())
                .eq(HsaMemberCouponUse::getOrderNumber, orderInfo.getOrderNumber())
                .in(HsaMemberCouponUse::getMemberCouponLinkGuid, memberCouponGuids);
        final List<HsaMemberCouponUse> list = this.list(wrapper);
        //选中优惠map
        final Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap = getCheckDiscountMap(lockedDiscount, memberCoupons);
        //已有更新
        if (CollUtil.isNotEmpty(list)) {
            //更新已有数据
            updateOldMemberUse(orderInfo, memberCouponGuids, list, checkDiscountMap);
        }
        if (CollUtil.isEmpty(memberCouponGuids)) {
            return;
        }
        //新增使用记录
        saveMemberUse(orderInfo, memberCouponGuids, checkDiscountMap, memberCoupons);
    }

    /**
     * 新增优惠券使用记录
     *
     * @param orderInfo         订单信息
     * @param memberCouponGuids 会员优惠券guid
     * @param checkDiscountMap  选中类型
     */
    private void saveMemberUse(SettlementLockedOrderInfoDTO orderInfo,
                               List<String> memberCouponGuids,
                               Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap,
                               List<HsaMemberCouponLink> memberCoupons) {
        //新增使用
        List<HsaMemberCouponUse> recordList = new ArrayList<>();
        final List<String> guids = guidGeneratorUtil.getGuidsNew(HsaMemberCouponUse.class.getSimpleName(), memberCouponGuids.size());
        if (StringUtils.isEmpty(orderInfo.getOperatorAccountName()) || SourceTypeEnum.isMallSource(orderInfo.getSource())) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(orderInfo.getMemberInfoGuid());
            orderInfo.setOperatorAccountName(hsaOperationMemberInfo.getUserName() + "/" + hsaOperationMemberInfo.getPhoneNum());
            orderInfo.setUserName(hsaOperationMemberInfo.getUserName());
            orderInfo.setUserPhone(hsaOperationMemberInfo.getPhoneNum());
            log.info("operatorAccountName memberInfo:{}", orderInfo.getOperatorAccountName());
        }
        MemberCouponAssembler.addRecordList(
                orderInfo,
                memberCouponGuids,
                checkDiscountMap,
                guids,
                recordList,
                memberCoupons);
        this.saveBatch(recordList);
    }

    /**
     * 更新已有数据
     *
     * @param orderInfo         订单
     * @param memberCouponGuids 优惠券
     * @param list              已有优惠券
     * @param checkDiscountMap  选中类型
     */
    private void updateOldMemberUse(SettlementLockedOrderInfoDTO orderInfo,
                                    List<String> memberCouponGuids,
                                    List<HsaMemberCouponUse> list,
                                    Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap) {
        for (HsaMemberCouponUse u : list) {
            //更新锁定时间
            u.setLockTime(orderInfo.getOrderTime());
            //优惠金额
            Optional.ofNullable(checkDiscountMap.get(u.getMemberCouponLinkGuid()))
                    .ifPresent(req -> u.setDiscountAmount(req.getDiscountAmount()));
            //实付
            u.setOrderPaidAmount(orderInfo.getOrderPaidAmount());
            //操作人
            u.setOperatorAccountName(orderInfo.getOperatorAccountName());
        }
        //移除存在
        final Set<String> codeSet = list.stream().map(HsaMemberCouponUse::getMemberCouponLinkGuid).collect(Collectors.toSet());
        memberCouponGuids.removeIf(codeSet::contains);
        //更新
        this.updateBatchById(list);
    }

    /**
     * 构造选中map
     *
     * @param lockedDiscount 锁定参数
     * @param memberCoupons  优惠券
     * @return couponGuid, req
     */
    private Map<String, SettlementLockedDiscountReqDTO> getCheckDiscountMap(SettlementOrderLockDTO lockedDiscount,
                                                                            List<HsaMemberCouponLink> memberCoupons) {
        final List<SettlementLockedDiscountReqDTO> checkDiscountList = lockedDiscount.getCheckDiscountList();
        final Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap = new HashMap<>();
        for (HsaMemberCouponLink c : memberCoupons) {
            for (SettlementLockedDiscountReqDTO d : checkDiscountList) {
                if (d.getDiscountGuid().equals(c.getCouponCode())
                        && d.getDiscountOptionId().equals(c.getCode())) {
                    checkDiscountMap.put(c.getGuid(), d);
                    break;
                }
            }
        }
        return checkDiscountMap;
    }

    @Override
    public void markUse(CouponMarkUseQO qo) {
        //过滤逻辑删除
        if (qo.getCouponActivityDTO().getCouponType() != CouponTypeEnum.COUPON_EXCHANGE.getCode()) {
            final LambdaQueryWrapper<HsaMemberCouponUse> queryWrapper = new LambdaQueryWrapper<HsaMemberCouponUse>()
                    .eq(HsaMemberCouponUse::getMemberCouponLinkGuid, qo.getCouponGuid());
            final HsaMemberCouponUse couponUse = this.getOne(queryWrapper);
            if (Objects.nonNull(couponUse)) {
                //已核销过
                log.error("优惠券已核销过:{}", JacksonUtils.writeValueAsString(qo));
                return;
            }
        }
        final String guid = guidGeneratorUtil.getStringGuid(HsaMemberCouponUse.class.getSimpleName());
        HsaMemberCouponUse hsaMemberCouponUse = MemberCouponAssembler.toHsaMemberCouponUse(qo, guid);
        //订单相关
        Optional.ofNullable(qo.getOrderNumber())
                .ifPresent(orderNumber -> {
                    CrmOrderDetailQo crmOrderDetailQo = new CrmOrderDetailQo();
                    crmOrderDetailQo.setOrderCode(orderNumber);
                    //查询订单
                    final CrmOrderDetailVo orderInfo = externalSupport.storeServer(ThreadLocalCache.getSystem()).getOrderDetail(crmOrderDetailQo);
                    log.info("markUse crm: orderInfo:{}", JacksonUtils.writeValueAsString(orderInfo));
                    if (Objects.nonNull(orderInfo)) {

                        //实付
                        hsaMemberCouponUse.setOrderPaidAmount(orderInfo.getAmountPaid());
                    }
                });
        this.save(hsaMemberCouponUse);

    }

    @Override
    public List<HsaMemberCouponUse> listByOrderNumber(String operSubjectGuid, String orderNumber,
                                                      List<String> discountIdList) {
        final LambdaQueryWrapper<HsaMemberCouponUse> queryWrapper = new LambdaQueryWrapper<HsaMemberCouponUse>()
                .eq(HsaMemberCouponUse::getOperSubjectGuid, operSubjectGuid)
                .in(CollUtil.isNotEmpty(discountIdList), HsaMemberCouponUse::getCode, discountIdList)
                .eq(HsaMemberCouponUse::getOrderNumber, orderNumber);
        return this.list(queryWrapper);
    }

    @Override
    public void afterPayCoupon(String operSubjectGuid, String orderNumber, LocalDateTime payTime) {
        //更新支付时间
        HsaMemberCouponUse couponUse = new HsaMemberCouponUse();
        couponUse.setPayTime(payTime);

        final LambdaUpdateWrapper<HsaMemberCouponUse> updateWrapper = new UpdateWrapper<HsaMemberCouponUse>()
                .lambda()
                .eq(HsaMemberCouponUse::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaMemberCouponUse::getOrderNumber, orderNumber);

        this.update(couponUse, updateWrapper);
    }
}
