package com.holderzone.member.base.manage;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.base.assembler.CouponPackageAssembler;
import com.holderzone.member.base.dto.UsageDataBatch;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponPackageLinkMapper;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponUseMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.common.constant.ExcelExportConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.ItemNum;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyDiscountBaseReqDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.coupon.*;
import com.holderzone.member.common.qrcode.QrCodeSupport;
import com.holderzone.member.common.util.DesensitizedUtils;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.excel.ExcelUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.coupon.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Objects;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优惠劵查询
 *
 * <AUTHOR>
 * @date 2023/8/21
 **/
@Slf4j
@Component
public class MemberCouponQueryManage {

    @Resource
    private HsaMemberCouponLinkMapper memberCouponLinkMapper;

    @Resource
    private HsaMemberCouponUseMapper memberCouponUseMapper;

    @Resource
    private HsaMemberCouponPackageLinkMapper memberCouponPackageLinkMapper;

    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;

    @Resource
    private HsaOperationMemberInfoMapper operationMemberInfoMapper;

    @Resource
    private QrCodeSupport qrCodeSupport;

    /**
     * 即将过期时间：小时
     */
    private static final int BE_ABOUT_HOUR = 72;


    public PageResult<CouponPackageGiveVO> pagePackageDetail(CouponPackageGiveQO qo) {
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        //查询 券包发放明细
        List<HsaMemberCouponPackageLink> packageLinkList = memberCouponPackageLinkMapper.listDetail(qo);
        List<CouponPackageGiveVO> packageGiveVOList = CouponPackageAssembler.toCouponPackageGiveVOs(packageLinkList);
        return PageUtil.pageResult(packageLinkList, packageGiveVOList);
    }

    public CouponPackageGiveCountVO countPackageGiveNum(CouponPackageGiveQO qo) {
        //按发券状态统计
        final List<ItemNum> itemNumList = memberCouponPackageLinkMapper.countGiveNum(qo);
        final Map<Integer, Integer> couponNumMap = itemNumList.stream().collect(Collectors.toMap(i -> Integer.parseInt(i.getItem()), ItemNum::getNum));
        CouponPackageGiveCountVO vo = new CouponPackageGiveCountVO();
        //发放成功数
        vo.setGiveSuccessNum(couponNumMap.getOrDefault(BooleanEnum.TRUE.getCode(), 0));
        //发放失败数
        vo.setGiveFailNum(couponNumMap.getOrDefault(BooleanEnum.FALSE.getCode(), 0));
        return vo;
    }

    public PageResult<CouponGiveVO> pageCouponDetail(CouponGiveQO qo) {
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        //查询劵明细
        List<HsaMemberCouponLink> packageLinkList = memberCouponLinkMapper.listDetail(qo);
        List<CouponGiveVO> packageGiveVOList = CouponPackageAssembler.toCouponGiveVOs(packageLinkList);

        //兑换券使用次数
        dealUsageSituation(packageGiveVOList);
        return PageUtil.pageResult(packageLinkList, packageGiveVOList);
    }

    private void dealUsageSituation(List<CouponGiveVO> packageGiveVOList) {
        if (CollUtil.isNotEmpty(packageGiveVOList)) {
            List<CouponGiveRuleDTO> couponRuleList = Lists.newArrayList();
            for (CouponGiveVO couponGiveVO : packageGiveVOList) {
                CouponGiveRuleDTO couponGiveRule = new CouponGiveRuleDTO();
                BeanUtils.copyProperties(couponGiveVO, couponGiveRule);
                couponGiveRule.setExchangeLimit(couponGiveVO.getRuleVO().getExchangeLimit());
                couponGiveRule.setExchangeTimes(couponGiveVO.getRuleVO().getExchangeTimes());
                couponRuleList.add(couponGiveRule);
            }
            Map<String, String> usedNumberMap = setUsedNumber(couponRuleList);
            if (CollUtil.isNotEmpty(usedNumberMap)) {
                for (CouponGiveVO couponGiveVO : packageGiveVOList) {
                    if (usedNumberMap.containsKey(couponGiveVO.getCouponGuid())) {
                        couponGiveVO.setUsageSituation(usedNumberMap.get(couponGiveVO.getCouponGuid()));
                    }
                }
            }
        }
    }

    /**
     * 设置优惠券使用次数信息
     * 
     * @param packageGiveVOList 优惠券规则列表
     * @return 使用情况映射 (couponGuid -> 使用情况描述)
     */
    private Map<String, String> setUsedNumber(List<CouponGiveRuleDTO> packageGiveVOList) {
        if (CollUtil.isEmpty(packageGiveVOList)) {
            return Collections.emptyMap();
        }
        
        // 过滤出兑换券
        List<CouponGiveRuleDTO> exchangeCoupons = packageGiveVOList.stream()
                .filter(coupon -> Objects.equals(CouponTypeEnum.COUPON_EXCHANGE.getCode(), coupon.getCouponType()))
                .collect(Collectors.toList());
                
        if (CollUtil.isEmpty(exchangeCoupons)) {
            return Collections.emptyMap();
        }

        // 批量获取使用数据
        UsageDataBatch usageData = batchGetUsageData(exchangeCoupons);
        
        // 计算并构建使用情况描述
        return buildUsageSituationMap(packageGiveVOList, usageData);
    }

    /**
     * 批量获取使用数据
     */
    private UsageDataBatch batchGetUsageData(List<CouponGiveRuleDTO> exchangeCoupons) {
        // 提取所需的ID列表
        List<String> guidList = exchangeCoupons.stream()
                .map(CouponGiveRuleDTO::getCouponGuid)
                .collect(Collectors.toList());
        List<String> codeList = exchangeCoupons.stream()
                .map(CouponGiveRuleDTO::getCode)
                .collect(Collectors.toList());
        
        // 并行获取使用记录和占用次数
        CompletableFuture<Map<String, List<HsaMemberCouponUse>>> usedRecordsFuture = 
                CompletableFuture.supplyAsync(() -> getMemberCouponUseMapByNum(guidList));
        CompletableFuture<Map<String, Integer>> occupiedCountsFuture = 
                CompletableFuture.supplyAsync(() -> getCodeUseMapByNum(codeList));
        
        try {
            return new UsageDataBatch(
                    usedRecordsFuture.get(),
                    occupiedCountsFuture.get()
            );
        } catch (InterruptedException e) {
            log.warn("批量获取使用数据被中断", e);
            // 重新设置中断状态
            Thread.currentThread().interrupt();
            // 降级处理：同步获取
            return new UsageDataBatch(
                    getMemberCouponUseMapByNum(guidList),
                    getCodeUseMapByNum(codeList)
            );
        } catch (ExecutionException e) {
            log.warn("批量获取使用数据执行异常", e);
            // 降级处理：同步获取
            return new UsageDataBatch(
                    getMemberCouponUseMapByNum(guidList),
                    getCodeUseMapByNum(codeList)
            );
        }
    }

    /**
     * 构建使用情况描述映射
     */
    private Map<String, String> buildUsageSituationMap(List<CouponGiveRuleDTO> allCoupons, 
                                                       UsageDataBatch usageData) {
        Map<String, String> usageSituationMap = new HashMap<>(allCoupons.size());
        
        for (CouponGiveRuleDTO coupon : allCoupons) {
            // 只处理兑换券且验证必要字段
            if (!Objects.equals(CouponTypeEnum.COUPON_EXCHANGE.getCode(), coupon.getCouponType()) 
                    || !isValidExchangeCoupon(coupon)) {
                continue;
            }
            
            // 计算已使用次数
            int usedCount = calculateUsedCount(coupon, usageData);
            
            // 生成使用情况描述
            String usageSituation = generateUsageSituation(coupon, usedCount);
            usageSituationMap.put(coupon.getCouponGuid(), usageSituation);
        }
        
        return usageSituationMap;
    }

    /**
     * 验证兑换券是否有效
     */
    private boolean isValidExchangeCoupon(CouponGiveRuleDTO coupon) {
        return Objects.nonNull(coupon.getExchangeTimes()) 
                && Objects.nonNull(coupon.getExchangeLimit())
                && StringUtils.hasText(coupon.getCouponGuid());
    }

    /**
     * 计算已使用次数
     */
    private int calculateUsedCount(CouponGiveRuleDTO coupon, UsageDataBatch usageData) {
        int usedCount = 0;
        
        // 已使用次数（从使用记录获取）
        List<HsaMemberCouponUse> usedRecords = usageData.getUsedRecordsMap()
                .get(coupon.getCouponGuid());
        if (usedRecords != null) {
            usedCount += usedRecords.size();
        }
        
        // 已占用次数（从订单折扣服务获取）
        Integer occupiedCount = usageData.getOccupiedCountsMap()
                .get(coupon.getCode());
        if (occupiedCount != null) {
            usedCount += occupiedCount;
        }
        
        return usedCount;
    }

    /**
     * 生成使用情况描述
     */
    private String generateUsageSituation(CouponGiveRuleDTO coupon, int usedCount) {
        // 无使用限制
        if (Objects.equals(BooleanEnum.FALSE.getCode(), coupon.getExchangeLimit())) {
            return String.format("已使用%d次，还剩不限次", usedCount);
        }
        
        // 有使用限制
        int remainingCount = Math.max(0, coupon.getExchangeTimes() - usedCount);
        // 更新剩余次数到对象中（保持原有逻辑）
        coupon.setExchangeTimes(remainingCount);
        
        return String.format("已使用%d次，还剩%d次", usedCount, remainingCount);
    }

    /**
     * 获取优惠券码占用次数映射
     */
    private Map<String, Integer> getCodeUseMapByNum(List<String> codeList) {
        if (CollUtil.isEmpty(codeList)) {
            return Collections.emptyMap();
        }
        
        return hsaMemberOrderDiscountService.getUsedCouponNum(
                codeList,
                BooleanEnum.FALSE.getCode(),
                SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode(), 
                null);
    }

    /**
     * 获取会员优惠券使用记录映射
     */
    private Map<String, List<HsaMemberCouponUse>> getMemberCouponUseMapByNum(List<String> guidList) {
        if (CollUtil.isEmpty(guidList)) {
            return Collections.emptyMap();
        }
        
        List<HsaMemberCouponUse> usedCoupons = memberCouponUseMapper.selectList(
                new LambdaQueryWrapper<HsaMemberCouponUse>()
                        .in(HsaMemberCouponUse::getMemberCouponLinkGuid, guidList));

        return usedCoupons.stream()
                .collect(Collectors.groupingBy(HsaMemberCouponUse::getMemberCouponLinkGuid));
    }



    public CouponGiveCountVO countGiveNum(CouponGiveQO qo) {
        final CouponGiveCountVO countVO = new CouponGiveCountVO();
        //发放数
        countVO.setGiveCouponNum(memberCouponLinkMapper.countGiveNum(qo));
        //未核销 = 未过期、未失效 - 已核销
        countVO.setUnwrittenNum(memberCouponLinkMapper.countUnwrittenNumByCoupon(qo));
        //失效数
        countVO.setInvalidNum(memberCouponLinkMapper.countOnlyInvalidNum(qo));
        //已核销
        countVO.setWrittenNum(memberCouponLinkMapper.countWrittenNumByCoupon(qo));
        //核销率
        final BigDecimal writeRate = BigDecimalUtil.rate(countVO.getWrittenNum(), countVO.getGiveCouponNum());
        log.info("发放明细-核销率：{}/{} x 100 = {}", countVO.getWrittenNum(), countVO.getGiveCouponNum(), writeRate);
        countVO.setWriteRate(writeRate);
        //过期数
        countVO.setExpiresNum(memberCouponLinkMapper.countExpireNum(qo));
        return countVO;
    }

    public PageResult<MemberCouponVO> memberPageCouponDetail(MemberCouponQO qo) {
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        //会员已领优惠券
        List<HsaMemberCouponLink> couponLinks = memberCouponLinkMapper.memberPageCouponDetail(qo);
        List<MemberCouponVO> couponVOList = CouponPackageAssembler.toMemberCouponVOs(couponLinks);
        if (CollUtil.isEmpty(couponVOList)) {
            return PageUtil.emptyPageResult();
        }
        //兑换券使用次数
        dealMemberUsageSituation(couponVOList);
        //填充核销记录
        fillUseCouponDetail(couponVOList);
        return PageUtil.pageResult(couponLinks, couponVOList);
    }

    private void dealMemberUsageSituation(List<MemberCouponVO> couponVOList) {
        if (CollUtil.isNotEmpty(couponVOList)) {
            List<CouponGiveRuleDTO> couponRuleList = Lists.newArrayList();
            for (MemberCouponVO couponGiveVO : couponVOList) {
                CouponGiveRuleDTO couponGiveRule = new CouponGiveRuleDTO();
                BeanUtils.copyProperties(couponGiveVO, couponGiveRule);
                couponGiveRule.setCouponGuid(couponGiveVO.getGuid());
                couponGiveRule.setCouponType(couponGiveVO.getCouponType());
                couponGiveRule.setExchangeLimit(couponGiveVO.getRuleVO().getExchangeLimit());
                couponGiveRule.setExchangeTimes(couponGiveVO.getRuleVO().getExchangeTimes());
                couponRuleList.add(couponGiveRule);
            }
            Map<String, String> usedNumberMap = setUsedNumber(couponRuleList);
            if (CollUtil.isNotEmpty(usedNumberMap)) {
                for (MemberCouponVO couponGiveVO : couponVOList) {
                    if (usedNumberMap.containsKey(couponGiveVO.getGuid())) {
                        couponGiveVO.setUsageSituation(usedNumberMap.get(couponGiveVO.getGuid()));
                    }
                }
            }
        }
    }

    /**
     * 填充核销记录
     *
     * @param couponVOList 优惠券
     */
    private void fillUseCouponDetail(List<MemberCouponVO> couponVOList) {
        //查询核销记录
        final List<String> couponLinksGuidList = couponVOList.stream().map(MemberCouponVO::getGuid).collect(Collectors.toList());
        final LambdaQueryWrapper<HsaMemberCouponUse> queryWrapper = new LambdaQueryWrapper<HsaMemberCouponUse>()
                .in(HsaMemberCouponUse::getMemberCouponLinkGuid, couponLinksGuidList);
        final List<HsaMemberCouponUse> couponUseList = memberCouponUseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(couponUseList)) {
            return;
        }
        dealGroupingByUse(couponVOList, couponUseList);
    }

    private static void dealGroupingByUse(List<MemberCouponVO> couponVOList, List<HsaMemberCouponUse> couponUseList) {
        Map<String, List<HsaMemberCouponUse>> memberCouponUseMap = couponUseList.stream()
                .collect(Collectors.groupingBy(HsaMemberCouponUse::getMemberCouponLinkGuid));

        for (MemberCouponVO couponVO : couponVOList) {
            if (memberCouponUseMap.containsKey(couponVO.getGuid())) {
                List<HsaMemberCouponUse> hsaMemberCouponUseList = memberCouponUseMap.get(couponVO.getGuid());
                if (CollUtil.isNotEmpty(hsaMemberCouponUseList)) {
                    dealCouponVO(couponVO, hsaMemberCouponUseList);
                }
            }
        }
    }

    private static void dealCouponVO(MemberCouponVO couponVO, List<HsaMemberCouponUse> hsaMemberCouponUseList) {
        if (hsaMemberCouponUseList.size() > 1) {
            //按时间排序
            hsaMemberCouponUseList = hsaMemberCouponUseList.stream()
                    .sorted(Comparator.comparing(HsaMemberCouponUse::getGmtCreate)).collect(Collectors.toList());
            HsaMemberCouponUse couponUse = hsaMemberCouponUseList.get(0);
            hsaMemberCouponUseList.remove(0);
            List<MemberCouponUseBaseVO> memberCouponUseBaseVOS = Lists.newArrayList();
            for (HsaMemberCouponUse hsaMemberCouponUse : hsaMemberCouponUseList) {
                MemberCouponUseBaseVO memberCouponUseBaseVO = new MemberCouponUseBaseVO();
                memberCouponUseBaseVO.setUseStoreName(hsaMemberCouponUse.getStoreName());
                memberCouponUseBaseVO.setUseTime(hsaMemberCouponUse.getUseTime());
                memberCouponUseBaseVO.setOrderNumber(hsaMemberCouponUse.getOrderNumber());
                memberCouponUseBaseVOS.add(memberCouponUseBaseVO);
            }
            setUse(couponVO, couponUse);
            couponVO.setMemberCouponUseBaseVOS(memberCouponUseBaseVOS);
        } else {
            HsaMemberCouponUse couponUse = hsaMemberCouponUseList.get(0);
            setUse(couponVO, couponUse);
        }
    }

    private static void setUse(MemberCouponVO couponVO, HsaMemberCouponUse couponUse) {
        //核销时间
        couponVO.setUseTime(couponUse.getUseTime());
        //订单号
        couponVO.setOrderNumber(couponUse.getOrderNumber());
        //门店名称
        couponVO.setUseStoreName(couponUse.getStoreName());
    }

    public void memberExportDetail(MemberCouponQO qo, HttpServletResponse response) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //导出标识，排除多余数据
        qo.setIsExport(BooleanEnum.TRUE.getCode());
        ExcelUtil.setPageSize(qo);
        //会员已领优惠券数量
        final PageResult<MemberCouponVO> pageResult = memberPageCouponDetail(qo);
        final List<MemberCouponVO> couponLinks = pageResult.getRecords();
        if (CollectionUtils.isEmpty(couponLinks)) {
            return;
        }
        //最大导出数校验
        if (couponLinks.size() > ExcelExportConstant.MAX_EXPORT_NUM) {
            throw new MemberMarketingException(MemberAccountExceptionEnum.ERROR_SUBSIDY_MAX_EXPORT_EXCEL);
        }
        try {
            //转换导出结构
            List<MemberCouponExportVO> exportVOList = CouponPackageAssembler.toMemberCouponExportVOs(couponLinks);
            HsaOperationMemberInfo memberDetail = operationMemberInfoMapper.queryByGuid(qo.getMemberGuid());
            //手机号脱敏
            final String phoneNum = DesensitizedUtils.desensitizedFilePhone(memberDetail.getPhoneNum());
            //文件名： 155xxxx0001优惠券2023-08-23
            String fileName = phoneNum + StringConstant.COUPON + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.DATE_PATTERN);
            fileName = URLEncoder.encode(fileName, ExcelExportConstant.UTF_8);
            response.setContentType(ExcelExportConstant.CONTENT_TYPE);
            response.setHeader(ExcelExportConstant.HEADER, ExcelExportConstant.ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
            response.setCharacterEncoding(ExcelExportConstant.UTF_8);
            //导出数据
            EasyExcel.write(response.getOutputStream(), MemberCouponExportVO.class)
                    .sheet(ExcelExportConstant.DEFAULT_SHEET)
                    .doWrite(exportVOList);
        } catch (Exception e) {
            throw new MemberMarketingException("导出会员已领优惠劵数据失败 + " + e.getMessage());
        }
    }

    public MemberCouponCountVO memberCountGiveNum(MemberCouponQO qo) {
        MemberCouponCountVO vo = new MemberCouponCountVO();
        //可用数量
        vo.setUnwrittenNum(memberCouponLinkMapper.countUnwrittenNum(qo));
        //过期数量
        vo.setInvalidNum(memberCouponLinkMapper.countInvalidNum(qo));
        //统计核销
        vo.setWrittenNum(memberCouponLinkMapper.countWrittenNum(qo));
        return vo;
    }

    public List<ItemNum> countCoupon(CouponListCountQO qo) {
        return memberCouponLinkMapper.countCoupon(qo);
    }

    public Map<String, CouponListCountVO> countListCoupon(CouponListCountQO qo) {
        //发放数
        final List<ItemNum> itemNumList = memberCouponLinkMapper.countCoupon(qo);
        if (CollUtil.isEmpty(itemNumList)) {
            return Collections.emptyMap();
        }
        //发放数
        Map<String, CouponListCountVO> resultMap = new HashMap<>();
        for (ItemNum itemNum : itemNumList) {
            final CouponListCountVO countVO = resultMap.getOrDefault(itemNum.getItem(), new CouponListCountVO());
            countVO.setGivenCount(itemNum.getNum());
            resultMap.put(itemNum.getItem(), countVO);
        }
        //核销数
        final List<ItemNum> itemNumUseList = memberCouponLinkMapper.countUseCoupon(qo);
        for (ItemNum itemNum : itemNumUseList) {
            final CouponListCountVO countVO = resultMap.getOrDefault(itemNum.getItem(), new CouponListCountVO());
            countVO.setUseCount(itemNum.getNum());
            resultMap.put(itemNum.getItem(), countVO);
        }
        return resultMap;
    }

    /**
     * 统计发放、核销、券包数量
     *
     * @param qo 参数
     * @return
     */
    public Map<String, CouponPackageCountVO> countPackageActivity(PackageListCountQO qo) {
        //券 发放成功记录数
        final List<ItemNum> couponNumList = memberCouponLinkMapper.countCouponPackage(qo);
        //核销数
        final List<ItemNum> couponUseNumList = memberCouponLinkMapper.countUseCouponPackage(qo);
        //券包 发放成功记录数
        final List<ItemNum> couponPackageNumList = memberCouponPackageLinkMapper.countCouponPackage(qo);
        //key为券包code
        final Map<String, Integer> couponNumMap = couponNumList.stream().collect(Collectors.toMap(ItemNum::getItem, ItemNum::getNum));
        final Map<String, Integer> couponUseNumMap = couponUseNumList.stream().collect(Collectors.toMap(ItemNum::getItem, ItemNum::getNum));
        final Map<String, Integer> couponPackageNumMap = couponPackageNumList.stream().collect(Collectors.toMap(ItemNum::getItem, ItemNum::getNum));
        Set<String> keys = new HashSet<>();
        keys.addAll(couponNumMap.keySet());
        keys.addAll(couponUseNumMap.keySet());
        keys.addAll(couponPackageNumMap.keySet());
        Map<String, CouponPackageCountVO> countVoMap = new HashMap<>(keys.size());
        for (String key : keys) {
            CouponPackageCountVO vo = new CouponPackageCountVO();
            //券记录数
            Optional.ofNullable(couponNumMap.get(key)).ifPresent(vo::setCouponNum);
            //核销数
            Optional.ofNullable(couponUseNumMap.get(key)).ifPresent(vo::setWriteNum);
            //券包记录数
            Optional.ofNullable(couponPackageNumMap.get(key)).ifPresent(vo::setCouponPackageNum);
            countVoMap.put(key, vo);
        }
        return countVoMap;
    }

    public MemberCouponWxCountVO countMemberNum(MemberCouponNumQO qo) {
        MemberCouponWxCountVO vo = new MemberCouponWxCountVO();
        //可用数
        vo.setUnwrittenNum(memberCouponLinkMapper.countMemberNum(qo));
        //三天后过期
        qo.setExpireDate(LocalDateTime.now().plusHours(BE_ABOUT_HOUR));
        vo.setBeAboutNum(memberCouponLinkMapper.countMemberNum(qo));
        return vo;
    }

    public List<MemberCouponWxVO> pageMemberCouponByTime(MemberCouponListQO qo) {
        //按时间分页查询会员已领券
        List<HsaMemberCouponLink> couponLinks = memberCouponLinkMapper.pageMemberCouponByTime(qo);
        final List<MemberCouponWxVO> couponWxVOList = CouponPackageAssembler.toMemberCouponWxVos(couponLinks, qo);
        //已使用情况
        couponUsageSituation(couponWxVOList);
        //可用券
        if (qo.getUseType() == BooleanEnum.FALSE.getCode()) {
            //近3天内过期的
            final LocalDateTime beAboutTime = LocalDateTime.now().plusHours(BE_ABOUT_HOUR);
            couponWxVOList.forEach(v -> {
                if (v.getCouponEffectiveEndTime().isBefore(beAboutTime)) {
                    //即将过期标志
                    v.setBeAboutState(BooleanEnum.TRUE.getCode());
                }
            });
        }
        return couponWxVOList;
    }

    /**
     * 分页查询会员优惠券（按时间排序）
     * 
     * @param qo 查询条件
     * @return 分页结果
     */
    public PageResult<MemberCouponWxVO> pageableMemberCouponByTime(MemberCouponQO qo) {
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        
        // 将MemberCouponQO转换为MemberCouponListQO以适配数据层查询
        MemberCouponListQO listQO = convertToListQO(qo);
        
        //按时间分页查询会员已领券
        List<HsaMemberCouponLink> couponLinks = memberCouponLinkMapper.pageableMemberCouponByTime(listQO);
        final List<MemberCouponWxVO> couponWxVOList = CouponPackageAssembler.toMemberCouponWxVos(couponLinks, listQO);
        
        if (CollUtil.isEmpty(couponWxVOList)) {
            return PageUtil.emptyPageResult();
        }
        
        //已使用情况
        couponUsageSituation(couponWxVOList);
        //可用券 - 假设可用券对应useType=0
        if (listQO.getUseType() == BooleanEnum.FALSE.getCode()) {
            //近3天内过期的
            final LocalDateTime beAboutTime = LocalDateTime.now().plusHours(BE_ABOUT_HOUR);
            couponWxVOList.forEach(v -> {
                if (v.getCouponEffectiveEndTime().isBefore(beAboutTime)) {
                    //即将过期标志
                    v.setBeAboutState(BooleanEnum.TRUE.getCode());
                }
            });
        }
        return PageUtil.pageResult(couponLinks, couponWxVOList);
    }

    /**
     * 将MemberCouponQO转换为MemberCouponListQO
     * 
     * @param qo MemberCouponQO对象
     * @return MemberCouponListQO对象
     */
    private MemberCouponListQO convertToListQO(MemberCouponQO qo) {
        MemberCouponListQO listQO = new MemberCouponListQO();
        listQO.setMemberGuid(qo.getMemberGuid());
        listQO.setOperSubjectGuid(qo.getOperSubjectGuid());
        
        // 根据状态设置useType: 可用=0, 历史=1
        // 根据CouponMemberStateEnum判断：UN_EXPIRE(3)为可用状态，其他为历史状态
        if (qo.getState() != null && Objects.equals(qo.getState(), com.holderzone.member.common.enums.coupon.CouponMemberStateEnum.UN_EXPIRE.getCode())) {
            listQO.setUseType(BooleanEnum.FALSE.getCode()); // 可用券
        } else {
            listQO.setUseType(BooleanEnum.TRUE.getCode()); // 历史券
        }
        
        return listQO;
    }

    private void couponUsageSituation(List<MemberCouponWxVO> couponWxVOList) {
        if (CollUtil.isNotEmpty(couponWxVOList)) {
            List<CouponGiveRuleDTO> couponRuleList = Lists.newArrayList();
            for (MemberCouponWxVO couponGiveVO : couponWxVOList) {
                CouponGiveRuleDTO couponGiveRule = new CouponGiveRuleDTO();
                BeanUtils.copyProperties(couponGiveVO, couponGiveRule);
                couponGiveRule.setCouponGuid(couponGiveVO.getGuid());
                couponGiveRule.setCouponType(couponGiveVO.getCouponType());
                couponGiveRule.setExchangeLimit(couponGiveVO.getExchangeLimit());
                couponGiveRule.setExchangeTimes(couponGiveVO.getExchangeTimes());
                couponRuleList.add(couponGiveRule);
            }
            Map<String, String> usedNumberMap = setUsedNumber(couponRuleList);

            Map<String, CouponGiveRuleDTO> couponGiveRuleDTOMap = couponRuleList.stream()
                    .collect(Collectors.toMap(CouponGiveRuleDTO::getCouponGuid, Function.identity(), (obj, obj1) -> obj));
            checkExchangeTimes(couponWxVOList, usedNumberMap, couponGiveRuleDTOMap);
        }
    }

    private static void checkExchangeTimes(List<MemberCouponWxVO> couponWxVOList,
                                           Map<String, String> usedNumberMap,
                                           Map<String, CouponGiveRuleDTO> couponGiveRuleDTOMap) {
        if (CollUtil.isNotEmpty(usedNumberMap)) {
            for (MemberCouponWxVO couponGiveVO : couponWxVOList) {
                if (usedNumberMap.containsKey(couponGiveVO.getGuid())) {
                    couponGiveVO.setUsageSituation(usedNumberMap.get(couponGiveVO.getGuid()));
                    if (couponGiveVO.getExchangeLimit() == BooleanEnum.TRUE.getCode()) {
                        couponGiveVO.setExchangeTimes(couponGiveRuleDTOMap.get(couponGiveVO.getGuid()).getExchangeTimes());
                    }
                }
            }
        }
    }

    /**
     * 结算规则：可用券
     *
     * @param dto 入参
     * @return 优惠券
     */
    public List<HsaMemberCouponLink> listByUseSettlement(Integer couponType, SettlementApplyOrderDTO dto) {
        //处理指定优惠券查询
        dealDiscountOptionIdList(dto);
        //构造查询参数
        final MemberCouponListQO qo = MemberCouponListQO.buildUseQuery(dto, couponType);
        //按时间分页查询会员已领券
        List<HsaMemberCouponLink> hasMemberCouponLinks = memberCouponLinkMapper.listByUseSettlement(qo);
        if (StringUtils.isEmpty(qo.getMemberGuid()) && !StringUtils.isEmpty(dto.getOrderInfo().getOrderGuid())) {
            // 查询直接绑定到订单上的优惠券
            qo.setOrderGuid(dto.getOrderInfo().getOrderGuid());
            List<HsaMemberCouponLink> orderCouponLinks = memberCouponLinkMapper.listByUseSettlement(qo);
            if (!CollectionUtils.isEmpty(orderCouponLinks)) {
                hasMemberCouponLinks.addAll(orderCouponLinks);
            }
        }
        return hasMemberCouponLinks;
    }

    private static void dealDiscountOptionIdList(SettlementApplyOrderDTO dto) {
        if (CollUtil.isNotEmpty(dto.getCheckDiscountList())) {
            List<SettlementApplyDiscountBaseReqDTO> checkDiscountList =
                    dto.getCheckDiscountList().stream().filter(in -> SettlementDiscountOptionEnum.isCoupon(in.getDiscountOption()))
                            .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(checkDiscountList)) {
                Set<String> discountOptionIdSet = new HashSet<>();
                for (SettlementApplyDiscountBaseReqDTO settlementApplyDiscountBaseReqDTO : checkDiscountList) {
                    if (CollUtil.isNotEmpty(settlementApplyDiscountBaseReqDTO.getDiscountOptionIds())) {
                        discountOptionIdSet.addAll(settlementApplyDiscountBaseReqDTO.getDiscountOptionIds());
                    }
                }
                if (CollUtil.isNotEmpty(discountOptionIdSet)) {
                    dto.setDiscountOptionId(new ArrayList<>(discountOptionIdSet));
                }
            }
        }
    }

    public MemberCouponWxDetailVO detail(String memberCouponGuid) {
        //券详情
        final HsaMemberCouponLink detail = memberCouponLinkMapper.detail(memberCouponGuid);
        if (detail == null) {
            throw new MemberBaseException("数据不存在");
        }
        MemberCouponWxDetailVO memberCoupon = CouponPackageAssembler.toMemberCouponWxDetailVO(detail);
        List<CouponGiveRuleDTO> couponRuleList = Lists.newArrayList();
        CouponGiveRuleDTO couponGiveRule = new CouponGiveRuleDTO();
        BeanUtils.copyProperties(memberCoupon, couponGiveRule);
        couponGiveRule.setCouponGuid(memberCoupon.getGuid());
        couponGiveRule.setCouponType(memberCoupon.getCouponType());
        couponGiveRule.setExchangeLimit(memberCoupon.getExchangeLimit());
        couponGiveRule.setExchangeTimes(memberCoupon.getExchangeTimes());
        couponRuleList.add(couponGiveRule);
        Map<String, String> usedNumberMap = setUsedNumber(couponRuleList);
        Map<String, CouponGiveRuleDTO> couponGiveRuleDTOMap = couponRuleList.stream()
                .collect(Collectors.toMap(CouponGiveRuleDTO::getCouponGuid, Function.identity(), (obj, obj1) -> obj));
        if (CollUtil.isNotEmpty(usedNumberMap) && (usedNumberMap.containsKey(memberCoupon.getGuid()))) {
            memberCoupon.setUsageSituation(usedNumberMap.get(memberCoupon.getGuid()));
            if (memberCoupon.getExchangeLimit() == BooleanEnum.TRUE.getCode()) {
                memberCoupon.setExchangeTimes(couponGiveRuleDTOMap.get(memberCoupon.getGuid()).getExchangeTimes());
            }
        }
        return memberCoupon;
    }
}
