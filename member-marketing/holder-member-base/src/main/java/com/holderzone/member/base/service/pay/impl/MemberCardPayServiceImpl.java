package com.holderzone.member.base.service.pay.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.client.RequestGoalgoService;
import com.holderzone.member.base.client.StoreBaseService;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaCardPayRecord;
import com.holderzone.member.base.entity.card.HsaMemberCardRule;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.*;
import com.holderzone.member.base.mapper.card.*;
import com.holderzone.member.base.mapper.member.*;
import com.holderzone.member.base.service.card.HsaCardInfoService;
import com.holderzone.member.base.service.card.HsaElectronicCardService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.HsaMemberFundingDetailService;
import com.holderzone.member.base.service.pay.MemberCardPayService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.PaySettingBaseRes;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.base.WeChatAuthParamDTO;
import com.holderzone.member.common.dto.card.CardInfoDetailDTO;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import com.holderzone.member.common.dto.member.MemberCardOpenCardPayCheckDTO;
import com.holderzone.member.common.dto.member.MemberCardOpenCardPayDTO;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.*;
import com.holderzone.member.common.enums.exception.CardOperationExceptionEnum;
import com.holderzone.member.common.enums.exception.MemberInfoCardExceptionEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.SaasAggPayFeign;
import com.holderzone.member.common.qo.base.AppletStoreQO;
import com.holderzone.member.common.qo.card.MemberCardAggPayQO;
import com.holderzone.member.common.qo.card.MemberCardPayPollingQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.util.wechat.WeChatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_0;
import static com.holderzone.member.common.constant.NumberConstant.NUMBER_1;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-03-17 11:25
 */
@Slf4j
@Service
public class MemberCardPayServiceImpl implements MemberCardPayService {


    @Resource
    private HsaCardInfoService hsaCardInfoService;

    @Resource
    private HsaCardInfoMapper cardInfoMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaMemberConsumptionPayWayMapper hsaMemberConsumptionPayWayMapper;

    @Resource
    private HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    @Resource
    private HsaMemberFundingDetailService hsaMemberFundingDetailService;

    @Resource
    private SaasAggPayFeign saasAggPayFeign;

    @Resource
    private HsaCardPayRecordMapper hsaCardPayRecordMapper;

    @Resource
    private HsaMemberInfoCardMapper hsaMemberInfoCardMapper;

    @Resource
    private SystemRoleHelper systemRoleHelper;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private HsaMemberFundingDetailMapper hsaMemberFundingDetailMapper;

    @Resource
    private Executor memberBaseThreadExecutor;

    @Resource
    private HsaMemberInfoWeChatMapper hsaMemberInfoWeChatMapper;

    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private HsaCardInfoMapper hsaCardInfoMapper;

    @Resource
    private HsaMemberCardRuleMapper memberCardRuleMapper;

    @Resource
    private HsaMemberLabelMapper hsaMemberLabelMapper;

    @Resource
    private HsaLabelSettingService hsaLabelSettingService;

    @Resource
    private HsaElectronicCardService hsaElectronicCardService;

    @Value("${feign.base}")
    private String RequestBaseHost;

    @Resource
    private RequestGoalgoService hsaRequestGoalgoService;

    @Resource
    private StoreBaseService storeBaseService;

    private static final String FALSE_STRING = "false";
    
    // 支付相关常量
    private final static String BASE_CARD_PAY_CALLBACK = "%s/base/applets/card/callback";
    private static final String CARD_PAY_BODY = "会员卡支付/充值开通";
    private static final String CARD_PAY_GOODS_NAME = "会员卡支付/充值开通";
    private static final String PAY_POWER_ID = "53";
    private static final String CURRENCY_RMB = "RMB";

    /**
     * 会员卡预下单处理
     *
     * @param memberCardAggPayQO memberCardAggPayQO
     * @return AggPayDataDTO
     */
    @Override
    public MemberCardPerPayVO memberCardPerPay(MemberCardAggPayQO memberCardAggPayQO) {
        MemberCardPerPayVO memberCardPerPayVO = new MemberCardPerPayVO();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("headerUserInfo----->{}", JSON.toJSONString(headerUserInfo));
        if (memberCardAggPayQO.getBusinessType() == CardBusinessTypeEnum.RECHARGE_OPEN_CARD.getCode() || memberCardAggPayQO.getBusinessType() == CardBusinessTypeEnum.CONSUMPTION_OPEN_CARD.getCode()) {
            return openCardByConsumption(memberCardAggPayQO, memberCardPerPayVO, headerUserInfo);
        }
        return memberCardPerPayVO;
    }

    @Override
    public void openCardPayCheck(MemberCardOpenCardPayCheckDTO dto) {
        // 校验会员卡
        checkMemberInfoCard(dto.getCardGuid(), dto.getMemberInfoGuid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String openCardPay(MemberCardOpenCardPayDTO dto) {
        log.info("付费开卡参数：openCardPay----->{}", JSON.toJSONString(dto));

        if (StrUtil.isNotEmpty(dto.getOrderNumber())) {
            callbackOpenCardPay(dto);
            return dto.getOrderNumber();
        }

        // 校验会员卡
        checkMemberInfoCard(dto.getCardGuid(), dto.getMemberInfoGuid());

        MemberCardAggPayQO memberCardAggPayQO = new MemberCardAggPayQO();
        memberCardAggPayQO.setCardGuid(dto.getCardGuid());
        memberCardAggPayQO.setMemberInfoGuid(dto.getMemberInfoGuid());
        memberCardAggPayQO.setPayMoney(dto.getPayMoney());
        memberCardAggPayQO.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        memberCardAggPayQO.setPayWay(dto.getPayWay() == NUMBER_0 ? PayWayEnum.WE_CHANT_PAY.getCode() : PayWayEnum.ALIPAY.getCode());
        memberCardAggPayQO.setBusinessType(dto.getBusinessType());

        String orderGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        String payGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        log.info("付费开卡生成orderNum====>{}", orderGuid);
        log.info("付费开卡生成payGuid====>{}", payGuid);
        saveCardPayRecord(memberCardAggPayQO, ThreadLocalCache.getHeaderUserInfo(), orderGuid, payGuid);

        return orderGuid;
    }

    private void callbackOpenCardPay(MemberCardOpenCardPayDTO payDto) {
        log.info("付费开卡支付结果，paySuccess：{}", JSON.toJSONString(payDto));
        HsaCardPayRecord hsaCardPayRecord = hsaCardPayRecordMapper.selectOne(new LambdaQueryWrapper<HsaCardPayRecord>()
                .eq(HsaCardPayRecord::getOrderGuid, payDto.getOrderNumber())
                .eq(HsaCardPayRecord::getMemberInfoGuid, payDto.getMemberInfoGuid())
                .eq(HsaCardPayRecord::getPayCondition, NUMBER_0));
        log.info("付费开卡预下单记录hsaCardPayRecord====>{}", JSON.toJSON(hsaCardPayRecord));
        if (Objects.isNull(hsaCardPayRecord)) {
            return;
        }
        if (hsaCardPayRecord.getBusinessType() == NumberConstant.NUMBER_20 || hsaCardPayRecord.getBusinessType() == NumberConstant.NUMBER_10) {
            CardInfoDetailDTO available = cardInfoMapper.getSelfFreeOpenCard(hsaCardPayRecord.getCardGuid());
            HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaCardPayRecord.getCardGuid());
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaCardPayRecord.getMemberInfoGuid());
            MemberCardOpenDTO dto = new MemberCardOpenDTO();
            dto.setMemberInfoGuid(hsaOperationMemberInfo.getGuid())
                    .setCardGuid(hsaCardPayRecord.getCardGuid())
                    .setOperSubjectGuid(hsaCardPayRecord.getOperSubjectGuid())
                    .setEnterpriseGuid(hsaCardPayRecord.getEnterpriseGuid())
                    .setOpenWay(hsaCardPayRecord.getElectronicOpenWay())
                    .setSource(hsaCardPayRecord.getSource())
                    .setCardName(hsaCardBaseInfo.getCardName())
                    .setMemberPhoneNum(hsaOperationMemberInfo.getPhoneNum());
            if (hsaMemberInfoCardMapper.selectCount(new LambdaQueryWrapper<HsaMemberInfoCard>()
                    .eq(HsaMemberInfoCard::getCardGuid, dto.getCardGuid())
                    .eq(HsaMemberInfoCard::getMemberInfoGuid, hsaOperationMemberInfo.getGuid())
                    .isNotNull(HsaMemberInfoCard::getElectronicCardGuid)) > 0) {
                log.info("付费开卡会员电子卡已经开通，cardGuid：{}", dto.getCardGuid());
                return;
            }
            HsaMemberInfoCard hsaMemberInfoCard = hsaElectronicCardService.getMemberPayOpenCard(dto, available, hsaCardPayRecord);
            log.info("付费开卡结果appletOpenCardVO：{}", JSON.toJSONString(hsaMemberInfoCard));
            //消费记录
            HsaMemberConsumption hsaMemberConsumption = this.getMemberPayConsumption(payDto.getOrderNumber(), ThreadLocalCache.getHeaderUserInfo(), hsaCardPayRecord, hsaMemberInfoCard);
            //充值支付方式
            HsaMemberConsumptionPayWay memberConsumptionPayWay = new HsaMemberConsumptionPayWay();
            memberConsumptionPayWay.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumptionPayWay.class.getSimpleName()));
            memberConsumptionPayWay.setConsumptionGuid(hsaMemberConsumption.getGuid());
            memberConsumptionPayWay.setPayAmount(hsaCardPayRecord.getPaymentMoney());
            memberConsumptionPayWay.setPayWay(hsaCardPayRecord.getPayWay());
            memberConsumptionPayWay.setPayName(PayWayEnum.getPayName(hsaCardPayRecord.getPayWay()));
            hsaMemberConsumptionPayWayMapper.insert(memberConsumptionPayWay);
            hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
            hsaCardPayRecord.setPayCondition(NUMBER_1);
            hsaCardPayRecordMapper.updateByGuid(hsaCardPayRecord);
            hsaMemberConsumptionMapper.insert(hsaMemberConsumption);
        }

    }

    /**
     * 充值消费开卡
     *
     * @param memberCardAggPayQO
     * @param memberCardPerPayVO
     * @param headerUserInfo
     * @return
     */
    private MemberCardPerPayVO openCardByConsumption(MemberCardAggPayQO memberCardAggPayQO, MemberCardPerPayVO memberCardPerPayVO, HeaderUserInfo headerUserInfo) {
        checkMemberInfoCard(memberCardAggPayQO.getCardGuid(), memberCardAggPayQO.getMemberInfoGuid());
        if (memberCardAggPayQO.getCheckPay() == NUMBER_0) {
            return memberCardPerPayVO;
        }
        String orderGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        String payGuid = guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName());
        log.info("生成orderNum====>{}", orderGuid);
        log.info("生成payGuid====>{}", payGuid);

        //获取门店配置信息
        SaasAggPayOdooDTO saasAggPayOdooDTO =
                getPaySetting(memberCardAggPayQO, headerUserInfo, orderGuid, payGuid, memberCardPerPayVO);
        log.info("聚合支付配置信息:{}", JSON.toJSON(saasAggPayOdooDTO));

        AggPayRespDTO aggPayRespDTO = saasAggPayFeign.pay(saasAggPayOdooDTO);

        log.info("预下单结果返回AggPayRespDTO====>{}", aggPayRespDTO);
        memberCardPerPayVO.setAggPayRespDTO(aggPayRespDTO);
        if (aggPayRespDTO.getResult().equals(StringConstant.SUCCESS) && aggPayRespDTO.getCode().equals(NumberConstant.NUMBER_10000 + "")) {
            saveCardPayRecord(memberCardAggPayQO, headerUserInfo, orderGuid, payGuid);
        }
        memberCardPerPayVO.setAmount(memberCardAggPayQO.getPayMoney());
        memberCardPerPayVO.setAppSecret(saasAggPayOdooDTO.getPaymentInfo().getAppSecret());
        memberCardPerPayVO.setDeveloperId(saasAggPayOdooDTO.getReqDTO().getDeveloperId());
        memberCardPerPayVO.setEnterpriseGuid(memberCardAggPayQO.getEnterpriseGuid());
        memberCardPerPayVO.setEnterpriseName(saasAggPayOdooDTO.getReqDTO().getEnterpriseName());
        memberCardPerPayVO.setOrderGuid(orderGuid);
        memberCardPerPayVO.setPayGuid(payGuid);
        memberCardPerPayVO.setMemberInfoGuid(memberCardAggPayQO.getMemberInfoGuid());
        memberCardPerPayVO.setSignature(saasAggPayOdooDTO.getReqDTO().getSignature());
        return memberCardPerPayVO;
    }

    private void saveCardPayRecord(MemberCardAggPayQO memberCardAggPayQO, HeaderUserInfo headerUserInfo, String orderGuid, String payGuid) {
        HsaCardPayRecord hsaCardOpenPayRecord = new HsaCardPayRecord();
        hsaCardOpenPayRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaCardPayRecord.class.getSimpleName()))
                .setCardGuid(memberCardAggPayQO.getCardGuid())
                .setEnterpriseGuid(memberCardAggPayQO.getEnterpriseGuid())
                .setOperSubjectGuid(headerUserInfo.getOperSubjectGuid())
                .setOrderGuid(orderGuid)
                .setMemberInfoGuid(memberCardAggPayQO.getMemberInfoGuid())
                .setPayWay(memberCardAggPayQO.getPayWay())
                .setPayGuid(payGuid)
                .setSource(headerUserInfo.getSource())
                .setElectronicOpenWay(memberCardAggPayQO.getBusinessType() == NumberConstant.NUMBER_10 ? NumberConstant.NUMBER_2 : NUMBER_1)
                .setBusinessType(memberCardAggPayQO.getBusinessType())
                .setPaymentMoney(memberCardAggPayQO.getPayMoney())
                .setSelfType(memberCardAggPayQO.getBusinessType() == NumberConstant.NUMBER_10
                        ? NumberConstant.NUMBER_2
                        : NUMBER_1)
                .setPayCondition(NUMBER_0);
        hsaCardPayRecordMapper.insert(hsaCardOpenPayRecord);
    }

    @Override
    public MemberCardPollingPayVO memberCardPolling(MemberCardPayPollingQO memberCardPayPollingQO) {
        MemberCardPollingPayVO pollingPayVO = new MemberCardPollingPayVO();
        SaasPollingOdooDTO pollingDTO = new SaasPollingOdooDTO();
        PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setEnterpriseGuid(memberCardPayPollingQO.getEnterpriseGuid());
        paymentInfoDTO.setAppSecret(memberCardPayPollingQO.getAppSecret());
        paymentInfoDTO.setAppId(memberCardPayPollingQO.getAppId());
        pollingDTO.setPaymentInfo(paymentInfoDTO);
        pollingDTO.setDevice_id(memberCardPayPollingQO.getDevice_id());
        pollingDTO.setOrderGuid(memberCardPayPollingQO.getOrderGuid());
        pollingDTO.setPayGuid(memberCardPayPollingQO.getPayGuid());
        pollingDTO.setEnterpriseName(memberCardPayPollingQO.getEnterpriseName());
        pollingDTO.setEnterpriseGuid(memberCardPayPollingQO.getEnterpriseGuid());
        AggPayPollingRespDTO aggPayRespDTO = saasAggPayFeign.polling(pollingDTO);
        log.info("轮询结果，aggPayRespDTO：{}", JSON.toJSONString(aggPayRespDTO));
        BeanUtils.copyProperties(aggPayRespDTO, pollingPayVO);
        return pollingPayVO;
    }

    /**
     * 校验会员卡开通条件
     *
     * @param cardGuid       会员卡GUID
     * @param memberInfoGuid 会员GUID
     * @throws MemberBaseException 当校验失败时抛出业务异常
     */
    private void checkMemberInfoCard(String cardGuid, String memberInfoGuid) {
        // 参数验证
        validateCardAndMemberParams(cardGuid, memberInfoGuid);
        
        // 验证会员卡基础信息
        validateCardExists(cardGuid);
        
        // 验证会员状态
        validateMemberStatus(memberInfoGuid);
        
        // 验证会员卡是否已开通
        validateCardNotAlreadyOpened(cardGuid, memberInfoGuid);
        
        // 验证电子卡开通条件
        CardInfoDetailDTO cardDetail = validateElectronicCardConditions(cardGuid);
        
        // 验证开卡权限范围
        validateOpenCardScope(cardDetail, memberInfoGuid);
    }
    
    /**
     * 验证参数有效性
     */
    private void validateCardAndMemberParams(String cardGuid, String memberInfoGuid) {
        if (StringUtils.isBlank(cardGuid)) {
            throw new IllegalArgumentException("会员卡GUID不能为空");
        }
        if (StringUtils.isBlank(memberInfoGuid)) {
            throw new IllegalArgumentException("会员GUID不能为空");
        }
    }
    
    /**
     * 验证会员卡是否存在
     */
    private void validateCardExists(String cardGuid) {
        HsaCardBaseInfo cardInfo = hsaCardBaseInfoMapper.queryByGuid(cardGuid);
        if (Objects.isNull(cardInfo)) {
            log.error("会员卡不存在，cardGuid: {}", cardGuid);
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_CARD_NOT_EXIST);
        }
        }

    /**
     * 验证会员状态是否正常
     */
    private void validateMemberStatus(String memberInfoGuid) {
        HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberInfoGuid);
        if (Objects.isNull(memberInfo)) {
            log.error("会员信息不存在，memberInfoGuid: {}", memberInfoGuid);
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_NOT_REGISTER);
        }
        
        if (memberInfo.getAccountState() == AccountStateEnum.FREEZE.getCode()) {
            log.error("会员账户已冻结，不能开通会员卡，memberInfoGuid: {}", memberInfoGuid);
            throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_MEMBER_DISABLED);
        }
    }
    
    /**
     * 验证会员卡是否已经开通
     */
    private void validateCardNotAlreadyOpened(String cardGuid, String memberInfoGuid) {
        long count = hsaMemberInfoCardMapper.selectCount(new LambdaQueryWrapper<HsaMemberInfoCard>()
                .eq(HsaMemberInfoCard::getCardGuid, cardGuid)
                .eq(HsaMemberInfoCard::getMemberInfoGuid, memberInfoGuid)
                .isNotNull(HsaMemberInfoCard::getElectronicCardGuid));
                
        if (count > 0) {
            log.error("会员卡已开通，cardGuid: {}, memberInfoGuid: {}", cardGuid, memberInfoGuid);
            throw new MemberBaseException(
                    systemRoleHelper.getReplace(CardOperationExceptionEnum.MEMBER_ALREADY_OPEN,
                            ThreadLocalCache.getOperSubjectGuid())
            );
        }
    }
    
    /**
     * 验证电子卡开通条件
     */
    private CardInfoDetailDTO validateElectronicCardConditions(String cardGuid) {
        CardInfoDetailDTO cardDetail = hsaCardInfoMapper.getSelfFreeOpenCard(cardGuid);
        log.info("电子卡开通条件，cardGuid: {}, available: {}", cardGuid, cardDetail);
        
        if (Objects.isNull(cardDetail)) {
            log.error("电子发卡状态异常，cardGuid: {}", cardGuid);
            throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_FAIL);
        }

        // 验证卡片状态
        validateCardStatus(cardDetail);
        
        // 验证发卡数量限制
        validateCardCountLimit(cardDetail);
        
        // 验证发卡状态
        validateCardSendStatus(cardDetail);
        
        return cardDetail;
    }
    
    /**
     * 验证卡片状态
     */
    private void validateCardStatus(CardInfoDetailDTO cardDetail) {
        if (cardDetail.getCardStatus() == CardStatusEnum.FINISH.getCode()) {
            log.error("会员卡已结束开通，cardStatus: {}", cardDetail.getCardStatus());
            throw new MemberBaseException(
                systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, 
                        ThreadLocalCache.getOperSubjectGuid())
            );
        }
    }
    
    /**
     * 验证发卡数量限制
     */
    private void validateCardCountLimit(CardInfoDetailDTO cardDetail) {
        if (cardDetail.getSendCountLimit() != NumberConstant.NUMBER_0 
                && cardDetail.getCountLimit() <= NumberConstant.NUMBER_0) {
            log.error("发卡数量已达上限，sendCountLimit: {}, countLimit: {}", 
                    cardDetail.getSendCountLimit(), cardDetail.getCountLimit());
            throw new MemberBaseException(
                systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, 
                        ThreadLocalCache.getOperSubjectGuid())
            );
        }
    }
    
    /**
     * 验证发卡状态
     */
    private void validateCardSendStatus(CardInfoDetailDTO cardDetail) {
        if (cardDetail.getSendStatus() != SendCardStateEnum.CARD_STATE_START.getCode()) {
            log.error("电子卡发卡状态异常，sendStatus: {}", cardDetail.getSendStatus());
            throw new MemberBaseException(
                systemRoleHelper.getReplace(MemberInfoCardExceptionEnum.CARD_OPEN_FINISH, 
                        ThreadLocalCache.getOperSubjectGuid())
            );
        }
    }
    
    /**
     * 验证开卡权限范围
     */
    private void validateOpenCardScope(CardInfoDetailDTO cardDetail, String memberInfoGuid) {
        if (!judgeOpenCardScope(cardDetail, memberInfoGuid)) {
            log.error("会员不在开卡范围内，memberInfoGuid: {}, openCardScopeType: {}", 
                    memberInfoGuid, cardDetail.getOpenCardScopeType());
            throw new MemberBaseException(MemberInfoCardExceptionEnum.CARD_OPEN_FAIL);
        }
    }

    /**
     * 判断开卡权限
     *
     * @param available  卡详情信息
     * @param memberGuid 会员guid
     * @return 判断结果
     */
    private boolean judgeOpenCardScope(CardInfoDetailDTO available, String memberGuid) {
        if (Objects.isNull(available.getOpenCardScopeType())) {
            return false;
        }
        //0所有注册会员
        if (available.getOpenCardScopeType() == NumberConstant.NUMBER_0) {
            return true;

        }
        return checkOpenCardMember(available, memberGuid);
    }

    private boolean checkOpenCardMember(CardInfoDetailDTO available, String memberGuid) {
        //1仅满足条件会员
        if (available.getOpenCardScopeType() == NUMBER_1) {
            return checkOpenCardScope(available, memberGuid);
        }

        if (available.getOpenCardScopeType() == 2) {
            //指定会员
            return appointMember(available, memberGuid);
        }

        return false;
    }

    private boolean checkOpenCardScope(CardInfoDetailDTO available, String memberGuid) {
        if (StringUtils.isNotBlank(available.getOpenCardRegisterChannel())) {
            return checkScopeMember(available);
        }

        if (!StringUtils.isEmpty(available.getOpenCardMemberLabelGuid())) {
            Map<String, HsaMemberLabel> labels = hsaMemberLabelMapper.selectList(new LambdaQueryWrapper<HsaMemberLabel>()
                            .eq(HsaMemberLabel::getOperationMemberInfoGuid, memberGuid))
                    .stream()
                    .collect(Collectors.toMap(HsaMemberLabel::getLabelSettingGuid, Function.identity(), (obj, obj1) -> obj));
            return checkContains(available, labels);
        }
        return false;
    }

    private boolean checkScopeMember(CardInfoDetailDTO available) {
        if (!available.getOpenCardRegisterChannel().contains(com.holderzone.member.common.util.verify.ObjectUtil.
                objToString(ThreadLocalCache.getHeaderUserInfo().getSource())) && available.getOpenCardScopeConditionType() == NUMBER_0) {
            return false;
        } else {
            return available.getOpenCardScopeConditionType() == NUMBER_1;
        }
    }

    private boolean appointMember(CardInfoDetailDTO available, String memberGuid) {
        Integer count = memberCardRuleMapper.selectCount(new LambdaQueryWrapper<HsaMemberCardRule>()
                .eq(HsaMemberCardRule::getOperationMemberInfoGuid, memberGuid)
                .eq(HsaMemberCardRule::getCardOpenRuleGuid, available.getCardOpenRuleGuid()));
        return count > NumberConstant.NUMBER_0;
    }

    private static boolean checkContains(CardInfoDetailDTO available, Map<String, HsaMemberLabel> labels) {
        if (CollectionUtil.isNotEmpty(labels)) {
            List<String> labelGuid = JSONObject.parseArray(available.getOpenCardMemberLabelGuid(), String.class);
            for (String guid : labelGuid) {
                if (labels.containsKey(guid))
                    return true;
            }
        }
        return false;
    }

    @Override
    public void callBack(AggPayCallbackDTO aggPayCallbackDTO) {
        log.info("预下单成功 orderGUID：{}, payGUID：{}", aggPayCallbackDTO.getOrderGUID(), aggPayCallbackDTO.getPayGUID());
        log.info("预下单成功 aggPayCallbackDTO：{}", JSONObject.toJSONString(aggPayCallbackDTO));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberCardPollingPayVO paySuccess(MemberCardPayPollingQO memberCardPayPollingQO) {
        MemberCardPollingPayVO memberCardPollingPayVO = this.memberCardPolling(memberCardPayPollingQO);
        log.info("支付结果，paySuccess：{}", JSON.toJSONString(memberCardPollingPayVO));
        if (memberCardPollingPayVO.getMsg().equals(StringConstant.PAY_SUCCESS) && memberCardPollingPayVO.getCode().equals(NumberConstant.NUMBER_10000 + "")) {
            HsaCardPayRecord hsaCardPayRecord = hsaCardPayRecordMapper.selectOne(new LambdaQueryWrapper<HsaCardPayRecord>()
                    .eq(HsaCardPayRecord::getOrderGuid, memberCardPollingPayVO.getOrderGUID())
                    .eq(HsaCardPayRecord::getPayGuid, memberCardPollingPayVO.getPayGUID())
                    .eq(HsaCardPayRecord::getMemberInfoGuid, memberCardPayPollingQO.getMemberInfoGuid())
                    .eq(HsaCardPayRecord::getPayCondition, NUMBER_0));
            log.info("会员卡聚合支付预下单记录hsaCardPayRecord====>{}", JSON.toJSON(hsaCardPayRecord));
            if (Objects.isNull(hsaCardPayRecord)) {
                return memberCardPollingPayVO;
            }
            if (hsaCardPayRecord.getBusinessType() == NumberConstant.NUMBER_20 || hsaCardPayRecord.getBusinessType() == NumberConstant.NUMBER_10) {
                CardInfoDetailDTO available = cardInfoMapper.getSelfFreeOpenCard(hsaCardPayRecord.getCardGuid());
                HsaCardBaseInfo hsaCardBaseInfo = hsaCardBaseInfoMapper.queryByGuid(hsaCardPayRecord.getCardGuid());
                HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaCardPayRecord.getMemberInfoGuid());
                MemberCardOpenDTO dto = new MemberCardOpenDTO();
                dto.setMemberInfoGuid(hsaOperationMemberInfo.getGuid())
                        .setCardGuid(hsaCardPayRecord.getCardGuid())
                        .setOperSubjectGuid(hsaCardPayRecord.getOperSubjectGuid())
                        .setEnterpriseGuid(hsaCardPayRecord.getEnterpriseGuid())
                        .setOpenWay(hsaCardPayRecord.getElectronicOpenWay())
                        .setSource(hsaCardPayRecord.getSource())
                        .setCardName(hsaCardBaseInfo.getCardName())
                        .setMemberPhoneNum(hsaOperationMemberInfo.getPhoneNum());
                if (hsaMemberInfoCardMapper.selectCount(new LambdaQueryWrapper<HsaMemberInfoCard>()
                        .eq(HsaMemberInfoCard::getCardGuid, dto.getCardGuid())
                        .eq(HsaMemberInfoCard::getMemberInfoGuid, hsaOperationMemberInfo.getGuid())
                        .isNotNull(HsaMemberInfoCard::getElectronicCardGuid)) > 0) {
                    log.info("会员电子卡已经开通，cardGuid：{}", dto.getCardGuid());
                    return memberCardPollingPayVO;
                }
                HsaMemberInfoCard hsaMemberInfoCard = hsaElectronicCardService.getMemberPayOpenCard(dto, available, hsaCardPayRecord);
                log.info("开卡结果appletOpenCardVO：{}", JSON.toJSONString(hsaMemberInfoCard));
                //消费记录
                HsaMemberConsumption hsaMemberConsumption = this.getMemberPayConsumption(memberCardPollingPayVO.getOrderNo(), ThreadLocalCache.getHeaderUserInfo(), hsaCardPayRecord, hsaMemberInfoCard);
                //充值支付方式
                HsaMemberConsumptionPayWay memberConsumptionPayWay = new HsaMemberConsumptionPayWay();
                memberConsumptionPayWay.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumptionPayWay.class.getSimpleName()));
                memberConsumptionPayWay.setConsumptionGuid(hsaMemberConsumption.getGuid());
                memberConsumptionPayWay.setPayAmount(hsaCardPayRecord.getPaymentMoney());
                memberConsumptionPayWay.setPayWay(hsaCardPayRecord.getPayWay());
                memberConsumptionPayWay.setPayName(PayWayEnum.getPayName(hsaCardPayRecord.getPayWay()));
                hsaMemberConsumptionPayWayMapper.insert(memberConsumptionPayWay);
                hsaMemberInfoCardMapper.updateByGuid(hsaMemberInfoCard);
                hsaCardPayRecord.setPayCondition(NUMBER_1);
                memberCardPollingPayVO.setOwnGuid(hsaMemberInfoCard.getGuid());
                hsaCardPayRecordMapper.updateByGuid(hsaCardPayRecord);
                hsaMemberConsumptionMapper.insert(hsaMemberConsumption);
            }

        }
        return memberCardPollingPayVO;
    }

    /**
     * 构建会员付费消费记录
     * 
     * @param transactionId 银行交易ID
     * @param headerUserInfo 用户头信息
     * @param payRecord 支付记录
     * @param memberCard 会员卡信息
     * @return 会员消费记录
     */
    private HsaMemberConsumption getMemberPayConsumption(String transactionId, HeaderUserInfo headerUserInfo, 
                                                       HsaCardPayRecord payRecord, HsaMemberInfoCard memberCard) {
        // 参数验证
        if (Objects.isNull(payRecord) || Objects.isNull(memberCard) || Objects.isNull(headerUserInfo)) {
            throw new IllegalArgumentException("支付记录、会员卡信息和用户信息不能为空");
        }
        
        // 构建基础消费记录
        HsaMemberConsumption consumption = buildBasicConsumption(transactionId, payRecord, memberCard);
        
        // 设置操作员信息
        setOperatorInfo(consumption, headerUserInfo, memberCard);
        
        // 计算并设置余额信息
        calculateAndSetBalance(consumption, payRecord, memberCard);
        
        // 生成资金明细记录
        List<HsaMemberFundingDetail> fundingDetails = generateFundingDetails(consumption, payRecord, memberCard);
        
        // 保存资金明细（如果有有效记录）
        saveFundingDetailsIfValid(fundingDetails);
        
        return consumption;
    }
    
    /**
     * 构建基础消费记录信息
     */
    private HsaMemberConsumption buildBasicConsumption(String transactionId, HsaCardPayRecord payRecord, HsaMemberInfoCard memberCard) {
        HsaMemberConsumption consumption = new HsaMemberConsumption();
        LocalDateTime now = LocalDateTime.now();
        
        // 基础信息设置
        consumption.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberConsumption.class.getSimpleName()));
        consumption.setOperSubjectGuid(payRecord.getOperSubjectGuid());
        consumption.setMemberInfoGuid(memberCard.getMemberInfoGuid());
        consumption.setMemberInfoCardGuid(memberCard.getGuid());
        consumption.setCardNum(memberCard.getElectronicCardNum());
        consumption.setEnterpriseGuid(memberCard.getEnterpriseGuid());
        
        // 金额信息设置
        consumption.setOrderAmount(payRecord.getPaymentMoney());
        consumption.setOrderPaidAmount(payRecord.getPaymentMoney());
        consumption.setOrderDiscountAmount(BigDecimal.ZERO);
        
        // 状态和类型设置
        consumption.setIsComplete(NUMBER_1);
        consumption.setIsCancel(BooleanEnum.FALSE.getCode());
        consumption.setConsumptionType(determineConsumptionType(payRecord.getBusinessType()));
        
        // 时间信息设置
        consumption.setConsumptionTime(now);
        consumption.setOrderTime(now);
        
        // 订单信息设置
        consumption.setOrderSource(payRecord.getSource());
        consumption.setOrderGuid(payRecord.getPayGuid());
        consumption.setOrderNumber(payRecord.getOrderGuid());
        consumption.setBankTransactionId(transactionId);
        consumption.setOrderType(MarketConsumptionOrderTypeEnum.ORDER_PAID_MEMBER.getCode());
        
        return consumption;
    }
    
    /**
     * 设置操作员信息
     */
    private void setOperatorInfo(HsaMemberConsumption consumption, HeaderUserInfo headerUserInfo, HsaMemberInfoCard memberCard) {
        if (isFromSpecialSource(headerUserInfo.getSource())) {
            HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(memberCard.getMemberInfoGuid());
            if (Objects.nonNull(memberInfo)) {
                consumption.setOperatorTelName(memberInfo.getUserName() + StringConstant.STR_BIAS + memberInfo.getPhoneNum());
            }
        } else {
            consumption.setOperatorTelName(ThreadLocalCache.getOperatorTelName());
        }
    }
    
    /**
     * 判断是否为特殊来源
     */
    private boolean isFromSpecialSource(Integer source) {
        return source == SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode() 
            || SourceTypeEnum.getMallSource().contains(source);
    }
    
    /**
     * 计算并设置余额信息
     */
    private void calculateAndSetBalance(HsaMemberConsumption consumption, HsaCardPayRecord payRecord, HsaMemberInfoCard memberCard) {
        // 计算总余额：卡余额 + 赠送金额 + 补贴金额
        BigDecimal totalBalance = calculateTotalBalance(memberCard);
        consumption.setCardResidualBalance(totalBalance);
        
        // 如果是充值类型，需要更新余额
        if (isRechargeType(payRecord.getBusinessType())) {
            BigDecimal newBalance = totalBalance.add(payRecord.getPaymentMoney());
            consumption.setCardResidualBalance(newBalance);
            memberCard.setCardAmount(memberCard.getCardAmount().add(payRecord.getPaymentMoney()));
        }
    }
    
    /**
     * 计算总余额
     */
    private BigDecimal calculateTotalBalance(HsaMemberInfoCard memberCard) {
        BigDecimal cardAmount = Objects.isNull(memberCard.getCardAmount()) ? BigDecimal.ZERO : memberCard.getCardAmount();
        BigDecimal giftAmount = Objects.isNull(memberCard.getGiftAmount()) ? BigDecimal.ZERO : memberCard.getGiftAmount();
        BigDecimal subsidyAmount = Objects.isNull(memberCard.getSubsidyAmount()) ? BigDecimal.ZERO : memberCard.getSubsidyAmount();
        
        return cardAmount.add(giftAmount).add(subsidyAmount);
    }
    
    /**
     * 生成资金明细记录
     */
    private List<HsaMemberFundingDetail> generateFundingDetails(HsaMemberConsumption consumption, 
                                                              HsaCardPayRecord payRecord, HsaMemberInfoCard memberCard) {
        List<HsaMemberFundingDetail> fundingDetails = new ArrayList<>();
        
        // 添加基础资金明细
        fundingDetails.add(createFundingDetail(
            memberCard.getCardAmount(), 
            consumption.getCardResidualBalance(), 
            memberCard, 
            payRecord, 
            consumption
        ));
        
        // 如果是充值类型，添加充值明细
        if (isRechargeType(payRecord.getBusinessType())) {
            fundingDetails.add(createFundingDetail(
                payRecord.getPaymentMoney(), 
                consumption.getCardResidualBalance(), 
                memberCard, 
                null, 
                consumption
            ));
        }
        
        return fundingDetails;
    }
    
    /**
     * 创建资金明细记录
     */
    private HsaMemberFundingDetail createFundingDetail(BigDecimal rechargeAmount, BigDecimal cardBalance, 
                                                     HsaMemberInfoCard memberCard, HsaCardPayRecord payRecord, 
                                                     HsaMemberConsumption consumption) {
        HsaMemberFundingDetail detail = new HsaMemberFundingDetail();
        
        // 基础信息
        detail.setGuid(guidGeneratorUtil.getStringGuid(HsaMemberFundingDetail.class.getSimpleName()));
        detail.setEnterpriseGuid(consumption.getEnterpriseGuid());
        detail.setMemberInfoCardGuid(memberCard.getGuid());
        detail.setMemberInfoGuid(memberCard.getMemberInfoGuid());
        detail.setMemberConsumptionGuid(consumption.getGuid());
        detail.setOperSubjectGuid(consumption.getOperSubjectGuid());
        detail.setOperatorTelName(consumption.getOperatorTelName());
        
        // 卡片信息
        detail.setCardGuid(memberCard.getCardGuid());
        detail.setCardName(memberCard.getCardName());
        detail.setCardType(determineCardType(memberCard));
        detail.setCardNum(determineCardNum(memberCard));
        
        // 金额信息
        detail.setRechargeAmount(rechargeAmount);
        detail.setCardRechargeResidualBalance(cardBalance);
        detail.setCardSubsidyResidualBalance(memberCard.getSubsidyAmount());
        detail.setCardGiftResidualBalance(memberCard.getGiftAmount());
        detail.setAmountRechargeFundingType(NumberConstant.NUMBER_0);
        
        // 业务信息
        detail.setChangeSource(consumption.getOrderSource());
        detail.setIsValid(NUMBER_1);
        
        // 根据支付记录设置备注和来源类型
        setRemarkAndSourceType(detail, payRecord);
        
        return detail;
    }
    
    /**
     * 设置备注和来源类型
     */
    private void setRemarkAndSourceType(HsaMemberFundingDetail detail, HsaCardPayRecord payRecord) {
        if (Objects.isNull(payRecord)) {
            detail.setRemark("充值");
            detail.setStoreName("充值");
            detail.setAmountSourceType(AmountSourceTypeEnum.RECHARGE.getCode());
        } else {
            detail.setRemark("预存余额");
            detail.setAmountSourceType(AmountSourceTypeEnum.PRESTORE_BALANCE.getCode());
        }
    }
    
    /**
     * 确定卡片类型
     */
    private Integer determineCardType(HsaMemberInfoCard memberCard) {
        return StringUtils.isEmpty(memberCard.getElectronicCardNum()) 
            ? CardTypeEnum.CARD_TYPE_MAIN.getCode() 
            : CardTypeEnum.CARD_TYPE_EQUITY.getCode();
    }
    
    /**
     * 确定卡号
     */
    private String determineCardNum(HsaMemberInfoCard memberCard) {
        return StringUtils.isEmpty(memberCard.getElectronicCardNum()) 
            ? memberCard.getPhysicalCardNum() 
            : memberCard.getElectronicCardNum();
    }
    
    /**
     * 确定消费类型
     */
    private Integer determineConsumptionType(Integer businessType) {
        return (businessType == NumberConstant.NUMBER_10 || businessType == NumberConstant.NUMBER_30) 
            ? ConsumptionTypeEnum.TYPE_RECHARGE.getCode() 
            : ConsumptionTypeEnum.TYPE_CONSUMPTION.getCode();
    }
    
    /**
     * 判断是否为充值类型
     */
    private boolean isRechargeType(Integer businessType) {
        return businessType == NumberConstant.NUMBER_10;
    }
    
    /**
     * 保存有效的资金明细记录
     */
    private void saveFundingDetailsIfValid(List<HsaMemberFundingDetail> fundingDetails) {
        List<HsaMemberFundingDetail> validDetails = fundingDetails.stream()
            .filter(detail -> detail.getRechargeAmount().compareTo(BigDecimal.ZERO) > 0)
            .collect(Collectors.toList());
            
        if (CollUtil.isNotEmpty(validDetails)) {
            hsaMemberFundingDetailService.saveBatch(validDetails);
        }
    }



    @Override
    public SaasAggPayOdooDTO getPaySetting(MemberCardAggPayQO memberCardAggPayQO,
                                           HeaderUserInfo headerUserInfo,
                                           String orderGuid,
                                           String payGuid,
                                           MemberCardPerPayVO memberCardPerPayVO) {
        // 参数验证
        validatePaySettingParams(memberCardAggPayQO, headerUserInfo, orderGuid, payGuid, memberCardPerPayVO);
        
        // 获取支付配置信息
        PaySettingBaseRes paySetting = getPaySettingBaseRes(
            memberCardAggPayQO.getAppId(), 
            headerUserInfo.getOperSubjectGuid(), 
            memberCardAggPayQO.getStoreId()
        );
        
        long currentTime = System.currentTimeMillis();
        
        // 构建支付信息DTO
        PaymentInfoDTO paymentInfo = buildPaymentInfo(paySetting, memberCardAggPayQO);
        
        // 构建预交易请求DTO
        AggPayPreTradingReqDTO preTrading = buildPreTradingRequest(
            memberCardAggPayQO, paySetting, orderGuid, payGuid, currentTime
        );
        
        // 设置签名
        setPaymentSignature(preTrading, paySetting, orderGuid, payGuid, currentTime);
        
        // 设置AppId到返回对象
        memberCardPerPayVO.setAppId(paySetting.getAppId());
        
        // 构建最终的聚合支付DTO
        return buildSaasAggPayOdooDTO(
            paymentInfo, preTrading, memberCardAggPayQO, paySetting, currentTime
        );
    }

    @Override
    public StoreBaseInfo getAppletStore(AppletStoreQO appletStoreQO) {
        List<StoreBaseInfo> storeBaseInfoList = storeBaseService.queryStore(null, 0);
        if (CollUtil.isEmpty(storeBaseInfoList)) {
            return new StoreBaseInfo();
        }
        validate(storeBaseInfoList);
        //门店根据经纬度，排序
        if (StringUtils.isEmpty(appletStoreQO.getLongitude())) {
            return storeBaseInfoList.get(0);
        } else {
            List<StoreBaseInfo> baseInfoList = dealCalculateDistance(storeBaseInfoList, appletStoreQO.getLongitude(), appletStoreQO.getLatitude());
            return baseInfoList.get(0);
        }

    }

    @Override
    public List<StoreBaseInfo> getAppletStoreList(AppletStoreQO appletStoreQO) {
        List<StoreBaseInfo> storeBaseInfoList = storeBaseService.queryStore(null, 0);
        if (CollUtil.isEmpty(storeBaseInfoList)) {
            return Collections.emptyList();
        }
        validate(storeBaseInfoList);
        //门店根据经纬度，排序
        if (StringUtils.isEmpty(appletStoreQO.getLongitude())) {
            return storeBaseInfoList;
        } else {
            return dealCalculateDistance(storeBaseInfoList, appletStoreQO.getLongitude(), appletStoreQO.getLatitude());
        }
    }

    private void validate(List<StoreBaseInfo> storeBaseInfoList) {
        for (StoreBaseInfo storeBaseInfo : storeBaseInfoList) {
            String address = storeBaseInfo.getAddress();

            String contact = storeBaseInfo.getContact();
            if (!StringUtils.isEmpty(contact) && contact.equals(FALSE_STRING)) {
                storeBaseInfo.setContact(null);
            }

            String addressPoint = storeBaseInfo.getAddressPoint();
            if (!StringUtils.isEmpty(addressPoint) && addressPoint.equals(FALSE_STRING)) {
                storeBaseInfo.setAddressPoint(null);
            }

            if (!StringUtils.isEmpty(address) && address.equals(FALSE_STRING)) {
                storeBaseInfo.setAddress(null);
            }

        }

    }

    /**
     * 计算最近门店
     */
    private List<StoreBaseInfo> dealCalculateDistance(List<StoreBaseInfo> storeBaseInfos, String longitude, String latitude) {
        if (CollUtil.isEmpty(storeBaseInfos)) {
            return Collections.emptyList();
        }
        BigDecimal distance;//门店距离
        String latitudeV;   //纬度
        String longitudeV;  //经度
        log.info("开始计算经纬度：{}", JSON.toJSONString(storeBaseInfos));
        for (StoreBaseInfo storeBaseInfo : storeBaseInfos) {
            String addressPoint = storeBaseInfo.getAddress_point();
            if (org.springframework.util.StringUtils.isEmpty(addressPoint)) {
                storeBaseInfo.setDistance(new BigDecimal(99999));
                continue;
            }
            //addressPoint:由纬度 + "," + 经度 组成
            String[] split = addressPoint.split(",");
            if (split.length == 2) {
                latitudeV = split[0]; //第一个参数为：纬度
                longitudeV = split[1]; //第二个参数为：经度
                //当前门店距离
                distance = com.holderzone.member.common.util.DistanceUtils.getDistance(longitudeV, latitudeV, longitude, latitude);
            } else {
                distance = new BigDecimal(99999);
            }
            storeBaseInfo.setDistance(distance);
        }
        storeBaseInfos = storeBaseInfos.stream()
                .sorted(Comparator.comparing(StoreBaseInfo::getDistance)).collect(Collectors.toList());
        return storeBaseInfos;
    }

    /**
     * 验证支付设置参数
     */
    private void validatePaySettingParams(MemberCardAggPayQO memberCardAggPayQO,
                                        HeaderUserInfo headerUserInfo,
                                        String orderGuid,
                                        String payGuid,
                                        MemberCardPerPayVO memberCardPerPayVO) {
        if (Objects.isNull(memberCardAggPayQO)) {
            throw new IllegalArgumentException("会员卡聚合支付参数不能为空");
        }
        if (Objects.isNull(headerUserInfo)) {
            throw new IllegalArgumentException("用户头信息不能为空");
        }
        if (StringUtils.isBlank(orderGuid)) {
            throw new IllegalArgumentException("订单GUID不能为空");
        }
        if (StringUtils.isBlank(payGuid)) {
            throw new IllegalArgumentException("支付GUID不能为空");
        }
        if (Objects.isNull(memberCardPerPayVO)) {
            throw new IllegalArgumentException("会员卡预支付VO不能为空");
        }
        if (Objects.isNull(memberCardAggPayQO.getPayMoney()) || 
            memberCardAggPayQO.getPayMoney().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("支付金额必须大于0");
        }
    }
    
    /**
     * 构建支付信息DTO
     */
    private PaymentInfoDTO buildPaymentInfo(PaySettingBaseRes paySetting, MemberCardAggPayQO memberCardAggPayQO) {
        PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setAppId(paySetting.getAppId());
        paymentInfo.setAppSecret(paySetting.getAppSecret());
        paymentInfo.setEnterpriseGuid(memberCardAggPayQO.getEnterpriseGuid());
        return paymentInfo;
    }
    
    /**
     * 构建预交易请求DTO
     */
    private AggPayPreTradingReqDTO buildPreTradingRequest(MemberCardAggPayQO memberCardAggPayQO,
                                                        PaySettingBaseRes paySetting,
                                                        String orderGuid,
                                                        String payGuid,
                                                        long currentTime) {
        AggPayPreTradingReqDTO preTrading = new AggPayPreTradingReqDTO();
        
        // 基础信息设置
        preTrading.setOrderGUID(orderGuid);
        preTrading.setPayGUID(payGuid);
        preTrading.setAmount(memberCardAggPayQO.getPayMoney());
        preTrading.setTimestamp(currentTime);
        
        // 开发者和企业信息
        preTrading.setDeveloperId(paySetting.getDeveloperId());
        preTrading.setEnterpriseGuid(memberCardAggPayQO.getEnterpriseGuid());
        preTrading.setEnterpriseName(paySetting.getEnterpriseName());
        preTrading.setSubAppId(paySetting.getSubAppId());
        
        // 获取微信OpenId
        String subOpenId = hsaMemberInfoWeChatMapper.getMemberInfoOpenId(
            ThreadLocalCache.getOperSubjectGuid(), 
            memberCardAggPayQO.getMemberInfoGuid()
        );
        preTrading.setSubOpenId(subOpenId);
        
        // 商品信息
        preTrading.setBody(CARD_PAY_BODY);
        preTrading.setGoodsName(CARD_PAY_GOODS_NAME);
        preTrading.setPayPowerId(PAY_POWER_ID);
        preTrading.setCurrency(CURRENCY_RMB);
        
        return preTrading;
    }
    
    /**
     * 设置支付签名
     */
    private void setPaymentSignature(AggPayPreTradingReqDTO preTrading,
                                   PaySettingBaseRes paySetting,
                                   String orderGuid,
                                   String payGuid,
                                   long currentTime) {
        WeChatAuthParamDTO weChatAuthParam = new WeChatAuthParamDTO();
        weChatAuthParam.setAppId(paySetting.getAppId());
        weChatAuthParam.setAppSecret(paySetting.getAppSecret());
        weChatAuthParam.setDeveloperKey(paySetting.getDeveloperKey());
        weChatAuthParam.setOrderGUID(orderGuid);
        weChatAuthParam.setPayGUID(payGuid);
        weChatAuthParam.setTimestamp(String.valueOf(currentTime));
        
        String signature = WeChatUtil.getWeChatAuthSignature(weChatAuthParam);
        preTrading.setSignature(signature);
    }
    
    /**
     * 构建最终的聚合支付DTO
     */
    private SaasAggPayOdooDTO buildSaasAggPayOdooDTO(PaymentInfoDTO paymentInfo,
                                                   AggPayPreTradingReqDTO preTrading,
                                                   MemberCardAggPayQO memberCardAggPayQO,
                                                   PaySettingBaseRes paySetting,
                                                   long currentTime) {
        SaasAggPayOdooDTO saasAggPayOdoo = new SaasAggPayOdooDTO();
        
        // 基础信息设置
        saasAggPayOdoo.setPaymentInfo(paymentInfo);
        saasAggPayOdoo.setReqDTO(preTrading);
        saasAggPayOdoo.setRequestTimestamp(currentTime);
        
        // 企业信息
        saasAggPayOdoo.setEnterpriseGuid(memberCardAggPayQO.getEnterpriseGuid());
        saasAggPayOdoo.setEnterpriseName(paySetting.getEnterpriseName());
        
        // 支付配置
        saasAggPayOdoo.setIsQuickReceipt(false);
        saasAggPayOdoo.setIsLast(true);
        
        // 设置回调地址
        String callBackUrl = buildCallbackUrl();
        saasAggPayOdoo.setSaasCallBackUrl(callBackUrl);
        
        return saasAggPayOdoo;
    }
    
    /**
     * 构建回调地址
     */
    private String buildCallbackUrl() {
        String callBackUrl = String.format(BASE_CARD_PAY_CALLBACK, RequestBaseHost);
        log.info("会员卡支付回调地址，callBackUrl：{}", callBackUrl);
        return callBackUrl;
    }

    private PaySettingBaseRes getPaySettingBaseRes(String appId, String operSubjectGuid, String storeId) {
        PaySettingDTO paySettingDTO = new PaySettingDTO();
        paySettingDTO.setAppId(appId);
        paySettingDTO.setOperSubjectGuid(operSubjectGuid);
        paySettingDTO.setStoreId(storeId);
        return externalSupport.storeServer(ThreadLocalCache.getSystem()).getPaySetting(paySettingDTO);
    }
}
