package com.holderzone.member.base.service.redeem.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.member.HsaMemberLabel;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.card.HsaCardBaseInfoMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.assembler.RedeemApplyAssembler;
import com.holderzone.member.base.service.card.HsaElectronicCardService;
import com.holderzone.member.base.service.card.HsaMemberInfoCardService;
import com.holderzone.member.base.service.card.MemberCardOperationService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.HsaMemberLabelService;
import com.holderzone.member.base.service.member.HsaOperationMemberInfoService;
import com.holderzone.member.base.service.redeem.RedeemApplyService;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.redeem.RequestRedeemApplyDTO;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.ElectronicOpenWayEnum;
import com.holderzone.member.common.enums.coupon.RedeemActiveTypeEnum;
import com.holderzone.member.common.enums.exception.MemberInfoCardExceptionEnum;
import com.holderzone.member.common.enums.growth.SumValueChangeEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.enums.member.LabelTriggerTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.CreatePhysicalCardSecretQO;
import com.holderzone.member.common.qo.card.OpenElectronicCardQO;
import com.holderzone.member.common.qo.card.UpdateMemberCardBalanceQO;
import com.holderzone.member.common.qo.integral.UpdateIntegralQO;
import com.holderzone.member.common.qo.member.AddMemberLabelCorrelationQO;
import com.holderzone.member.common.qo.member.RequestMemberGrowthValue;
import com.holderzone.member.common.qo.redeem.RespondEditActiveVO;
import com.holderzone.member.common.vo.card.CardBaseInfoVO;
import com.holderzone.member.common.vo.card.RedeemOwnCardVO;
import com.holderzone.member.common.vo.coupon.MemberCouponPackageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 兑换实现类
 */
@Service
@Slf4j
public class RedeemApplyServiceImpl implements RedeemApplyService {

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private RedeemApplyAssembler redeemApplyAssembler;

    @Resource
    private IHsaMemberCouponLinkService hsaMemberCouponLinkService;

    @Resource
    private Executor memberBaseThreadExecutor;

    @Resource
    private ShortMessageSendService sendService;

    @Resource
    private HsaCardBaseInfoMapper hsaCardBaseInfoMapper;

    @Resource
    private HsaElectronicCardService electronicCardService;

    @Resource
    private HsaOperationMemberInfoService hsaOperationMemberInfoService;

    @Resource
    private MemberCardOperationService memberCardOperationService;

    @Resource
    private HsaMemberInfoCardService memberInfoCardService;

    @Resource
    private HsaMemberLabelService hsaMemberLabelService;

    /**
     * "兑换码"
     */
    private static final String REDEEM_CODE = "兑换码";

    /**
     * "-"
     */
    private static final String MINUS = "-";

    /**
     * ""
     */
    private static final String EMPTY = "";

    /**
     * 资金明细，增加
     */
    private static final int ADD = 0;

    @Override
    public boolean doRedeem(RequestRedeemApplyDTO dto) {
        log.info("兑换入参：{}", JSON.toJSONString(dto));
        log.info("兑换userInfo：{}", ThreadLocalCache.get());
        RespondEditActiveVO activityVO = dto.getHsaRedeemCodeActive();
        RedeemActiveTypeEnum typeEnum = RedeemActiveTypeEnum.getEnum(activityVO.getActiveRedeemType());
        if (Objects.isNull(typeEnum)) {
            return false;
        }
        HsaOperationMemberInfo hsaOperationMemberInfo = null;
        if (StringUtils.isNotEmpty(dto.getMemberGuid())) {
            hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(dto.getMemberGuid());
            if (CollUtil.isNotEmpty(activityVO.getLabelGuidList())) {
                log.info("兑换码活动关联标签：{}", JSON.toJSONString(activityVO.getLabelGuidList()));
                hsaMemberLabelService.addMemberInfoLabel(new AddMemberLabelCorrelationQO()
                        .setLabelGuid(activityVO.getLabelGuidList())
                        .setMemberInfoGuid(Collections.singletonList(dto.getMemberGuid())));
            }
        }
        switch (typeEnum) {
            case REDEEM_COUPON:
                sendCoupon(dto, activityVO, hsaOperationMemberInfo);
                return true;
            case REDEEM_CARD:
                sendMemberCard(dto, hsaOperationMemberInfo);
                return true;
            case REDEEM_MONEY:
                UpdateMemberCardBalanceQO memberCardBalanceVO = getUpdateMemberCardBalanceQO(dto, activityVO);
                memberInfoCardService.updateMemberCardBalance(memberCardBalanceVO);
                return true;
            case REDEEM_INTEGRATION_VALUE:
                sendIntegral(activityVO, hsaOperationMemberInfo);
                return true;
            case REDEEM_GROWTH_VALUE:
                sendGrowth(dto, activityVO, hsaOperationMemberInfo);
                return true;
            default:
                throw new MemberBaseException("兑换类型错误");
        }

    }

    private void sendGrowth(RequestRedeemApplyDTO dto,
                            RespondEditActiveVO activityVO,
                            HsaOperationMemberInfo hsaOperationMemberInfo) {
        RequestMemberGrowthValue request = new RequestMemberGrowthValue();
        request.setMemberInfoGuidList(Collections.singletonList(dto.getMemberGuid()));
        request.setGrowthValue((Integer.parseInt(activityVO.getRedeemRights().get(0).getNum() + EMPTY)));
        request.setGrowthValueType(ADD);
        request.setSource(ThreadLocalCache.getSource());
        request.setRemark(REDEEM_CODE + activityVO.getActiveName() + MINUS + activityVO.getActiveCode());
        request.setSourceType(AmountSourceTypeEnum.REDEEM_CODE.getCode());
        if (Objects.nonNull(hsaOperationMemberInfo))
            request.setOperatorAccountName(hsaOperationMemberInfo.getUserName() + "/" + hsaOperationMemberInfo.getPhoneNum());
        hsaOperationMemberInfoService.updateMemberGrowth(request);
    }

    private void sendIntegral(RespondEditActiveVO activityVO, HsaOperationMemberInfo hsaOperationMemberInfo) {
        if (Objects.isNull(hsaOperationMemberInfo)) {
            return;
        }
        UpdateIntegralQO request = new UpdateIntegralQO();
        request.setIntegral(Integer.parseInt(activityVO.getRedeemRights().get(0).getNum() + EMPTY));
        request.setRemark(REDEEM_CODE + activityVO.getActiveName() + MINUS + activityVO.getActiveCode());
        request.setSourceType(SumValueChangeEnum.REDEEM_CODE.getCode());
        request.setMemberInfoGuids(Collections.singletonList(hsaOperationMemberInfo.getGuid()));
        request.setIntegralType(ADD);
        request.setOperatorAccountName(hsaOperationMemberInfo.getUserName() + "/" + hsaOperationMemberInfo.getPhoneNum());
        hsaOperationMemberInfoService.updateMemberIntegral(request);
    }

    private void sendCoupon(RequestRedeemApplyDTO dto, RespondEditActiveVO activityVO, HsaOperationMemberInfo hsaOperationMemberInfo) {
        Map<String, String> codeMap = new HashMap<>();
        Set<String> labelGuid = new HashSet<>(CollUtil.isNotEmpty(activityVO.getLabelGuidList()) ?
                activityVO.getLabelGuidList() : Collections.emptyList());
        List<HsaMemberCouponLink> hsaMemberCouponLinks = redeemApplyAssembler.formMemberCouponLink(dto, activityVO, hsaOperationMemberInfo, codeMap, labelGuid);
        if (CollUtil.isEmpty(hsaMemberCouponLinks)) {
            return;
        }
        memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.saveBatch(hsaMemberCouponLinks));

        // 筛选已绑定会员
        List<HsaMemberCouponLink> hasMemberCouponLinks = hsaMemberCouponLinks.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getMemberGuid()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(hasMemberCouponLinks)) {

            List<MemberCouponPackageVO> memberCouponPackageVOS = Lists.newArrayList();
            for (HsaMemberCouponLink hsaMemberCouponLink : hasMemberCouponLinks) {
                MemberCouponPackageVO memberCouponPackageVO = new MemberCouponPackageVO();
                memberCouponPackageVO.setCouponType(hsaMemberCouponLink.getCouponType());
                memberCouponPackageVO.setCouponName(hsaMemberCouponLink.getCouponName());
                memberCouponPackageVO.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveEndTime());
                memberCouponPackageVO.setOperSubjectGuid(hsaMemberCouponLink.getOperSubjectGuid());
                memberCouponPackageVO.setEnterpriseGuid(hsaMemberCouponLink.getEnterpriseGuid());
                memberCouponPackageVO.setPhoneNum(hsaOperationMemberInfo.getPhoneNum());
                memberCouponPackageVO.setMemberGuid(hsaOperationMemberInfo.getGuid());
                memberCouponPackageVOS.add(memberCouponPackageVO);
            }
            sendService.sendMemberCouponNotice(memberCouponPackageVOS);

            // 券分组
            Map<String, List<MemberCouponPackageVO>> memberCouponPackageMap = memberCouponPackageVOS.stream()
                    .collect(Collectors.groupingBy(e -> e.getCouponType() + e.getCouponName()));
            List<MemberCouponPackageVO> memberCouponPackageGroupByVOS = Lists.newArrayList();
            memberCouponPackageMap.forEach((key, value) -> {
                MemberCouponPackageVO memberCouponPackageVO = value.get(0);
                memberCouponPackageVO.setCouponNum(value.size());
                memberCouponPackageGroupByVOS.add(memberCouponPackageVO);
            });
            memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.sendMemberCouponNotice(memberCouponPackageGroupByVOS));
        }
    }

    private void sendMemberCard(RequestRedeemApplyDTO dto, HsaOperationMemberInfo hsaOperationMemberInfo) {
        CardBaseInfoVO cardBaseInfoVO = dto.getCardBaseInfoVO();
        try {
            if (Objects.nonNull(hsaOperationMemberInfo)) {
                OpenElectronicCardQO cardQO = new OpenElectronicCardQO();
                String openWayName = dto.getHsaRedeemCodeActive().getActiveName() + "(" + dto.getHsaRedeemCodeActive().getActiveCode() + ")";
                cardQO.singletonCardGuidListDTO(cardBaseInfoVO.getGuid(), cardBaseInfoVO.getCardName(), openWayName, ThreadLocalCache.getSource(), ElectronicOpenWayEnum.REDEEM_OPEN.getCode());
                cardQO.setMemberInfoGuid(Collections.singletonList(hsaOperationMemberInfo.getGuid()));
                electronicCardService.batchOpen(cardQO);
            } else {
                memberCardOperationService.createPhysicalCardSecret(new CreatePhysicalCardSecretQO()
                        .setCardGuid(cardBaseInfoVO.getGuid())
                        .setCardName(cardBaseInfoVO.getCardName())
                        .setIssueElectronicCard(0)
                        .setCreateCount(1)
                        .setCardAmount(cardBaseInfoVO.getCardValueMoney()));
            }
        } catch (MemberBaseException e) {
            throw e;
        } catch (Exception e) {
            log.info("兑换活动开卡失败：{}", MemberInfoCardExceptionEnum.BATCH_OPEN_ELECTRONIC_CARD_FAIL.getDes());
        }
    }

    private static UpdateMemberCardBalanceQO getUpdateMemberCardBalanceQO(RequestRedeemApplyDTO dto, RespondEditActiveVO activityVO) {
        UpdateMemberCardBalanceQO memberCardBalanceVO = new UpdateMemberCardBalanceQO();
        memberCardBalanceVO.setMemberCardGuid(Collections.singletonList(dto.getMemberInfoCardGuid()));
        memberCardBalanceVO.setRechargeAmount(activityVO.getRedeemRights().get(0).getNum());
        memberCardBalanceVO.setAmountFundingType(ADD);
        memberCardBalanceVO.setRemark(REDEEM_CODE + activityVO.getActiveName() + MINUS + activityVO.getActiveCode());
        memberCardBalanceVO.setAmountSourceType(AmountSourceTypeEnum.REDEEM_CODE.getCode());
        return memberCardBalanceVO;
    }
}
