package com.holderzone.member.base.manage;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.CouponPackageAssembler;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponUseMapper;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkInvalidQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponUseQO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseBaseVO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseCountVO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseVO;
import com.holderzone.member.common.support.StoreCommonSupport;
import com.holderzone.member.common.util.page.PageUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 优惠劵核销
 *
 * <AUTHOR>
 * @date 2023/11/21
 **/
@Slf4j
@Component
public class MemberCouponUseManage {

    @Resource
    private HsaMemberCouponLinkMapper memberCouponLinkMapper;

    @Resource
    private HsaMemberCouponUseMapper memberCouponUseMapper;

    @Resource
    private StoreCommonSupport storeCommonSupport;

    @Resource
    private IHsaMemberCouponLinkService memberCouponLinkService;


    /**
     * 核销明细查询
     *
     * @param qo 核销条件
     * @return 明细
     */
    public PageResult<CouponUseVO> pageDetail(CouponUseQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //分页参数
        qo.startPage();
        //查询 券包发放明细
        List<CouponUseVO> packageLinkList = memberCouponLinkMapper.listUseDetail(qo);
        if (CollUtil.isEmpty(packageLinkList)) {
            return PageUtil.emptyPageResult();
        }
        //查询规则
        final List<String> guids = packageLinkList.stream().map(CouponUseBaseVO::getMemberCouponLinkGuid).collect(Collectors.toList());
        final List<HsaMemberCouponLink> couponLinks = memberCouponLinkMapper.queryByGuids(guids);
        final Map<String, HsaMemberCouponLink> couponLinkMap = couponLinks.stream().collect(Collectors.toMap(HsaMemberCouponLink::getGuid, v -> v, (v1, v2) -> v2));
        for (CouponUseVO couponUseVO : packageLinkList) {
            HsaMemberCouponLink couponLink = couponLinkMap.get(couponUseVO.getMemberCouponLinkGuid());
            if (couponLink != null) {
                couponUseVO.setCouponType(couponLink.getCouponType());
                couponUseVO.setCouponPackageType(couponLink.getCouponPackageType());
            }

            //规则
            Optional.ofNullable(couponLinkMap.get(couponUseVO.getMemberCouponLinkGuid()))
                    .ifPresent(hsaMemberCouponLink ->
                            couponUseVO.setRuleVO(CouponPackageAssembler.toCouponGiveRuleVO(hsaMemberCouponLink))
                    );
        }
        return PageUtil.pageResult(packageLinkList);
    }

    /**
     * 统计核销结果
     *
     * @param qo 核销条件
     * @return 核销结果
     */
    public CouponUseCountVO countCouponUseNum(CouponUseQO qo) {
        // 设置操作主体GUID
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());

        // 核销统计 - 如果无数据直接返回空对象
        CouponUseCountVO couponUseCountVO = Optional.ofNullable(memberCouponLinkMapper.countUse(qo))
                .orElse(new CouponUseCountVO());

        // 如果没有核销数据，直接返回
        if (couponUseCountVO.getUseNum() <= 0) {
            return couponUseCountVO;
        }

        // 设置带动消费金额 - 使用Optional处理空值
        Optional.ofNullable(memberCouponLinkMapper.sumOrderPaidAmount(qo))
                .ifPresent(couponUseCountVO::setOrderPaidAmount);

        // 处理TOP核销门店信息
        this.setTopStoreInfo(qo, couponUseCountVO);

        return couponUseCountVO;
    }

    /**
     * 设置TOP核销门店信息
     *
     * @param qo 查询条件
     * @param couponUseCountVO 核销统计结果
     */
    private void setTopStoreInfo(CouponUseQO qo, CouponUseCountVO couponUseCountVO) {
        final String storeGuidStr = memberCouponLinkMapper.queryTopStoreByUse(qo);
        final String storeNameStr = memberCouponLinkMapper.queryTopStoreNameByUse(qo);
        if (StringUtil.isBlank(storeGuidStr)) {
            return;
        }

        // 解析门店GUID列表
        final List<String> storeGuidList = this.parseStoreGuidList(storeGuidStr);
        final List<String> storeNameList = this.parseStoreGuidList(storeNameStr);
        if (CollUtil.isEmpty(storeGuidList)) {
            log.warn("解析门店GUID列表为空, 原始字符串: {}", storeGuidStr);
            return;
        }

        try {
            // 找到出现次数最多的门店GUID和门店名称
            String mostFrequentStoreGuid = findMostFrequentElement(storeGuidList);
            String mostFrequentStoreName = findMostFrequentElement(storeNameList);

            if (StringUtil.isNotBlank(mostFrequentStoreGuid)) {
                couponUseCountVO.setStoreGuid(mostFrequentStoreGuid);
                log.debug("设置TOP门店GUID: {}", mostFrequentStoreGuid);
            }

            if (StringUtil.isNotBlank(mostFrequentStoreName)) {
                couponUseCountVO.setStoreName(mostFrequentStoreName);
                log.debug("设置TOP门店名称: {}", mostFrequentStoreName);
            }

        } catch (Exception e) {
            log.error("查询门店信息异常, 门店GUID列表: {}", storeGuidList, e);
            // 异常情况下不影响主流程，继续执行
        }
    }

    /**
     * 找到列表中出现次数最多的元素
     *
     * @param list 要统计的列表
     * @return 出现次数最多的元素，如果列表为空则返回null
     */
    private String findMostFrequentElement(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }

        // 统计每个元素的出现次数
        Map<String, Long> frequencyMap = list.stream()
                .filter(StringUtil::isNotBlank)
                .collect(Collectors.groupingBy(
                        element -> element,
                        Collectors.counting()
                ));

        // 找到出现次数最多的元素
        return frequencyMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    /**
     * 解析门店GUID列表
     *
     * @param storeGuidStr 门店GUID字符串（以、分隔）
     * @return 门店GUID列表
     */
    private List<String> parseStoreGuidList(String storeGuidStr) {
        if (StringUtil.isBlank(storeGuidStr)) {
            return Collections.emptyList();
        }

        return Arrays.stream(storeGuidStr.split("、"))
                .map(String::trim)
                .filter(StringUtil::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 标记使用
     *
     * @param qo 标记参数
     */
    public void markUse(CouponMarkUseQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        memberCouponLinkService.markUse(qo);
    }

    /**
     * 作废
     *
     * @param qo 作废参数
     */
    public void invalid(CouponMarkInvalidQO qo) {
        memberCouponLinkService.invalid(qo);
    }
}
