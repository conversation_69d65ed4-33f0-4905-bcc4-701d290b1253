package com.holderzone.member.base.controller.merchant;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.member.HsaMemberImportRecordErrorService;
import com.holderzone.member.base.service.member.HsaMemberImportRecordService;
import com.holderzone.member.base.service.member.HsaOperationMemberInfoService;
import com.holderzone.member.base.service.member.MemberPortrayalService;
import com.holderzone.member.base.service.member.feign.MemberFeignService;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.member.MemberAddDTO;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.member.MemberUpdateDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.partner.PartnerReserveMemberInfoDTO;
import com.holderzone.member.common.dto.portrayal.MemberPortrayalDetailsDTO;
import com.holderzone.member.common.enums.exception.ExceptionEnum;
import com.holderzone.member.common.enums.member.MemberStoreEnum;
import com.holderzone.member.common.qo.integral.UpdateIntegralQO;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.vo.member.*;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 运营主体会员信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-12
 */
@RestController
@RequestMapping("/hsa-member")
@Slf4j
public class HsaOperationMemberInfoController {

    @Lazy
    @Autowired
    private HsaOperationMemberInfoService hsaOperationMemberInfoService;

    @Resource
    private IHsaMemberCouponLinkService hsaMemberCouponLinkService;

    @Lazy
    @Resource
    private HsaMemberImportRecordErrorService hsaMemberImportRecordErrorService;

    @Lazy
    @Resource
    private HsaMemberImportRecordService hsaMemberImportRecordService;

    @Resource
    private MemberPortrayalService memberPortrayalService;

    @Value("${member.upload.memberDownloadExcelUrl}")
    private String downloadExcelUrl;

    @Value("${member.upload.partnerMemberDownloadExcelUrl}")
    private String partnerMemberDownloadExcelUrl;

    @Resource
    private MemberFeignService memberFeignService;

    /**
     * 保存
     *
     * @param request request model
     * @return boolean
     */
    @ApiOperation("新增顾客")
    @PostMapping(value = "/saveOperationMemberInfo", produces = "application/json;charset=utf-8")
    public Result saveOperationMemberInfo(@RequestBody @Validated SaveOperationMemberInfoQO request) {
        return Result.success(hsaOperationMemberInfoService.saveOperationMemberInfo(request));
    }

    /**
     * 调整积分
     *
     * @param request 调整积分请求参数
     * @return 操作结果
     */
    @ApiOperation("调整积分")
    @PostMapping(value = "/update_member_integral", produces = "application/json;charset=utf-8")
    public Result<Boolean> updateMemberIntegral(@RequestBody @Validated UpdateIntegralQO request) {
        return Result.success(hsaOperationMemberInfoService.updateMemberIntegral(request));
    }

    /**
     * 查询会员列表 一体机 人脸筛查
     *
     * @param memberListQO 查询会员列表QO
     * @return 会员列表返回结果
     */
    @PostMapping("/ter_list_member_info")
    Result<PageResult<TerFaceMemberInfoVO>> terListMemberInfo(@RequestBody TerFaceMemberListQO memberListQO) {
        return Result.success(hsaOperationMemberInfoService.terListMemberInfo(memberListQO));
    }

    /**
     * 人脸绑定
     *
     * @param request
     */
    @ApiOperation("人脸绑定")
    @PostMapping(value = "/member_face_registered", produces = "application/json;charset=utf-8")
    public Result memberFaceRegistered(@RequestBody MemberHumanFace request) {
        return Result.success(hsaOperationMemberInfoService.memberFaceRegistered(request));
    }

    /**
     * 人脸绑定 一体机使用
     *
     * @param request
     */
    @ApiOperation("人脸绑定")
    @PostMapping(value = "/ter_member_face_registered", produces = "application/json;charset=utf-8")
    public Result<Boolean> terMemberFaceRegistered(@RequestBody MemberHumanFace request) {
        return Result.success(hsaOperationMemberInfoService.terMemberFaceRegistered(request));
    }


    /**
     * 人脸清除
     *
     * @param request
     */
    @ApiOperation("人脸清除")
    @PostMapping(value = "/member_face_clear", produces = "application/json;charset=utf-8")
    public Result memberFaceClear(@RequestBody MemberHumanFace request) {
        return Result.success(hsaOperationMemberInfoService.memberFaceClear(request));
    }

    /**
     * tryReleaseShared更新顾客信息
     *
     * @param request request model
     * @return boolean
     */
    @ApiOperation("tryReleaseShared更新顾客信息")
    @PostMapping(value = "/update_by_member_guid", produces = "application/json;charset=utf-8")
    public Result updateByMemberGuid(@RequestBody UpdateOperationMemberInfoQO request) {
        return Result.isSuccess(hsaOperationMemberInfoService.updateByMemberGuid(request));
    }

    /**
     * 会员导入
     *
     * @param fileUrl excel文件地址
     * @return 操作结果
     */
    @ApiOperation("会员导入，传递excel文件地址")
    @GetMapping(value = "/memberUploadExcelUrl", produces = "application/json;charset=utf-8")
    public Result memberUploadExcelUrl(@RequestParam("fileUrl") String fileUrl, @RequestParam("name") String name) {
        return Result.success(hsaOperationMemberInfoService.sendMemberUploadExcelUrl(fileUrl, name));
    }

    /**
     * 会员导入模板下载地址
     *
     * @return
     */
    @ApiOperation("会员导入模板下载地址，返回excel文件地址")
    @GetMapping(value = "/downloadExcelUrl", produces = "application/json;charset=utf-8")
    public Result downloadExcelUrl() {
        if (ThreadLocalCache.isPartner()) {
            return Result.success(partnerMemberDownloadExcelUrl);
        }
        return Result.success(downloadExcelUrl);
    }

    /**
     * 批量下载会员失败导入结果
     *
     * @param guid
     * @return 操作结果
     */
    @ApiOperation("批量下载会员失败导入结果，返回excel文件地址")
    @GetMapping(value = "/download_member_import_error", produces = "application/json;charset=utf-8")
    public Result memberImportRecordErrorDownloadExcelUrl(@RequestParam(value = "guid") String guid) {
        return Result.success(hsaMemberImportRecordErrorService.memberImportRecordErrorDownloadExcelUrl(guid));
    }

    /**
     * 查询会员导入记录
     *
     * @param pageIndex
     * @param pageSize
     * @return 操作结果
     */
    @ApiOperation("查询会员导入记录")
    @GetMapping(value = "/query_member_import_record", produces = "application/json;charset=utf-8")
    public Result getListMemberImportRecord(@RequestParam(value = "pageIndex") Long pageIndex,
                                            @RequestParam(value = "pageSize") Long pageSize) {
        return Result.success(hsaMemberImportRecordService.getListMemberImportRecord(pageIndex, pageSize));
    }

    /**
     * 更新记录下载次数
     *
     * @param guid
     * @return 操作结果
     */
    @ApiOperation("更新记录下载次数")
    @GetMapping(value = "/update/download/num", produces = "application/json;charset=utf-8")
    public Result updateDownloadNum(@RequestParam(value = "guid") String guid) {
        return Result.success(hsaMemberImportRecordService.updateDownloadNum(guid));
    }

    /**
     * 查看会员详情
     *
     * @return 操作结果
     */
    @GetMapping(value = "/get_detail", produces = "application/json;charset=utf-8")
    public Result<OperationMemberDetailVO> getOperationMemberDetail(@RequestParam("guid") String guid) {
        return Result.success(hsaOperationMemberInfoService.getOperationMemberDetail(guid));
    }

    @GetMapping(value = "/get/member/label/grade", produces = "application/json;charset=utf-8")
    public MemberLabelGradeVO getMemberLabelAndGradeInfo(@RequestParam("guid") String guid) {
        return memberFeignService.getMemberLabelAndGradeInfo(guid);
    }

    /**
     * 修改会员密码
     *
     * @return 操作结果
     */
    @PutMapping(value = "/update_password", produces = "application/json;charset=utf-8")
    public Result updateOperationMemberPhone(@RequestBody @Validated OperationMemberPasswordQO request) {
        return Result.isSuccess(hsaOperationMemberInfoService.updateOperationMemberPassword(request));
    }

    /**
     * 修改会员账户密码
     *
     * @param request 修改会员账户密码
     * @return 操作结果
     */
    @PutMapping(value = "/update_account_password", produces = "application/json;charset=utf-8")
    public Result<Boolean> updateMemberAccountPassword(@RequestBody @Validated OperationMemberPasswordQO request) {
        return Result.isSuccess(hsaOperationMemberInfoService.updateMemberAccountPassword(request));
    }

    /**
     * 修改会员账户状态（启用或者禁用）
     *
     * @param request
     * @return //
     */
    @PutMapping(value = "/update_state", produces = "application/json;charset=utf-8")
    public Result updateOperationMemberState(@RequestBody @Validated OperationMemberStateQO request) {
        return Result.isSuccess(hsaOperationMemberInfoService.updateOperationMemberState(request));
    }

    /**
     * 修改会员手机号码
     *
     * @return 操作结果
     */
    @PutMapping(value = "/update_phone", produces = "application/json;charset=utf-8")
    public Result updateOperationMemberPhone(@RequestBody @Validated OperationMemberPhoneQO request) {
        return Result.isSuccess(hsaOperationMemberInfoService.updateOperationMemberPhone(request));
    }

    /**
     * 修改会员门店信息
     *
     * @param request
     * @return 操作结果
     */
    @PutMapping(value = "/update_store", produces = "application/json;charset=utf-8")
    public Result updateOperationMemberStore(@RequestBody @Validated OperationMemberStoreQO request) {
        return Result.isSuccess(hsaOperationMemberInfoService.updateOperationMemberStore(request));
    }

    /**
     * 通过会员guid解除会员门店信息
     *
     * @param guid
     * @return 操作结果
     */
    @GetMapping(value = "/update_relieve_store", produces = "application/json;charset=utf-8")
    public Result relieveOperationMemberStore(String guid) {
        int rows = hsaOperationMemberInfoService.relieveOperationMemberStore(guid);
        if (rows > 0) {
            return Result.success(MemberStoreEnum.RELIEVE_STORE.getDes());
        }
        return Result.error(ExceptionEnum.OPERATION_FAILURE.getDes());
    }

    /**
     * 查询会员列表
     *
     * @param memberListQO 查询会员列表QO
     * @return 查询结果
     */
    @PostMapping("/list_member_info")
    public Result listMemberInfo(@RequestBody @Validated MemberListQO memberListQO) {
        try {
            return Result.success(hsaOperationMemberInfoService.listMemberInfo(memberListQO));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Result.error(ExceptionEnum.OPERATION_FAILURE.getDes());
    }

    /**
     * 批量调整会员成长值
     *
     * @param request request
     * @return
     */
    @PostMapping("/update_member_growth")
    Result updateMemberGrowth(@RequestBody RequestMemberGrowthValue request) {
        return Result.success(hsaOperationMemberInfoService.updateMemberGrowth(request));
    }

    /**
     * 实时计算会员等级
     *
     * @param growthValue
     * @return
     */
    @GetMapping("/calculate_extra_grade")
    Result calculateExtraAmount(@RequestParam(value = "growthValue") Integer growthValue) {
        return Result.success(hsaOperationMemberInfoService.calculateExtraAmount(growthValue));
    }

    /**
     * 查看等级账户列表
     *
     * @param request 筛选条件
     * @return 操作结果
     */
    @ApiOperation("查看等级账户列表")
    @PostMapping("/account_list")
    public Result<PageResult> getAccountList(@RequestBody GradeAccountQO request) {

        return Result.success(hsaOperationMemberInfoService.getAccountList(request));
    }

    /**
     * 导出等级账户列表信息
     *
     * @param request 筛选条件
     * @return 操作结果
     */
    @ApiOperation("导出等级账户列表信息")
    @PostMapping("/account_export")
    public void accountExport(@RequestBody GradeAccountQO request, HttpServletResponse response) {
        hsaOperationMemberInfoService.accountExport(request, response);
    }

    /**
     * 查询当前会员等级权益记录列表
     *
     * @param memberInfoGuid 会员guid
     * @return 操作结果
     */
    @ApiOperation("查询当前会员等级权益记录列表")
    @GetMapping("/get_grade_equities_record/{memberInfoGuid}")
    public Result getGradeEquitiesRecordList(@PathVariable("memberInfoGuid") String memberInfoGuid) {
        List<GradeEquitiesRecordVO> gradeEquitiesRecordList = null;
        gradeEquitiesRecordList = hsaOperationMemberInfoService.getGradeEquitiesRecordList(memberInfoGuid);
        return Result.success(gradeEquitiesRecordList);
    }

    /**
     * 通过会员guid以及运营主体查询会员列表
     *
     * @param guids           guids
     * @param operSubjectGuid 运营主体
     * @return 会员列表
     */
    @PostMapping(value = "/get_operation_member_info_list")
    public Result<List<HsaOperationMemberInfo>> getOperationMemberInfoList(@RequestBody List<String> guids,
                                                                           @RequestParam(value = "operSubjectGuid", required = false) String operSubjectGuid) {
        return Result.success(hsaOperationMemberInfoService.getOperationMemberInfoList(guids, operSubjectGuid));
    }

    /**
     * 通过会员guid以及运营主体查询会员列表
     */
    @PostMapping(value = "/get_operation_member_info_list_by_guids")
    public List<MemberInfoVO> getOperationMemberInfoList(@RequestBody MemberListQO qeury) {
        log.info("会员guids查询会员信息:{}", JacksonUtils.writeValueAsString(qeury));
        return hsaOperationMemberInfoService.listMemberInfoByGuids(qeury);
    }

    /**
     * 通过会员手机号查询当前运营主体下会员列表
     */
    @PostMapping(value = "/get_operation_member_info_list_by_phone")
    public Result<PageResult<MemberInfoVO>> getOperationMemberInfoListByPhone(@RequestBody MemberListQO query) {
        log.info("通过会员手机号查询当前运营主体下会员列表:{}", JacksonUtils.writeValueAsString(query));
        return Result.success(hsaOperationMemberInfoService.listMemberInfoByPhone(query));
    }

    /**
     * 查看会员手机相关信息列表
     *
     * @param guids           会员guids
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    @PostMapping(value = "/get_member_phone_list")
    public Result<List<MemberPhoneVO>> getMemberPhoneList(@RequestBody List<String> guids,
                                                          @RequestParam(value = "operSubjectGuid", required = false) String operSubjectGuid) {
        return Result.success(hsaOperationMemberInfoService.getMemberPhoneList(guids, operSubjectGuid));
    }

    /**
     * 通过运营主体查询会员列表
     *
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    @PostMapping(value = "/get_member_list_by_subject")
    public List<HsaOperationMemberInfo> getMemberListBySubject(@RequestParam("operSubjectGuid") String operSubjectGuid) {
        return hsaOperationMemberInfoService.getMemberListBySubject(operSubjectGuid);
    }

    /**
     * 通过运营主体查询会员列表
     *
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    @PostMapping(value = "/get_member_guid_by_subject")
    public Result<List<String>> getMemberGuidBySubject(@RequestParam("operSubjectGuid") String operSubjectGuid) {
        return Result.success(hsaOperationMemberInfoService.getMemberGuidBySubject(operSubjectGuid));
    }

    /**
     * 通过运营主体查询会员列表
     *
     * @param guids 会员guids
     * @return 操作结果
     */
    @PostMapping(value = "/get_member_list_by_guids")
    public Result<List<HsaOperationMemberInfo>> getMemberListByGuids(@RequestBody List<String> guids) {
        return Result.success(hsaOperationMemberInfoService.getMemberListByGuids(guids));
    }

    /**
     * 通过关键字查询会员guid
     *
     * @param keywords        关键字
     * @param operSubjectGuid 运营主体
     * @return 操作结果
     */
    @PostMapping(value = "/get_member_info_guids")
    public List<String> getMemberInfoGuids(@RequestParam("keywords") String keywords,
                                           @RequestParam(value = "operSubjectGuid", required = false) String operSubjectGuid) {
        return hsaOperationMemberInfoService.getMemberInfoGuids(keywords, operSubjectGuid);
    }

    @SneakyThrows
    @ApiOperation("处理成长值")
    @PostMapping(value = "/initializeGrowthValue")
    public Result initializeGrowthValue(@RequestParam(value = "file") MultipartFile file, String operSubjectGuid) {
        hsaOperationMemberInfoService.initializeGrowthValue(file, operSubjectGuid);
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation("查询会员名称")
    @PostMapping(value = "/findMemberNameByGuid")
    Result<String> findMemberNameByGuid(@RequestParam(value = "memberGuid") String memberGuid) {
        return Result.success(hsaOperationMemberInfoService.findMemberNameByGuid(memberGuid));
    }

    @ApiOperation("增加角色类型")
    @PostMapping(value = "/add_role_type")
    public Result<Void> addRoleType(@RequestParam(value = "phoneNum") String phoneNum,
                                    @RequestParam(value = "roleType") String roleType) {
        hsaOperationMemberInfoService.addRoleType(phoneNum, roleType);
        return Result.success();
    }

    @ApiOperation("删除角色类型")
    @PostMapping(value = "/delete_role_type")
    public Result<Void> deleteRoleType(@RequestParam(value = "phoneNum") String phoneNum,
                                       @RequestParam(value = "roleType") String roleType) {
        hsaOperationMemberInfoService.deleteRoleType(phoneNum, roleType);
        return Result.success();
    }

    @ApiOperation("预定会员信息查询")
    @GetMapping("/query_member_by_guid_list")
    public List<PartnerReserveMemberInfoDTO> queryMemberByGuidList(@RequestParam(value = "memberGuidList") List<String> memberGuidList) {
        return hsaOperationMemberInfoService.queryMemberByGuidList(memberGuidList);
    }

    @ApiOperation("就餐会员信息查询")
    @GetMapping("/query_member_by_phone_list")
    public List<PartnerReserveMemberInfoDTO> queryMemberByPhoneList(@RequestParam(value = "phoneList") List<String> phoneList) {
        return hsaOperationMemberInfoService.queryMemberByPhoneList(phoneList);
    }

    @ApiOperation("初始化老数据会员全拼")
    @PostMapping("/initMemberPinyin")
    Result<Boolean> initMemberPinyin(@RequestBody List<String> operSubjectGuid) {
        return Result.success(hsaOperationMemberInfoService.initMemberPinyin(operSubjectGuid));
    }

    /**
     * 查询会员信息以及亲属人脸
     * @param acquireMemberFaceQO acquireMemberFaceQO
     * @return AcquireMemberFaceVO
     */
    @ApiOperation("查询会员信息以及亲属人脸")
    @PostMapping("/getAcquireMemberFace")
    Result<AcquireMemberFaceVO> getAcquireMemberFace(@RequestBody AcquireMemberFaceQO acquireMemberFaceQO) {
        return Result.success(hsaOperationMemberInfoService.getAcquireMemberFace(acquireMemberFaceQO));
    }

    @ApiOperation("校验会员账号是否存在")
    @PostMapping("/checkMemberAccount")
    Result<Boolean> checkMemberAccount(@RequestParam(value = "memberAccount") String memberAccount) {
        return Result.success(hsaOperationMemberInfoService.checkMemberAccount(memberAccount));
    }

    /**
     * 获取会员信息
     *
     * @param queryDTO 请求参数
     * @return 会员信息
     */
    @PostMapping("/getMemberInfo")
    Result<MemberBasicInfoVO> getMemberInfo(@RequestBody MemberQueryDTO queryDTO) {
        return Result.success(hsaOperationMemberInfoService.getMemberInfo(queryDTO));
    }


    /**
     * 根据openid查询会员信息
     */
    @ApiOperation("根据openid查询会员信息")
    @PostMapping("/getMemberByOpenid")
    public Result<MemberBasicInfoVO> getMemberByOpenid(@RequestBody MemberQueryDTO queryDTO) {
        return Result.success(hsaOperationMemberInfoService.getMemberByOpenid(queryDTO));
    }

    /**
     * 批量获取会员信息
     *
     * @param queryDTO 请求参数
     * @return 会员信息
     */
    @PostMapping("/batch/getMemberInfo")
    Result<List<MemberBasicInfoVO>> batchGetMemberInfo(@RequestBody MemberQueryDTO queryDTO) {
        return Result.success(hsaOperationMemberInfoService.batchGetMemberInfo(queryDTO));
    }

    /**
     * 获取会员账户信息
     *
     * @param queryDTO 请求参数
     * @return 会员账户信息
     */
    @PostMapping("/memberAccountInfo")
    Result<MemberBasicInfoVO> memberAccountInfo(@RequestBody MemberQueryDTO queryDTO) {
        return Result.success(hsaOperationMemberInfoService.getMemberAccountInfo(queryDTO));
    }

    /**
     * 新增会员信息
     *
     * @param addDTO 请求参数
     * @return 会员信息
     */
    @PostMapping("/addMemberInfo")
    Result<MemberBasicInfoVO> addMemberInfo(@RequestBody MemberAddDTO addDTO) {
        return Result.success(hsaOperationMemberInfoService.addMemberInfo(addDTO));
    }

    /**
     * 修改会员信息
     *
     * @param updateDTO 请求参数
     * @return 操作结果
     */
    @PostMapping("/updateMemberInfo")
    Result<Void> updateMemberInfo(@RequestBody MemberUpdateDTO updateDTO) {
        hsaOperationMemberInfoService.updateMemberInfo(updateDTO);
        return Result.success();
    }


    /**
     * 手动发券
     */
    @ApiOperation("手动发券")
    @PostMapping("/sendMemberCoupon")
    Result<Boolean> sendMemberCoupon(@RequestBody MemberSendCouponQO memberSendCouponQO) {
        log.info("手动发送优惠券：{}", JSON.toJSONString(memberSendCouponQO));
        hsaMemberCouponLinkService.sendMemberCoupon(memberSendCouponQO);
        return Result.success();
    }

    /**
     * 获取会员列表（按等级类型：付费或免费）
     *
     * @param gradeType 等级类型 1:付费会员 0:免费会员
     * @return 会员列表
     */
    @GetMapping("/listMembersByGradeType")
    public Result<List<MemberBasicInfoVO>> listMembersByGradeType(@RequestParam("gradeType") Integer gradeType) {
        return Result.success(hsaOperationMemberInfoService.listMembersByGradeType(gradeType));
    }

    /**
     * 查询会员画像
     */
    @ApiOperation("查询会员画像")
    @PostMapping("/queryMemberPortrayal")
    public Result<MemberPortrayalDetailsDTO> queryMemberPortrayal(@RequestBody MemberQueryDTO memberQueryDTO) {
        MemberPortrayalDetailsDTO memberPortrayalDetailsDTO = memberPortrayalService.queryMemberPortrayal(memberQueryDTO.getMemberGuid());
        return Result.success(memberPortrayalDetailsDTO);
    }
}
