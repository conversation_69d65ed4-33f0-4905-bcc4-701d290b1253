package com.holderzone.member.base.support;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.manage.MemberCouponQueryManage;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.enums.ApplyCommodityEnum;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.module.settlement.apply.dto.*;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.apply.vo.detail.SettlementApplyDiscountDetailOfCouponVO;
import com.holderzone.member.common.module.settlement.constant.SettlementConstant;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.coupon.ResponseCouponCommodityVO;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代金券券工具类
 *
 * <AUTHOR>
 * @date 2023/10/24
 * @since 1.8
 */
@Slf4j
@Component
public class VoucherCouponSupport {

    /**
     * 优惠券查询管理
     */
    @Resource
    private MemberCouponQueryManage couponQueryManage;

    @Resource
    private MemberCouponSupport memberCouponSupport;

    /**
     * 代金优惠券查询
     *
     * @param dto 入参
     * @return 优惠对象
     */
    public SettlementApplyOrderVO listDiscount(SettlementApplyOrderDTO dto) {
        final List<HsaMemberCouponLink> couponLinks = couponQueryManage.listByUseSettlement(CouponTypeEnum.COUPON_VOUCHER.getCode(), dto);
        if (CollUtil.isEmpty(couponLinks)) {
            return null;
        }
        return memberCouponSupport.fromSettlementApplyOrderVO(dto, couponLinks);
    }

    /**
     * 计算优惠
     *
     * @param dto 入参
     * @return 订单对象
     */
    public SettlementApplyOrderVO calculateDiscount(SettlementApplyOrderDTO dto) {
        final SettlementApplyOrderVO orderVO = listDiscount(dto);
        return calculateCheckDiscount(dto, orderVO);
    }

    /**
     * 计算优惠
     *
     * @param dto 入参
     * @return 订单对象
     */
    public SettlementApplyOrderVO autoCheckDiscount(SettlementApplyOrderDTO dto) {
        SettlementApplyOrderVO orderVO = listDiscount(dto);
        if (orderVO == null || CollUtil.isEmpty(orderVO.getDiscountList())) {
            return orderVO;
        }
        //计算最优结果
        final SettlementApplyOrderVO resultOrderVo = autoCalculateCheckDiscount(dto, orderVO);
        resultOrderVo.getDiscountList().parallelStream().forEach(list -> {
            //这里不要，还要跟其他优惠一起对比
            list.setDiscountAmount(BigDecimal.ZERO);
            for (SettlementApplyDiscountDetailVO detailVO : list.getDiscountList()) {
                if (detailVO.getIsChecked() == BooleanEnum.TRUE.getCode()) {
                    //最优标志
                    detailVO.setOptimal(BooleanEnum.TRUE.getCode());
                    //先取消，还要和其他优惠一起算最优
                    detailVO.setIsChecked(BooleanEnum.FALSE.getCode());
                }
            }
        });
        return resultOrderVo;
    }

    /**
     * 自动计算最优
     *
     * @param dto     订单入参
     * @param orderVO 订单返回对象
     * @return 优惠结果
     */
    private SettlementApplyOrderVO autoCalculateCheckDiscount(SettlementApplyOrderDTO dto, SettlementApplyOrderVO orderVO) {
        //构造自动选择最优参数
        final Map<String, SettlementCouponDTO> checkDiscountMap = buildAutoCheckParamMap(dto, orderVO);
        if (checkDiscountMap.isEmpty()) {
            return orderVO;
        }
        //深拷贝对象
        final String orderStr = JacksonUtils.writeValueAsString(orderVO);
        final String dtoStr = JacksonUtils.writeValueAsString(dto);
//        //1、单选不可叠加 优惠券；多张
        for (Map.Entry<String, SettlementCouponDTO> entry : checkDiscountMap.entrySet()) {
            final SettlementApplyOrderVO currentOrderVo = JacksonUtils.toObject(SettlementApplyOrderVO.class, orderStr);
            final SettlementApplyOrderDTO currentDto = JacksonUtils.toObject(SettlementApplyOrderDTO.class, dtoStr);
            //计算结果
            final SettlementApplyOrderVO resultOrderVo = calculateCheckDiscountResult(currentDto, currentOrderVo,
                    Collections.singletonMap(entry.getKey(), entry.getValue()));
            //优惠用完
            if (resultOrderVo.getOderInfo().useUp()) {
                return resultOrderVo;
            }
            //最优替换
            if (orderVO.getOderInfo().getDiscountAmount().compareTo(resultOrderVo.getOderInfo().getDiscountAmount()) < 0) {
                orderVO = resultOrderVo;
            }
        }
        //无叠加
        if (CollUtil.isEmpty(dto.getAppendMap())) {
            log.warn("优惠券：无叠加规则，默认不可叠加最优");
            return orderVO;
        }
        //2、多选叠加
        return autoCalculateCheckMoreDiscount(dto, orderVO, checkDiscountMap, orderStr, dtoStr);
    }

    /**
     * 多选叠加
     *
     * @param dto              订单入参
     * @param orderVO          订单返回值
     * @param checkDiscountMap 选中优惠
     * @param orderStr         订单str
     * @param dtoStr           订单入参str
     * @return 优惠结果
     */
    private SettlementApplyOrderVO autoCalculateCheckMoreDiscount(SettlementApplyOrderDTO dto,
                                                                  SettlementApplyOrderVO orderVO,
                                                                  Map<String, SettlementCouponDTO> checkDiscountMap,
                                                                  String orderStr,
                                                                  String dtoStr) {
        final Set<String> appendCouponList = getAppendList(dto);
        //单选已经用了，再加回去
        if (!checkDiscountMap.isEmpty()) {
            final Map<String, Integer> couponLimtNumberMap = dto.getCouponLimtNumberMap();
            for (Map.Entry<String, SettlementCouponDTO> entry : checkDiscountMap.entrySet()) {
                //优惠券限制数量,默认1
                final Integer couponLimit = couponLimtNumberMap.getOrDefault(entry.getKey(), 1);
                entry.getValue().setCouponLimitNum(couponLimit);
            }
        }
        //保留可叠加
        checkDiscountMap.entrySet().removeIf(next -> !appendCouponList.contains(next.getKey()));
        //深拷贝对象
        final SettlementApplyOrderVO currentOrderVo = JacksonUtils.toObject(SettlementApplyOrderVO.class, orderStr);
        final SettlementApplyOrderDTO currentDto = JacksonUtils.toObject(SettlementApplyOrderDTO.class, dtoStr);
        //计算结果
        final SettlementApplyOrderVO resultOrderVo = calculateCheckDiscountResult(currentDto, currentOrderVo, checkDiscountMap);
        //优惠用完
        if (resultOrderVo.getOderInfo().useUp()) {
            return resultOrderVo;
        }
        //对比最优
        if (orderVO.getOderInfo().getDiscountAmount().compareTo(resultOrderVo.getOderInfo().getDiscountAmount()) < 0) {
            return resultOrderVo;
        }
        return orderVO;
    }

    /**
     * 获取券累加类型
     *
     * @param dto 订单入参
     * @return 叠加类型set
     */
    private Set<String> getAppendList(SettlementApplyOrderDTO dto) {
        final Map<Integer, Set<String>> appendMap = dto.getAppendMap();
        Set<String> resultOptionList = new HashSet<>();
        //todo 所有类型优惠券
        SettlementDiscountOptionEnum.getCoupon().forEach(c -> {
            Set<String> optionDiscountList = appendMap.get(c);
            if (CollUtil.isNotEmpty(optionDiscountList)) {
                final List<String> optionKey = optionDiscountList.stream()
                        .map(discountGuid -> SettlementDiscountOptionEnum.optionMapKey(c, discountGuid))
                        .collect(Collectors.toList());
                resultOptionList.addAll(optionKey);
            }
        });
        return resultOptionList;
    }

    /**
     * 构造自动选择最优参数
     *
     * @param dto     订单入参
     * @param orderVO 订单对象
     * @return discountGuid, SettlementCouponDTO
     */
    private Map<String, SettlementCouponDTO> buildAutoCheckParamMap(SettlementApplyOrderDTO dto,
                                                                    SettlementApplyOrderVO orderVO) {
        //构造优惠券选中map：所有启用的
        final LinkedHashMap<String, SettlementCouponDTO> checkDiscountMap = orderVO.getDiscountList().stream()
                .filter(list -> list.getDiscountList().stream()
                        .anyMatch(d -> d.getIsEnabled() == BooleanEnum.TRUE.getCode())
                )
                .collect(Collectors.toMap(c ->
                                //key
                                SettlementDiscountOptionEnum.optionMapKey(c.getDiscountOption(), c.getDiscountGuid()),
                        c -> {
                            //value
                            final List<String> couponCodes = c.getDiscountList().stream()
                                    .map(SettlementApplyDiscountDetailVO::getDiscountOptionId)
                                    .collect(Collectors.toList());
                            //券类型对象
                            return new SettlementCouponDTO(couponCodes, c.getCouponLimitNum());
                        }
                        , (v1, v2) -> v1,
                        LinkedHashMap::new));
        if (CollUtil.isEmpty(checkDiscountMap)) {
            return Collections.emptyMap();
        }
        //优惠券限制数量 > 1 的更新
        final Map<String, Integer> couponLimtNumberMap = dto.getCouponLimtNumberMap();
        if (!couponLimtNumberMap.isEmpty()) {
            for (Map.Entry<String, Integer> entry : couponLimtNumberMap.entrySet()) {
                //优惠券限制数量
                Optional.ofNullable(checkDiscountMap.get(entry.getKey()))
                        .ifPresent(value -> value.setCouponLimitNum(entry.getValue()));
            }
        }
        return checkDiscountMap;
    }

    /**
     * 计算选中优惠
     *
     * @param dto     入参
     * @param orderVO 订单
     * @return 计算结果
     */
    private SettlementApplyOrderVO calculateCheckDiscount(SettlementApplyOrderDTO dto, SettlementApplyOrderVO orderVO) {
        if (orderVO == null || CollUtil.isEmpty(orderVO.getDiscountList())) {
            return orderVO;
        }
        
        if (CollUtil.isNotEmpty(dto.getCheckDiscountList())) {
            //选中优惠券，数量
            final LinkedHashMap<String, SettlementCouponDTO> checkDiscountMap = dto.getCheckDiscountList().stream()
                    .filter(c -> SettlementDiscountOptionEnum.isCoupon(c.getDiscountOption()))
                    .collect(Collectors.toMap(c ->
                                    SettlementDiscountOptionEnum.optionMapKey(c.getDiscountOption(), c.getDiscountGuid()),
                            c -> new SettlementCouponDTO(c.getDiscountOptionIds(), c.getCouponLimitNum()),
                            (v1, v2) -> v2,
                            LinkedHashMap::new)
                    );
            log.info("选中了优惠券:{}", JacksonUtils.writeValueAsString(checkDiscountMap));
            if (checkDiscountMap.isEmpty()) {
                return orderVO;
            }
            return calculateCheckDiscountResult(dto, orderVO, checkDiscountMap);
        } else {
            //列表计算优惠 代金券单独优惠项
            return calculateList(dto, orderVO);
        }
    }

    /**
     * 列表计算优惠（没有选中券时）
     */
    private SettlementApplyOrderVO calculateList(SettlementApplyOrderDTO dto, SettlementApplyOrderVO orderVO) {
        final List<SettlementApplyDiscountVO> discountList = orderVO.getDiscountList();
        if (discountList.isEmpty()) {
            return orderVO;
        }
        
        //商品
        Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap = getCommodityCodeMap(dto);
        
        //类型
        for (SettlementApplyDiscountVO optionDiscountVO : discountList) {
            //具体某张券
            for (SettlementApplyDiscountDetailVO detailVO : optionDiscountVO.getDiscountList()) {
                //启用券才计算
                if (detailVO.getIsEnabled() != BooleanEnum.TRUE.getCode()) {
                    continue;
                }
                
                //计算单张券的优惠金额
                calculateSingleVoucherDiscount(dto, commodityCodeMap, detailVO);
            }
            //计算累计优惠
            optionDiscountVO.calculateDiscountAmount();
        }
        
        return orderVO;
    }

    /**
     * 计算单张代金券的优惠金额
     */
    private void calculateSingleVoucherDiscount(SettlementApplyOrderDTO dto, 
                                               Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap,
                                               SettlementApplyDiscountDetailVO detailVO) {
        SettlementApplyDiscountDetailOfCouponVO couponVO = (SettlementApplyDiscountDetailOfCouponVO) detailVO;
        
        //计算该代金券能优惠的金额
        BigDecimal couponAmount = couponVO.getCouponAmount();
        BigDecimal totalCommodityAmount = commodityCodeMap.values().stream()
                .flatMap(Collection::stream)
                .map(SettlementApplyCommodityDTO::getCommodityTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        //代金券优惠金额不能超过商品总金额
        BigDecimal actualDiscount = couponAmount.min(totalCommodityAmount);
        couponVO.setDiscountAmount(actualDiscount);
    }

    /**
     * 计算的最终结果
     *
     * @param dto              订单入参
     * @param orderVO          订单结果对象
     * @param checkDiscountMap 选中优惠
     * @return 计算结果
     */
    private SettlementApplyOrderVO calculateCheckDiscountResult(SettlementApplyOrderDTO dto,
                                                                SettlementApplyOrderVO orderVO,
                                                                Map<String, SettlementCouponDTO> checkDiscountMap) {
        //商品
        Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap = getCommodityCodeMap(dto);
        //累计使用
        checkMoreCoupon(dto, orderVO, checkDiscountMap, commodityCodeMap);
        //已选择累计金额
        final BigDecimal sum = orderVO.getDiscountList().stream()
                .map(d -> BigDecimalUtil.nonNullValue(d.getDiscountAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderVO.getOderInfo().setDiscountAmount(sum);
        //返回商品分摊金额
        final List<SettlementApplyCommodityVO> commodityList = commodityCodeMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(c -> SettlementApplyCommodityVO.build(c.getRid(), c.getCommodityId(), c.getDiscountFee()))
                .collect(Collectors.toList());
        orderVO.setCommodityList(commodityList);
        return orderVO;
    }

    /**
     * 选中多张优惠券
     *
     * @param dto              订单入参
     * @param orderVO          订单结果
     * @param checkDiscountMap 选中优惠
     * @param commodityCodeMap 商品
     */
    private void checkMoreCoupon(SettlementApplyOrderDTO dto,
                                 SettlementApplyOrderVO orderVO,
                                 Map<String, SettlementCouponDTO> checkDiscountMap,
                                 Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap) {
        if (checkDiscountMap.isEmpty()) {
            return;
        }
        List<SettlementApplyDiscountVO> sortCheckDiscountList = getSortCheckDiscountList(orderVO, checkDiscountMap);
        if (sortCheckDiscountList.isEmpty()) {
            return;
        }
        //类型
        for (SettlementApplyDiscountVO optionDiscountVO : sortCheckDiscountList) {
            //具体某张券
            for (SettlementApplyDiscountDetailVO detailVO : optionDiscountVO.getDiscountList()) {

                //判断优惠券单笔限制张数
                if (isUseUpCoupon(dto)) {
                    //计算累计优惠
                    detailVO.setIsEnabled(BooleanEnum.FALSE.getCode());
                    continue;
                }

                //计算选中项
                calculateCheckDiscountDetail(dto, checkDiscountMap, commodityCodeMap, detailVO);
            }
            //计算累计优惠
            optionDiscountVO.calculateDiscountAmount();
        }
    }

    /**
     * 按选中项排序计算
     *
     * @param orderVO          订单数据
     * @param checkDiscountMap 选中优惠项
     * @return 优惠列表
     */
    private List<SettlementApplyDiscountVO> getSortCheckDiscountList(SettlementApplyOrderVO orderVO,
                                                                     Map<String, SettlementCouponDTO> checkDiscountMap) {
        final List<SettlementApplyDiscountVO> discountList = orderVO.getDiscountList();
        //按后台结算规则排序
        List<SettlementApplyDiscountVO> sortCheckDiscountList = new ArrayList<>();
        for (Map.Entry<String, SettlementCouponDTO> entry : checkDiscountMap.entrySet()) {
            //优惠券
            discountList.stream()
                    .filter(d -> entry.getKey().equals(SettlementDiscountOptionEnum.optionMapKey(d.getDiscountOption(), d.getDiscountGuid())))
                    .findFirst()
                    .ifPresent(sortCheckDiscountList::add);
        }
        return sortCheckDiscountList;
    }

    /**
     * 优惠券用尽
     *
     * @param dto 入参
     * @return 是否
     */
    private boolean isUseUpCoupon(SettlementApplyOrderDTO dto) {
        return dto.getOrderInfo().getCouponLimitNum() <= 0;
    }

    /**
     * 计算选中优惠券详情
     *
     * @param dto              订单入参
     * @param checkDiscountMap 选中优惠
     * @param commodityCodeMap 商品
     * @param detailVO         优惠券详情
     */
    private void calculateCheckDiscountDetail(SettlementApplyOrderDTO dto,
                                              Map<String, SettlementCouponDTO> checkDiscountMap,
                                              Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap,
                                              SettlementApplyDiscountDetailVO detailVO) {
        //启用券才能选择
        if (detailVO.getIsEnabled() != BooleanEnum.TRUE.getCode()) {
            return;
        }
        final String key = SettlementDiscountOptionEnum.optionMapKey(detailVO.getDiscountOption(), detailVO.getDiscountGuid());
        final SettlementCouponDTO couponDTO = checkDiscountMap.get(key);
        if (couponDTO == null) {
            return;
        }
        final List<String> coupons = couponDTO.getCouponCodes();
        int couponLimitNum = couponDTO.getCouponLimitNum();
        //选中了当前优惠券,张数
        if (couponLimitNum > 0 && coupons.contains(detailVO.getDiscountOptionId())) {
            final SettlementApplyOrderInfoDTO orderInfo = dto.getOrderInfo();
            //计算商品分摊
            final boolean use = calculateCommodityDiscount((SettlementApplyDiscountDetailOfCouponVO) detailVO, commodityCodeMap);
            if (use) {
                //当前-1
                couponDTO.couponLimitNumSubtract1();
                //订单总限制-1
                orderInfo.couponLimitNumSubtract1();
            }
        }
    }

    /**
     * 转为商品方法(深拷贝)
     *
     * @param dto 订单入参
     * @return CommodityCode，list
     */
    private Map<String, List<SettlementApplyCommodityDTO>> getCommodityCodeMap(SettlementApplyOrderDTO dto) {
        if (CollUtil.isEmpty(dto.getOrderCommodityList())) {
            return Collections.emptyMap();
        }
        final String commodityListJson = JacksonUtils.writeValueAsString(dto.getOrderCommodityList());
        //商品:按顺序
        return JacksonUtils.toObjectList(SettlementApplyCommodityDTO.class, commodityListJson)
                .stream()
                .collect(Collectors.groupingBy(SettlementApplyCommodityDTO::getCommodityCode, LinkedHashMap::new, Collectors.toList()));
    }

    /**
     * 商品优惠分摊
     *
     * @param detailOfCouponVO 优惠券返回对象
     * @param commodityCodeMap 商品
     * @return 是否使用
     */
    private boolean calculateCommodityDiscount(SettlementApplyDiscountDetailOfCouponVO detailOfCouponVO,
                                               final Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap) {
        //当前券满足的商品
        final Map<String, List<SettlementApplyCommodityDTO>> commodityRespMap =
                memberCouponSupport.applyCommodityList(commodityCodeMap,
                        detailOfCouponVO.getApplyCommodity(),
                        detailOfCouponVO.getApplyCommodityJson(),
                        detailOfCouponVO.getApplyStoreGuidList());
        if (CollectionUtils.isEmpty(commodityRespMap)) {
            return false;
        }
        //本张优惠券金额
        final BigDecimal couponAmount = detailOfCouponVO.getCouponAmount();
        final List<SettlementApplyCommodityDTO> commodityRespList = commodityRespMap.values()
                .stream()
                .flatMap(Collection::stream).collect(Collectors.toList());
        log.info("优惠券code{}，当前使用商品{}", detailOfCouponVO.getDiscountOptionId(), JacksonUtils.writeValueAsString(commodityRespList));
        
        //商品筛选：移除已完全优惠的商品（参考折扣券实现）
        commodityRespList.removeIf(SettlementApplyCommodityDTO::checkApplyCommodity);
        
        if (CollUtil.isEmpty(commodityRespList)) {
            log.info("代金券code{}，当前使用商品售价已优惠完不再参与代金券", detailOfCouponVO.getDiscountOptionId());
            return false;
        }
        log.info("代金券code{}，筛选后参与计算的商品数量：{}", detailOfCouponVO.getDiscountOptionId(), commodityRespList.size());
        
        //商品剩余 - 参考折扣券实现，优先使用afterDiscountTotalPrice
        final BigDecimal commodityResidualFee = commodityRespList.stream()
                                                        .map(c -> Objects.nonNull(c.getAfterDiscountTotalPrice()) ?
                                                                          c.getAfterDiscountTotalPrice() :
                                                                          c.getDiscountTotalPriceInShopCar().subtract(c.getDiscountFee()))
                                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (commodityResidualFee.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        //计算实际优惠
        return calculateCommodityDiscountAmount(detailOfCouponVO, couponAmount, commodityRespList, commodityResidualFee);
    }

    /**
     * 计算实际优惠
     *
     * @param detailOfCouponVO     优惠详情
     * @param couponAmount         券金额
     * @param commodityRespList    商品
     * @param commodityResidualFee 商品剩余
     * @return 是否使用
     */
    private boolean calculateCommodityDiscountAmount(SettlementApplyDiscountDetailOfCouponVO detailOfCouponVO,
                                                     BigDecimal couponAmount,
                                                     List<SettlementApplyCommodityDTO> commodityRespList,
                                                     BigDecimal commodityResidualFee) {
        //当前券实际最大优惠
        final BigDecimal realityDiscountFee = BigDecimalUtil.min(couponAmount, commodityResidualFee);
        log.info("代金券code{}，最终优惠金额：{}，商品剩余总额：{}", 
                detailOfCouponVO.getDiscountOptionId(), realityDiscountFee, commodityResidualFee);
        //正好用完 比列:1
        final boolean fixScale = realityDiscountFee.compareTo(commodityResidualFee) == 0;
        final int endSize = commodityRespList.size() - 1;
        //最后一个
        BigDecimal sumDiscountFee = BigDecimal.ZERO;
        
        //返回商品分摊金额 - 参考折扣券实现，只进行一次循环
        List<SettlementApplyCommodityVO> commodityList = Lists.newArrayList();
        
        for (int i = 0; i < endSize; i++) {
            final SettlementApplyCommodityDTO applyCommodityDTO = commodityRespList.get(i);
            //优惠金额计算
            BigDecimal currentCommodityDiscount = getCommodityDiscountFee(fixScale, applyCommodityDTO, commodityResidualFee, realityDiscountFee);
            if (currentCommodityDiscount.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("代金券code{}，商品{}[{}]优惠金额{}小于等于0，不参与优惠", 
                        detailOfCouponVO.getDiscountOptionId(), applyCommodityDTO.getCommodityName(), 
                        applyCommodityDTO.getCommodityId(), currentCommodityDiscount);
                continue;
            }
            log.info("代金券code{}，商品{}[{}]分摊优惠金额：{}", 
                    detailOfCouponVO.getDiscountOptionId(), applyCommodityDTO.getCommodityName(), 
                    applyCommodityDTO.getCommodityId(), currentCommodityDiscount);
            //累加
            sumDiscountFee = sumDiscountFee.add(currentCommodityDiscount);
            applyCommodityDTO.addDiscountFee(currentCommodityDiscount);
            
            //多券累加特殊处理 - 参考折扣券实现
            applyCommodityDTO.subtractAfterDiscountTotalPrice(currentCommodityDiscount);
            
            //同时构建返回的商品列表
            addCommodityToList(commodityList, applyCommodityDTO, currentCommodityDiscount);
        }
        //最后的剩余优惠金额（精度差值）
        final BigDecimal endDiscountAmount = realityDiscountFee.subtract(sumDiscountFee);
        log.info("代金券code{}，前{}个商品累计分摊：{}，剩余金额：{}", 
                detailOfCouponVO.getDiscountOptionId(), endSize, sumDiscountFee, endDiscountAmount);
        
        allocateRemainingDiscount(commodityRespList, endDiscountAmount);
        
        // 处理最后一个商品（与折扣券逻辑完全一致）
        setLastCommodity(commodityRespList, endSize, realityDiscountFee, sumDiscountFee, commodityList);
        if (endSize < commodityRespList.size()) {
            final SettlementApplyCommodityDTO lastCommodity = commodityRespList.get(endSize);
            log.info("代金券code{}，最后一个商品{}[{}]分摊优惠金额：{}", 
                    detailOfCouponVO.getDiscountOptionId(), lastCommodity.getCommodityName(), 
                    lastCommodity.getCommodityId(), endDiscountAmount);
        }
        
        //实际分配的总优惠金额（使用realityDiscountFee作为总优惠金额）
        detailOfCouponVO.setDiscountAmount(realityDiscountFee);
        //选中
        detailOfCouponVO.setIsChecked(BooleanEnum.TRUE.getCode());
        
        log.info("代金券code{}，计算完成，总优惠金额：{}，分摊商品数：{}", 
                detailOfCouponVO.getDiscountOptionId(), realityDiscountFee, commodityList.size());
        
        detailOfCouponVO.setCommodityList(commodityList);
        
        return true;
    }

    /**
     * 处理最后一个商品的优惠分摊（参考折扣券实现）
     */
    private static void setLastCommodity(List<SettlementApplyCommodityDTO> commodityRespList,
                                         int endSize, BigDecimal totalDiscountFee,
                                         BigDecimal sumDiscountFee,
                                         List<SettlementApplyCommodityVO> commodityList) {
        final SettlementApplyCommodityDTO endCommodityDto = commodityRespList.get(endSize);
        BigDecimal afterDiscountAmount = totalDiscountFee.subtract(sumDiscountFee);
        
        SettlementApplyCommodityVO commodityVO = new SettlementApplyCommodityVO();
        commodityVO.setCommodityId(endCommodityDto.getCommodityId());
        commodityVO.setCommodityName(endCommodityDto.getCommodityName());
        commodityVO.setSkuId(endCommodityDto.getSkuId());
        commodityVO.setRid(endCommodityDto.getRid());
        commodityVO.setDiscountFee(afterDiscountAmount);
        commodityVO.setShareDiscountFee(afterDiscountAmount);
        commodityList.add(commodityVO);
    }

    /**
     * 添加商品到优惠列表
     *
     * @param commodityList 商品列表
     * @param applyCommodityDTO 商品DTO
     * @param discountAmount 优惠金额
     */
    private void addCommodityToList(List<SettlementApplyCommodityVO> commodityList,
                                    SettlementApplyCommodityDTO applyCommodityDTO,
                                    BigDecimal discountAmount) {
        SettlementApplyCommodityVO commodityVO = new SettlementApplyCommodityVO();
        commodityVO.setCommodityId(applyCommodityDTO.getCommodityId());
        commodityVO.setCommodityName(applyCommodityDTO.getCommodityName());
        commodityVO.setSkuId(applyCommodityDTO.getSkuId());
        commodityVO.setRid(applyCommodityDTO.getRid());
        commodityVO.setDiscountFee(discountAmount);
        commodityVO.setShareDiscountFee(discountAmount);
        commodityList.add(commodityVO);
    }

    /**
     * 分配剩余优惠金额（精度差值）
     * 从最后一个商品开始，往前找有剩余金额的商品来承接
     *
     * @param commodityRespList 商品列表
     * @param remainingAmount   剩余优惠金额
     */
    private void allocateRemainingDiscount(List<SettlementApplyCommodityDTO> commodityRespList, BigDecimal remainingAmount) {
        if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        
        //从最后一个商品开始，往前找有剩余金额的商品来承接
        for (int i = commodityRespList.size() - 1; i >= 0; i--) {
            final SettlementApplyCommodityDTO commodityDto = commodityRespList.get(i);
            //计算当前商品在前置优惠后的剩余可用金额
            final BigDecimal baseResidualFee = Objects.nonNull(commodityDto.getAfterDiscountTotalPrice()) ?
                    commodityDto.getAfterDiscountTotalPrice() :
                    commodityDto.getDiscountTotalPriceInShopCar().subtract(commodityDto.getDiscountFee());
            
            if (baseResidualFee.compareTo(BigDecimal.ZERO) > 0) {
                //找到有剩余金额的商品，分配剩余优惠
                final BigDecimal actualRemainingDiscount = BigDecimalUtil.min(baseResidualFee, remainingAmount);
                commodityDto.addDiscountFee(actualRemainingDiscount);
                
                //多券累加特殊处理 - 参考折扣券实现
                commodityDto.subtractAfterDiscountTotalPrice(actualRemainingDiscount);
                log.info("代金券剩余金额{}分配给商品{}[{}]", actualRemainingDiscount, commodityDto.getCommodityName(), commodityDto.getCommodityId());
                return;
            }
        }
        //如果没有商品能承接剩余金额，说明所有商品都被完全优惠了
        log.info("代金券剩余金额{}无法分配，所有商品都已完全优惠", remainingAmount);
    }

    /**
     * 优惠金额
     *
     * @param fixScale             固定比列
     * @param applyCommodityDTO    商品
     * @param commodityResidualFee 当前商品剩余总额
     * @return 优惠
     */
    private BigDecimal getCommodityDiscountFee(boolean fixScale,
                                               SettlementApplyCommodityDTO applyCommodityDTO,
                                               BigDecimal commodityResidualFee,
                                               BigDecimal realityDiscountFee) {
        //当前商品剩余可用 - 参考折扣券实现
        final BigDecimal currentCommodityResidualFee = Objects.nonNull(applyCommodityDTO.getAfterDiscountTotalPrice()) ?
                applyCommodityDTO.getAfterDiscountTotalPrice() :
                applyCommodityDTO.getDiscountTotalPriceInShopCar().subtract(applyCommodityDTO.getDiscountFee());
        //全部抵扣完，固定比例 1
        if (fixScale) {
            //剩余可优惠都给他
            return currentCommodityResidualFee;
        }
        //比列：当前商品剩余总额/ 所有商品剩余总额
        final BigDecimal scale = currentCommodityResidualFee.divide(commodityResidualFee, SettlementConstant.SCALE_LENGTH, RoundingMode.DOWN);
        //最大优惠金额 * 比例
        final BigDecimal discountFee = realityDiscountFee.multiply(scale).setScale(2, RoundingMode.DOWN);
        //不超过商品剩余金额
        return BigDecimalUtil.min(discountFee, currentCommodityResidualFee);
    }


    /**
     * 当前券满足的商品，用于计算分摊
     *
     * @param commodityCodeMap   商品入参
     * @param applyCommodity     商品类型
     * @param applyCommodityJson 适用商品
     * @return CommodityCode，list
     */
    private Map<String, List<SettlementApplyCommodityDTO>> applyCommodityList(Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap,
                                                                              Integer applyCommodity,
                                                                              String applyCommodityJson) {
        //全部商品
        if (applyCommodity == ApplyCommodityEnum.ALL.getCode()) {
            return commodityCodeMap;
        }
        //数据为空
        if (StringUtils.isBlank(applyCommodityJson)) {
            return Collections.emptyMap();
        }
        //适用商品
        final List<ResponseCouponCommodityVO> commodityVOList =
                JacksonUtils.toObjectList(ResponseCouponCommodityVO.class, applyCommodityJson);

        //商品编码set
        final Set<String> commodityCodeSet = commodityVOList.stream().map(ResponseCouponCommodityVO::getCommodityId).collect(Collectors.toSet());
        //部分适用
        if (applyCommodity == ApplyCommodityEnum.PART.getCode()) {
            return getPartApplyMap(commodityCodeMap, commodityCodeSet);
        }
        //不适用
        if (applyCommodity == ApplyCommodityEnum.NONE.getCode()) {
            //不管渠道：pos 商城，其中一个适用
            Map<String, List<SettlementApplyCommodityDTO>> commodityRespMap = new HashMap<>(commodityCodeMap);
            commodityCodeSet.forEach(commodityRespMap::remove);
            //还剩适用
            return commodityRespMap;
        }
        return Collections.emptyMap();
    }

    private static Map<String, List<SettlementApplyCommodityDTO>> getPartApplyMap(Map<String, List<SettlementApplyCommodityDTO>> commodityCodeMap, Set<String> commodityCodeSet) {
        Map<String, List<SettlementApplyCommodityDTO>> commodityRespMap = new HashMap<>();
        for (String s : commodityCodeSet) {
            List<SettlementApplyCommodityDTO> couponCommodityDTOs = commodityCodeMap.get(s);
            if (CollUtil.isNotEmpty(couponCommodityDTOs)) {
                List<SettlementApplyCommodityDTO> applyCommodityDTOS = new ArrayList<>();
                for (SettlementApplyCommodityDTO couponCommodityDTO : couponCommodityDTOs) {
                    if (couponCommodityDTO.getGoodsType() != 2) {
                        applyCommodityDTOS.add(couponCommodityDTO);
                    }
                }
                if (CollUtil.isNotEmpty(applyCommodityDTOS)) {
                    commodityRespMap.put(s, applyCommodityDTOS);
                }
            }
        }
        return commodityRespMap;
    }
}
