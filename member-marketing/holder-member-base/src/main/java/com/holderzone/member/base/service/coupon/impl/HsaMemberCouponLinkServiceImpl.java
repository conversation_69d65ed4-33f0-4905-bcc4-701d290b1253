package com.holderzone.member.base.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.assembler.CouponPackageAssembler;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.base.dto.context.ExchangeDiscountContext;
import com.holderzone.member.base.dto.context.ExchangeProcessResult;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.service.card.business.TerCardCheckBusinessService;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponLinkMapper;
import com.holderzone.member.base.mapper.coupon.HsaMemberCouponUseMapper;
import com.holderzone.member.base.mapper.member.HsaMemberInfoWeChatMapper;
import com.holderzone.member.base.service.assembler.MemberCouponApplyAssembler;
import com.holderzone.member.base.service.cache.CacheService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponUseService;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.base.service.send.ShortMessageSendService;
import com.holderzone.member.base.service.send.WechatSendService;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.coupon.MemberCouponLinkDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.enums.exception.MemberMarketExceptionEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.QrcodeTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponActivityDTO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkInvalidQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.settlement.apply.dto.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.coupon.CouponDtlQO;
import com.holderzone.member.common.qo.member.MemberInfoCouponQO;
import com.holderzone.member.common.qo.member.MemberSendCouponQO;
import com.holderzone.member.common.qo.redeem.RequestCheckRedeemApplyQO;
import com.holderzone.member.common.qo.redeem.RespondEditActiveVO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qrcode.QrCodeSupport;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.coupon.*;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员优惠券 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
@Slf4j
public class HsaMemberCouponLinkServiceImpl extends HolderBaseServiceImpl<HsaMemberCouponLinkMapper, HsaMemberCouponLink>
        implements IHsaMemberCouponLinkService {

    @Resource
    private QrCodeSupport qrCodeSupport;

    @Resource
    private WechatSendService wechatSendService;

    @Resource
    private HsaMemberInfoWeChatMapper hsaMemberInfoWeChatMapper;

    @Resource
    private CacheService cacheService;

    @Resource
    private ShortMessageSendService sendService;

    @Resource
    private HsaMemberCouponLinkMapper hsaMemberCouponLinkMapper;

    /**
     * 优惠券使用记录
     */
    @Resource
    private IHsaMemberCouponUseService memberCouponUseService;

    @Resource
    private HsaMemberCouponUseMapper hsaMemberCouponUseMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;

    @Resource
    private MemberCouponApplyAssembler memberCouponApplyAssembler;

    @Resource
    public Executor memberBaseThreadExecutor;

    @Resource
    private IHsaMemberCouponLinkService hsaMemberCouponLinkService;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    //优惠券锁定信息
    private static final String LOCK_COUPON_DISCOUNT = "LOCK_COUPON_DISCOUNT:";

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private TerCardCheckBusinessService checkBusinessService;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Override
    public CouponQrCodeVO getQrCode(String memberCouponGuid) {
        final HsaMemberCouponLink memberCouponLink = queryByGuid(memberCouponGuid);
        if (ObjectUtil.isNull(memberCouponLink) || memberCouponLink.deleted()) {
            throw new MemberBaseException(MemberMarketExceptionEnum.MEMBER_COUPON_NULL);
        }
        //状态判断
        if (!CouponMemberStateEnum.canUse(memberCouponLink.getState())) {
            throw new MemberBaseException(MemberMarketExceptionEnum.MEMBER_COUPON_NOT_USE);
        }
        CouponQrCodeVO vo = new CouponQrCodeVO();
        vo.setCode(memberCouponLink.getCode());
        //生成二维码
        vo.setQrCode(qrCodeSupport.getQrStr(memberCouponLink.getCode(), QrcodeTypeEnum.COUPON.getDes()));
        vo.setCouponName(memberCouponLink.getCouponName());
        vo.setThresholdType(memberCouponLink.getThresholdType());
        vo.setThresholdAmount(memberCouponLink.getThresholdAmount());
        vo.setDiscountAmount(memberCouponLink.getDiscountAmount());
        return vo;
    }

    @Override
    public MemberCouponLinkVO getMemberPhoneByCouponCode(String couponCode) {
        // 查询
        HsaMemberCouponLink hsaMemberCouponLink = getByMemberCouponLinkCode(couponCode);

        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaMemberCouponLink.getMemberGuid());
        checkBusinessService.checkMemberInfo(hsaOperationMemberInfo);

        //券状态判断 3 未过期 4 已过期 5 已使用 6 已失效

        Integer state = hsaMemberCouponLink.getState();
        if (state == CouponMemberStateEnum.APPLY.getCode()) {
            throw new MemberBaseException(MemberAccountExceptionEnum.COUPON_APPLY);
        }

        if (state == CouponMemberStateEnum.OVER.getCode()) {
            throw new MemberBaseException(MemberAccountExceptionEnum.COUPON_OVER);
        }

        if (state == CouponMemberStateEnum.EXPIRE.getCode()) {
            throw new MemberBaseException(MemberAccountExceptionEnum.COUPON_EXPIRE);
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(hsaMemberCouponLink.getCouponEffectiveStartTime())) {
            throw new MemberBaseException(MemberAccountExceptionEnum.COUPON_NOT_USABLE);
        }
        MemberCouponLinkVO memberCouponLinkVO = new MemberCouponLinkVO();
        BeanUtils.copyProperties(hsaMemberCouponLink, memberCouponLinkVO);
        memberCouponLinkVO.setDiscountGuid(hsaMemberCouponLink.getCode());
        return memberCouponLinkVO;
    }

    @Override
    public CouponGiveVO getCouponGiveVOByCouponCode(String couponCode) {
        // 查询
        HsaMemberCouponLink hsaMemberCouponLink = getByMemberCouponLinkCode(couponCode);
        if (Objects.isNull(hsaMemberCouponLink)) {
            return null;
        }
        log.info("优惠券码查询结果=======>{}", hsaMemberCouponLink);
        return CouponPackageAssembler.toCouponGiveVO(hsaMemberCouponLink);
    }

    private HsaMemberCouponLink getByMemberCouponLinkCode(String couponCode) {
        //二维码验证
        if (couponCode.contains(QrcodeTypeEnum.COUPON.getDes())) {
            log.info("优惠券二维码扫码，二维码key=======>{}", couponCode);
            Object content = redisTemplate.opsForValue().get(couponCode);
            if (null == content) {
                throw new MemberBaseException(MemberAccountExceptionEnum.COUPON_NOT_FOUND);
            }
            couponCode = content.toString();
        }

        HsaMemberCouponLink hsaMemberCouponLink = hsaMemberCouponLinkMapper.selectOne(new LambdaQueryWrapper<HsaMemberCouponLink>()
                .eq(HsaMemberCouponLink::getCode, couponCode)
                .eq(HsaMemberCouponLink::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));

        if (Objects.isNull(hsaMemberCouponLink)) {
            // 如果优惠查询为空 则需要查询兑换码
            verifyRedeemCode(couponCode);
            throw new MemberBaseException(MemberAccountExceptionEnum.COUPON_NOT_FOUND);
        }
        return hsaMemberCouponLink;
    }

    private void verifyRedeemCode(String couponCode) {
        // 如果优惠券没有查询到，则查询兑换码
        try {
            RequestCheckRedeemApplyQO requestCheckRedeemApplyQO = new RequestCheckRedeemApplyQO();
            requestCheckRedeemApplyQO.setRedeemCode(couponCode);
            Result<RespondEditActiveVO> respondEditActiveVOResult = memberMarketingFeign.queryByRedeemCode(requestCheckRedeemApplyQO);
            RespondEditActiveVO data = respondEditActiveVOResult.getData();
            if (Objects.nonNull(data) && !org.apache.commons.lang3.StringUtils.isEmpty(data.getGuid())) {
                throw new MemberBaseException(MemberAccountExceptionEnum.BEFORE_LOGIN_MEMBER);
            }
        } catch (MemberBaseException e) {
            throw new MemberBaseException(e.getMessage());
        } catch (Exception e) {
            log.error("查询兑换码失败，{}", couponCode, e);
        }
    }

    @Override
    public void sendMemberCouponNotice(List<MemberCouponPackageVO> memberCouponLinks) {

        log.info("优惠券到账推送参数:{}", JSON.toJSONString(memberCouponLinks));

        List<String> getMemberGuidList =
                memberCouponLinks.stream().map(MemberCouponPackageVO::getMemberGuid).collect(Collectors.toList());

        // 获取小程序名称
        Result<WeChatConfigInfoVO> weChatConfigInfoVOResult = memberMallToolFeign.queryByOperSubjectGuid();
        log.info("小程序名称:{}", JSON.toJSONString(weChatConfigInfoVOResult));
        String appName = Strings.EMPTY;
        if (Objects.nonNull(weChatConfigInfoVOResult.getData()) && StringUtils.isNotEmpty(weChatConfigInfoVOResult.getData().getAppName())) {
            appName = weChatConfigInfoVOResult.getData().getAppName();
        }
        log.info("实际推送OpenId:{}", JSON.toJSONString(getMemberGuidList));
        for (MemberCouponPackageVO memberOpenIdVO : memberCouponLinks) {
            Map<String, String> prams = new HashMap<>();
            String couponName = memberOpenIdVO.getCouponName();
            if (StringUtils.isNotEmpty(memberOpenIdVO.getCouponPackageName())) {
                couponName = memberOpenIdVO.getCouponPackageName();
            }
            LocalDateTime couponEffectiveEndTime = memberOpenIdVO.getCouponEffectiveEndTime();
            String minutes = couponEffectiveEndTime.getMinute() == 0 ? "00" : couponEffectiveEndTime.getMinute() + "";
            prams.put("thing1", couponName);
            prams.put("number6", memberOpenIdVO.getCouponNum() + "");
            prams.put("thing10", appName);
            prams.put("time4", couponEffectiveEndTime.getYear() + "年"
                    + couponEffectiveEndTime.getMonthValue() + "月"
                    + couponEffectiveEndTime.getDayOfMonth() + "日" + " "
                    + couponEffectiveEndTime.getHour() + ":" + minutes);
            prams.put("thing5", "领券成功！请立即使用");

            MessagesSendQO qo = new MessagesSendQO();

            qo.setPrams(prams);

            qo.setOperSubjectGuid(memberOpenIdVO.getOperSubjectGuid());
            qo.setPhone(memberOpenIdVO.getPhoneNum());
            qo.setEnterpriseGuid(memberOpenIdVO.getEnterpriseGuid());
            qo.setMemberGuid(memberOpenIdVO.getMemberGuid());
            qo.setTemplateName("优惠券到账提醒");

            wechatSendService.send(qo);
        }


    }

    @Override
    public void sendMemberCouponExpireNotice() {
        log.info("定时触发三天内过期优惠券提醒，当前执行时间：{}", LocalDateTime.now());
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("当前执行线程：{}", headerUserInfo);
        headerUserInfo.setSystem(SystemEnum.RETAIL.getCode());
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));

        String current = DateUtil.getTmpDate(new Date(), StringConstant.FORMAT);
        if (Boolean.TRUE.equals(cacheService.setLock(StringConstant.XXL_JOB_COUPON_NOTICE + current))) {
            log.info("执行时间重复跳出，当前时间：{}", current);
            return;
        }
        LocalDateTime now = LocalDateTime.now().plusDays(3);
        List<MemberCouponOpenIdVO> memberCouponOpenIdVOList = hsaMemberInfoWeChatMapper.getMemberCouponOpenId(now);

        if (CollUtil.isNotEmpty(memberCouponOpenIdVOList)) {

            List<MemberCouponOpenIdVO> couponOpenIdVOList = Lists.newArrayList();

            for (MemberCouponOpenIdVO memberCouponOpenIdVO : memberCouponOpenIdVOList) {
                if (Duration.between(memberCouponOpenIdVO.getReachTime(), memberCouponOpenIdVO.getCouponEffectiveEndTime()).toHours() >= 72) {
                    couponOpenIdVOList.add(memberCouponOpenIdVO);
                }
            }
            if (CollUtil.isEmpty(couponOpenIdVOList)) {
                cacheService.delete(StringConstant.XXL_JOB_COUPON_NOTICE + current);
                return;
            }

            //短信推送
            sendService.sendMemberCouponExpireNotice(couponOpenIdVOList);

            Map<String, List<MemberCouponOpenIdVO>> memberCouponLinkMap =
                    couponOpenIdVOList.stream().collect(Collectors.groupingBy(MemberCouponOpenIdVO::getMemberGuid));


            List<String> couponGuid = Lists.newArrayList();
            Set<String> memberGuid = couponOpenIdVOList.stream().map(MemberCouponOpenIdVO::getMemberGuid).collect(Collectors.toSet());
            for (String guid : memberGuid) {
                List<MemberCouponOpenIdVO> memberCouponOpenIdVOS = memberCouponLinkMap.get(guid);
                memberCouponOpenIdVOS = memberCouponOpenIdVOS.stream()
                        .sorted(Comparator.comparing(MemberCouponOpenIdVO::getCouponEffectiveEndTime))
                        .collect(Collectors.toList());
                Map<String, String> prams = new HashMap<>();

                MemberCouponOpenIdVO firstMemberCouponOpenIdVO = memberCouponOpenIdVOS.get(0);
                LocalDateTime couponEffectiveEndTime = firstMemberCouponOpenIdVO.getCouponEffectiveEndTime();
                String minutes = couponEffectiveEndTime.getMinute() == 0 ? "00" : couponEffectiveEndTime.getMinute() + "";
                prams.put("thing2", firstMemberCouponOpenIdVO.getCouponName());
                prams.put("number7", memberCouponOpenIdVOS.size() + "");
                prams.put("time3", couponEffectiveEndTime.getYear() + "年"
                        + couponEffectiveEndTime.getMonthValue() + "月"
                        + couponEffectiveEndTime.getDayOfMonth() + "日" + " "
                        + couponEffectiveEndTime.getHour() + ":" + minutes);
                prams.put("thing1", "您的优惠券即将过期");

                MessagesSendQO qo = new MessagesSendQO();

                qo.setPrams(prams);
                qo.setOperSubjectGuid(memberCouponOpenIdVOS.get(0).getOperSubjectGuid());
                qo.setEnterpriseGuid(memberCouponOpenIdVOS.get(0).getEnterpriseGuid());
                qo.setMemberGuid(memberCouponOpenIdVOS.get(0).getMemberGuid());
                qo.setOpenId(memberCouponOpenIdVOS.get(0).getOpenId());
                qo.setTemplateName("优惠券过期提醒");
                headerUserInfo.setOperSubjectGuid(memberCouponOpenIdVOS.get(0).getOperSubjectGuid());
                ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));

                couponGuid.addAll(memberCouponOpenIdVOS.stream().map(MemberCouponOpenIdVO::getCouponGuid)
                        .collect(Collectors.toList()));
                wechatSendService.send(qo);
            }
            baseMapper.updateIsExpireRemind(couponGuid);
        }
        cacheService.delete(StringConstant.XXL_JOB_COUPON_NOTICE + current);
    }

    @Override
    public boolean updateStateByApply(String memberInfoGuid, String couponCode, List<String> codes) {
        int size = hsaMemberCouponLinkMapper.updateStateByUse(memberInfoGuid, couponCode, codes, CouponMemberStateEnum.APPLY.getCode());
        return size > 0;
    }

    @Override
    public List<String> list(String operSubjectGuid, Map<String, List<String>> couponMap) {
        List<String> guidList = new ArrayList<>();
        //不同类型的券查询，code不唯一
        for (Map.Entry<String, List<String>> entry : couponMap.entrySet()) {
            final List<String> guids = hsaMemberCouponLinkMapper.listGuid(operSubjectGuid, entry.getKey(), entry.getValue());
            if (CollUtil.isEmpty(guids)) {
                continue;
            }
            guidList.addAll(guids);
        }
        return guidList;
    }

    @Override
    public List<String> listGuid(String operSubjectGuid, Map<String, List<String>> couponMap) {
        List<String> guidList = new ArrayList<>();
        //不同类型的券查询，code不唯一
        for (Map.Entry<String, List<String>> entry : couponMap.entrySet()) {
            final List<String> guids = hsaMemberCouponLinkMapper.listGuid(operSubjectGuid, entry.getKey(), entry.getValue());
            if (CollUtil.isEmpty(guids)) {
                continue;
            }
            guidList.addAll(guids);
        }
        return guidList;
    }

    @Override
    public List<HsaMemberCouponLink> listAllByCode(String operSubjectGuid, Map<String, List<String>> couponMap) {
        List<HsaMemberCouponLink> couponList = new ArrayList<>();
        //不同类型的券查询，code不唯一
        for (Map.Entry<String, List<String>> entry : couponMap.entrySet()) {
            final List<HsaMemberCouponLink> couponLinks = hsaMemberCouponLinkMapper.listAllByCode(operSubjectGuid, entry.getKey(), entry.getValue());
            if (CollUtil.isEmpty(couponLinks)) {
                continue;
            }
            couponList.addAll(couponLinks);
        }
        return couponList;
    }

    @Override
    public void locked(SettlementOrderLockDTO lockedDiscount) {
        String couponKey = LOCK_COUPON_DISCOUNT + lockedDiscount.getCodeType() + lockedDiscount.getOrderInfo().getOrderNumber();
        stringRedisTemplate.opsForValue().set(couponKey, JSON.toJSONString(lockedDiscount));
    }

    @Override
    @Transactional
    @RedissonLock(lockName = "COUPON_MARK_USE", tryLock = true, leaseTime = 10)
    public void markUse(CouponMarkUseQO qo) {
        final List<String> memberCouponGuids = Collections.singletonList(qo.getCouponGuid());
        //券
        final HsaMemberCouponLink couponLink = this.queryByGuid(qo.getCouponGuid());
        MemberCouponLinkDTO memberCouponLinkDTO = new MemberCouponLinkDTO();
        BeanUtils.copyProperties(couponLink, memberCouponLinkDTO);
        qo.setMemberCouponLinkDTO(memberCouponLinkDTO);
        if (couponLink.getCouponType() == CouponTypeEnum.COUPON_EXCHANGE.getCode()) {
            int num = hsaMemberCouponUseMapper.selectCount(new LambdaQueryWrapper<HsaMemberCouponUse>()
                    .eq(HsaMemberCouponUse::getMemberCouponLinkGuid, couponLink.getGuid()));
            CouponActivityDTO couponActivityDTO = qo.getCouponActivityDTO();
            if (couponActivityDTO.getExchangeLimit() == BooleanEnum.TRUE.getCode()
                    && couponActivityDTO.getExchangeTimes() - (num + 1) == 0) {
                //下单、券改为已使用
                hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.APPLY.getCode());

            }
            //人工锁定优惠券
            memberCouponUseService.markUse(qo);
            return;
        } else if (couponLink.getCouponType() == CouponTypeEnum.COUPON_DISCOUNT.getCode()) {
            qo.setDiscountAmount(BigDecimal.ZERO);
            couponLink.setDiscountAmount(BigDecimal.ZERO);
            hsaMemberCouponLinkMapper.updateByGuid(couponLink);
        } else {
            qo.setDiscountAmount(couponLink.getDiscountAmount());
        }
        //下单、券改为已使用
        hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.APPLY.getCode());
        //人工锁定优惠券
        memberCouponUseService.markUse(qo);
    }

    @Override
    @Transactional
    @RedissonLock(lockName = "COUPON_INVALID", tryLock = true, leaseTime = 10)
    public void invalid(CouponMarkInvalidQO qo) {
        //券
        final HsaMemberCouponLink couponLink = this.queryByGuid(qo.getCouponGuid());
        if (Objects.isNull(couponLink)) {
            throw new MemberBaseException("优惠券不存在");
        }
        //未过期才能作废
        if (couponLink.getState() != CouponMemberStateEnum.UN_EXPIRE.getCode()) {
            throw new MemberBaseException("优惠券不可作废");
        }
        final List<String> memberCouponGuids = Collections.singletonList(qo.getCouponGuid());
        //下单、券改为已使用
        hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.OVER.getCode());
    }

    @Override
    public void unlocked(SettlementUnLockedDiscountDTO discountDTO) {
        //优惠券状态回退
        final List<HsaMemberCouponUse> couponUseList = memberCouponUseService.listByOrderNumber(discountDTO.getOperSubjectGuid(),
                discountDTO.getOrderNo(),
                discountDTO.getDiscountIdList());
        if (CollUtil.isEmpty(couponUseList)) {
            log.info("优惠释放：优惠券未使用，订单号：{}", discountDTO.getOrderNo());
            // 即使没有使用记录，也清理可能存在的锁定缓存
            cleanupUnlockedCouponCache(discountDTO);
            return;
        }
        //回退使用的优惠券状态
        rollbackApplyCouponState(couponUseList, discountDTO);
        //删除使用记录
        final List<Long> couponUseIds = couponUseList.stream().map(HsaBaseEntity::getId).collect(Collectors.toList());
        memberCouponUseService.removeByIds(couponUseIds);

        // 清理对应的锁定缓存
        cleanupUnlockedCouponCache(discountDTO);
    }

    /**
     * 回退优惠券状态
     *
     * @param couponUseList 使用券
     */
    private void rollbackApplyCouponState(List<HsaMemberCouponUse> couponUseList, SettlementUnLockedDiscountDTO discountDTO) {
        //查询已使用优惠券
        final List<String> memberCouponGuids = couponUseList.stream().map(HsaMemberCouponUse::getMemberCouponLinkGuid).collect(Collectors.toList());
        final LambdaQueryWrapper<HsaMemberCouponLink> couponWrapper = new LambdaQueryWrapper<HsaMemberCouponLink>()
                .in(HsaBaseEntity::getGuid, memberCouponGuids)
                .eq(HsaMemberCouponLink::getState, CouponMemberStateEnum.APPLY.getCode());
        //查询优惠券
        final List<HsaMemberCouponLink> couponLinks = this.list(couponWrapper);
        if (CollUtil.isEmpty(couponLinks)) {
            return;
        }

        //过滤可退的
        List<String> discountIdList = discountDTO.getDiscountIdList();
        log.info("可退发券码：{}", discountIdList);

        LocalDateTime now = LocalDateTime.now();
        couponLinks.forEach(couponLink -> {
            //判断可退
            if (CollUtil.isNotEmpty(discountIdList)
                    && (discountIdList.contains(couponLink.getCode()))) {
                setState(couponLink, now);
            }
        });
        //券状态修改
        this.updateBatchById(couponLinks);
    }

    private static void setState(HsaMemberCouponLink couponLink, LocalDateTime now) {
        //判断是否过期
        if (couponLink.getCouponEffectiveEndTime().isAfter(now)) {
            //未过期
            couponLink.setState(CouponMemberStateEnum.UN_EXPIRE.getCode());
        } else {
            //已过期
            couponLink.setState(CouponMemberStateEnum.EXPIRE.getCode());
        }
    }

    /**
     * 支付后优惠券处理
     * 主要功能：
     * 1. 获取订单锁定的优惠券信息
     * 2. 根据优惠券类型进行不同的处理逻辑
     * 3. 更新优惠券状态为已使用
     * 4. 记录优惠券使用详情
     *
     * @param discountDTO 支付后折扣处理参数，包含订单号、支付时间、优惠券类型等信息
     */
    @Override
    public void afterPayDiscount(SettlementPayAfterDiscountDTO discountDTO) {
        log.info("开始处理支付后优惠券，订单号：{}，优惠券类型：{}", discountDTO.getOrderNo(), discountDTO.getCodeType());

        try {
            // 获取锁定的优惠券信息
            SettlementOrderLockDTO lockedDiscount = getSettlementOrderLockDTO(discountDTO);
            if (!validateLockedDiscount(lockedDiscount, discountDTO.getOrderNo())) {
                return;
            }

            // 过滤匹配当前优惠券类型的折扣信息
            List<SettlementLockedDiscountReqDTO> checkDiscountList = filterMatchingDiscounts(lockedDiscount, discountDTO.getCodeType());
            if (CollUtil.isEmpty(checkDiscountList)) {
                log.warn("未找到匹配的优惠券类型，订单号：{}，优惠券类型：{}", discountDTO.getOrderNo(), discountDTO.getCodeType());
                return;
            }

            // 构建优惠券映射并查询优惠券详情
            Map<String, List<String>> couponMap = buildCouponMap(checkDiscountList);
            List<HsaMemberCouponLink> couponLinks = getCouponLinks(lockedDiscount.getOrderInfo(), couponMap);
            if (CollUtil.isEmpty(couponLinks)) {
                log.warn("未找到对应的优惠券信息，订单号：{}", discountDTO.getOrderNo());
                return;
            }

            // 根据优惠券类型选择处理策略
            processCouponsByType(discountDTO, checkDiscountList, lockedDiscount.getOrderInfo(), couponLinks, lockedDiscount);

            log.info("支付后优惠券处理完成，订单号：{}，处理券数：{}", discountDTO.getOrderNo(), couponLinks.size());

            // 清理锁定缓存
            cleanupLockedCouponCache(discountDTO);

        } catch (Exception e) {
            log.error("支付后优惠券处理异常，订单号：{}，错误信息：{}", discountDTO.getOrderNo(), e.getMessage(), e);
            // 即使处理异常也要清理缓存，避免内存泄漏
            try {
                cleanupLockedCouponCache(discountDTO);
            } catch (Exception cleanupException) {
                log.warn("清理优惠券锁定缓存失败，订单号：{}，错误信息：{}", discountDTO.getOrderNo(), cleanupException.getMessage());
            }
            throw new MemberBaseException("支付后优惠券处理失败：" + e.getMessage());
        }
    }

    /**
     * 验证锁定的优惠券信息是否有效
     *
     * @param lockedDiscount 锁定的优惠券信息
     * @param orderNo 订单号
     * @return 是否有效
     */
    private boolean validateLockedDiscount(SettlementOrderLockDTO lockedDiscount, String orderNo) {
        if (Objects.isNull(lockedDiscount)) {
            log.warn("未获取到折扣券占用记录，订单号：{}", orderNo);
            return false;
        }

        if (CollUtil.isEmpty(lockedDiscount.getCheckDiscountList())) {
            log.warn("折扣券占用记录为空，订单号：{}", orderNo);
            return false;
        }

        log.info("获取折扣券占用记录成功，订单号：{}，优惠券数量：{}", orderNo, lockedDiscount.getCheckDiscountList().size());
        return true;
    }

    /**
     * 过滤匹配当前优惠券类型的折扣信息
     *
     * @param lockedDiscount 锁定的优惠券信息
     * @param codeType 优惠券类型
     * @return 匹配的折扣信息列表
     */
    private List<SettlementLockedDiscountReqDTO> filterMatchingDiscounts(SettlementOrderLockDTO lockedDiscount, Integer codeType) {
        return lockedDiscount.getCheckDiscountList()
                .stream()
                .filter(discount -> Objects.equals(codeType, discount.getDiscountOption()))
                .collect(Collectors.toList());
    }

    /**
     * 构建优惠券映射关系
     * 将优惠券按照GUID分组，便于批量查询
     *
     * @param checkDiscountList 需要处理的折扣信息列表
     * @return 优惠券映射关系 Map<优惠券GUID, List<优惠券代码>>
     */
    private Map<String, List<String>> buildCouponMap(List<SettlementLockedDiscountReqDTO> checkDiscountList) {
        return checkDiscountList.stream()
                .collect(Collectors.groupingBy(
                        SettlementLockedDiscountReqDTO::getDiscountGuid,
                        Collectors.mapping(SettlementLockedDiscountReqDTO::getDiscountOptionId, Collectors.toList())
                ));
    }

    /**
     * 获取优惠券详细信息
     *
     * @param orderInfo 订单信息
     * @param couponMap 优惠券映射关系
     * @return 优惠券详细信息列表
     */
    private List<HsaMemberCouponLink> getCouponLinks(SettlementLockedOrderInfoDTO orderInfo, Map<String, List<String>> couponMap) {
        try {
            return listAllByCode(orderInfo.getOperSubjectGuid(), couponMap);
        } catch (Exception e) {
            log.error("查询优惠券详情失败，运营主体：{}，错误信息：{}", orderInfo.getOperSubjectGuid(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据优惠券类型选择不同的处理策略
     *
     * @param discountDTO 支付后折扣处理参数
     * @param checkDiscountList 需要处理的折扣信息列表
     * @param orderInfo 订单信息
     * @param couponLinks 优惠券详细信息列表
     * @param lockedDiscount 锁定的优惠券信息
     */
    private void processCouponsByType(SettlementPayAfterDiscountDTO discountDTO,
                                      List<SettlementLockedDiscountReqDTO> checkDiscountList,
                                      SettlementLockedOrderInfoDTO orderInfo,
                                      List<HsaMemberCouponLink> couponLinks,
                                      SettlementOrderLockDTO lockedDiscount) {

        if (discountDTO.getCodeType().equals(SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode())) {
            // 兑换券处理：需要考虑使用次数限制
            log.info("处理兑换券，订单号：{}，券数：{}", discountDTO.getOrderNo(), couponLinks.size());
            dealExchangeDiscount(discountDTO, checkDiscountList, orderInfo, couponLinks, lockedDiscount);
        } else {
            // 普通优惠券处理：直接标记为已使用
            log.info("处理普通优惠券，订单号：{}，券数：{}", discountDTO.getOrderNo(), couponLinks.size());
            processRegularCoupons(discountDTO, couponLinks, lockedDiscount);
        }
    }

    /**
     * 处理普通优惠券
     * 1. 更新优惠券状态为已使用
     * 2. 记录锁定信息
     * 3. 执行支付后操作
     *
     * @param discountDTO 支付后折扣处理参数
     * @param couponLinks 优惠券详细信息列表
     * @param lockedDiscount 锁定的优惠券信息
     */
    private void processRegularCoupons(SettlementPayAfterDiscountDTO discountDTO,
                                       List<HsaMemberCouponLink> couponLinks,
                                       SettlementOrderLockDTO lockedDiscount) {
        try {
            // 提取优惠券GUID列表
            List<String> memberCouponGuids = couponLinks.stream()
                    .map(HsaBaseEntity::getGuid)
                    .collect(Collectors.toList());

            // 批量更新优惠券状态为已使用
            int updateCount = hsaMemberCouponLinkMapper.updateStateByGuids(memberCouponGuids, CouponMemberStateEnum.APPLY.getCode());
            log.info("更新优惠券状态成功，订单号：{}，更新数量：{}", discountDTO.getOrderNo(), updateCount);

            // 记录优惠券锁定信息（下单时间为锁定时间）
            memberCouponUseService.lockedCoupon(lockedDiscount, couponLinks);

            // 执行支付后操作（更新支付时间等）
            memberCouponUseService.afterPayCoupon(discountDTO.getOperSubjectGuid(), discountDTO.getOrderNo(), discountDTO.getPayTime());

        } catch (Exception e) {
            log.error("处理普通优惠券失败，订单号：{}，错误信息：{}", discountDTO.getOrderNo(), e.getMessage(), e);
            throw new MemberBaseException("处理普通优惠券失败：" + e.getMessage());
        }
    }

    /**
     * 处理兑换券的支付后逻辑
     * 兑换券相比普通优惠券有以下特殊处理：
     * 1. 需要考虑使用次数限制
     * 2. 需要记录每次使用的详细信息
     * 3. 达到使用次数上限时才标记为已使用
     *
     * @param discountDTO 支付后折扣处理参数
     * @param checkDiscountList 需要处理的折扣信息列表
     * @param orderInfo 订单信息
     * @param couponLinks 优惠券详细信息列表
     * @param lockedDiscount 锁定的优惠券信息
     */
    private void dealExchangeDiscount(SettlementPayAfterDiscountDTO discountDTO,
                                      List<SettlementLockedDiscountReqDTO> checkDiscountList,
                                      SettlementLockedOrderInfoDTO orderInfo,
                                      List<HsaMemberCouponLink> couponLinks,
                                      SettlementOrderLockDTO lockedDiscount) {
        try {
            log.info("开始处理兑换券，订单号：{}，券数：{}", discountDTO.getOrderNo(), checkDiscountList.size());

            // 1. 准备基础数据
            ExchangeDiscountContext context = prepareExchangeDiscountContext(checkDiscountList, discountDTO, lockedDiscount, couponLinks);

            // 2. 处理每张兑换券
            ExchangeProcessResult result = processEachExchangeCoupon(checkDiscountList, orderInfo, context);

            // 3. 批量更新券状态
            batchUpdateExchangeCouponStatus(result.getCouponsToUpdate(), discountDTO.getOrderNo());

            // 4. 批量保存使用记录
            batchSaveExchangeCouponRecords(result.getUseRecords(), discountDTO.getOrderNo());

            // 5. 执行支付后操作
            memberCouponUseService.afterPayCoupon(discountDTO.getOperSubjectGuid(), discountDTO.getOrderNo(), discountDTO.getPayTime());

            log.info("兑换券处理完成，订单号：{}，使用记录数：{}，状态更新券数：{}",
                    discountDTO.getOrderNo(), result.getUseRecords().size(), result.getCouponsToUpdate().size());

        } catch (Exception e) {
            log.error("处理兑换券异常，订单号：{}，错误信息：{}", discountDTO.getOrderNo(), e.getMessage(), e);
            throw new MemberBaseException("处理兑换券失败：" + e.getMessage());
        }
    }

    /**
     * 准备兑换券处理所需的上下文数据
     *
     * @param checkDiscountList 需要处理的折扣信息列表
     * @param discountDTO 支付后折扣处理参数
     * @param lockedDiscount 锁定的优惠券信息
     * @param couponLinks 优惠券详细信息列表
     * @return 兑换券处理上下文
     */
    private ExchangeDiscountContext prepareExchangeDiscountContext(List<SettlementLockedDiscountReqDTO> checkDiscountList,
                                                                   SettlementPayAfterDiscountDTO discountDTO,
                                                                   SettlementOrderLockDTO lockedDiscount,
                                                                   List<HsaMemberCouponLink> couponLinks) {
        // 提取优惠券代码列表
        List<String> discountOptionIds = checkDiscountList.stream()
                .map(SettlementLockedDiscountReqDTO::getDiscountOptionId)
                .collect(Collectors.toList());

        // 查询占用次数
        Map<String, Integer> occupiedCountMap = getOccupiedCouponCount(discountOptionIds, discountDTO);

        // 查询券基本信息
        Map<String, HsaMemberCouponLink> couponLinkMap = getCouponLinkMap(discountOptionIds);

        // 查询已使用次数
        Map<String, List<HsaMemberCouponUse>> usedCountMap = getUsedCouponCount(couponLinkMap);

        // 构建选中优惠券映射
        Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap = getCheckDiscountMap(lockedDiscount, couponLinks);

        return new ExchangeDiscountContext(occupiedCountMap, couponLinkMap, usedCountMap, checkDiscountMap);
    }

    /**
     * 获取优惠券占用次数
     *
     * @param discountOptionIds 优惠券代码列表
     * @param discountDTO 折扣信息
     * @return 占用次数映射
     */
    private Map<String, Integer> getOccupiedCouponCount(List<String> discountOptionIds, SettlementPayAfterDiscountDTO discountDTO) {
        try {
            return hsaMemberOrderDiscountService.getUsedCouponNum(
                    discountOptionIds,
                    BooleanEnum.FALSE.getCode(),
                    discountDTO.getCodeType(),
                    discountDTO.getOrderNo());
        } catch (Exception e) {
            log.error("查询优惠券占用次数失败，订单号：{}，错误信息：{}", discountDTO.getOrderNo(), e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 获取优惠券基本信息映射
     *
     * @param discountOptionIds 优惠券代码列表
     * @return 优惠券信息映射
     */
    private Map<String, HsaMemberCouponLink> getCouponLinkMap(List<String> discountOptionIds) {
        try {
            List<HsaMemberCouponLink> couponLinks = hsaMemberCouponLinkMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberCouponLink>()
                            .in(HsaMemberCouponLink::getCode, discountOptionIds));

            return couponLinks.stream()
                    .collect(Collectors.toMap(HsaMemberCouponLink::getCode, Function.identity(), (v1, v2) -> v1));
        } catch (Exception e) {
            log.error("查询优惠券基本信息失败，券代码：{}，错误信息：{}", discountOptionIds, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 获取优惠券已使用次数映射
     *
     * @param couponLinkMap 优惠券信息映射
     * @return 已使用次数映射
     */
    private Map<String, List<HsaMemberCouponUse>> getUsedCouponCount(Map<String, HsaMemberCouponLink> couponLinkMap) {
        try {
            List<String> memberCouponLinkGuids = couponLinkMap.values().stream()
                    .map(HsaMemberCouponLink::getGuid)
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(memberCouponLinkGuids)) {
                return new HashMap<>();
            }

            List<HsaMemberCouponUse> usedCoupons = hsaMemberCouponUseMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberCouponUse>()
                            .in(HsaMemberCouponUse::getMemberCouponLinkGuid, memberCouponLinkGuids));

            return usedCoupons.stream()
                    .collect(Collectors.groupingBy(HsaMemberCouponUse::getMemberCouponLinkGuid));
        } catch (Exception e) {
            log.error("查询优惠券使用记录失败，错误信息：{}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 处理每张兑换券
     *
     * @param checkDiscountList 需要处理的折扣信息列表
     * @param orderInfo 订单信息
     * @param context 处理上下文
     * @return 处理结果
     */
    private ExchangeProcessResult processEachExchangeCoupon(List<SettlementLockedDiscountReqDTO> checkDiscountList,
                                                            SettlementLockedOrderInfoDTO orderInfo,
                                                            ExchangeDiscountContext context) {
        List<HsaMemberCouponUse> useRecords = new ArrayList<>();
        List<HsaMemberCouponLink> couponsToUpdate = new ArrayList<>();

        for (SettlementLockedDiscountReqDTO discountReq : checkDiscountList) {
            try {
                // 获取对应的优惠券信息
                HsaMemberCouponLink couponLink = context.getCouponLinkMap().get(discountReq.getDiscountOptionId());
                if (Objects.isNull(couponLink)) {
                    log.warn("未找到对应的兑换券，代码：{}", discountReq.getDiscountOptionId());
                    continue;
                }

                // 添加使用记录
                addExchangeCouponUseRecords(orderInfo, discountReq, couponLink, context.getCheckDiscountMap(), useRecords);

                // 检查是否需要更新券状态
                checkAndAddCouponForStatusUpdate(discountReq, couponLink, context, couponsToUpdate);

            } catch (Exception e) {
                log.error("处理单张兑换券失败，券代码：{}，错误信息：{}", discountReq.getDiscountOptionId(), e.getMessage(), e);
                // 继续处理其他券，不中断整个流程
            }
        }

        return new ExchangeProcessResult(useRecords, couponsToUpdate);
    }

    /**
     * 添加兑换券使用记录
     *
     * @param orderInfo 订单信息
     * @param discountReq 折扣请求信息
     * @param couponLink 优惠券信息
     * @param checkDiscountMap 选中优惠券映射
     * @param useRecords 使用记录列表
     */
    private void addExchangeCouponUseRecords(SettlementLockedOrderInfoDTO orderInfo,
                                             SettlementLockedDiscountReqDTO discountReq,
                                             HsaMemberCouponLink couponLink,
                                             Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap,
                                             List<HsaMemberCouponUse> useRecords) {
        try {
            // 生成使用记录的GUID
            final List<String> guids = guidGeneratorUtil.getGuidsNew(
                    HsaMemberCouponUse.class.getSimpleName(),
                    discountReq.getUsedTimes());

            log.info("为兑换券生成使用记录，券代码：{}，使用次数：{}", couponLink.getCode(), discountReq.getUsedTimes());

            // 为每次使用创建记录
            for (int i = 0; i < discountReq.getUsedTimes(); i++) {
                HsaMemberCouponUse useRecord = createExchangeCouponUseRecord(
                        orderInfo, couponLink, checkDiscountMap, guids.get(i));
                useRecords.add(useRecord);
            }
        } catch (Exception e) {
            log.error("添加兑换券使用记录失败，券代码：{}，错误信息：{}", couponLink.getCode(), e.getMessage(), e);
            throw new MemberBaseException("添加兑换券使用记录失败");
        }
    }

    /**
     * 创建单个兑换券使用记录
     *
     * @param orderInfo 订单信息
     * @param couponLink 优惠券信息
     * @param checkDiscountMap 选中优惠券映射
     * @param guid 记录GUID
     * @return 使用记录
     */
    private HsaMemberCouponUse createExchangeCouponUseRecord(SettlementLockedOrderInfoDTO orderInfo,
                                                             HsaMemberCouponLink couponLink,
                                                             Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap,
                                                             String guid) {
        HsaMemberCouponUse useRecord = new HsaMemberCouponUse();

        // 基本信息
        useRecord.setGuid(guid);
        useRecord.setOperSubjectGuid(orderInfo.getOperSubjectGuid());
        useRecord.setOrderNumber(orderInfo.getOrderNumber());
        useRecord.setMemberCouponLinkGuid(couponLink.getGuid());
        useRecord.setLockTime(orderInfo.getOrderTime());
        useRecord.setSource(orderInfo.getSource());
        useRecord.setOrderPaidAmount(orderInfo.getOrderPaidAmount());

        // 店铺信息
        setStoreInfo(useRecord, orderInfo, checkDiscountMap.get(couponLink.getGuid()));

        // 优惠信息
        SettlementLockedDiscountReqDTO ref = checkDiscountMap.get(couponLink.getGuid());
        if (Objects.nonNull(ref)) {
            useRecord.setDiscountAmount(ref.getDiscountAmount());
        }

        // 优惠券信息
        setCouponInfo(useRecord, couponLink);

        // 会员信息
        setMemberInfo(useRecord, orderInfo);

        return useRecord;
    }

    /**
     * 设置店铺信息
     */
    private void setStoreInfo(HsaMemberCouponUse useRecord,
                              SettlementLockedOrderInfoDTO orderInfo,
                              SettlementLockedDiscountReqDTO ref) {
        if (Objects.nonNull(orderInfo.getStoreGuid())) {
            useRecord.setStoreGuid(orderInfo.getStoreGuid());
            useRecord.setStoreName(orderInfo.getStoreName());
        } else if (Objects.nonNull(ref)) {
            useRecord.setStoreGuid(ref.getStoreGuid());
            useRecord.setStoreName(ref.getStoreName());
        }
    }

    /**
     * 设置优惠券信息
     */
    private void setCouponInfo(HsaMemberCouponUse useRecord, HsaMemberCouponLink couponLink) {
        useRecord.setCouponName(couponLink.getCouponName());
        useRecord.setCouponCode(couponLink.getCouponCode());
        useRecord.setMemberGuid(couponLink.getMemberGuid());
        useRecord.setCode(couponLink.getCode());
        useRecord.setCouponPackageCode(couponLink.getCouponPackageCode());
        useRecord.setCouponPackageName(couponLink.getCouponPackageName());
    }

    /**
     * 设置会员信息
     */
    private void setMemberInfo(HsaMemberCouponUse useRecord, SettlementLockedOrderInfoDTO orderInfo) {
        try {
            HsaOperationMemberInfo memberInfo = hsaOperationMemberInfoMapper.queryByGuid(orderInfo.getMemberInfoGuid());
            if (Objects.nonNull(memberInfo)) {
                // 根据系统类型设置操作人名称
                if (ThreadLocalCache.getSystem() == SystemEnum.MALL.getCode()) {
                    useRecord.setOperatorAccountName(memberInfo.getUserName() + "/" + memberInfo.getPhoneNum());
                } else {
                    useRecord.setOperatorAccountName(orderInfo.getOperatorAccountName());
                }

                useRecord.setMemberPhone(memberInfo.getPhoneNum());
                useRecord.setUserName(memberInfo.getUserName());
            }
        } catch (Exception e) {
            log.error("设置会员信息失败，会员GUID：{}，错误信息：{}", orderInfo.getMemberInfoGuid(), e.getMessage(), e);
            // 设置默认值，不影响主流程
            useRecord.setOperatorAccountName(orderInfo.getOperatorAccountName());
        }
    }

    /**
     * 检查并添加需要更新状态的优惠券
     *
     * @param discountReq 折扣请求信息
     * @param couponLink 优惠券信息
     * @param context 处理上下文
     * @param couponsToUpdate 需要更新的券列表
     */
    private void checkAndAddCouponForStatusUpdate(SettlementLockedDiscountReqDTO discountReq,
                                                  HsaMemberCouponLink couponLink,
                                                  ExchangeDiscountContext context,
                                                  List<HsaMemberCouponLink> couponsToUpdate) {
        // 如果是不限次数的券，无需更新状态
        if (couponLink.getExchangeLimit() == 0) {
            log.info("券：{}，总次数为不限制，无需更新状态", couponLink.getCode());
            return;
        }

        // 计算总使用次数并判断是否需要更新状态
        if (shouldUpdateCouponStatus(discountReq, couponLink, context)) {
            couponsToUpdate.add(couponLink);
        }
    }

    /**
     * 判断是否应该更新优惠券状态
     *
     * @param discountReq 折扣请求信息
     * @param couponLink 优惠券信息
     * @param context 处理上下文
     * @return 是否需要更新状态
     */
    private boolean shouldUpdateCouponStatus(SettlementLockedDiscountReqDTO discountReq,
                                             HsaMemberCouponLink couponLink,
                                             ExchangeDiscountContext context) {
        int limitNumber = couponLink.getExchangeTimes();
        int totalUsedNumber = discountReq.getUsedTimes();

        // 加上占用次数
        Integer occupiedCount = context.getOccupiedCountMap().get(discountReq.getDiscountOptionId());
        if (Objects.nonNull(occupiedCount)) {
            totalUsedNumber += occupiedCount;
        }

        // 加上已使用次数
        List<HsaMemberCouponUse> usedList = context.getUsedCountMap().get(couponLink.getGuid());
        if (CollUtil.isNotEmpty(usedList)) {
            totalUsedNumber += usedList.size();
        }

        log.info("券：{}，限制次数：{}，已消耗总次数：{}", couponLink.getCode(), limitNumber, totalUsedNumber);

        return limitNumber <= totalUsedNumber;
    }

    /**
     * 批量更新兑换券状态
     *
     * @param couponsToUpdate 需要更新状态的券列表
     * @param orderNo 订单号
     */
    private void batchUpdateExchangeCouponStatus(List<HsaMemberCouponLink> couponsToUpdate, String orderNo) {
        if (CollUtil.isEmpty(couponsToUpdate)) {
            log.info("无需更新兑换券状态，订单号：{}", orderNo);
            return;
        }

        try {
            List<String> memberCouponGuids = couponsToUpdate.stream()
                    .map(HsaBaseEntity::getGuid)
                    .collect(Collectors.toList());

            int updateCount = hsaMemberCouponLinkMapper.updateStateByGuids(
                    memberCouponGuids,
                    CouponMemberStateEnum.APPLY.getCode());

            log.info("批量更新兑换券状态完成，订单号：{}，更新数量：{}", orderNo, updateCount);
        } catch (Exception e) {
            log.error("批量更新兑换券状态失败，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            throw new MemberBaseException("批量更新兑换券状态失败");
        }
    }

    /**
     * 批量保存兑换券使用记录
     *
     * @param useRecords 使用记录列表
     * @param orderNo 订单号
     */
    private void batchSaveExchangeCouponRecords(List<HsaMemberCouponUse> useRecords, String orderNo) {
        if (CollUtil.isEmpty(useRecords)) {
            log.info("无兑换券使用记录需要保存，订单号：{}", orderNo);
            return;
        }

        try {
            memberCouponUseService.saveBatch(useRecords);
            log.info("批量保存兑换券使用记录完成，订单号：{}，记录数：{}", orderNo, useRecords.size());
        } catch (Exception e) {
            log.error("批量保存兑换券使用记录失败，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            throw new MemberBaseException("批量保存兑换券使用记录失败");
        }
    }


    /**
     * 构造选中map
     *
     * @param lockedDiscount 锁定参数
     * @param memberCoupons  优惠券
     * @return couponGuid, req
     */
    private Map<String, SettlementLockedDiscountReqDTO> getCheckDiscountMap(SettlementOrderLockDTO lockedDiscount,
                                                                            List<HsaMemberCouponLink> memberCoupons) {
        final List<SettlementLockedDiscountReqDTO> checkDiscountList = lockedDiscount.getCheckDiscountList();
        final Map<String, SettlementLockedDiscountReqDTO> checkDiscountMap = new HashMap<>();
        for (HsaMemberCouponLink c : memberCoupons) {
            for (SettlementLockedDiscountReqDTO d : checkDiscountList) {
                if (d.getDiscountGuid().equals(c.getCouponCode())
                        && d.getDiscountOptionId().equals(c.getCode())) {
                    checkDiscountMap.put(c.getGuid(), d);
                    break;
                }
            }
        }
        return checkDiscountMap;
    }

    /**
     * 获取锁定优惠信息
     */
    private SettlementOrderLockDTO getSettlementOrderLockDTO(SettlementPayAfterDiscountDTO discountDTO) {
        String couponKey = LOCK_COUPON_DISCOUNT + discountDTO.getCodeType() + discountDTO.getOrderNo();
        String value = stringRedisTemplate.opsForValue().get(couponKey);
        return JSON.parseObject(value, SettlementOrderLockDTO.class);
    }

    /**
     * 清理优惠券锁定缓存
     * 支付完成后清理临时锁定的优惠券信息，释放Redis内存
     *
     * @param discountDTO 支付后折扣处理参数
     */
    private void cleanupLockedCouponCache(SettlementPayAfterDiscountDTO discountDTO) {
        try {
            String couponKey = LOCK_COUPON_DISCOUNT + discountDTO.getCodeType() + discountDTO.getOrderNo();
            Boolean deleted = stringRedisTemplate.delete(couponKey);
            if (Boolean.TRUE.equals(deleted)) {
                log.info("清理优惠券锁定缓存成功，订单号：{}，优惠券类型：{}", discountDTO.getOrderNo(), discountDTO.getCodeType());
            } else {
                log.warn("优惠券锁定缓存不存在或已删除，订单号：{}，优惠券类型：{}", discountDTO.getOrderNo(), discountDTO.getCodeType());
            }
        } catch (Exception e) {
            log.error("清理优惠券锁定缓存异常，订单号：{}，错误信息：{}", discountDTO.getOrderNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 清理释放时的优惠券锁定缓存
     * 订单取消或优惠券释放时清理对应的锁定缓存
     *
     * @param discountDTO 优惠券释放参数
     */
    private void cleanupUnlockedCouponCache(SettlementUnLockedDiscountDTO discountDTO) {
        try {
            // 根据不同的优惠券类型清理对应的缓存
            // 由于释放时可能涉及多种优惠券类型，这里采用模式匹配删除
            String orderNo = discountDTO.getOrderNo();
            String keyPattern = LOCK_COUPON_DISCOUNT + "*" + orderNo;

            // 使用Redis的keys命令查找匹配的键（注意：生产环境谨慎使用keys命令）
            // 更好的做法是维护一个订单到缓存key的映射关系
            Set<String> keys = stringRedisTemplate.keys(keyPattern);
            if (CollUtil.isNotEmpty(keys)) {
                Long deletedCount = stringRedisTemplate.delete(keys);
                log.info("清理优惠券释放缓存完成，订单号：{}，删除缓存数：{}", orderNo, deletedCount);
            } else {
                log.info("未找到需要清理的优惠券锁定缓存，订单号：{}", orderNo);
            }
        } catch (Exception e) {
            log.error("清理优惠券释放缓存异常，订单号：{}，错误信息：{}", discountDTO.getOrderNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    public Map<String, Integer> listByCodeRecord(CouponDtlQO couponDtlQO) {
        List<String> dtlList = couponDtlQO.getDtlList();
        log.info("查询券核销数量：{}", dtlList);
        Map<String, Integer> map = new HashMap<>();
        List<HsaMemberCouponLink> hsaMemberCouponLinks = hsaMemberCouponLinkMapper.selectList(new LambdaQueryWrapper<HsaMemberCouponLink>()
                .in(HsaMemberCouponLink::getDtlGuid, dtlList));

        if (CollUtil.isNotEmpty(hsaMemberCouponLinks)) {
            Map<String, List<HsaMemberCouponLink>> hashMap = hsaMemberCouponLinks.stream().collect(Collectors.groupingBy(HsaMemberCouponLink::getDtlGuid));
            for (String i : dtlList) {
                List<HsaMemberCouponLink> childLinks = hashMap.get(i);
                if (CollUtil.isNotEmpty(childLinks)) {
                    List<String> list = childLinks.stream().map(HsaBaseEntity::getGuid).collect(Collectors.toList());
                    Integer num = hsaMemberCouponUseMapper
                            .selectCount(new LambdaQueryWrapper<HsaMemberCouponUse>().in(HsaMemberCouponUse::getMemberCouponLinkGuid, list));

                    num = num == null ? 0 : num;
                    map.put(i, num);
                }
            }
        }
        return map;
    }

    @Override
    public void sendMemberCoupon(MemberSendCouponQO memberSendCouponQO) {
        log.info("发送会员优惠券：{}", JSON.toJSONString(memberSendCouponQO));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        Integer couponPackageType = Optional.ofNullable(memberSendCouponQO.getCouponPackageType())
                .orElse(CouponPackageTypeEnum.COUPON_ADMIN_SEND.getCode());
        String activityName = CouponPackageTypeEnum.getDesByCode(couponPackageType);
        String couponPackageCode = Optional.ofNullable(memberSendCouponQO.getCouponPackageCode())
                .orElse(headerUserInfo.getUserName() + "-" + headerUserInfo.getTel());
        memberSendCouponQO.setCouponPackageType(couponPackageType);
        memberSendCouponQO.setCouponPackageCode(couponPackageCode);
        memberSendCouponQO.setActivityName(activityName);

        for (MemberInfoCouponQO memberInfoCouponQO : memberSendCouponQO.getMemberInfoCouponQOS()) {
            memberSendCouponQO.setMemberName(memberInfoCouponQO.getMemberName());
            memberSendCouponQO.setMemberGuid(memberInfoCouponQO.getMemberGuid());
            memberSendCouponQO.setMemberPhone(memberInfoCouponQO.getMemberPhone());
            List<HsaMemberCouponLink> hsaMemberCouponLinks = memberCouponApplyAssembler.formMemberCouponLink(memberSendCouponQO);
            if (CollUtil.isNotEmpty(hsaMemberCouponLinks) && StringUtils.isNotEmpty(memberSendCouponQO.getMemberGuid())) {
                memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.saveBatch(hsaMemberCouponLinks));

                List<MemberCouponPackageVO> memberCouponPackageVOS = Lists.newArrayList();
                for (HsaMemberCouponLink hsaMemberCouponLink : hsaMemberCouponLinks) {
                    MemberCouponPackageVO memberCouponPackageVO = memberCouponApplyAssembler.getMemberCouponPackageVO(hsaMemberCouponLink, memberSendCouponQO);
                    memberCouponPackageVOS.add(memberCouponPackageVO);
                }
                sendService.sendMemberCouponNotice(memberCouponPackageVOS);

                // 券分组
                Map<String, List<MemberCouponPackageVO>> memberCouponPackageMap = memberCouponPackageVOS.stream()
                        .collect(Collectors.groupingBy(e -> e.getCouponType() + e.getCouponName()));
                List<MemberCouponPackageVO> memberCouponPackageGroupByVOS = com.beust.jcommander.internal.Lists.newArrayList();
                memberCouponPackageMap.forEach((key, value) -> {
                    MemberCouponPackageVO memberCouponPackageVO = value.get(0);
                    memberCouponPackageVO.setCouponNum(value.size());
                    memberCouponPackageGroupByVOS.add(memberCouponPackageVO);
                });
                memberBaseThreadExecutor.execute(() -> hsaMemberCouponLinkService.sendMemberCouponNotice(memberCouponPackageGroupByVOS));
            }
        }
    }
}
