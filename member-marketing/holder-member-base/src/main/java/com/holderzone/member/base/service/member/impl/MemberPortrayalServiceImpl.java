package com.holderzone.member.base.service.member.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.mapper.grade.HsaMemberGradeInfoMapper;
import com.holderzone.member.base.service.member.HsaMemberConsumptionService;
import com.holderzone.member.base.service.member.HsaOperationMemberInfoService;
import com.holderzone.member.base.service.member.MemberPortrayalService;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.portrayal.MemberPortrayalDetailsDTO;
import com.holderzone.member.common.entity.portrayal.CustomizeLabelDetails;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.SexEnum;
import com.holderzone.member.common.enums.order.OrderRefundTypeEnum;
import com.holderzone.member.common.enums.portrayal.MemberPortrayalFieldEnum;
import com.holderzone.member.common.enums.portrayal.StatisticalPeriodEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.portrayal.MemberPortrayalDetailsVO;
import com.holderzone.member.common.vo.portrayal.MemberPortrayalFieldDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_2;


@Service
@Slf4j
@RequiredArgsConstructor
public class MemberPortrayalServiceImpl implements MemberPortrayalService {

    private final MemberMarketingFeign memberMarketingFeign;

    private final HsaOperationMemberInfoService operationMemberInfoService;

    private final HsaMemberConsumptionService memberConsumptionService;

    private final HsaMemberGradeInfoMapper memberGradeInfoMapper;

    @Override
    public MemberPortrayalDetailsDTO queryMemberPortrayal(String memberGuid) {
        HsaOperationMemberInfo one = operationMemberInfoService.getOne(new LambdaQueryWrapper<HsaOperationMemberInfo>()
                .eq(HsaOperationMemberInfo::getGuid, memberGuid)
                .eq(HsaOperationMemberInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaOperationMemberInfo::getIsDelete, 0));
        if (ObjectUtils.isEmpty(one)) {
            log.warn("[未查询到会员信息]memberGuid={} operSubjectGuid={}", memberGuid, ThreadLocalCache.getOperSubjectGuid());
            throw new MemberBaseException("未查询到会员信息");
        }
        MemberPortrayalDetailsDTO detailsDTO = new MemberPortrayalDetailsDTO();
        detailsDTO.setMemberName(one.getUserName());
        detailsDTO.setMemberGuid(one.getGuid());
        detailsDTO.setMemberPhone(DesensitizedUtil.mobilePhone(one.getPhoneNum()));
        detailsDTO.setHeadImgUrl(one.getHeadImgUrl());

        // 查询会员画像配置
        MemberPortrayalDetailsVO portrayalDetailsVO = memberMarketingFeign.queryApplySetting(ThreadLocalCache.getOperSubjectGuid());
        if (ObjectUtils.isEmpty(portrayalDetailsVO)) {
            log.warn("[未查询到会员画像配置]operSubjectGuid={}", ThreadLocalCache.getOperSubjectGuid());
            return detailsDTO;
        }
        setBasicsFieldList(portrayalDetailsVO.getBasicsFieldList(), one, detailsDTO);
        setConsumeFieldList(portrayalDetailsVO.getConsumeFieldList(), one, detailsDTO);
        setRechargeFieldList(portrayalDetailsVO.getRechargeFieldList(), one, detailsDTO);
        return detailsDTO;
    }

    /**
     * 基础信息
     */
    private void setBasicsFieldList(List<MemberPortrayalFieldDetailsVO> basicsFieldList,
                                    HsaOperationMemberInfo one,
                                    MemberPortrayalDetailsDTO detailsDTO) {
        List<MemberPortrayalFieldDetailsVO> basicsFieldRespList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(basicsFieldList)) {
            for (MemberPortrayalFieldDetailsVO field : basicsFieldList) {
                handlePortrayalBasicsField(one, field, basicsFieldRespList);
            }
        }
        detailsDTO.setBasicsFieldList(basicsFieldRespList);
    }

    /**
     * 消费信息
     */
    private void setConsumeFieldList(List<MemberPortrayalFieldDetailsVO> consumeFieldList,
                                     HsaOperationMemberInfo one,
                                     MemberPortrayalDetailsDTO detailsDTO) {
        List<MemberPortrayalFieldDetailsVO> consumeFieldRespList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(consumeFieldList)) {
            List<HsaMemberConsumption> memberConsumptionList = memberConsumptionService.listConsumptionByMemberGuid(one.getGuid());
            List<HsaMemberConsumption> memberReturnList = memberConsumptionService.listReturnByMemberGuid(one.getGuid());
            memberReturnList.forEach(e -> e.setOrderPaidAmount(e.getOrderPaidAmount().negate()));
            for (MemberPortrayalFieldDetailsVO field : consumeFieldList) {
                handlePortrayalConsumptionField(field, memberConsumptionList, consumeFieldRespList, memberReturnList);
            }
        }
        detailsDTO.setConsumeFieldList(consumeFieldRespList);
    }

    private void handlePortrayalConsumptionField(MemberPortrayalFieldDetailsVO field,
                                                 List<HsaMemberConsumption> memberConsumptionList,
                                                 List<MemberPortrayalFieldDetailsVO> consumeFieldRespList,
                                                 List<HsaMemberConsumption> memberReturnList) {
        MemberPortrayalFieldEnum fieldEnum = MemberPortrayalFieldEnum.getEnum(field.getField());
        switch (Objects.requireNonNull(fieldEnum)) {
            case CONSUME_COUNT:
                MemberPortrayalFieldDetailsVO consumeCountDTO = new MemberPortrayalFieldDetailsVO();
                consumeCountDTO.setField(field.getField());
                consumeCountDTO.setFieldName(field.getFieldName());
                consumeCountDTO.setFieldType(field.getFieldType());
                consumeCountDTO.setFieldSort(field.getFieldSort());

                consumeCountDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                consumeCountDTO.setRecentDays(field.getRecentDays());
                consumeCountDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                consumeCountDTO.setAmountLimit(field.getAmountLimit());

                List<HsaMemberConsumption> consumeCountList = filterStatisticalPeriod(field, memberConsumptionList);
                consumeCountList = consumeCountList.stream()
                        .filter(e -> e.getRefundType() != OrderRefundTypeEnum.ALL_REFUND.getCode())
                        .collect(Collectors.toList());
                consumeCountList = filterAmountLimit(field, consumeCountList);
                int consumeCountListSize = 0;
                if (null != consumeCountList) {
                    consumeCountListSize = consumeCountList.size();
                }
                handleCustomizeLabel(field, consumeCountDTO, consumeCountListSize);

                consumeCountDTO.setFieldValue(String.valueOf(consumeCountListSize));
                consumeFieldRespList.add(consumeCountDTO);
                break;
            case CONSUME_AMOUNT:
                MemberPortrayalFieldDetailsVO consumeAmountDTO = new MemberPortrayalFieldDetailsVO();
                consumeAmountDTO.setField(field.getField());
                consumeAmountDTO.setFieldName(field.getFieldName());
                consumeAmountDTO.setFieldType(field.getFieldType());
                consumeAmountDTO.setFieldSort(field.getFieldSort());

                consumeAmountDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                consumeAmountDTO.setRecentDays(field.getRecentDays());
                consumeAmountDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                consumeAmountDTO.setAmountLimit(field.getAmountLimit());

                BigDecimal orderAmountTotal = calculateAmountTotalString(field, memberConsumptionList, memberReturnList);
                consumeAmountDTO.setFieldValue(orderAmountTotal.toPlainString());
                consumeFieldRespList.add(consumeAmountDTO);
                break;
            case GUEST_SINGLE_PRICE:
                MemberPortrayalFieldDetailsVO guestSinglePriceDTO = new MemberPortrayalFieldDetailsVO();
                guestSinglePriceDTO.setField(field.getField());
                guestSinglePriceDTO.setFieldName(field.getFieldName());
                guestSinglePriceDTO.setFieldType(field.getFieldType());
                guestSinglePriceDTO.setFieldSort(field.getFieldSort());

                guestSinglePriceDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                guestSinglePriceDTO.setRecentDays(field.getRecentDays());
                guestSinglePriceDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                guestSinglePriceDTO.setAmountLimit(field.getAmountLimit());

                List<HsaMemberConsumption> guestSinglePriceList = filterStatisticalPeriod(field, memberConsumptionList);
                guestSinglePriceList = filterAmountLimit(field, guestSinglePriceList);
                BigDecimal guestSinglePrice = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(guestSinglePriceList)) {
                    int guestSinglePriceListSize = guestSinglePriceList.size();
                    List<HsaMemberConsumption> guestReturnCountList = filterStatisticalPeriod(field, memberReturnList);
                    if (!CollectionUtils.isEmpty(guestReturnCountList)) {
                        guestSinglePriceList.addAll(guestReturnCountList);
                    }
                    BigDecimal orderPaidAmountTotal = guestSinglePriceList.stream()
                            .filter(Objects::nonNull)
                            .map(HsaMemberConsumption::getOrderPaidAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    guestSinglePrice = orderPaidAmountTotal.divide(BigDecimal.valueOf(guestSinglePriceListSize),
                            2, RoundingMode.HALF_UP);
                }

                // 客单价 = 累计消费实付金额（扣减反结账/部分退款金额）/ 累计消费订单笔数（整单退/反结账状态订单除外）
                guestSinglePriceDTO.setFieldValue(guestSinglePrice.toPlainString());
                consumeFieldRespList.add(guestSinglePriceDTO);
                break;
            case LAST_CONSUME_TIME:
                MemberPortrayalFieldDetailsVO lastConsumeTimeDTO = new MemberPortrayalFieldDetailsVO();
                lastConsumeTimeDTO.setField(field.getField());
                lastConsumeTimeDTO.setFieldName(field.getFieldName());
                lastConsumeTimeDTO.setFieldType(field.getFieldType());
                lastConsumeTimeDTO.setFieldSort(field.getFieldSort());

                lastConsumeTimeDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                lastConsumeTimeDTO.setRecentDays(field.getRecentDays());
                lastConsumeTimeDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                lastConsumeTimeDTO.setAmountLimit(field.getAmountLimit());

                if (CollectionUtils.isEmpty(memberConsumptionList)) {
                    lastConsumeTimeDTO.setFieldValue(StringConstant.STR_BIAS_TWO);
                } else {
                    memberConsumptionList.sort(Comparator.comparing(HsaMemberConsumption::getGmtCreate).reversed());
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(StringConstant.FORMAT_YYYY_MM_DD);
                    lastConsumeTimeDTO.setFieldValue(memberConsumptionList.get(0).getGmtCreate().format(formatter));
                }
                consumeFieldRespList.add(lastConsumeTimeDTO);
                break;
            default:
                break;
        }
    }

    /**
     * 统计周期过滤
     */
    private List<HsaMemberConsumption> filterStatisticalPeriod(MemberPortrayalFieldDetailsVO field,
                                                               List<HsaMemberConsumption> memberConsumptionList) {
        List<HsaMemberConsumption> consumeCountList = new ArrayList<>();
        if (CollectionUtils.isEmpty(memberConsumptionList)) {
            log.warn("[统计周期过滤]消费记录为空");
            return consumeCountList;
        }
        StatisticalPeriodEnum periodEnum = StatisticalPeriodEnum.getEnum(field.getStatisticalPeriod());
        switch (Objects.requireNonNull(periodEnum)) {
            case REGISTERED_TO_NOW:
                consumeCountList.addAll(memberConsumptionList);
                break;
            case SOME_TIME_RECENTLY:
                LocalDateTime recentDaysStart = LocalDateTime.now().minusDays(field.getRecentDays());
                consumeCountList.addAll(memberConsumptionList.stream()
                        .filter(c -> !c.getConsumptionTime().isBefore(recentDaysStart))
                        .collect(Collectors.toList()));
                break;
            case THIS_YEAR:
                LocalDateTime thisYearStart = LocalDateTime.now().withDayOfYear(1).with(LocalTime.MIN);
                consumeCountList.addAll(memberConsumptionList.stream()
                        .filter(c -> !c.getConsumptionTime().isBefore(thisYearStart))
                        .collect(Collectors.toList()));
                break;
            case CURRENT_MONTH:
                LocalDateTime thisMonthStart = LocalDateTime.now().withDayOfMonth(1).with(LocalTime.MIN);
                consumeCountList.addAll(memberConsumptionList.stream()
                        .filter(c -> !c.getConsumptionTime().isBefore(thisMonthStart))
                        .collect(Collectors.toList()));
                break;
            default:
                break;
        }
        return consumeCountList;
    }

    /**
     * 充值信息
     */
    private void setRechargeFieldList(List<MemberPortrayalFieldDetailsVO> rechargeFieldList,
                                      HsaOperationMemberInfo one,
                                      MemberPortrayalDetailsDTO detailsDTO) {
        List<MemberPortrayalFieldDetailsVO> rechargeFieldRespList = new ArrayList<>();
        List<HsaMemberConsumption> memberRechargeList = memberConsumptionService.listRechargeByMemberGuid(one.getGuid());
        if (!CollectionUtils.isEmpty(rechargeFieldList)) {
            for (MemberPortrayalFieldDetailsVO field : rechargeFieldList) {
                handlePortrayalRechargeField(field, memberRechargeList, rechargeFieldRespList);
            }
        }
        detailsDTO.setRechargeFieldList(rechargeFieldRespList);
    }

    private void handlePortrayalRechargeField(MemberPortrayalFieldDetailsVO field,
                                              List<HsaMemberConsumption> memberRechargeList,
                                              List<MemberPortrayalFieldDetailsVO> rechargeFieldRespList) {
        MemberPortrayalFieldEnum fieldEnum = MemberPortrayalFieldEnum.getEnum(field.getField());
        switch (Objects.requireNonNull(fieldEnum)) {
            case RECHARGE_COUNT:
                MemberPortrayalFieldDetailsVO rechargeCountDTO = new MemberPortrayalFieldDetailsVO();
                rechargeCountDTO.setField(field.getField());
                rechargeCountDTO.setFieldName(field.getFieldName());
                rechargeCountDTO.setFieldType(field.getFieldType());
                rechargeCountDTO.setFieldSort(field.getFieldSort());

                rechargeCountDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                rechargeCountDTO.setRecentDays(field.getRecentDays());
                rechargeCountDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                rechargeCountDTO.setAmountLimit(field.getAmountLimit());

                List<HsaMemberConsumption> rechargeCountList = filterStatisticalPeriod(field, memberRechargeList);
                rechargeCountList = filterOrderPaidAmountLimit(field, rechargeCountList);
                int rechargeCountListSize = 0;
                if (!CollectionUtils.isEmpty(rechargeCountList)) {
                    rechargeCountListSize = rechargeCountList.size();
                }
                rechargeCountDTO.setFieldValue(String.valueOf(rechargeCountListSize));
                rechargeFieldRespList.add(rechargeCountDTO);
                break;
            case RECHARGE_AMOUNT:
                MemberPortrayalFieldDetailsVO rechargeAmountDTO = new MemberPortrayalFieldDetailsVO();
                rechargeAmountDTO.setField(field.getField());
                rechargeAmountDTO.setFieldName(field.getFieldName());
                rechargeAmountDTO.setFieldType(field.getFieldType());
                rechargeAmountDTO.setFieldSort(field.getFieldSort());

                rechargeAmountDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                rechargeAmountDTO.setRecentDays(field.getRecentDays());
                rechargeAmountDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                rechargeAmountDTO.setAmountLimit(field.getAmountLimit());

                List<HsaMemberConsumption> rechargeAmountList = filterStatisticalPeriod(field, memberRechargeList);
                BigDecimal rechargeAmountTotal = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(rechargeAmountList)) {
                    rechargeAmountTotal = rechargeAmountList.stream()
                            .filter(Objects::nonNull)
                            .map(HsaMemberConsumption::getOrderPaidAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                rechargeAmountDTO.setFieldValue(rechargeAmountTotal.toPlainString());
                rechargeFieldRespList.add(rechargeAmountDTO);
                break;
            case AVERAGE_RECHARGE_AMOUNT:
                MemberPortrayalFieldDetailsVO averageRechargeAmountDTO = new MemberPortrayalFieldDetailsVO();
                averageRechargeAmountDTO.setField(field.getField());
                averageRechargeAmountDTO.setFieldName(field.getFieldName());
                averageRechargeAmountDTO.setFieldType(field.getFieldType());
                averageRechargeAmountDTO.setFieldSort(field.getFieldSort());

                averageRechargeAmountDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                averageRechargeAmountDTO.setRecentDays(field.getRecentDays());
                averageRechargeAmountDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                averageRechargeAmountDTO.setAmountLimit(field.getAmountLimit());

                List<HsaMemberConsumption> averageRechargeAmountList = filterStatisticalPeriod(field, memberRechargeList);
                BigDecimal averageRechargeAmount = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(averageRechargeAmountList)) {
                    BigDecimal orderPaidAmountTotal = averageRechargeAmountList.stream()
                            .filter(Objects::nonNull)
                            .map(HsaMemberConsumption::getOrderPaidAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    int averageRechargeAmountListSize = averageRechargeAmountList.size();
                    averageRechargeAmount = orderPaidAmountTotal.divide(BigDecimal.valueOf(averageRechargeAmountListSize),
                            2, RoundingMode.HALF_UP);
                }
                averageRechargeAmountDTO.setFieldValue(averageRechargeAmount.toPlainString());
                rechargeFieldRespList.add(averageRechargeAmountDTO);
                break;
            case LAST_RECHARGE_TIME:
                MemberPortrayalFieldDetailsVO lastRechargeTimeDTO = new MemberPortrayalFieldDetailsVO();
                lastRechargeTimeDTO.setField(field.getField());
                lastRechargeTimeDTO.setFieldName(field.getFieldName());
                lastRechargeTimeDTO.setFieldType(field.getFieldType());
                lastRechargeTimeDTO.setFieldSort(field.getFieldSort());

                lastRechargeTimeDTO.setStatisticalPeriod(field.getStatisticalPeriod());
                lastRechargeTimeDTO.setRecentDays(field.getRecentDays());
                lastRechargeTimeDTO.setIsSelectAmountLimit(field.getIsSelectAmountLimit());
                lastRechargeTimeDTO.setAmountLimit(field.getAmountLimit());

                if (CollectionUtils.isEmpty(memberRechargeList)) {
                    lastRechargeTimeDTO.setFieldValue(StringConstant.STR_BIAS_TWO);
                } else {
                    memberRechargeList.sort(Comparator.comparing(HsaMemberConsumption::getGmtCreate).reversed());
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(StringConstant.FORMAT_YYYY_MM_DD);
                    lastRechargeTimeDTO.setFieldValue(memberRechargeList.get(0).getGmtCreate().format(formatter));
                }
                rechargeFieldRespList.add(lastRechargeTimeDTO);
                break;
            default:
                break;
        }
    }

    private void handlePortrayalBasicsField(HsaOperationMemberInfo one,
                                            MemberPortrayalFieldDetailsVO field,
                                            List<MemberPortrayalFieldDetailsVO> basicsFieldRespList) {
        MemberPortrayalFieldEnum fieldEnum = MemberPortrayalFieldEnum.getEnum(field.getField());
        switch (Objects.requireNonNull(fieldEnum)) {
            case GENDER:
                MemberPortrayalFieldDetailsVO genderDTO = new MemberPortrayalFieldDetailsVO();
                genderDTO.setField(field.getField());
                genderDTO.setFieldName(field.getFieldName());
                genderDTO.setFieldType(field.getFieldType());
                genderDTO.setFieldSort(field.getFieldSort());
                String sex = getSex(one.getSex());
                genderDTO.setFieldValue(sex);
                basicsFieldRespList.add(genderDTO);
                break;
            case BIRTHDAY:
                MemberPortrayalFieldDetailsVO birthdayDTO = getBirthdayDTO(one, field);
                basicsFieldRespList.add(birthdayDTO);
                break;
            case REGISTER_TIME:
                MemberPortrayalFieldDetailsVO registerTimeDTO = new MemberPortrayalFieldDetailsVO();
                registerTimeDTO.setField(field.getField());
                registerTimeDTO.setFieldName(field.getFieldName());
                registerTimeDTO.setFieldType(field.getFieldType());
                registerTimeDTO.setFieldSort(field.getFieldSort());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(StringConstant.FORMAT_YYYY_MM_DD);
                registerTimeDTO.setFieldValue(one.getGmtCreate().format(formatter));
                basicsFieldRespList.add(registerTimeDTO);
                break;
            case MEMBER_GRADE:
                MemberPortrayalFieldDetailsVO memberGradeDTO = getGradeDTO(one, field);
                basicsFieldRespList.add(memberGradeDTO);
                break;
            default:
                break;
        }
    }


    /**
     * 性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    private String getSex(Integer sex) {
        if (ObjectUtils.isEmpty(sex)) {
            return "未知";
        }
        if (sex == SexEnum.SEX_NAN.getCode()) {
            return "男性";
        }
        if (sex == SexEnum.SEX_NV.getCode()) {
            return "女性";
        }
        return "未知";
    }

    private MemberPortrayalFieldDetailsVO getBirthdayDTO(HsaOperationMemberInfo one,
                                                         MemberPortrayalFieldDetailsVO field) {
        MemberPortrayalFieldDetailsVO birthdayDTO = new MemberPortrayalFieldDetailsVO();
        birthdayDTO.setField(field.getField());
        birthdayDTO.setFieldName(field.getFieldName());
        birthdayDTO.setFieldType(field.getFieldType());
        birthdayDTO.setFieldSort(field.getFieldSort());
        if (ObjectUtils.isEmpty(one.getBirthday())) {
            birthdayDTO.setFieldValue(StringConstant.STR_BIAS_TWO);
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(StringConstant.FORMAT_YYYY_MM_DD);
            birthdayDTO.setFieldValue(one.getBirthday().format(formatter));
        }
        return birthdayDTO;
    }

    private MemberPortrayalFieldDetailsVO getGradeDTO(HsaOperationMemberInfo one,
                                                      MemberPortrayalFieldDetailsVO field) {
        MemberPortrayalFieldDetailsVO memberGradeDTO = new MemberPortrayalFieldDetailsVO();
        memberGradeDTO.setField(field.getField());
        memberGradeDTO.setFieldName(field.getFieldName());
        memberGradeDTO.setFieldType(field.getFieldType());
        memberGradeDTO.setFieldSort(field.getFieldSort());

        String memberGradeInfoGuid = StringUtils.isEmpty(one.getMemberPaidGradeInfoGuid())
                ? one.getMemberGradeInfoGuid() : one.getMemberPaidGradeInfoGuid();
        HsaMemberGradeInfo hsaMemberGradeInfo = memberGradeInfoMapper.selectOne(new LambdaQueryWrapper<HsaMemberGradeInfo>()
                .eq(HsaMemberGradeInfo::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaMemberGradeInfo::getGuid, memberGradeInfoGuid)
                .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode())
                .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE, NUMBER_2));
        if (Objects.isNull(hsaMemberGradeInfo)) {
            memberGradeDTO.setFieldValue(StringConstant.STR_BIAS_TWO);
        }
        memberGradeDTO.setFieldValue(hsaMemberGradeInfo.getName());
        return memberGradeDTO;
    }

    private void handleCustomizeLabel(MemberPortrayalFieldDetailsVO field,
                                      MemberPortrayalFieldDetailsVO consumeCountDTO,
                                      int consumeCountListSize) {
        if (!CollectionUtils.isEmpty(field.getCustomizeLabelList())) {
            String json = JacksonUtils.writeValueAsString(field.getCustomizeLabelList());
            consumeCountDTO.setCustomizeLabelList(JacksonUtils.toObjectList(CustomizeLabelDetails.class, json));
            for (CustomizeLabelDetails customizeLabel : field.getCustomizeLabelList()) {
                if (ObjectUtils.isEmpty(customizeLabel.getIncludeStartNum())
                        && !ObjectUtils.isEmpty(customizeLabel.getIncludeEndNum())
                        && consumeCountListSize <= customizeLabel.getIncludeEndNum()) {
                    consumeCountDTO.setFieldName(customizeLabel.getLabelName());
                }

                if (!ObjectUtils.isEmpty(customizeLabel.getIncludeStartNum())
                        && !ObjectUtils.isEmpty(customizeLabel.getIncludeEndNum())
                        && consumeCountListSize >= customizeLabel.getIncludeStartNum() &&
                        consumeCountListSize <= customizeLabel.getIncludeEndNum()) {
                    consumeCountDTO.setFieldName(customizeLabel.getLabelName());
                }

                if (!ObjectUtils.isEmpty(customizeLabel.getIncludeStartNum())
                        && ObjectUtils.isEmpty(customizeLabel.getIncludeEndNum())
                        && consumeCountListSize >= customizeLabel.getIncludeStartNum()) {
                    consumeCountDTO.setFieldName(customizeLabel.getLabelName());
                }

            }
        }
    }

    private List<HsaMemberConsumption> filterAmountLimit(MemberPortrayalFieldDetailsVO field,
                                                         List<HsaMemberConsumption> consumeCountList) {
        if (!CollectionUtils.isEmpty(consumeCountList)
                && !ObjectUtils.isEmpty(field.getIsSelectAmountLimit())
                && field.getIsSelectAmountLimit() == BooleanEnum.TRUE.getCode()
                && !ObjectUtils.isEmpty(field.getAmountLimit())) {
            consumeCountList = consumeCountList.stream()
                    .filter(c -> BigDecimalUtil.greaterEqual(c.getOrderAmount(), field.getAmountLimit()))
                    .collect(Collectors.toList());
        }
        return consumeCountList;
    }

    private List<HsaMemberConsumption> filterOrderPaidAmountLimit(MemberPortrayalFieldDetailsVO field,
                                                                  List<HsaMemberConsumption> consumeCountList) {
        if (!CollectionUtils.isEmpty(consumeCountList)
                && !ObjectUtils.isEmpty(field.getIsSelectAmountLimit())
                && field.getIsSelectAmountLimit() == BooleanEnum.TRUE.getCode()
                && !ObjectUtils.isEmpty(field.getAmountLimit())) {
            consumeCountList = consumeCountList.stream()
                    .filter(c -> BigDecimalUtil.greaterEqual(c.getOrderPaidAmount(), field.getAmountLimit()))
                    .collect(Collectors.toList());
        }
        return consumeCountList;
    }

    private BigDecimal calculateAmountTotalString(MemberPortrayalFieldDetailsVO field,
                                                  List<HsaMemberConsumption> memberConsumptionList,
                                                  List<HsaMemberConsumption> memberReturnList) {
        List<HsaMemberConsumption> consumeAmountList = filterStatisticalPeriod(field, memberConsumptionList);
        List<HsaMemberConsumption> returnCountList = filterStatisticalPeriod(field, memberReturnList);
        if (!CollectionUtils.isEmpty(returnCountList)) {
            consumeAmountList.addAll(returnCountList);
        }
        BigDecimal orderAmountTotal = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(consumeAmountList)) {
            orderAmountTotal = consumeAmountList.stream()
                    .filter(Objects::nonNull)
                    .map(HsaMemberConsumption::getOrderPaidAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return orderAmountTotal;
    }

}
