package com.holderzone.member.base.service.settlement.chain;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.dto.SettlementBackDiscountDTO;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.base.service.member.feign.MemberFeignService;
import com.holderzone.member.base.service.settlement.assembler.SettlementFullReductionFoldAssembler;
import com.holderzone.member.base.support.FullReductionFoldSupport;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.SettleApplyCommodityDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.module.settlement.apply.dto.*;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRunQO;
import com.holderzone.member.common.qo.activity.SynOrderDiscountQO;
import com.holderzone.member.common.qo.card.BarkOrderDiscountCallbackQO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRunVO;
import com.holderzone.member.common.vo.member.MemberLabelGradeVO;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 满减满折活动优惠
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class FullReductionFoldSettleHandler extends SettleHandler {

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;

    @Resource
    private MemberFeignService memberFeignService;

    @Resource
    private FullReductionFoldSupport support;

    @Override
    public int option() {
        return SettlementDiscountOptionEnum.FULL_OFF.getCode();
    }

    @Override
    public SettlementApplyOrderVO listDiscount(SettlementApplyOrderDTO dto) {
        SettlementApplyOrderInfoDTO orderInfo = dto.getOrderInfo();
        FullReductionFoldActivityRunQO reductionFoldActivityRunQO = getActivityRunQO(dto, orderInfo);
        List<FullReductionFoldActivityRunVO> fullReductionFoldActivityRunVOS = memberMarketingFeign.querySettleCommodityByRun(reductionFoldActivityRunQO);

        if (CollUtil.isEmpty(fullReductionFoldActivityRunVOS)) {
            return new SettlementApplyOrderVO();
        }
        return support.getSettlementApplyOrderVO(dto, fullReductionFoldActivityRunVOS, orderInfo);
    }


    private FullReductionFoldActivityRunQO getActivityRunQO(SettlementApplyOrderDTO dto,
                                                            SettlementApplyOrderInfoDTO orderInfo) {
        FullReductionFoldActivityRunQO reductionFoldActivityRunQO = new FullReductionFoldActivityRunQO();
         List<String> discountGuidList = new ArrayList<>();
        // 兼容多门店
        reductionFoldActivityRunQO.setStoreGuidList(orderInfo.getStoreGuidList());
        reductionFoldActivityRunQO.setApplyBusiness(Integer.parseInt(orderInfo.getBusiness()));
        reductionFoldActivityRunQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());

        if (StringUtils.isNotEmpty(orderInfo.getMemberInfoGuid())) {
            MemberLabelGradeVO memberLabelGradeVO = memberFeignService.getMemberLabelAndGradeInfo(orderInfo.getMemberInfoGuid());
            reductionFoldActivityRunQO.setMemberLabelGuidList(memberLabelGradeVO.getMemberLabelList());
            reductionFoldActivityRunQO.setMemberGradeGuid(memberLabelGradeVO.getMemberGradeInfoGuid());
            reductionFoldActivityRunQO.setMemberInfoGuid(orderInfo.getMemberInfoGuid());
        }

        //商品列表处理
        List<SettleApplyCommodityDTO> settleApplyCommodityDTOS = SettlementFullReductionFoldAssembler.convertFromApplyCommodityDTOS(dto);
        reductionFoldActivityRunQO.setSettleApplyCommodityDTOS(settleApplyCommodityDTOS);

        if (CollUtil.isNotEmpty(dto.getCheckDiscountList())
                && dto.getIsAppointDiscount() == BooleanEnum.TRUE.getCode()
                && CollUtil.isNotEmpty(dto.getCheckDiscountList()
                .stream()
                .filter(c -> SettlementDiscountOptionEnum.FULL_OFF.getCode() == c.getDiscountOption())
                .collect(Collectors.toList()))
        ) {
            discountGuidList.addAll(dto.getCheckDiscountList()
                                                                   .stream()
                                                                   .filter(c -> SettlementDiscountOptionEnum.FULL_OFF.getCode() == c.getDiscountOption())
                                                                   .map(SettlementApplyDiscountBaseReqDTO::getDiscountGuid).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(dto.getAppendDiscountOptionGuid())){
            discountGuidList.addAll(dto.getAppendDiscountOptionGuid());
        }
        reductionFoldActivityRunQO.setDiscountGuidList(discountGuidList.stream().distinct().collect(Collectors.toList()));
        return reductionFoldActivityRunQO;
    }


    @Override
    public SettlementApplyOrderVO calculateDiscount(SettleHandlerCalculateChain chain) {
        //选中
        final String checkDiscountGuid = chain.getReq().getCheckDiscountList().stream()
                .filter(c -> SettlementDiscountOptionEnum.FULL_OFF.getCode() == c.getDiscountOption())
                .map(SettlementApplyDiscountBaseReqDTO::getDiscountGuid)
                .findFirst().orElse("");
        if (StringUtil.isBlank(checkDiscountGuid)) {
            return null;
        }

        log.info("选中了满减满折折扣:{}", checkDiscountGuid);
        SettlementApplyOrderDTO settlementApplyOrderDTO = chain.getReq();
        settlementApplyOrderDTO.setIsAppointDiscount(BooleanEnum.TRUE.getCode());
        return listDiscount(settlementApplyOrderDTO);
    }

    @Override
    public void lockDiscount(SettlementOrderLockDTO lockedDiscount) {
        log.info("满减满折折扣锁定");
    }

    @Override
    public void unlockDiscount(SettlementUnLockedDiscountDTO discountDTO) {
        log.info("满减满折折扣解锁");
    }

    /**
     * 支付后操作
     */
    @Override
    public void afterPayDiscount(SettlementPayAfterDiscountDTO discountDTO) {
        List<SynOrderDiscountQO> synOrderDiscountQOS = Lists.newArrayList();
        List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = discountDTO.getOrderDiscountByOptionMap()
                .get(SettlementDiscountOptionEnum.FULL_OFF.getCode());
        for (HsaMemberOrderDiscount hsaMemberOrderDiscount : hsaMemberOrderDiscounts) {
            SynOrderDiscountQO synOrderDiscountQO = new SynOrderDiscountQO();
            BeanUtils.copyProperties(discountDTO, synOrderDiscountQO);
            synOrderDiscountQO
                    .setDiscountGuid(hsaMemberOrderDiscount.getDiscountGuid())
                    .setMemberGuid(hsaMemberOrderDiscount.getMemberGuid())
                    .setCouponRollback(hsaMemberOrderDiscount.getCouponRollback());

            synOrderDiscountQOS.add(synOrderDiscountQO);
        }
        memberMarketingFeign.saveFullReductionRecord(synOrderDiscountQOS);

        // 打标签
        support.markLabel(discountDTO, hsaMemberOrderDiscounts);
    }

    /**
     * 回退优惠
     */
    @Override
    public void backDiscount(SettlementBackDiscountDTO discountDTO) {
        log.info("满减满折折扣回退：{}", JSON.toJSONString(discountDTO));
        //整单退则无需处理商品优惠
        if (discountDTO.getIsIntegralBack() == BooleanEnum.TRUE.getCode()) {
            List<HsaMemberOrderDiscount> discountList = discountDTO.getOrderDiscountByOptionMap()
                    .get(SettlementDiscountOptionEnum.FULL_OFF.getCode());

            //过滤可退款的优惠
            discountList = discountList.stream().filter(in -> in.getDiscountState() == BooleanEnum.FALSE.getCode()
                            && in.getCouponRollback() == BooleanEnum.TRUE.getCode())
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(discountList)) {
                log.info("订单：{}，没有满足可退款的满减满折优惠记录，流程终止", discountDTO.getOrderNo());
                return;
            }
            discountList.forEach(in -> {
                in.setDiscountState(BooleanEnum.TRUE.getCode());
                in.setCommodityJson(null);
                hsaMemberOrderDiscountService.updateByGuid(in);
            });

            // 整单退 不需要处理标签了
        }
        //修改统计记录
        updateRecord(discountDTO);
    }

    private void updateRecord(SettlementBackDiscountDTO discountDTO) {
        BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO = new BarkOrderDiscountCallbackQO();
        BeanUtils.copyProperties(discountDTO, barkOrderDiscountCallbackQO);
        memberMarketingFeign.updateFullReductionRecord(barkOrderDiscountCallbackQO);
    }
}
