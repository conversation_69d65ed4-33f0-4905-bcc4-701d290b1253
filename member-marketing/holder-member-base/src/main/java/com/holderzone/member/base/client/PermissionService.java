package com.holderzone.member.base.client;

import cn.hutool.core.util.StrUtil;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.ipass.SubjectVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class PermissionService {

    @Resource
    private IPaasFeign iPaasFeign;

    @Resource
    private ExternalSupport externalSupport;

    /**
     * 获取用户在企业下的功能权限
     *
     * @return 功能权限
     */
    public List<String> queryIdentificationNames() {
        return externalSupport.baseServer(ThreadLocalCache.getSystem()).listIdentificationNames();
    }

    /**
     * 根据账号查询所属企业有权限的运营主体
     *
     * @param identificationName 权限标识
     * @return 运营主体
     */
    public List<SubjectVO> querySubjects(String identificationName) {
        return externalSupport.baseServer(ThreadLocalCache.getSystem()).listIdentificationSubjects(identificationName);
    }
}
