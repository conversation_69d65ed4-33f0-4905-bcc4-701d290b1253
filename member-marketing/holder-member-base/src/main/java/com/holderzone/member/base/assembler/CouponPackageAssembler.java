package com.holderzone.member.base.assembler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.dto.coupon.ResponseCouponNumDTO;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponPackageLink;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.coupon.CouponMemberStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.qo.coupon.MemberCouponListQO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.excel.ExcelUtil;
import com.holderzone.member.common.vo.coupon.*;
import jodd.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 发劵宝
 *
 * <AUTHOR>
 * @date 2023/8/10
 **/
public class CouponPackageAssembler {

    private CouponPackageAssembler() {

    }

    public static List<CouponPackageGiveVO> toCouponPackageGiveVOs(List<HsaMemberCouponPackageLink> packageLinkList) {
        if (CollectionUtils.isEmpty(packageLinkList)) {
            return Collections.emptyList();
        }
        List<CouponPackageGiveVO> giveVOList = Lists.newArrayList();
        for (HsaMemberCouponPackageLink hsaMemberCouponPackageLink : packageLinkList) {
            giveVOList.add(toCouponPackageGiveVO(hsaMemberCouponPackageLink));
        }
        return giveVOList;
    }

    public static CouponPackageGiveVO toCouponPackageGiveVO(HsaMemberCouponPackageLink hsaMemberCouponPackageLink) {
        CouponPackageGiveVO couponPackageGiveVO = new CouponPackageGiveVO();
        couponPackageGiveVO.setGuid(hsaMemberCouponPackageLink.getGuid());
        couponPackageGiveVO.setActivityName(hsaMemberCouponPackageLink.getActivityName());
        couponPackageGiveVO.setActivityGuid(hsaMemberCouponPackageLink.getActivityGuid());
        couponPackageGiveVO.setActivityCode(hsaMemberCouponPackageLink.getActivityCode());
        //券记录
        Optional.ofNullable(hsaMemberCouponPackageLink.getCouponPackageJson()).ifPresent(json ->
                couponPackageGiveVO.setCouponPackageJson(JacksonUtils.toObjectList(ResponseCouponNumDTO.class, json))
        );
        couponPackageGiveVO.setUserName(hsaMemberCouponPackageLink.getUserName());
        couponPackageGiveVO.setMemberPhone(hsaMemberCouponPackageLink.getMemberPhone());
        couponPackageGiveVO.setSource(hsaMemberCouponPackageLink.getSource());
        couponPackageGiveVO.setStoreName(hsaMemberCouponPackageLink.getStoreName());
        couponPackageGiveVO.setReachTime(hsaMemberCouponPackageLink.getReachTime());
        couponPackageGiveVO.setUpdateTime(hsaMemberCouponPackageLink.getGmtModified());
        couponPackageGiveVO.setState(hsaMemberCouponPackageLink.getState());
        couponPackageGiveVO.setReason(hsaMemberCouponPackageLink.getReason());
        return couponPackageGiveVO;

    }

    public static List<CouponGiveVO> toCouponGiveVOs(List<HsaMemberCouponLink> packageLinkList) {
        if (CollectionUtils.isEmpty(packageLinkList)) {
            return Collections.emptyList();
        }
        List<CouponGiveVO> couponGiveVOList = Lists.newArrayList();
        for (HsaMemberCouponLink hsaMemberCouponLink : packageLinkList) {
            couponGiveVOList.add(toCouponGiveVO(hsaMemberCouponLink));
        }
        return couponGiveVOList;

    }

    public static CouponGiveVO toCouponGiveVO(HsaMemberCouponLink hsaMemberCouponLink) {
        CouponGiveVO couponGiveVO = new CouponGiveVO();
        couponGiveVO.setCouponGuid(hsaMemberCouponLink.getGuid());
        couponGiveVO.setCouponCode(hsaMemberCouponLink.getCouponCode());
        couponGiveVO.setCouponName(hsaMemberCouponLink.getCouponName());
        couponGiveVO.setCouponType(hsaMemberCouponLink.getCouponType());
        couponGiveVO.setCode(hsaMemberCouponLink.getCode());
        couponGiveVO.setCouponEffectiveStartTime(hsaMemberCouponLink.getCouponEffectiveStartTime());
        couponGiveVO.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveEndTime());
        couponGiveVO.setMemberGuid(hsaMemberCouponLink.getMemberGuid());
        couponGiveVO.setUserName(hsaMemberCouponLink.getUserName());
        couponGiveVO.setMemberPhone(hsaMemberCouponLink.getMemberPhone());
        couponGiveVO.setSource(hsaMemberCouponLink.getSource());
        couponGiveVO.setCouponPackageType(hsaMemberCouponLink.getCouponPackageType());
        couponGiveVO.setCouponPackageCode(hsaMemberCouponLink.getCouponPackageCode());
        couponGiveVO.setStoreGuid(hsaMemberCouponLink.getStoreGuid());
        couponGiveVO.setStoreName(hsaMemberCouponLink.getStoreName());
        couponGiveVO.setReachTime(hsaMemberCouponLink.getReachTime());
        couponGiveVO.setState(hsaMemberCouponLink.getState());
        couponGiveVO.setRemark(hsaMemberCouponLink.getRemark());
        //券规则
        final CouponGiveRuleVO ruleVO = toCouponGiveRuleVO(hsaMemberCouponLink);
        couponGiveVO.setRuleVO(ruleVO);
        return couponGiveVO;
    }

    /**
     * 规则
     *
     * @param hsaMemberCouponLink 券
     * @return 优惠券规则
     */
    public static CouponGiveRuleVO toCouponGiveRuleVO(HsaMemberCouponLink hsaMemberCouponLink) {
        CouponGiveRuleVO couponGiveRuleVO = new CouponGiveRuleVO();
        couponGiveRuleVO.setThresholdType(hsaMemberCouponLink.getThresholdType());
        couponGiveRuleVO.setDiscountAmountLimit(hsaMemberCouponLink.getDiscountAmountLimit());
        couponGiveRuleVO.setSingleOrderUsedLimit(hsaMemberCouponLink.getSingleOrderUsedLimit());
        couponGiveRuleVO.setShareRelation(hsaMemberCouponLink.getShareRelation());
        couponGiveRuleVO.setDiscountAmount(hsaMemberCouponLink.getDiscountAmount());
        couponGiveRuleVO.setThresholdAmount(hsaMemberCouponLink.getThresholdAmount());
        couponGiveRuleVO.setApplyDateLimited(hsaMemberCouponLink.getApplyDateLimited());
        couponGiveRuleVO.setApplyTimeLimitedType(hsaMemberCouponLink.getApplyTimeLimitedType());
        couponGiveRuleVO.setApplyTimeLimitedJson(hsaMemberCouponLink.getApplyTimeLimitedJson());
        couponGiveRuleVO.setApplyBusiness(hsaMemberCouponLink.getApplyBusiness());
        couponGiveRuleVO.setApplyTerminal(hsaMemberCouponLink.getApplyTerminal());
        //应用终端
        Optional.ofNullable(hsaMemberCouponLink.getApplyTerminalJson()).ifPresent(json ->
                couponGiveRuleVO.setApplyTerminalList(JSON.parseArray(json, String.class))
        );
        //应用业务
        Optional.ofNullable(hsaMemberCouponLink.getApplyBusinessJson()).ifPresent(json ->
                couponGiveRuleVO.setApplyBusinessList(JSON.parseArray(json, String.class))
        );
        couponGiveRuleVO.setApplyLabelGuidJson(hsaMemberCouponLink.getApplyLabelGuidJson());
        couponGiveRuleVO.setApplicableAllStore(hsaMemberCouponLink.getApplicableAllStore());
        couponGiveRuleVO.setApplicableAllStoreJson(hsaMemberCouponLink.getApplicableAllStoreJson());
        couponGiveRuleVO.setApplyCommodityJson(hsaMemberCouponLink.getApplyCommodityJson());
        couponGiveRuleVO.setApplyCommodity(hsaMemberCouponLink.getApplyCommodity());
        couponGiveRuleVO.setRemark(hsaMemberCouponLink.getRemark());
        couponGiveRuleVO.setExchangeLimit(hsaMemberCouponLink.getExchangeLimit());
        couponGiveRuleVO.setExchangeTimes(hsaMemberCouponLink.getExchangeTimes());
        return couponGiveRuleVO;
    }

    public static List<MemberCouponVO> toMemberCouponVOs(List<HsaMemberCouponLink> packageLinkList) {
        if (CollectionUtils.isEmpty(packageLinkList)) {
            return Collections.emptyList();
        }
        List<MemberCouponVO> memberCouponVOList = Lists.newArrayList();
        for (HsaMemberCouponLink hsaMemberCouponLink : packageLinkList) {
            memberCouponVOList.add(toMemberCouponVO(hsaMemberCouponLink));
        }
        return memberCouponVOList;
    }

    private static MemberCouponVO toMemberCouponVO(HsaMemberCouponLink hsaMemberCouponLink) {
        MemberCouponVO memberCouponVO = new MemberCouponVO();
        memberCouponVO.setGuid(hsaMemberCouponLink.getGuid());
        memberCouponVO.setCouponCode(hsaMemberCouponLink.getCouponCode());
        memberCouponVO.setCouponName(hsaMemberCouponLink.getCouponName());
        memberCouponVO.setCouponType(hsaMemberCouponLink.getCouponType());
        memberCouponVO.setCode(hsaMemberCouponLink.getCode());
        memberCouponVO.setState(hsaMemberCouponLink.getState());
        memberCouponVO.setReachTime(hsaMemberCouponLink.getReachTime());
        memberCouponVO.setCouponEffectiveStartTime(hsaMemberCouponLink.getCouponEffectiveStartTime());
        memberCouponVO.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveEndTime());
        memberCouponVO.setSource(hsaMemberCouponLink.getSource());
        memberCouponVO.setCouponPackageType(hsaMemberCouponLink.getCouponPackageType());
        memberCouponVO.setCouponPackageCode(hsaMemberCouponLink.getCouponPackageCode());
        memberCouponVO.setStoreGuid(hsaMemberCouponLink.getStoreGuid());
        memberCouponVO.setStoreName(hsaMemberCouponLink.getStoreName());
        //规则
        final CouponGiveRuleVO ruleVO = toCouponGiveRuleVO(hsaMemberCouponLink);
        memberCouponVO.setRuleVO(ruleVO);
        return memberCouponVO;
    }

    public static List<MemberCouponExportVO> toMemberCouponExportVOs(List<MemberCouponVO> couponLinks) {
        if (CollectionUtils.isEmpty(couponLinks)) {
            return Collections.emptyList();
        }
        List<MemberCouponExportVO> memberCouponVoList = Lists.newArrayList();
        for (MemberCouponVO couponVO : couponLinks) {
            fromMemberCouponExportVO(couponVO, memberCouponVoList);
        }
        return memberCouponVoList;

    }


    private static void fromMemberCouponExportVO(MemberCouponVO couponVO,
                                                 List<MemberCouponExportVO> memberCouponVoList) {
        MemberCouponExportVO memberCouponExportVO = toMemberCouponExportVO(couponVO);

        List<MemberCouponExportVO> couponExportVOS = Lists.newArrayList();
        if (CollUtil.isNotEmpty(couponVO.getMemberCouponUseBaseVOS())) {
            for (MemberCouponUseBaseVO memberCouponUseBaseVO : couponVO.getMemberCouponUseBaseVOS()) {
                MemberCouponExportVO entry = new MemberCouponExportVO();
                final String time = Optional.ofNullable(memberCouponUseBaseVO.getUseTime())
                        .map(DateUtil::toString).orElse("-");
                entry.setUseTime(time);
                entry.setUseStoreName(ExcelUtil.getOrDefault(memberCouponUseBaseVO.getUseStoreName()));
                entry.setOrderNumber(ExcelUtil.getOrDefault(memberCouponUseBaseVO.getOrderNumber()));
                couponExportVOS.add(entry);
            }
        }
        memberCouponVoList.add(memberCouponExportVO);
        if (CollUtil.isNotEmpty(couponExportVOS)) {
            memberCouponVoList.addAll(couponExportVOS);
        }
    }

    private static MemberCouponExportVO toMemberCouponExportVO(MemberCouponVO couponVO) {
        MemberCouponExportVO memberCouponExportVO = new MemberCouponExportVO();
        memberCouponExportVO.setCouponCode(couponVO.getCouponCode());
        memberCouponExportVO.setCouponName(couponVO.getCouponName());
        //券类型
        memberCouponExportVO.setCouponTypeTxt(CouponTypeEnum.getDesByCode(couponVO.getCouponType()));
        memberCouponExportVO.setCode(couponVO.getCode());
        //券状态
        memberCouponExportVO.setStateTxt(CouponMemberStateEnum.getDesByCode(couponVO.getState()));
        memberCouponExportVO.setReachTime(couponVO.getReachTime());
        //有效期：yyyy-mm-dd hh:mm:ss 至 yyyy-mm-dd hh:mm:ss
        memberCouponExportVO.setCouponEffectiveTime(
                DateUtil.toString(couponVO.getCouponEffectiveStartTime())
                        + "至" + DateUtil.toString(couponVO.getCouponEffectiveEndTime())
        );
        //来源
        memberCouponExportVO.setSourceTxt(SourceTypeEnum.getDesByCode(couponVO.getSource()));
        //场景描述，eg： 发券宝-定向发券（024498）
        String scene = CouponPackageTypeEnum.getSceneByCode(couponVO.getCouponPackageType());
        scene = StringUtil.isBlank(scene) ? scene : scene + "(" + couponVO.getCouponPackageCode() + ")";
        memberCouponExportVO.setCouponPackageTypeTxt(scene);
        memberCouponExportVO.setStoreName(ExcelUtil.getOrDefault(couponVO.getStoreName()));
        //时间
        final String useTime = Optional.ofNullable(couponVO.getUseTime())
                .map(DateUtil::toString).orElse("-");
        memberCouponExportVO.setUseTime(useTime);

        memberCouponExportVO.setUseStoreName(ExcelUtil.getOrDefault(couponVO.getUseStoreName()));
        memberCouponExportVO.setOrderNumber(ExcelUtil.getOrDefault(couponVO.getOrderNumber()));
        memberCouponExportVO.setUsageSituation(couponVO.getUsageSituation());

        memberCouponExportVO.setDiscountDynamic(showDiscountDynamics(couponVO.getCouponType(),couponVO.getRuleVO()));
        return memberCouponExportVO;
    }

    public static String showDiscountDynamics(Integer couponType, CouponGiveRuleVO ruleVO) {
        final CouponTypeEnum anEnum = CouponTypeEnum.getEnum(couponType);
        if (anEnum == null) {
            return "";
        }
        if (anEnum == CouponTypeEnum.COUPON_VOUCHER) {
            return getCouponVoucherDynamics(ruleVO);
        } else if (anEnum == CouponTypeEnum.COUPON_DISCOUNT) {
            return getCouponDiscountDynamics(ruleVO);
        } else if (anEnum == CouponTypeEnum.COUPON_EXCHANGE) {
            return getCouponExchangeDynamics(ruleVO);
        } else {
            return "";
        }
    }

    public static String getCouponVoucherDynamics(CouponGiveRuleVO ruleVO) {
        //代金券
        return ruleVO.getThresholdType() == 0 ?
                "无门槛减" + ruleVO.getDiscountAmount() + "元"
                : "满" + ruleVO.getThresholdAmount() + "减" + ruleVO.getDiscountAmount() + "元";
    }

    public static String getCouponDiscountDynamics(CouponGiveRuleVO ruleVO) {
        //折扣券
        return ruleVO.getThresholdType() == 0 ?
                "无门槛享" + ruleVO.getDiscountAmount() + "折"
                : "满" + ruleVO.getThresholdAmount() + "享" + ruleVO.getDiscountAmount() + "折";
    }

    public static String getCouponExchangeDynamics(CouponGiveRuleVO ruleVO) {
        //折扣券
        if (ruleVO.getThresholdType() == 0) {
            if (ruleVO.getExchangeLimit() == BooleanEnum.TRUE.getCode()) {
                return "无门槛可兑换" + ruleVO.getExchangeTimes() + "次";
            } else {
                return "无门槛可兑换不限次";
            }

        } else {
            if (ruleVO.getExchangeLimit() == BooleanEnum.TRUE.getCode()) {
                return "满" + ruleVO.getThresholdAmount() + "元可兑换" + ruleVO.getExchangeTimes() + "次";
            } else {
                return "满" + ruleVO.getThresholdAmount() + "元可兑换不限次";
            }
        }
    }


    public static List<MemberCouponWxVO> toMemberCouponWxVos(List<HsaMemberCouponLink> couponLinks, MemberCouponListQO qo) {
        if (CollectionUtils.isEmpty(couponLinks)) {
            return Collections.emptyList();
        }
        List<MemberCouponWxVO> couponWxVos = Lists.newArrayList();
        for (HsaMemberCouponLink hsaMemberCouponLink : couponLinks) {
            couponWxVos.add(toMemberCouponWxVO(hsaMemberCouponLink, qo));
        }
        return couponWxVos;
    }

    private static MemberCouponWxVO toMemberCouponWxVO(HsaMemberCouponLink hsaMemberCouponLink, MemberCouponListQO qo) {
        MemberCouponWxVO memberCouponWxVO = new MemberCouponWxVO();
        memberCouponWxVO.setId(hsaMemberCouponLink.getId());
        memberCouponWxVO.setGuid(hsaMemberCouponLink.getGuid());
        memberCouponWxVO.setApplyCommodity(hsaMemberCouponLink.getApplyCommodity());
        memberCouponWxVO.setCouponCode(hsaMemberCouponLink.getCouponCode());
        memberCouponWxVO.setState(hsaMemberCouponLink.getState());
        memberCouponWxVO.setCouponName(hsaMemberCouponLink.getCouponName());
        memberCouponWxVO.setShowState(hsaMemberCouponLink.getShowState());
        memberCouponWxVO.setCouponType(hsaMemberCouponLink.getCouponType());
        memberCouponWxVO.setReachTime(hsaMemberCouponLink.getReachTime());
        memberCouponWxVO.setCode(hsaMemberCouponLink.getCode());
        memberCouponWxVO.setCouponEffectiveStartTime(hsaMemberCouponLink.getCouponEffectiveStartTime());
        memberCouponWxVO.setCouponPackageCode(hsaMemberCouponLink.getCouponPackageCode());
        memberCouponWxVO.setCouponEffectiveEndTime(hsaMemberCouponLink.getCouponEffectiveEndTime());
        memberCouponWxVO.setCouponPackageType(hsaMemberCouponLink.getCouponPackageType());
        memberCouponWxVO.setSource(hsaMemberCouponLink.getSource());
        memberCouponWxVO.setStoreGuid(hsaMemberCouponLink.getStoreGuid());
        memberCouponWxVO.setStoreName(hsaMemberCouponLink.getStoreName());
        memberCouponWxVO.setThresholdType(hsaMemberCouponLink.getThresholdType());
        memberCouponWxVO.setThresholdAmount(hsaMemberCouponLink.getThresholdAmount());
        memberCouponWxVO.setDiscountAmount(hsaMemberCouponLink.getDiscountAmount());
        memberCouponWxVO.setDiscountAmountLimit(hsaMemberCouponLink.getDiscountAmountLimit());
        memberCouponWxVO.setSingleOrderUsedLimit(hsaMemberCouponLink.getSingleOrderUsedLimit());
        memberCouponWxVO.setShareRelation(hsaMemberCouponLink.getShareRelation());
        memberCouponWxVO.setExchangeLimit(hsaMemberCouponLink.getExchangeLimit());
        memberCouponWxVO.setExchangeTimes(hsaMemberCouponLink.getExchangeTimes());
        //用于排序: 发放时间、更新时间
        final LocalDateTime dateTime = qo.getUseType() == BooleanEnum.FALSE.getCode()
                ? hsaMemberCouponLink.getReachTime()
                : hsaMemberCouponLink.getGmtModified();
        memberCouponWxVO.setPageTime(dateTime);
        return memberCouponWxVO;
    }

    public static MemberCouponWxDetailVO toMemberCouponWxDetailVO(HsaMemberCouponLink detail) {
        MemberCouponWxDetailVO vo = new MemberCouponWxDetailVO();
        BeanUtils.copyProperties(detail, vo);
        vo.setRemark(detail.getRemark());
        //规则
        final CouponGiveRuleVO ruleVO = toCouponGiveRuleVO(detail);
        vo.setRuleVO(ruleVO);
        return vo;
    }
}
