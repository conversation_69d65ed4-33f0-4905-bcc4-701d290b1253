package com.holderzone.member.base.controller.settlement;

import com.beust.jcommander.internal.Lists;
import com.holderzone.member.base.service.settlement.SettlementCmdService;
import com.holderzone.member.base.service.settlement.SettlementQueryService;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.order.MemberOrderDiscountDTO;
import com.holderzone.member.common.entity.activity.ApplyRecordCommodity;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderCalculateDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyOrderVO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.specials.LimitSpecialsCommodityRecordQO;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 结算台：会员资产
 *
 * <AUTHOR>
 * @date 2023/10/16
 * @since 1.8
 */
@Slf4j
@RequestMapping("/member/discount")
@RestController
@RequiredArgsConstructor
public class SettlementController {
    /**
     * 结算规则查询
     */
    private final SettlementQueryService settlementQueryService;
    /**
     * 结算规则锁定
     */
    private final SettlementCmdService settlementCmdService;

    /**
     * 会员资产列表查询
     * <p>
     * 内部调用
     * 注意：这个地方构建 discountList 里面的SettlementApplyDiscountDetailVO 在vo里面有赋值
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/list")
    public List<SettlementApplyOrderVO> listDiscount(@RequestBody @Validated SettlementApplyOrderDTO dto) {
        return settlementQueryService.listDiscount(dto);
    }

    /**
     * 计算已选金额: 只算叠加项
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/calculate")
    public List<SettlementApplyOrderVO> calculateDiscount(@RequestBody @Validated SettlementApplyOrderCalculateDTO dto) {
        return settlementQueryService.calculateDiscount(dto);
    }

    /**
     * 下单后锁定优惠
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/locked")
    public Result<Boolean> lockedDiscount(@RequestBody @Validated SettlementOrderLockDTO dto) {
        return Result.success(settlementCmdService.lockedDiscount(dto));
    }

    /**
     * 取消订单、退款：释放优惠
     *
     * 食堂调用的是 ： /accumulation_discount_release_key
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/unLocked")
    public Result<Boolean> lockedDiscount(@RequestBody @Validated SettlementUnLockedDiscountDTO dto) {
        return Result.success(settlementCmdService.unLockedDiscount(dto));
    }

    /**
     * 获取限时特价 商品锁定记录
     */
    @PostMapping(value = "/getLimitSpecialsCommodityRecord")
    Map<String, List<ApplyRecordCommodity>> getLimitSpecialsCommodityRecord(@RequestBody LimitSpecialsCommodityRecordQO limitSpecialsCommodityRecordQO) {
        return settlementCmdService.getLimitSpecialsCommodityRecord(limitSpecialsCommodityRecordQO);
    }


    /**
     * 计算会员折扣 - 单独计算
     */
    @PostMapping(value = "/calculateMemberDiscount")
    public Result<SettlementApplyOrderVO> calculateMemberDiscount(@RequestBody SettlementApplyOrderDTO settlementApplyOrderDTO) {
        settlementApplyOrderDTO.setListOptions(Lists.newArrayList(SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode()));
        settlementApplyOrderDTO.setAutoCheck(BooleanEnum.FALSE.getCode());
        settlementApplyOrderDTO.setConfirmCheck(0);
        List<SettlementApplyOrderVO> settlementApplyOrderList = settlementQueryService.listDiscount(settlementApplyOrderDTO);
        log.info("calculateMemberDiscount:{}", settlementApplyOrderList);
        if (CollectionUtils.isEmpty(settlementApplyOrderList)) {
            return null;
        }
        return Result.success(settlementApplyOrderList.get(0));
    }

    /**
     * 查询订单优惠记录列表
     *
     * @param dto 订单锁定参数
     * @return 订单优惠记录列表
     */
    @PostMapping(value = "/listOrderDiscount")
    public Result<List<MemberOrderDiscountDTO>> listOrderDiscount(@RequestBody SettlementOrderLockDTO dto) {
        List<MemberOrderDiscountDTO> resultList = settlementQueryService.listOrderDiscount(dto);
        return Result.success(resultList);
    }
}
