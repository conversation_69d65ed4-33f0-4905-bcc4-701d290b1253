package com.holderzone.member.base.service.settlement.chain;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.dto.SettlementBackDiscountDTO;
import com.holderzone.member.base.dto.SettlementPayAfterDiscountDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.base.entity.member.HsaMemberOrderDiscount;
import com.holderzone.member.base.service.member.HsaMemberLabelService;
import com.holderzone.member.base.service.member.IHsaMemberOrderDiscountService;
import com.holderzone.member.base.service.member.feign.MemberFeignService;
import com.holderzone.member.base.service.settlement.assembler.SettlementLimitSpecialsAssembler;
import com.holderzone.member.base.support.LimitSpecialsSettleSupport;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.SettleApplyCommodityDTO;
import com.holderzone.member.common.entity.activity.ApplyRecordCommodity;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.module.settlement.apply.dto.*;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.activity.SynOrderDiscountQO;
import com.holderzone.member.common.qo.card.BarkOrderDiscountCallbackQO;
import com.holderzone.member.common.qo.card.OrderCommodityQO;
import com.holderzone.member.common.qo.member.AddMemberLabelCorrelationQO;
import com.holderzone.member.common.qo.member.UpdateLabelCorrelationStatusQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsActivityRunQO;
import com.holderzone.member.common.qo.specials.SpecialsActivityRecordQO;
import com.holderzone.member.common.vo.member.MemberLabelGradeVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRecordVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRunVO;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 限时特价活动优惠
 *
 * <AUTHOR>
 * @date 2024/09/26
 */
@Component
@Slf4j
@AllArgsConstructor
public class LimitSpecialsSettleHandler extends SettleHandler {

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private IHsaMemberOrderDiscountService hsaMemberOrderDiscountService;

    /**
     * 标签关联service
     */
    @Resource
    private HsaMemberLabelService hsaMemberLabelService;

    @Resource
    private MemberFeignService memberFeignService;

    @Resource
    private LimitSpecialsSettleSupport support;

    @Override
    public int option() {
        return SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode();
    }

    @Override
    public SettlementApplyOrderVO listDiscount(SettlementApplyOrderDTO dto) {
        SettlementApplyOrderInfoDTO orderInfo = dto.getOrderInfo();
        LimitSpecialsActivityRunQO limitSpecialsActivityRunQO = getLimitSpecialsActivityRunQO(dto, orderInfo);
        List<LimitSpecialsActivityRunVO> limitSpecialsActivityRunVOS = memberMarketingFeign.querySettleCommodityByRun(limitSpecialsActivityRunQO);

        if (CollUtil.isEmpty(limitSpecialsActivityRunVOS)) {
            return new SettlementApplyOrderVO();
        }

        return support.doApplyLimit(dto, limitSpecialsActivityRunVOS, orderInfo);
    }

    private LimitSpecialsActivityRunQO getLimitSpecialsActivityRunQO(SettlementApplyOrderDTO dto, SettlementApplyOrderInfoDTO orderInfo) {
        LimitSpecialsActivityRunQO limitSpecialsActivityRunQO = new LimitSpecialsActivityRunQO();
        List<String> discountGuidList = new ArrayList<>();
        limitSpecialsActivityRunQO.setStoreGuid(orderInfo.getStoreGuid());
        limitSpecialsActivityRunQO.setApplyBusiness(Integer.parseInt(orderInfo.getBusiness()));
        limitSpecialsActivityRunQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());

        if (StringUtils.isNotEmpty(orderInfo.getMemberInfoGuid())) {
            MemberLabelGradeVO memberLabelGradeVO = memberFeignService.getMemberLabelAndGradeInfo(orderInfo.getMemberInfoGuid());
            limitSpecialsActivityRunQO.setMemberLabelGuidList(memberLabelGradeVO.getMemberLabelList());
            limitSpecialsActivityRunQO.setMemberGradeGuid(memberLabelGradeVO.getMemberGradeInfoGuid());
            limitSpecialsActivityRunQO.setMemberInfoGuid(orderInfo.getMemberInfoGuid());
        }

        //商品列表处理
        List<SettleApplyCommodityDTO> settleApplyCommodityDTOS = SettlementLimitSpecialsAssembler.convertFromApplyCommodityDTOS(dto);
        limitSpecialsActivityRunQO.setSettleApplyCommodityDTOS(settleApplyCommodityDTOS);

        if (CollUtil.isNotEmpty(dto.getCheckDiscountList()) && dto.getIsAppointDiscount() == BooleanEnum.TRUE.getCode()) {
            discountGuidList.addAll(dto.getCheckDiscountList().stream().map(SettlementApplyDiscountBaseReqDTO::getDiscountGuid).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(dto.getAppendDiscountOptionGuid())){
            discountGuidList.addAll(dto.getAppendDiscountOptionGuid());
        }
        limitSpecialsActivityRunQO.setDiscountGuidList(discountGuidList.stream().distinct().collect(Collectors.toList()));

        return limitSpecialsActivityRunQO;
    }


    @Override
    public SettlementApplyOrderVO calculateDiscount(SettleHandlerCalculateChain chain) {
        //选中
        final String checkDiscountGuid = chain.getReq().getCheckDiscountList().stream()
                .filter(c -> SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode() == c.getDiscountOption())
                .map(SettlementApplyDiscountBaseReqDTO::getDiscountGuid)
                .findFirst().orElse("");
        if (StringUtil.isBlank(checkDiscountGuid)) {
            return null;
        }

        log.info("选中了限时特价折扣:{}", checkDiscountGuid);
        SettlementApplyOrderDTO settlementApplyOrderDTO = chain.getReq();
        settlementApplyOrderDTO.setIsAppointDiscount(BooleanEnum.TRUE.getCode());
        return listDiscount(settlementApplyOrderDTO);
    }

    @Override
    public void lockDiscount(SettlementOrderLockDTO lockedDiscount) {
        log.info("限时特价折扣锁定");
    }

    @Override
    public void unlockDiscount(SettlementUnLockedDiscountDTO discountDTO) {
        log.info("限时特价折扣解锁");
    }

    /**
     * 支付后操作
     */
    @Override
    public void afterPayDiscount(SettlementPayAfterDiscountDTO discountDTO) {
        List<SynOrderDiscountQO> synOrderDiscountQOS = Lists.newArrayList();
        List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts = discountDTO.getOrderDiscountByOptionMap()
                .get(SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode());
        for (HsaMemberOrderDiscount hsaMemberOrderDiscount : hsaMemberOrderDiscounts) {
            SynOrderDiscountQO synOrderDiscountQO = new SynOrderDiscountQO();
            BeanUtils.copyProperties(discountDTO, synOrderDiscountQO);
            synOrderDiscountQO
                    .setDiscountGuid(hsaMemberOrderDiscount.getDiscountGuid())
                    .setMemberGuid(hsaMemberOrderDiscount.getMemberGuid())
                    .setCouponRollback(hsaMemberOrderDiscount.getCouponRollback());

            synOrderDiscountQOS.add(synOrderDiscountQO);
        }
        memberMarketingFeign.saveRecord(synOrderDiscountQOS);

        // 打标签
        markLabel(discountDTO, hsaMemberOrderDiscounts);
    }

    /**
     * 批量打标签
     */
    private void markLabel(SettlementPayAfterDiscountDTO discountDTO,
                           List<HsaMemberOrderDiscount> hsaMemberOrderDiscounts) {
        AddMemberLabelCorrelationQO memberLabelQO = new AddMemberLabelCorrelationQO();
        List<String> discountGuidList = hsaMemberOrderDiscounts.stream()
                .map(HsaMemberOrderDiscount::getDiscountGuid)
                .distinct()
                .collect(Collectors.toList());
        LimitSpecialsActivityRunQO activityRunQO = new LimitSpecialsActivityRunQO();
        activityRunQO.setOperSubjectGuid(discountDTO.getOperSubjectGuid());
        activityRunQO.setDiscountGuidList(discountGuidList);
        List<LimitSpecialsActivityRunVO> limitSpecialsActivityRunVOList = memberMarketingFeign.queryRunInfo(activityRunQO);
        if (CollectionUtils.isEmpty(limitSpecialsActivityRunVOList)) {
            log.warn("没有限时特价活动");
            return;
        }
        List<String> labelGuidList = limitSpecialsActivityRunVOList.stream()
                .filter(a -> !CollectionUtils.isEmpty(a.getLabelGuidList()))
                .flatMap(a -> a.getLabelGuidList().stream())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(labelGuidList)) {
            log.warn("活动无需打标签");
            return;
        }
        memberLabelQO.setLabelGuid(labelGuidList);
        List<String> operationMemberInfoGuidList = hsaMemberOrderDiscounts.stream()
                .map(HsaMemberOrderDiscount::getMemberGuid)
                .distinct()
                .collect(Collectors.toList());
        memberLabelQO.setMemberInfoGuid(operationMemberInfoGuidList);
        String batchId = String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        memberLabelQO.setBatchId(batchId);
        log.info("[批量打标签]memberLabelQO={}", JacksonUtils.writeValueAsString(memberLabelQO));
        hsaMemberLabelService.addMemberInfoLabel(memberLabelQO);
    }

    /**
     * 回退优惠
     */
    @Override
    public void backDiscount(SettlementBackDiscountDTO discountDTO) {
        log.info("限时特价折扣回退：{}", JSON.toJSONString(discountDTO));
        List<HsaMemberOrderDiscount> discountList = discountDTO.getOrderDiscountByOptionMap()
                .get(SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode());

        //过滤可退款的优惠
        discountList = discountList.stream().filter(in -> in.getDiscountState() == BooleanEnum.FALSE.getCode()
                        && in.getCouponRollback() == BooleanEnum.TRUE.getCode())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(discountList)) {
            log.info("订单：{}，没有满足可退款的限时特价优惠记录，流程终止", discountDTO.getOrderNo());
            return;
        }

        //整单退则无需处理商品优惠
        if (discountDTO.getRefundType() == BooleanEnum.TRUE.getCode()) {
            discountList.forEach(in -> {
                in.setDiscountState(BooleanEnum.TRUE.getCode());
                in.setCommodityJson(null);
                hsaMemberOrderDiscountService.updateByGuid(in);
            });

            // 整单退不处理标签了 -- by.需求
        } else {
            List<OrderCommodityQO> orderCommodityQOList = discountDTO.getOrderCommodityQOList();
            if (!CollUtil.isEmpty(orderCommodityQOList)) {
                log.info("订单：{}，无商品信息，不处理更新商品优惠信息", discountDTO.getOrderNo());
                //处理部分退商品数量
                dealOrderCommodityNum(orderCommodityQOList, discountList);
            }
        }
        //修改统计记录
        updateRecord(discountDTO);
    }

    /**
     * 取消标签
     */
    private void cancelMarkLabel(SettlementBackDiscountDTO discountDTO,
                                 List<HsaMemberOrderDiscount> discountList) {
        List<String> activityCodeList = discountList.stream()
                .map(HsaMemberOrderDiscount::getDiscountGuid)
                .distinct()
                .collect(Collectors.toList());

        // 查询限时特价
        LimitSpecialsActivityRunQO activityRunQO = new LimitSpecialsActivityRunQO();
        activityRunQO.setOperSubjectGuid(discountDTO.getOperSubjectGuid());
        activityRunQO.setDiscountGuidList(activityCodeList);
        List<LimitSpecialsActivityRunVO> limitSpecialsActivityRunVOList = memberMarketingFeign.queryRunInfo(activityRunQO);
        if (CollectionUtils.isEmpty(limitSpecialsActivityRunVOList)) {
            log.warn("没有限时特价活动");
            return;
        }
        Map<String, LimitSpecialsActivityRunVO> activityRunVOMap = limitSpecialsActivityRunVOList.stream()
                .collect(Collectors.toMap(LimitSpecialsActivityRunVO::getActivityCode, Function.identity(), (v1, v2) -> v1));

        // 查询限时特价记录
        SpecialsActivityRecordQO activityRecordQO = new SpecialsActivityRecordQO();
        activityRecordQO.setActivityCodeList(activityCodeList);
        String memberGuid = discountList.get(0).getMemberGuid();
        activityRecordQO.setMemberGuid(memberGuid);
        log.info("[查询限时特价记录][参数]activityRecordQO={}", JacksonUtils.writeValueAsString(activityRecordQO));
        List<LimitSpecialsActivityRecordVO> activityRecordVOList = memberMarketingFeign.queryRecord(activityRecordQO);
        log.info("[查询限时特价记录][返回]activityRecordVOList={}", JacksonUtils.writeValueAsString(activityRecordVOList));
        Map<String, List<LimitSpecialsActivityRecordVO>> activityRecordVOGroupMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(activityRecordVOList)) {
            activityRecordVOList.removeIf(ar -> ar.getOrderNo().equals(discountDTO.getOrderNo()));
            if (!CollectionUtils.isEmpty(activityRecordVOList)) {
                activityRecordVOGroupMap = activityRecordVOList.stream()
                        .collect(Collectors.groupingBy(LimitSpecialsActivityRecordVO::getActivityCode));
            }
        }

        List<String> labelGuid = new ArrayList<>();
        for (HsaMemberOrderDiscount discount : discountList) {
            LimitSpecialsActivityRunVO activityRunVO = activityRunVOMap.get(discount.getDiscountGuid());
            if (ObjectUtils.isEmpty(activityRunVO)) {
                continue;
            }
            List<LimitSpecialsActivityRecordVO> recordVOList = activityRecordVOGroupMap.get(discount.getDiscountGuid());
            if (CollectionUtils.isEmpty(recordVOList) && !CollectionUtils.isEmpty(activityRunVO.getLabelGuidList())) {
                labelGuid.addAll(activityRunVO.getLabelGuidList());
            }
        }
        log.info("[需要取消的标签]labelGuid={}", labelGuid);
        if (CollectionUtils.isEmpty(labelGuid)) {
            return;
        }

        // 取消标签
        UpdateLabelCorrelationStatusQO statusQo = new UpdateLabelCorrelationStatusQO();
        statusQo.setLabelGuid(labelGuid);
        statusQo.setMemberGuid(memberGuid);
        log.info("[取消标签]statusQo={}", JacksonUtils.writeValueAsString(statusQo));
        hsaMemberLabelService.autoCancelConnection(statusQo);
    }

    private void updateRecord(SettlementBackDiscountDTO discountDTO) {
        BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO = new BarkOrderDiscountCallbackQO();
        BeanUtils.copyProperties(discountDTO, barkOrderDiscountCallbackQO);
        memberMarketingFeign.updateRecord(barkOrderDiscountCallbackQO);
    }

    private void dealOrderCommodityNum(List<OrderCommodityQO> orderCommodityQOList, List<HsaMemberOrderDiscount> discountList) {
        Map<String, OrderCommodityQO> orderCommodityQOMap = getOrderCommodityQOMap(orderCommodityQOList);

        for (HsaMemberOrderDiscount hsaMemberOrderDiscount : discountList) {
            if (!com.holderzone.framework.util.StringUtils.isEmpty(hsaMemberOrderDiscount.getCommodityJson())) {
                List<ApplyRecordCommodity> limitSpecialsRecordCommodities = JSON.parseArray(hsaMemberOrderDiscount.getCommodityJson(), ApplyRecordCommodity.class);
                List<ApplyRecordCommodity> specialsRecordCommodities = Lists.newArrayList();

                //更新限时特价活动锁定商品数据
                updateSpecialsRecordCommodities(limitSpecialsRecordCommodities, orderCommodityQOMap, specialsRecordCommodities);

                if (CollUtil.isNotEmpty(specialsRecordCommodities)) {
                    hsaMemberOrderDiscount.setCommodityJson(JSON.toJSONString(specialsRecordCommodities));
                } else {
                    hsaMemberOrderDiscount.setCommodityJson(null);
                    hsaMemberOrderDiscount.setDiscountState(BooleanEnum.TRUE.getCode());
                }
            }
            //修改商品数据
            hsaMemberOrderDiscountService.updateByGuid(hsaMemberOrderDiscount);
        }
    }

    private static Map<String, OrderCommodityQO> getOrderCommodityQOMap(List<OrderCommodityQO> orderCommodityQOList) {
        Map<String, OrderCommodityQO> orderCommodityQOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orderCommodityQOList)) {
            orderCommodityQOMap = orderCommodityQOList.stream().collect(Collectors.
                    toMap(OrderCommodityQO::getCommodityCode, Function.identity(), (entity1, entity2) -> entity1));


        }
        return orderCommodityQOMap;
    }

    private static void updateSpecialsRecordCommodities(List<ApplyRecordCommodity> limitSpecialsRecordCommodities,
                                                        Map<String, OrderCommodityQO> orderCommodityQOMap,
                                                        List<ApplyRecordCommodity> specialsRecordCommodities) {
        for (ApplyRecordCommodity applyRecordCommodity : limitSpecialsRecordCommodities) {
            OrderCommodityQO orderCommodityQO = orderCommodityQOMap.get(applyRecordCommodity.getCommodityCode());

            if (Objects.nonNull(orderCommodityQO)) {
                applyRecordCommodity.setTotalNumber(applyRecordCommodity.getTotalNumber()
                        .subtract(orderCommodityQO.getCommodityNum()));
            }

            if (applyRecordCommodity.getTotalNumber().compareTo(BigDecimal.ZERO) > 0) {
                specialsRecordCommodities.add(applyRecordCommodity);
            }
        }
    }
}
