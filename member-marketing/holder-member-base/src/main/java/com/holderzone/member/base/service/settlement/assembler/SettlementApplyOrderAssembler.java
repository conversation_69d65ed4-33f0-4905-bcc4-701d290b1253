package com.holderzone.member.base.service.settlement.assembler;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.member.base.dto.CardEquitiesListDTO;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.ApplyDictionariesEnum;
import com.holderzone.member.common.enums.equities.DiscountTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyCommodityDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyDiscountBaseReqDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderInfoDTO;
import com.holderzone.member.common.module.settlement.apply.vo.*;
import com.holderzone.member.common.module.settlement.apply.vo.detail.SettlementApplyDiscountDetailOfCouponVO;
import com.holderzone.member.common.module.settlement.apply.vo.detail.SettlementApplyDiscountDetailOfDiscountVO;
import com.holderzone.member.common.module.settlement.apply.vo.detail.SettlementApplyDiscountDetailOfIntegralVO;
import com.holderzone.member.common.module.settlement.constant.SettlementConstant;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.equities.CalculateMemberPriceCommodityQO;
import com.holderzone.member.common.qo.equities.MemberPriceCommodityQO;
import com.holderzone.member.common.qo.integral.CalculateIntegralDeductQO;
import com.holderzone.member.common.vo.equities.CalculateMemberPriceCommodityVO;
import com.holderzone.member.common.vo.equities.MembershipBenefitsVO;
import com.holderzone.member.common.vo.integral.CalculateOrderIntegralDeductVO;
import com.holderzone.member.common.vo.integral.MemberIntegralDeductVO;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 结算台 dto转 do
 *
 * <AUTHOR>
 * @date 2023/10/17
 * @since 1.8
 */
public class SettlementApplyOrderAssembler {

    private SettlementApplyOrderAssembler() {
        //default
    }


    /**
     * 转换为 会员折扣 qo
     *
     * @param dto 入参
     * @return 会员折扣查询对象
     */
    public static CalculateMemberPriceCommodityQO toCalculateMemberPriceCommodityQo(SettlementApplyOrderDTO dto) {
        final SettlementApplyOrderInfoDTO orderInfo = dto.getOrderInfo();

        // 构建会员折扣查询对象
        CalculateMemberPriceCommodityQO memberPriceQuery = new CalculateMemberPriceCommodityQO();

        // 设置会员和订单基本信息
        memberPriceQuery.setMemberInfoGuid(orderInfo.getMemberInfoGuid());
        memberPriceQuery.setTerminal(Optional.ofNullable(orderInfo.getTerminal()).orElse(""));
        memberPriceQuery.setBusiness(orderInfo.getBusiness());
        memberPriceQuery.setChannel(orderInfo.getChannel());
        memberPriceQuery.setCommodityTotalAmount(orderInfo.getCommodityTotalAmount());

        // 设置订单号信息
        memberPriceQuery.setOrderNum(orderInfo.getOrderNumber());
        memberPriceQuery.setOrderNumRedundant(orderInfo.getOrderNumber());

        // 默认不锁定优惠，锁定操作由单独方法处理
        memberPriceQuery.setIsOfferLocked(BooleanEnum.FALSE.getCode());

        memberPriceQuery.setStoreGuidList(orderInfo.getStoreGuidList());

        // 转换并设置订单商品列表
        List<MemberPriceCommodityQO> memberPriceCommodityQOS = toMemberPriceCommodityQoList(dto.getOrderCommodityList());
        if (CollUtil.isNotEmpty(memberPriceCommodityQOS)) {
            memberPriceCommodityQOS.removeIf(in -> Objects.nonNull(in.getAfterDiscountTotalPrice()) && in.getAfterDiscountTotalPrice().compareTo(BigDecimal.ZERO) <= 0);
            memberPriceQuery.setMemberPriceCommodityQOS(memberPriceCommodityQOS);
        }
        return memberPriceQuery;
    }

    /**
     * 转换商品对象list
     *
     * @param orderCommodityList 订单商品list
     * @return 优惠商品
     */
    public static List<MemberPriceCommodityQO> toMemberPriceCommodityQoList(List<SettlementApplyCommodityDTO> orderCommodityList) {
        List<MemberPriceCommodityQO> commodityQoList = Lists.newArrayList();
        for (SettlementApplyCommodityDTO settlementApplyCommodityDTO : orderCommodityList) {
            commodityQoList.add(toMemberPriceCommodityQo(settlementApplyCommodityDTO));
        }
        return commodityQoList;

    }

    /**
     * 将结算申请商品DTO转换为会员价格商品查询对象
     *
     * @param commodityDTO 结算申请商品DTO
     * @return 会员价格商品查询对象
     */
    private static MemberPriceCommodityQO toMemberPriceCommodityQo(SettlementApplyCommodityDTO commodityDTO) {
        if (Objects.isNull(commodityDTO)) {
            return null;
        }

        MemberPriceCommodityQO priceQuery = new MemberPriceCommodityQO();

        // 设置商品基本信息
        priceQuery.setRid(commodityDTO.getRid());
        priceQuery.setCommodityId(commodityDTO.getCommodityId());
        priceQuery.setCommodityName(commodityDTO.getCommodityName());
        priceQuery.setCommodityCode(commodityDTO.getCommodityCode());
        priceQuery.setGoodsType(commodityDTO.getGoodsType());
        priceQuery.setSkuId(commodityDTO.getSkuId());
        // 设置商品数量和价格信息
        priceQuery.setCommodityNum(commodityDTO.getCommodityNum());
        priceQuery.setCommodityPrice(determineCommodityPrice(commodityDTO));

        // 设置优惠相关信息
        priceQuery.setCommodityMinistryPrice(commodityDTO.getDiscountFee());
        priceQuery.setDiscountTotalPriceInShopCar(commodityDTO.getDiscountTotalPriceInShopCar());
        priceQuery.setAfterDiscountTotalPrice(commodityDTO.getAfterDiscountTotalPrice());
        priceQuery.setStoreGuid(commodityDTO.getStoreGuid());
        return priceQuery;
    }

    /**
     * 确定商品实际价格
     * 优先使用购物车中的折扣价，如果没有则使用商品原价
     */
    private static BigDecimal determineCommodityPrice(SettlementApplyCommodityDTO commodityDTO) {
        return Optional.ofNullable(commodityDTO.getAfterDiscountTotalPrice())
                .orElse(commodityDTO.getDiscountTotalPriceInShopCar());
    }

    /**
     * 转换会员卡折扣订单对象
     *
     * @param commodityTotalAmount 商品总金额
     * @param equitiesDto 会员卡权益信息
     * @param commodityVO 会员价格商品计算结果
     * @return 结算申请订单对象，如果没有折扣金额则返回null
     */
    public static SettlementApplyOrderVO fromCalculateOrderCardDiscountVO(
            BigDecimal commodityTotalAmount,
            CardEquitiesListDTO equitiesDto,
            CalculateMemberPriceCommodityVO commodityVO) {

        // 验证入参和折扣金额
        if (Objects.isNull(commodityVO) || Objects.isNull(commodityVO.getDiscountDynamicsAmount())) {
            return null;
        }

        // 构建基础订单对象
        SettlementApplyOrderVO orderVO = buildCardDiscountOrderVO(commodityVO, equitiesDto);

        // 检查余额并更新折扣可用状态
        updateDiscountAvailability(orderVO, equitiesDto, calculateRemainingAmount(
                commodityTotalAmount,
                orderVO.getCommodityList()
        ));

        return orderVO;
    }

    /**
     * 构建会员卡折扣订单对象
     */
    private static SettlementApplyOrderVO buildCardDiscountOrderVO(
            CalculateMemberPriceCommodityVO commodityVO,
            CardEquitiesListDTO equitiesDto) {

        SettlementApplyOrderVO orderVO = new SettlementApplyOrderVO();

        // 设置订单基础信息
        orderVO.setOderInfo(buildOrderInfo(commodityVO.getDiscountDynamicsAmount()));

        // 设置订单商品列表
        List<SettlementApplyCommodityVO> commodityList =
                fromMemberPriceCommodityQoList(commodityVO.getMemberPriceCommodityQOS());
        orderVO.setCommodityList(commodityList);

        // 设置订单折扣信息
        orderVO.setDiscountList(
                fromMembershipBenefitsVoOfDiscountList(
                        commodityVO,
                        SettlementDiscountOptionEnum.MEMBER_CARD_DISCOUNT.getCode(),
                        equitiesDto
                )
        );

        return orderVO;
    }

    /**
     * 计算剩余需支付金额
     */
    private static BigDecimal calculateRemainingAmount(
            BigDecimal totalAmount,
            List<SettlementApplyCommodityVO> commodityList) {

        if (CollUtil.isEmpty(commodityList)) {
            return totalAmount;
        }

        BigDecimal totalDiscountFee = commodityList.stream()
                .map(SettlementApplyCommodityVO::getDiscountFee)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        return totalAmount.subtract(totalDiscountFee);
    }

    /**
     * 更新折扣可用状态
     */
    private static void updateDiscountAvailability(
            SettlementApplyOrderVO orderVO,
            CardEquitiesListDTO equitiesDto,
            BigDecimal remainingAmount) {

        // 检查会员卡余额是否足够支付
        boolean isDiscountDisabled = isCardBalanceInsufficient(equitiesDto, remainingAmount);

        // 如果余额不足，将所有折扣项设置为不可用
        if (isDiscountDisabled && CollUtil.isNotEmpty(orderVO.getDiscountList())) {
            orderVO.getDiscountList().get(0).getDiscountList()
                    .forEach(discount -> discount.setIsEnabled(BooleanEnum.FALSE.getCode()));
        }
    }

    /**
     * 检查会员卡余额是否不足
     */
    private static boolean isCardBalanceInsufficient(
            CardEquitiesListDTO equitiesDto,
            BigDecimal remainingAmount) {
        BigDecimal accountBalance = equitiesDto.getAccountMoney();
        return accountBalance.compareTo(BigDecimal.ZERO) <= 0
                || accountBalance.compareTo(remainingAmount) < 0;
    }


    /**
     * 转换会员等级折扣订单对象
     *
     * @param priceCommodityVO 会员价格商品计算结果
     * @return 结算申请订单对象，如果没有折扣金额则返回null
     */
    public static SettlementApplyOrderVO fromCalculateOrderGradeDiscountVO(CalculateMemberPriceCommodityVO priceCommodityVO) {
        // 验证折扣金额是否存在
        if (Objects.isNull(priceCommodityVO) || Objects.isNull(priceCommodityVO.getDiscountDynamicsAmount())) {
            return null;
        }

        return buildGradeDiscountOrderVO(priceCommodityVO);
    }

    /**
     * 构建会员等级折扣订单对象
     *
     * @param priceCommodityVO 会员价格商品计算结果
     * @return 结算申请订单对象
     */
    private static SettlementApplyOrderVO buildGradeDiscountOrderVO(CalculateMemberPriceCommodityVO priceCommodityVO) {
        SettlementApplyOrderVO orderVO = new SettlementApplyOrderVO();

        // 设置订单基础信息
        orderVO.setOderInfo(buildOrderInfo(priceCommodityVO.getDiscountDynamicsAmount()));

        // 设置订单商品列表
        orderVO.setCommodityList(
                fromMemberPriceCommodityQoList(priceCommodityVO.getMemberPriceCommodityQOS())
        );

        // 设置订单折扣信息
        orderVO.setDiscountList(
                fromMembershipBenefitsVoOfDiscountList(
                        priceCommodityVO,
                        SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode(),
                        null
                )
        );

        return orderVO;
    }

    /**
     * 构建订单基础信息对象
     *
     * @param discountAmount 折扣金额
     * @return 订单基础信息对象
     */
    private static SettlementApplyOderInfoVO buildOrderInfo(BigDecimal discountAmount) {
        return new SettlementApplyOderInfoVO()
                .setDiscountAmount(discountAmount);
    }

    /**
     * 转换会员折扣优惠列表
     *
     * @param option 折扣类型：0-等级折扣，1-会员卡折扣
     * @param equitiesDto 会员卡权益信息
     * @return 折扣优惠列表
     */
    public static List<SettlementApplyDiscountVO> fromMembershipBenefitsVoOfDiscountList(
            CalculateMemberPriceCommodityVO commodityVO,
            int option,
            CardEquitiesListDTO equitiesDto) {

        if (CollUtil.isEmpty(commodityVO.getMembershipBenefitsVOS())) {
            return Lists.newArrayList();
        }

        // 获取第一个权益用于创建主折扣对象
        final MembershipBenefitsVO firstBenefit = commodityVO.getMembershipBenefitsVOS().get(0);

        // 创建主折扣对象
        SettlementApplyDiscountVO mainDiscountVO = createMainDiscountVO(firstBenefit, option);

        // 获取显示名称和卡号
        DiscountDisplayInfo displayInfo = getDiscountDisplayInfo(option, equitiesDto);

        // 转换折扣详情列表
        List<SettlementApplyDiscountDetailVO> discountDetails = commodityVO.getMembershipBenefitsVOS().stream()
                .map(benefitsVO -> createDiscountDetail(
                        commodityVO,
                        benefitsVO,
                        mainDiscountVO,
                        displayInfo.getDisplayName(),
                        displayInfo.getCardGuid()))
                .collect(Collectors.toList());

        // 设置折扣详情并返回结果
        mainDiscountVO.setDiscountList(discountDetails);
        return Lists.newArrayList(mainDiscountVO);
    }

    /**
     * 创建主折扣对象
     */
    private static SettlementApplyDiscountVO createMainDiscountVO(MembershipBenefitsVO benefitsVO, int option) {
        SettlementApplyDiscountVO discountVO = SettlementApplyDiscountVO.build(option);
        discountVO.setDiscountGuid(benefitsVO.getEquitiesGuid());
        return discountVO;
    }

    /**
     * 获取折扣显示信息
     */
    private static DiscountDisplayInfo getDiscountDisplayInfo(int option, CardEquitiesListDTO equitiesDto) {
        String displayName = Optional.ofNullable(equitiesDto)
                .map(CardEquitiesListDTO::getCardName)
                .orElse(SettlementConstant.GRADE_DISCOUNT);

        String cardGuid = Optional.ofNullable(equitiesDto)
                .map(CardEquitiesListDTO::getOwnGuid)
                .orElse(null);

        // 如果是会员等级折扣，强制使用等级折扣名称
        if (option == SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode()) {
            displayName = SettlementConstant.GRADE_DISCOUNT;
        }

        return new DiscountDisplayInfo(displayName, cardGuid);
    }

    /**
     * 创建折扣详情对象
     */
    private static SettlementApplyDiscountDetailVO createDiscountDetail(
            CalculateMemberPriceCommodityVO commodityVO,
            MembershipBenefitsVO benefitsVO,
            SettlementApplyDiscountVO mainDiscount,
            String displayName,
            String cardGuid) {

        SettlementApplyDiscountDetailVO detailVO = fromMembershipBenefitsVoByMemberDiscount(benefitsVO, cardGuid);

        // 设置折扣关联信息
        detailVO.setDiscountOption(mainDiscount.getDiscountOption());
        detailVO.setDiscountGuid(mainDiscount.getDiscountGuid());
        detailVO.setDiscountName(displayName);

        detailVO.setCommodityList(
                fromMemberPriceCommodityQoList(commodityVO.getMemberPriceCommodityQOS())
        );
        return detailVO;
    }

    /**
     * 折扣显示信息内部类
     */
    @Getter
    private static class DiscountDisplayInfo {
        private final String displayName;
        private final String cardGuid;

        public DiscountDisplayInfo(String displayName, String cardGuid) {
            this.displayName = displayName;
            this.cardGuid = cardGuid;
        }

    }

    /**
     * 将会员权益对象转换为折扣优惠详情对象
     *
     * @param benefitsVO 会员权益对象
     * @param memberCardGuid 会员卡标识
     * @return 折扣优惠详情对象
     */
    private static SettlementApplyDiscountDetailOfDiscountVO fromMembershipBenefitsVoByMemberDiscount(
            MembershipBenefitsVO benefitsVO,
            String memberCardGuid) {

        if (Objects.isNull(benefitsVO)) {
            return null;
        }

        SettlementApplyDiscountDetailOfDiscountVO discountDetail = new SettlementApplyDiscountDetailOfDiscountVO();

        // 设置基本折扣信息
        // 折扣金额和会员卡信息
        discountDetail.setDiscountAmount(benefitsVO.getDiscountDynamicsAmount());
        discountDetail.setMemberCardGuid(memberCardGuid);  // 会员卡标识
        discountDetail.setDiscountOptionId(benefitsVO.getEquitiesId());  // 权益ID

        // 设置动态折扣信息
        // 动态折扣比例，如果为空则默认为0
        discountDetail.setDiscountDynamics(
                Optional.ofNullable(benefitsVO.getDiscountDynamics())
                        .orElse(BigDecimal.ZERO)
        );

        // 设置单次折扣限制
        // 是否限制单次折扣金额
        discountDetail.setSingleDiscountsLimited(benefitsVO.getSingleDiscountsLimited());
        // 单次折扣限制金额
        discountDetail.setSingleDiscountsLimitedAmount(benefitsVO.getSingleDiscountsLimitedAmount());

        // 设置会员价格限制
        // 是否限制会员价
        discountDetail.setMemberPriceLimited(benefitsVO.getMemberPriceLimited());
        // 周期内折扣限制
        discountDetail.setPeriodDiscountLimited(benefitsVO.getPeriodDiscountLimited());
        // 当月最低消费金额
        discountDetail.setMinimumThisMonthAmount(benefitsVO.getMinimumThisMonthAmount());

        // 设置总折扣限制
        // 是否限制总折扣
        discountDetail.setTotalDiscountLimited(benefitsVO.getTotalDiscountLimited());
        // 总折扣限制金额
        discountDetail.setTotalDiscountLimitedAmount(benefitsVO.getTotalDiscountLimitedAmount());

        // 设置权益时间限制类型
        discountDetail.setEquitiesTimeLimitedType(benefitsVO.getEquitiesTimeLimitedType());

        return discountDetail;
    }

    /**
     * 转换积分优惠对象
     *
     * @param benefitsVO 积分计算结果
     * @return 积分优惠对象
     */
    private static SettlementApplyDiscountDetailOfIntegralVO fromMembershipBenefitsVoByIntegral(MemberIntegralDeductVO benefitsVO,
                                                                                                SettlementApplyOrderDTO dto) {
        SettlementApplyDiscountDetailOfIntegralVO integralDiscountDetail = new SettlementApplyDiscountDetailOfIntegralVO();

        // 设置基本折扣信息
        integralDiscountDetail.setDiscountAmount(benefitsVO.getIntegralDiscountAmount());
        integralDiscountDetail.setCycleResidueAmount(benefitsVO.getCycleResidueAmount());
        integralDiscountDetail.setEquitiesTimeLimitedType(benefitsVO.getEquitiesTimeLimitedType());
        integralDiscountDetail.setPeriodDiscountLimited(benefitsVO.getPeriodDiscountLimited());
        integralDiscountDetail.setDiscountOptionId(benefitsVO.getVersionId());
        integralDiscountDetail.setDiscountName(benefitsVO.getDeductName());

        // 设置积分相关信息
        integralDiscountDetail.setUsedIntegralNum(benefitsVO.getIntegralNum());
        integralDiscountDetail.setDisForNowMoney(benefitsVO.getDisForNowMoney());
        integralDiscountDetail.setDisIntegralNum(benefitsVO.getDisIntegralNum());

        // 非自动勾选模式下设置最优选项
        if (dto.getAutoCheck() == BooleanEnum.FALSE.getCode()) {
            integralDiscountDetail.setOptimal(benefitsVO.getIsOptimal());
        }

        return integralDiscountDetail;
    }

    /**
     * 转换会员折扣商品对象list
     *
     * @param memberPriceCommodityQos 结算结果商品list
     * @return 优惠商品list
     */
    public static List<SettlementApplyCommodityVO> fromMemberPriceCommodityQoList(List<MemberPriceCommodityQO> memberPriceCommodityQos) {
        if (CollUtil.isEmpty(memberPriceCommodityQos)) {
            return Collections.emptyList();
        }
        List<SettlementApplyCommodityVO> settlementApplyCommodityVolist = Lists.newArrayList();
        for (MemberPriceCommodityQO memberPriceCommodityQo : memberPriceCommodityQos) {
            settlementApplyCommodityVolist.add(fromMemberPriceCommodityQo(memberPriceCommodityQo));
        }
        return settlementApplyCommodityVolist;
    }

    /**
     * 转换会员折扣商品对象
     *
     * @param memberPriceCommodityQo 结算结果商品
     * @return 优惠商品list
     */
    private static SettlementApplyCommodityVO fromMemberPriceCommodityQo(MemberPriceCommodityQO memberPriceCommodityQo) {
        SettlementApplyCommodityVO settlementApplyCommodityVO = new SettlementApplyCommodityVO();

        settlementApplyCommodityVO.setRid(memberPriceCommodityQo.getRid());
        //商品id
        settlementApplyCommodityVO.setCommodityId(memberPriceCommodityQo.getCommodityId());
        settlementApplyCommodityVO.setDiscountFee(memberPriceCommodityQo.getCommodityMinistryPrice());

        //分摊金额
        settlementApplyCommodityVO.setShareDiscountFee(memberPriceCommodityQo.getCommodityMinistryPrice());
        settlementApplyCommodityVO.setCommodityName(memberPriceCommodityQo.getCommodityName());
        settlementApplyCommodityVO.setSkuId(memberPriceCommodityQo.getSkuId());
        return settlementApplyCommodityVO;
    }

    /**
     * 转换积分优惠订单对象
     *
     * @param integralDeductVO 积分计算结果
     * @param dto 结算申请订单DTO
     * @return 积分优惠订单对象
     */
    public static SettlementApplyOrderVO fromCalculateOrderIntegralDeductVO(CalculateOrderIntegralDeductVO integralDeductVO,
                                                                            SettlementApplyOrderDTO dto) {
        // 创建订单VO对象并设置基础信息
        SettlementApplyOrderVO applyOrderVO = new SettlementApplyOrderVO();
        applyOrderVO.setOderInfo(new SettlementApplyOderInfoVO());

        // 获取积分优惠列表
        final List<MemberIntegralDeductVO> benefitsVOList = integralDeductVO.getMembershipBenefitsVOS();
        List<SettlementApplyDiscountVO> applyDiscountVOList;

        if (CollUtil.isEmpty(benefitsVOList)) {
            // 如果没有积分优惠，创建默认的0积分优惠对象
            applyDiscountVOList = Lists.newArrayList(createDefaultIntegralDiscount(integralDeductVO.getIntegralConsumeRuleGuid()));
        } else {
            // 如果有积分优惠，转换优惠信息
            applyDiscountVOList = fromMembershipBenefitsVoOfIntegralList(benefitsVOList, dto);
        }

        applyOrderVO.setDiscountList(applyDiscountVOList);
        return applyOrderVO;
    }

    /**
     * 创建默认的积分优惠对象（无优惠金额）
     *
     * @param integralConsumeRuleGuid 积分消费规则GUID
     * @return 默认积分优惠对象
     */
    private static SettlementApplyDiscountVO createDefaultIntegralDiscount(String integralConsumeRuleGuid) {
        // 创建积分优惠对象
        SettlementApplyDiscountVO applyDiscountVo = SettlementApplyDiscountVO.buildByDiscountType(DiscountTypeEnum.INTEGRAL_DEDUCT.getCode());
        applyDiscountVo.setDiscountAmount(BigDecimal.ZERO);

        // 创建积分优惠详情对象
        SettlementApplyDiscountDetailOfIntegralVO detailOfDiscountVO = new SettlementApplyDiscountDetailOfIntegralVO();
        detailOfDiscountVO.setUsedIntegralNum(0);
        detailOfDiscountVO.setDiscountOption(applyDiscountVo.getDiscountOption());
        detailOfDiscountVO.setDiscountGuid(integralConsumeRuleGuid);
        detailOfDiscountVO.setDiscountAmount(BigDecimal.ZERO);
        detailOfDiscountVO.setDiscountName(SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getDes());
        detailOfDiscountVO.setIsEnabled(BooleanEnum.FALSE.getCode());

        // 设置优惠详情列表
        applyDiscountVo.setDiscountList(Lists.newArrayList(detailOfDiscountVO));

        return applyDiscountVo;
    }

    /**
     * 转换积分优惠对象列表
     *
     * @param benefitsVOList 积分结算结果列表
     * @param dto 结算申请订单DTO
     * @return 积分优惠对象列表
     */
    private static List<SettlementApplyDiscountVO> fromMembershipBenefitsVoOfIntegralList(List<MemberIntegralDeductVO> benefitsVOList,
                                                                                          SettlementApplyOrderDTO dto) {
        // 空列表检查
        if (CollUtil.isEmpty(benefitsVOList)) {
            return Lists.newArrayList();
        }

        // 创建积分优惠主对象
        SettlementApplyDiscountVO applyDiscountVo = buildIntegralDiscountVo(benefitsVOList);

        // 转换积分优惠详情列表
        List<SettlementApplyDiscountDetailVO> discountDetailList = benefitsVOList.stream()
                .map(benefitsVO -> buildIntegralDiscountDetail(benefitsVO, dto, applyDiscountVo))
                .collect(Collectors.toList());

        // 设置优惠详情列表并返回结果
        applyDiscountVo.setDiscountList(discountDetailList);
        return Lists.newArrayList(applyDiscountVo);
    }

    /**
     * 构建单个积分优惠详情对象
     *
     * @param benefitsVO 积分优惠VO
     * @param dto 结算申请订单DTO
     * @param parentDiscount 父级优惠对象
     * @return 积分优惠详情对象
     */
    private static SettlementApplyDiscountDetailVO buildIntegralDiscountDetail(
            MemberIntegralDeductVO benefitsVO,
            SettlementApplyOrderDTO dto,
            SettlementApplyDiscountVO parentDiscount) {

        // 创建基础优惠详情对象
        SettlementApplyDiscountDetailVO detailVO = fromMembershipBenefitsVoByIntegral(benefitsVO, dto);

        // 设置优惠关联信息
        detailVO.setDiscountOption(parentDiscount.getDiscountOption());
        detailVO.setDiscountGuid(parentDiscount.getDiscountGuid());
        detailVO.setDiscountOptionId(benefitsVO.getVersionId());
        detailVO.setDiscountAmount(benefitsVO.getIntegralDiscountAmount());

        // 设置启用状态
        detailVO.setIsEnabled(benefitsVO.getIntegralDiscountAmount().compareTo(BigDecimal.ZERO) > 0
                ? BooleanEnum.TRUE.getCode()
                : BooleanEnum.FALSE.getCode());

        // 设置排序和最优选项
        detailVO.setSort(benefitsVO.getSort());
        detailVO.setOptimal(Optional.ofNullable(benefitsVO.getIsOptimal())
                .orElse(BooleanEnum.FALSE.getCode()));

        // 设置商品列表
        detailVO.setCommodityList(fromMemberPriceCommodityQoList(benefitsVO.getMemberPriceCommodityQOS()));

        // 互斥共享
        detailVO.setShareRelation(benefitsVO.getRelationRule());

        return detailVO;
    }

    /**
     * 构造积分优惠对象
     *
     * @param benefitsVOList 积分计算结果
     * @return 积分优惠对象
     */
    private static SettlementApplyDiscountVO buildIntegralDiscountVo(List<MemberIntegralDeductVO> benefitsVOList) {
        // 参数验证
        if (CollUtil.isEmpty(benefitsVOList)) {
            return SettlementApplyDiscountVO.buildByDiscountType(DiscountTypeEnum.INTEGRAL_DEDUCT.getCode());
        }

        // 获取第一个积分优惠对象
        final MemberIntegralDeductVO membershipBenefitsVO = benefitsVOList.get(0);

        // 创建积分优惠对象
        SettlementApplyDiscountVO applyDiscountVo = SettlementApplyDiscountVO.buildByDiscountType(
                DiscountTypeEnum.INTEGRAL_DEDUCT.getCode()
        );

        // 设置积分抵现规则GUID（如果存在）
        if (membershipBenefitsVO != null) {
            applyDiscountVo.setDiscountGuid(membershipBenefitsVO.getDeductGuid());
        }

        return applyDiscountVo;
    }

    /**
     * 转换积分入参
     *
     * @param dto 公共结算参数
     * @return 积分入参
     */
    /**
     * 转换积分入参
     *
     * @param dto 公共结算参数
     * @return 积分入参查询对象
     */
    public static CalculateIntegralDeductQO toCalculateIntegralDeductQo(SettlementApplyOrderDTO dto) {
        final SettlementApplyOrderInfoDTO orderInfo = dto.getOrderInfo();

        // 构建积分抵扣查询对象
        CalculateIntegralDeductQO integralDeductQuery = new CalculateIntegralDeductQO();

        // 设置会员基本信息
        integralDeductQuery.setMemberInfoGuid(orderInfo.getMemberInfoGuid());
        integralDeductQuery.setBusiness(orderInfo.getBusiness());
        integralDeductQuery.setStoreGuid(orderInfo.getStoreGuid());

        // 处理终端类型转换
        String terminal = orderInfo.getTerminal();
        if (terminal.equals(ApplyDictionariesEnum.TERMINAL_MINI_PROGRAM.getType())) {
            // 小程序转换
            terminal = String.valueOf(SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode());
        } else if (terminal.equals(ApplyDictionariesEnum.CHANNEL_POS.getType())) {
            // 一体机转换
            terminal = String.valueOf(SourceTypeEnum.ADD_ONE_MACHINE.getCode());
        }
        integralDeductQuery.setTerminal(terminal);

        // 计算商品总金额（累加小计）
        integralDeductQuery.setCommodityTotalAmount(dto.getOrderCommodityList()
                .stream()
                .map(SettlementApplyCommodityDTO::getDiscountTotalPriceInShopCar)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        // 设置锁定积分信息
        if (CollUtil.isNotEmpty(dto.getCheckDiscountList())) {
            List<SettlementApplyDiscountBaseReqDTO> integralDiscounts = dto.getCheckDiscountList().stream()
                    .filter(in -> in.getDiscountOption() == SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode())
                    .collect(Collectors.toList());

            if (!integralDiscounts.isEmpty()) {
                SettlementApplyDiscountBaseReqDTO discountBaseReqDTO = integralDiscounts.get(0);
                integralDeductQuery.setLockedIntegral(discountBaseReqDTO.getLockedIntegral());
                integralDeductQuery.setLockedForNowMoney(discountBaseReqDTO.getLockedForNowMoney());
                integralDeductQuery.setDiscountOptionId(discountBaseReqDTO.getDiscountOptionId());
            }
        }

        // 设置运费金额（默认为0）
        integralDeductQuery.setFreightAmount(BigDecimal.ZERO);
        integralDeductQuery.setTableAmount(Optional.ofNullable(dto.getOrderInfo().getTableAmount()).orElse(BigDecimal.ZERO));

        // 转换并设置订单商品列表
        integralDeductQuery.setMemberPriceCommodityQOS(toMemberPriceCommodityQoList(dto.getOrderCommodityList()));

        return integralDeductQuery;
    }

    /**
     * 将会员优惠券关联对象转换为结算优惠券详情对象
     *
     * @param couponLink 会员优惠券关联对象
     * @return 结算优惠券详情对象
     */
    public static SettlementApplyDiscountDetailOfCouponVO fromMemberCouponLink(HsaMemberCouponLink couponLink) {
        SettlementApplyDiscountDetailOfCouponVO couponDetailVO = new SettlementApplyDiscountDetailOfCouponVO();

        // 设置优惠券基本信息
        couponDetailVO.setDiscountName(couponLink.getCouponName());
        couponDetailVO.setCouponType(couponLink.getCouponType());

        // 设置优惠券标识信息
        couponDetailVO.setDiscountGuid(couponLink.getCouponCode());      // 优惠券唯一标识
        couponDetailVO.setDiscountOptionId(couponLink.getCode());        // 已领取的优惠券编码

        // 设置优惠券有效期
        couponDetailVO.setCouponEffectiveStartTime(couponLink.getCouponEffectiveStartTime());
        couponDetailVO.setCouponEffectiveEndTime(couponLink.getCouponEffectiveEndTime());

        // 设置优惠券使用门槛
        couponDetailVO.setThresholdType(couponLink.getThresholdType());
        couponDetailVO.setThresholdAmount(couponLink.getThresholdAmount());

        // 设置优惠金额相关信息
        couponDetailVO.setCouponAmount(couponLink.getDiscountAmount());  // 优惠券面值，用于排序
        couponDetailVO.setDiscountAmount(BigDecimal.ZERO);               // 实际优惠金额，初始为0
        couponDetailVO.setDiscountAmountLimit(couponLink.getDiscountAmountLimit());

        // 设置商品使用范围
        couponDetailVO.setApplyCommodity(couponLink.getApplyCommodity());
        couponDetailVO.setApplyCommodityJson(couponLink.getApplyCommodityJson());
        couponDetailVO.setApplicableAllStore(couponLink.getApplicableAllStore());
        couponDetailVO.setApplicableAllStoreJson(couponLink.getApplicableAllStoreJson());

        if (Objects.nonNull(couponLink.getExchangeLimit()) && couponLink.getExchangeLimit() == 1) {
            couponDetailVO.setLimitExchangeTimes(couponLink.getExchangeTimes());
        }
        // 互斥共享
        couponDetailVO.setShareRelation(couponLink.getShareRelation());

        // 单笔使用限制
        couponDetailVO.setSingleOrderUsedLimit(couponLink.getSingleOrderUsedLimit());
        return couponDetailVO;
    }
}
