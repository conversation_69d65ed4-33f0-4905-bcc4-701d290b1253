package com.holderzone.member.base.service.integral.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.integral.HsaIntegralConsumeRule;
import com.holderzone.member.base.entity.integral.HsaIntegralDeductCommodity;
import com.holderzone.member.base.entity.integral.HsaMemberIntegralDeductDetail;
import com.holderzone.member.base.mapper.integral.HsaIntegralConsumeRuleMapper;
import com.holderzone.member.base.mapper.integral.HsaIntegralDeductCommodityMapper;
import com.holderzone.member.base.mapper.integral.HsaMemberIntegralDeductDetailMapper;
import com.holderzone.member.base.service.card.HsaCardInfoService;
import com.holderzone.member.base.service.integral.HsaIntegralDeductApplyService;
import com.holderzone.member.base.util.IntegralTaskCheckUtil;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.integral.DeductCommodityDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.ApplyCommodityTypeEnum;
import com.holderzone.member.common.enums.equities.DiscountTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.integral.ServiceableItemEnum;
import com.holderzone.member.common.enums.integral.SingleOrderTypeEnum;
import com.holderzone.member.common.enums.member.MemberTerminalExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.module.settlement.constant.SettlementConstant;
import com.holderzone.member.common.qo.equities.MemberPriceCommodityQO;
import com.holderzone.member.common.qo.integral.CalculateIntegralDeductQO;
import com.holderzone.member.common.qo.integral.IntegralConsumeDTO;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.integral.CalculateOrderIntegralDeductVO;
import com.holderzone.member.common.vo.integral.MemberIntegralDeductVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HsaIntegralDeductApplyServiceImpl implements HsaIntegralDeductApplyService {

    @Resource
    private HsaIntegralConsumeRuleMapper hsaIntegralConsumeRuleMapper;

    @Resource
    private HsaMemberIntegralDeductDetailMapper hsaMemberIntegralDeductDetailMapper;

    @Resource
    private HsaIntegralDeductCommodityMapper hsaIntegralDeductCommodityMapper;

    @Resource
    private HsaCardInfoService hsaCardInfoService;

    @Override
    public CalculateOrderIntegralDeductVO calculateOrderIntegralDeduct(CalculateIntegralDeductQO request) {
        log.info("积分抵现计算入参：{}", JSON.toJSONString(request));
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        CalculateOrderIntegralDeductVO integralDeductVO = new CalculateOrderIntegralDeductVO();

        //获积分消耗规则
        HsaIntegralConsumeRule hsaIntegralConsumeRule = hsaIntegralConsumeRuleMapper.selectOne(new LambdaQueryWrapper<HsaIntegralConsumeRule>()
                .eq(HsaIntegralConsumeRule::getOperSubjectGuid, headerUserInfo.getOperSubjectGuid()));

        if (Objects.isNull(hsaIntegralConsumeRule)) {
            log.info("积分消耗规则未初始化！主体{}", headerUserInfo.getOperSubjectGuid());
            integralDeductVO.setIsForNow(BooleanEnum.FALSE.getCode());
            return integralDeductVO;
        }

        Optional.of(hsaIntegralConsumeRule)
                .ifPresent(rule -> {
                    integralDeductVO.setIsForNow(rule.getIsForNow());
                    integralDeductVO.setIntegralConsumeRuleGuid(rule.getGuid());
                });

        //校验业务
        if (Objects.isNull(IntegralTaskCheckUtil.getCheckHsaGradeEquities(request, hsaIntegralConsumeRule))) {
            //业务不满足  不展示积分抵现优惠
            integralDeductVO.setIsForNow(BooleanEnum.FALSE.getCode());
            return integralDeductVO;
        }

        //单笔金额 = 小计总额+运费
        BigDecimal commodityTotalAmount = getCommodityTotalAmount(request, hsaIntegralConsumeRule);

        if (hsaIntegralConsumeRule.getSingleOrderMoney().compareTo(commodityTotalAmount) > 0) {
            log.info("单笔订单金额不足：需满足金额{}，实际金额{}", hsaIntegralConsumeRule.getSingleOrderMoney(), commodityTotalAmount);
            //周期剩余优惠
            cycleDiscount(request, hsaIntegralConsumeRule, integralDeductVO);
            return integralDeductVO;
        }
        //最终可抵扣金额
        BigDecimal cumulativeUnitPrice = getCheckLimitCycle(request, hsaIntegralConsumeRule, commodityTotalAmount);
        log.info("周期剩余优惠金额：{}", JSON.toJSONString(cumulativeUnitPrice));
        int numberOne = 0;

        if (cumulativeUnitPrice.compareTo(BigDecimal.ZERO) == numberOne) {
            log.info("单笔使用上限金额不足：{}", JSON.toJSONString(cumulativeUnitPrice));
            //周期剩余优惠
            cycleDiscount(request, hsaIntegralConsumeRule, integralDeductVO);
            return integralDeductVO;
        }

        //会员积分，优先使用外部传入的锁定值
        //会员可用积分 = 会员所有积分 - 冻结积分 - 占用积分
        int memberIntegral = hsaCardInfoService.getUsableIntegral(request.getMemberInfoGuid());
        log.info("会员积分：{}", JSON.toJSONString(memberIntegral));

        //计算最终抵扣
        List<MemberIntegralDeductVO> membershipBenefitsVOS = computeDeductionHandle(
                cumulativeUnitPrice,
                memberIntegral,
                numberOne,
                request,
                hsaIntegralConsumeRule);


        if (CollUtil.isEmpty(membershipBenefitsVOS)) {
            log.info("积分不足");
            //周期剩余优惠
            integralDeductVO.setIsForNow(BooleanEnum.FALSE.getCode());
            return integralDeductVO;
        }
        BeanUtils.copyProperties(request, integralDeductVO);
        integralDeductVO.setDiscountType(DiscountTypeEnum.INTEGRAL_DEDUCT.getCode());
        integralDeductVO.setCommodityTotalAmount(request.getCommodityTotalAmount());
        integralDeductVO.setMembershipBenefitsVOS(membershipBenefitsVOS);
        return integralDeductVO;
    }

    private BigDecimal getCommodityTotalAmount(CalculateIntegralDeductQO request, HsaIntegralConsumeRule hsaIntegralConsumeRule) {
        BigDecimal commodityTotalAmount;

        if (CollUtil.isNotEmpty(request.getMemberPriceCommodityQOS())) {
            //商品可抵现金额判断
            commodityTotalAmount = getCumulativeUnitPrice(request, hsaIntegralConsumeRule);
        } else {
            commodityTotalAmount = request.getCommodityTotalAmount();
        }
        List<String> parseArray = JSON.parseArray(hsaIntegralConsumeRule.getServiceableItem(), String.class);

        //运费
        if (parseArray.contains(String.valueOf(ServiceableItemEnum.ITEM_FREIGHT_AMOUNT.getCode()))
                && Objects.nonNull(request.getFreightAmount())) {
            commodityTotalAmount = commodityTotalAmount.add(request.getFreightAmount());
        }

        // 桌台费
        if (parseArray.contains(String.valueOf(ServiceableItemEnum.ITEM_TABLE_AMOUNT.getCode()))
                && Objects.nonNull(request.getTableAmount())) {
            commodityTotalAmount = commodityTotalAmount.add(request.getTableAmount());
        }
        return commodityTotalAmount;
    }

    private void cycleDiscount(CalculateIntegralDeductQO request,
                               HsaIntegralConsumeRule hsaIntegralConsumeRule,
                               CalculateOrderIntegralDeductVO integralDeductVO) {
        List<MemberIntegralDeductVO> integralDeductVOList = Lists.newArrayList();

        BigDecimal cycleResidueAmount = getLimitPrice(request, hsaIntegralConsumeRule, hsaIntegralConsumeRule.getPeriodDiscountLimited());

        List<IntegralConsumeDTO> integralConsumeDTOS = JSON.parseArray(hsaIntegralConsumeRule.getIntegralNowMoneyJson(), IntegralConsumeDTO.class);

        for (IntegralConsumeDTO integralConsumeDTO : integralConsumeDTOS) {
            MemberIntegralDeductVO memberIntegralDeductVO = new MemberIntegralDeductVO();
            memberIntegralDeductVO.setCycleResidueAmount(cycleResidueAmount);

            memberIntegralDeductVO.setDiscountType(DiscountTypeEnum.INTEGRAL_DEDUCT.getCode())
                    .setDeductName("积分抵现")
                    .setIntegralNum(0)
                    .setIntegralDiscountAmount(BigDecimal.ZERO)
                    .setEquitiesTimeLimitedType(hsaIntegralConsumeRule.getPeriodDiscountType())
                    .setDeductGuid(hsaIntegralConsumeRule.getGuid())
                    .setVersionId(hsaIntegralConsumeRule.getVersionId())
                    .setSort(integralConsumeDTO.getSort())
                    .setPeriodDiscountLimited(hsaIntegralConsumeRule.getIsLimit());

            memberIntegralDeductVO.setIsOptimal(BooleanEnum.FALSE.getCode());
            memberIntegralDeductVO.setVersionId(integralConsumeDTO.getGuid());
            memberIntegralDeductVO.setDisIntegralNum(integralConsumeDTO.getIntegralNum());
            memberIntegralDeductVO.setDisForNowMoney(integralConsumeDTO.getForNowMoney());
            integralDeductVOList.add(memberIntegralDeductVO);
        }

        integralDeductVO.setMembershipBenefitsVOS(integralDeductVOList);
    }

    private BigDecimal getLimitPrice(CalculateIntegralDeductQO qo,
                                     HsaIntegralConsumeRule hsaIntegralConsumeRule,
                                     BigDecimal discountPrice) {
        if (hsaIntegralConsumeRule.getIsLimit() == EquitiesLimitedTypeEnum.LIMITED.getCode()) {
            discountPrice = getDiscountPrice(qo, hsaIntegralConsumeRule, discountPrice);
        }
        return discountPrice;
    }

    private BigDecimal getDiscountPrice(CalculateIntegralDeductQO qo, HsaIntegralConsumeRule hsaIntegralConsumeRule, BigDecimal discountPrice) {
        Integer discountType = hsaIntegralConsumeRule.getPeriodDiscountType();
        BigDecimal periodDiscountLimited = hsaIntegralConsumeRule.getPeriodDiscountLimited();
        if (discountType == DataUnitEnum.DAY.getCode()) {
            discountPrice = calculateCycleDeduct(qo.getMemberInfoGuid(), discountPrice, periodDiscountLimited, DateUtil.getDayStartTime(), DateUtil.getDayEndTime());
        } else if (discountType == DataUnitEnum.WEEK.getCode()) {
            discountPrice = calculateCycleDeduct(qo.getMemberInfoGuid(), discountPrice, periodDiscountLimited, DateUtil.getWeekStartTime(), DateUtil.getWeekEndTime());
        } else if (discountType == DataUnitEnum.MONTH.getCode()) {
            discountPrice = calculateCycleDeduct(qo.getMemberInfoGuid(), discountPrice, periodDiscountLimited, DateUtil.getMonthStartTime(), DateUtil.getMonthEndTime());
        } else {
            discountPrice = calculateCycleDeduct(qo.getMemberInfoGuid(), discountPrice, periodDiscountLimited, DateUtil.getYearStartTime(), DateUtil.getYearEndTime());
        }
        return discountPrice;
    }


    /**
     * 计算周期限额
     *
     * @param discountPrice discountPrice
     */
    private BigDecimal calculateCycleDeduct(String memberInfoGuid,
                                            BigDecimal discountPrice,
                                            BigDecimal equitiesLimited,
                                            LocalDateTime startTime,
                                            LocalDateTime endTime) {
        if (discountPrice.compareTo(BigDecimal.ZERO) == 0) {
            return discountPrice;
        }
        if (Objects.nonNull(equitiesLimited)) {
            //获取当前已消耗优惠额度
            BigDecimal consumptionLimit = getConsumptionLimit(memberInfoGuid, startTime, endTime);
            //日限制剩余金额
            BigDecimal limitedAmount = equitiesLimited.subtract(consumptionLimit);
            log.info("当前规则下已使用额度：{}", consumptionLimit);
            if (limitedAmount.compareTo(BigDecimal.ZERO) < 0) {
                limitedAmount = BigDecimal.ZERO;
            }
            if (discountPrice.compareTo(limitedAmount) > 0) {
                discountPrice = limitedAmount;
            }
        }
        return discountPrice;
    }


    /**
     * 计算周期内已使用优惠额度
     *
     * @param startTime startTime
     * @param endTime   endTime
     * @return BigDecimal
     */
    private BigDecimal getConsumptionLimit(String memberInfoGuid, LocalDateTime startTime, LocalDateTime endTime) {
        BigDecimal dayConsumptionLimit = BigDecimal.ZERO;
        List<HsaMemberIntegralDeductDetail> hsaMemberIntegralDeductDetails = getHsaMemberGradePriceDetails(memberInfoGuid, startTime, endTime);
        if (CollUtil.isNotEmpty(hsaMemberIntegralDeductDetails)) {
            for (HsaMemberIntegralDeductDetail hsaMemberIntegralDeductDetail : hsaMemberIntegralDeductDetails) {
                dayConsumptionLimit = getAdd(hsaMemberIntegralDeductDetail.getDiscountedPrice(), dayConsumptionLimit);
            }
        }
        return dayConsumptionLimit;
    }


    private List<HsaMemberIntegralDeductDetail> getHsaMemberGradePriceDetails(String memberInfoGuid, LocalDateTime startTime, LocalDateTime endTime) {
        return hsaMemberIntegralDeductDetailMapper.selectList(new LambdaQueryWrapper<HsaMemberIntegralDeductDetail>()
                .eq(HsaMemberIntegralDeductDetail::getMemberInfoGuid, memberInfoGuid)
                .eq(HsaMemberIntegralDeductDetail::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .gt(Objects.nonNull(startTime), HsaMemberIntegralDeductDetail::getGmtCreate, startTime)
                .lt(Objects.nonNull(endTime), HsaMemberIntegralDeductDetail::getGmtCreate, endTime));
    }


    private BigDecimal getAdd(BigDecimal discountCommodityTotalAmount, BigDecimal decimal) {
        return discountCommodityTotalAmount.add(decimal);
    }


    /**
     * 计算会员价周期剩余次数
     *
     * @param qo                     qo
     * @param hsaIntegralConsumeRule hsaGradeEquities
     */
    private BigDecimal getCheckLimitCycle(CalculateIntegralDeductQO qo,
                                          HsaIntegralConsumeRule hsaIntegralConsumeRule,
                                          BigDecimal commodityTotalAmount) {
        //单笔以及比例折扣
        commodityTotalAmount = getSingleLimitAmount(hsaIntegralConsumeRule, commodityTotalAmount);

        //周期限额
        return getLimitPrice(qo, hsaIntegralConsumeRule, commodityTotalAmount);
    }

    /**
     * 单笔上限可用额度计算
     * @return 单笔限额可用额度
     */
    private static BigDecimal getSingleLimitAmount(HsaIntegralConsumeRule hsaIntegralConsumeRule,
                                                   BigDecimal commodityTotalAmount) {
        BigDecimal discountPrice = BigDecimal.ZERO;

        //上限金额
        if (hsaIntegralConsumeRule.getSingleOrderType() == SingleOrderTypeEnum.UPPER_LIMIT_AMOUNT.getCode()) {
            discountPrice = hsaIntegralConsumeRule.getSingleOrderValue();
        }

        //上限比例
        if (hsaIntegralConsumeRule.getSingleOrderType() == SingleOrderTypeEnum.UPPER_LIMIT_RATIO.getCode()) {
            BigDecimal singleOrderValue = hsaIntegralConsumeRule.getSingleOrderValue().divide(new BigDecimal(100), 2, RoundingMode.DOWN);
            discountPrice = commodityTotalAmount.multiply(singleOrderValue).setScale(2, RoundingMode.DOWN);
        }

        //单笔限额可用额度
        if (discountPrice.compareTo(BigDecimal.ZERO) > 0 && (commodityTotalAmount.compareTo(discountPrice) > 0)) {
            commodityTotalAmount = discountPrice;
        }

        return commodityTotalAmount;
    }

    private List<MemberIntegralDeductVO> computeDeductionHandle(BigDecimal cumulativeUnitPrice,
                                                                int memberTotalIntegral,
                                                                int numberOne,
                                                                CalculateIntegralDeductQO request,
                                                                HsaIntegralConsumeRule hsaIntegralConsumeRule) {

        //过滤出满足积分档位
        List<IntegralConsumeDTO> integralConsumeDTOS = JSON.parseArray(hsaIntegralConsumeRule.getIntegralNowMoneyJson(), IntegralConsumeDTO.class);

        List<MemberIntegralDeductVO> membershipBenefitsVOS = Lists.newArrayList();

        //指定档位计算
        if (Objects.nonNull(request.getLockedIntegral()) && request.getLockedIntegral() > 0) {
            IntegralConsumeDTO integralConsumeDTO = new IntegralConsumeDTO();
            List<IntegralConsumeDTO> identityContracts = integralConsumeDTOS
                    .stream()
                    .filter(in -> in.getGuid().equals(request.getDiscountOptionId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(identityContracts)) {
                integralConsumeDTOS.clear();
                IntegralConsumeDTO contractDTO = identityContracts.get(0);
                integralConsumeDTO.setForNowMoney(contractDTO.getForNowMoney());
                integralConsumeDTO.setIntegralNum(contractDTO.getIntegralNum());
                integralConsumeDTO.setSort(contractDTO.getSort());
                integralConsumeDTO.setGuid(request.getDiscountOptionId());
                integralConsumeDTOS.add(integralConsumeDTO);
            }
        }
        for (IntegralConsumeDTO integralConsumeDTO : integralConsumeDTOS) {

            //执行倍数
            int multiple = cumulativeUnitPrice.divide(integralConsumeDTO.getForNowMoney(), 2, RoundingMode.DOWN).intValue();

            int memberIntegral = memberTotalIntegral;

            log.info("会员可用积分：{}", memberIntegral);

            //抵扣金额
            BigDecimal deductAmount = new BigDecimal(BigDecimal.ROUND_UP);

            //消耗积分
            int consumeIntegralNum = numberOne;

            for (int i = numberOne; i < multiple; i++) {
                int localNum = memberIntegral - integralConsumeDTO.getIntegralNum();
                if (localNum < numberOne) {
                    break;
                }
                deductAmount = deductAmount.add(integralConsumeDTO.getForNowMoney());
                consumeIntegralNum = consumeIntegralNum + integralConsumeDTO.getIntegralNum();
                memberIntegral = localNum;
            }

            //比对最优档位
            dealDeduction(
                    deductAmount,
                    consumeIntegralNum,
                    membershipBenefitsVOS,
                    request,
                    hsaIntegralConsumeRule,
                    cumulativeUnitPrice,
                    integralConsumeDTO);
        }

        if (StringUtils.isNotEmpty(request.getDiscountOptionId()) && CollUtil.isNotEmpty(membershipBenefitsVOS)) {
            membershipBenefitsVOS = membershipBenefitsVOS
                    .stream().filter(vo -> vo.getIsOptimal() == BooleanEnum.TRUE.getCode())
                    .collect(Collectors.toList());
        }
        return membershipBenefitsVOS;
    }


    private void dealDeduction(BigDecimal deductAmount,
                               int consumeIntegralNum,
                               List<MemberIntegralDeductVO> membershipBenefitsVOS,
                               CalculateIntegralDeductQO request,
                               HsaIntegralConsumeRule hsaIntegralConsumeRule,
                               BigDecimal cumulativeUnitPrice,
                               IntegralConsumeDTO integralConsumeDTO) {
        MemberIntegralDeductVO memberIntegralDeductVO = new MemberIntegralDeductVO();
        if (CollUtil.isEmpty(membershipBenefitsVOS)) {
            memberIntegralDeductVO.setIsOptimal(deductAmount.compareTo(BigDecimal.ZERO) > 0 ?
                    BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        } else {
            comparisonAmount(deductAmount, consumeIntegralNum, membershipBenefitsVOS, memberIntegralDeductVO);
        }
        //计算积分,优惠金额分摊到商品
        dealIntegralDeductVO(request,
                hsaIntegralConsumeRule,
                deductAmount,
                consumeIntegralNum,
                memberIntegralDeductVO,
                cumulativeUnitPrice,
                integralConsumeDTO);

        membershipBenefitsVOS.add(memberIntegralDeductVO);
    }

    private static void comparisonAmount(BigDecimal deductAmount,
                                         int consumeIntegralNum,
                                         List<MemberIntegralDeductVO> membershipBenefitsVOS,
                                         MemberIntegralDeductVO memberIntegralDeductVO) {
        List<MemberIntegralDeductVO> deductVOS = membershipBenefitsVOS.stream()
                .filter(deductionDTO -> deductionDTO.getIsOptimal() == BooleanEnum.TRUE.getCode())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(deductVOS)) {
            MemberIntegralDeductVO deductVO = deductVOS.get(NumberConstant.NUMBER_0);

            if (deductAmount.compareTo(deductVO.getIntegralDiscountAmount()) > 0
                    || (deductAmount.compareTo(deductVO.getIntegralDiscountAmount()) == 0
                    && consumeIntegralNum < deductVO.getIntegralNum())) {
                memberIntegralDeductVO.setIsOptimal(BooleanEnum.TRUE.getCode());
                deductVO.setIsOptimal(BooleanEnum.FALSE.getCode());
            } else {
                memberIntegralDeductVO.setIsOptimal(BooleanEnum.FALSE.getCode());
            }
        } else {
            memberIntegralDeductVO.setIsOptimal(deductAmount.compareTo(BigDecimal.ZERO) > 0 ?
                    BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        }
    }

    private void dealIntegralDeductVO(
            CalculateIntegralDeductQO request,
            HsaIntegralConsumeRule hsaIntegralConsumeRule,
            BigDecimal deductAmount,
            int consumeIntegralNum,
            MemberIntegralDeductVO memberIntegralDeductVO,
            BigDecimal cumulativeUnitPrice,
            IntegralConsumeDTO integralConsumeDTO) {
        //优惠金额：商品分摊计算
        List<MemberPriceCommodityQO> memberPriceCommodityQOS = new ArrayList<>();
        for (MemberPriceCommodityQO memberPriceCommodityQO : request.getMemberPriceCommodityQOS()) {
            MemberPriceCommodityQO priceCommodityQO = new MemberPriceCommodityQO();
            BeanUtils.copyProperties(memberPriceCommodityQO, priceCommodityQO);
            //如果此商品售价已经优惠为0 则不再参与分摊
            if ((Objects.nonNull(memberPriceCommodityQO.getAfterDiscountTotalPrice())
                    && memberPriceCommodityQO.getAfterDiscountTotalPrice().compareTo(BigDecimal.ZERO) == 0)
                    || memberPriceCommodityQO.getDiscountTotalPriceInShopCar().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            priceCommodityQO.setCommodityNum(memberPriceCommodityQO.getCommodityNum().subtract(memberPriceCommodityQO.getExchangeDiscountNum()));
            memberPriceCommodityQOS.add(priceCommodityQO);
        }
        memberIntegralDeductVO.setMemberPriceCommodityQOS(memberPriceCommodityQOS);
        if (deductAmount.compareTo(BigDecimal.ZERO) > 0) {
            calculateCommodityMinistryPrice(request, memberIntegralDeductVO, hsaIntegralConsumeRule, deductAmount);
        }

        //基础组装
        getMemberIntegralDeductVO(request,
                hsaIntegralConsumeRule,
                deductAmount,
                consumeIntegralNum,
                memberIntegralDeductVO,
                cumulativeUnitPrice,
                integralConsumeDTO);
    }


    private void getMemberIntegralDeductVO(CalculateIntegralDeductQO request,
                                           HsaIntegralConsumeRule hsaIntegralConsumeRule,
                                           BigDecimal deductAmount,
                                           int consumeIntegralNum,
                                           MemberIntegralDeductVO memberIntegralDeductVO,
                                           BigDecimal commodityTotalAmount,
                                           IntegralConsumeDTO integralConsumeDTO) {

        memberIntegralDeductVO.setDiscountType(DiscountTypeEnum.INTEGRAL_DEDUCT.getCode())
                .setDeductName("积分抵现")
                .setIntegralNum(consumeIntegralNum)
                .setIntegralDiscountAmount(deductAmount)
                .setEquitiesTimeLimitedType(hsaIntegralConsumeRule.getPeriodDiscountType())
                .setDeductGuid(hsaIntegralConsumeRule.getGuid())
                .setPeriodDiscountLimited(hsaIntegralConsumeRule.getIsLimit())
                .setRelationRule(hsaIntegralConsumeRule.getRelationRule());

        memberIntegralDeductVO.setIntegralDiscountAmount(deductAmount);
        memberIntegralDeductVO.setIntegralNum(consumeIntegralNum);

        memberIntegralDeductVO.setDisIntegralNum(integralConsumeDTO.getIntegralNum());
        memberIntegralDeductVO.setDisForNowMoney(integralConsumeDTO.getForNowMoney());
        memberIntegralDeductVO.setSort(integralConsumeDTO.getSort());
        memberIntegralDeductVO.setVersionId(integralConsumeDTO.getGuid());
        memberIntegralDeductVO.setDiscountDynamicsAmount(deductAmount);
        memberIntegralDeductVO.setOrderRealPaymentAmount(commodityTotalAmount.subtract(deductAmount));
        //周期剩余优惠
        BigDecimal cycleResidueAmount = getLimitPrice(request, hsaIntegralConsumeRule, hsaIntegralConsumeRule.getPeriodDiscountLimited());
        memberIntegralDeductVO.setCycleResidueAmount(cycleResidueAmount);
    }

    /**
     * 计算商品优惠金额分摊
     * 单个商品优惠金额 = 总优惠金额 *【（商品小计-单品已优惠金额小计） / （商品总金额-总已优惠金额）】
     */
    private static void calculateCommodityMinistryPrice(CalculateIntegralDeductQO request,
                                                        MemberIntegralDeductVO memberIntegralDeductVO,
                                                        HsaIntegralConsumeRule hsaIntegralConsumeRule,
                                                        BigDecimal deductAmount) {
        //计算分摊
        final List<MemberPriceCommodityQO> usableCommodityList = memberIntegralDeductVO.getMemberPriceCommodityQOS()
                .stream().filter(c -> c.getUsable() == BooleanEnum.TRUE.getCode())
                .collect(Collectors.toList());
        //减去其他优惠
        //优先使用已优惠的总价进行分摊
        //商品总金额-总已优惠金额
        BigDecimal commodityTotalAmount = getUsableCommodityTotalAmount(request, hsaIntegralConsumeRule, usableCommodityList);

        BigDecimal usableAmount = deductAmount;
        final int endIndex = usableCommodityList.size() - 1;
        for (int i = 0; i < endIndex; i++) {
            final MemberPriceCommodityQO commodityQo = usableCommodityList.get(i);
            //商品小计-单品已优惠金额小计
            final BigDecimal commodityPrice = Objects.nonNull(commodityQo.getAfterDiscountTotalPrice())
                    ?
                    commodityQo.getAfterDiscountTotalPrice()
                    :
                    commodityQo.getCommodityNum().multiply(commodityQo.getCommodityPrice())
                            .subtract(commodityQo.getCommodityMinistryPrice());
            BigDecimal min = getDiscountAmount(commodityPrice, commodityTotalAmount, deductAmount);
            usableAmount = usableAmount.subtract(min);
            //不超过商品剩余金额
            commodityQo.addCommodityMinistryPrice(min);
        }

        // 运费
        List<String> parseArray = JSON.parseArray(hsaIntegralConsumeRule.getServiceableItem(), String.class);
        boolean isFreight = parseArray.contains(String.valueOf(ServiceableItemEnum.ITEM_FREIGHT_AMOUNT.getCode()))
                && BigDecimalUtil.greaterThanZero(request.getFreightAmount());
        if (isFreight) {
            BigDecimal min = getDiscountAmount(request.getFreightAmount(), commodityTotalAmount, deductAmount);
            usableAmount = usableAmount.subtract(min);
            memberIntegralDeductVO.setFreightDiscountAmount(min);
        }
        // 桌台费
        boolean isTable = parseArray.contains(String.valueOf(ServiceableItemEnum.ITEM_TABLE_AMOUNT.getCode()))
                && BigDecimalUtil.greaterThanZero(request.getTableAmount());
        if (isTable) {
            BigDecimal min = getDiscountAmount(request.getTableAmount(), commodityTotalAmount, deductAmount);
            usableAmount = usableAmount.subtract(min);
            memberIntegralDeductVO.setTableDiscountAmount(min);
        }

        //最后商品
        if (usableAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        if (endIndex >= 0) {
            final MemberPriceCommodityQO commodityQo = usableCommodityList.get(endIndex);
            commodityQo.addCommodityMinistryPrice(usableAmount);
        } else if (isFreight) {
            memberIntegralDeductVO.setFreightDiscountAmount(memberIntegralDeductVO.getFreightDiscountAmount().add(usableAmount));
        } else if (isTable) {
            memberIntegralDeductVO.setTableDiscountAmount(memberIntegralDeductVO.getTableDiscountAmount().add(usableAmount));
        }
    }

    private static BigDecimal getDiscountAmount(BigDecimal requestAmount,
                                                BigDecimal commodityTotalAmount,
                                                BigDecimal deductAmount) {
        //比例
        final BigDecimal scale = requestAmount
                .divide(commodityTotalAmount, SettlementConstant.SCALE_LENGTH, RoundingMode.DOWN);
        //比列金额
        final BigDecimal scaleAmount = deductAmount.multiply(scale).setScale(2, RoundingMode.DOWN);
        return BigDecimalUtil.min(scaleAmount, requestAmount);
    }

    private static BigDecimal getUsableCommodityTotalAmount(CalculateIntegralDeductQO request,
                                                            HsaIntegralConsumeRule hsaIntegralConsumeRule,
                                                            List<MemberPriceCommodityQO> usableCommodityList) {
        BigDecimal usableCommodityTotalAmount = usableCommodityList.stream().map(c ->
                        Objects.nonNull(c.getAfterDiscountTotalPrice())
                                ?
                                c.getAfterDiscountTotalPrice()
                                :
                                c.getCommodityNum().multiply(c.getCommodityPrice())
                                        .subtract(c.getCommodityMinistryPrice()))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 运费
        List<String> parseArray = JSON.parseArray(hsaIntegralConsumeRule.getServiceableItem(), String.class);
        if (parseArray.contains(String.valueOf(ServiceableItemEnum.ITEM_FREIGHT_AMOUNT.getCode()))
                && BigDecimalUtil.greaterThanZero(request.getFreightAmount())) {
            usableCommodityTotalAmount = usableCommodityTotalAmount.add(request.getFreightAmount());
        }
        // 桌台费
        if (parseArray.contains(String.valueOf(ServiceableItemEnum.ITEM_TABLE_AMOUNT.getCode()))
                && BigDecimalUtil.greaterThanZero(request.getTableAmount())) {
            usableCommodityTotalAmount = usableCommodityTotalAmount.add(request.getTableAmount());
        }
        return usableCommodityTotalAmount;
    }

    private BigDecimal getCumulativeUnitPrice(CalculateIntegralDeductQO qo, HsaIntegralConsumeRule hsaIntegralConsumeRule) {
        //参与商品累计单价
        BigDecimal cumulativeUnitPrice = BigDecimal.ZERO;

        //计算对应折扣商品总额
        List<String> parseArray = JSON.parseArray(hsaIntegralConsumeRule.getServiceableItem(), String.class);
        if (parseArray.contains(String.valueOf(ServiceableItemEnum.ITEM_ORDER_AMOUNT.getCode()))) {
            //计算涉及商品金额
            cumulativeUnitPrice = getDeductCommodityPrice(qo, hsaIntegralConsumeRule, cumulativeUnitPrice);
        }
        return cumulativeUnitPrice;
    }


    /**
     * 计算积分抵扣商品累计金额
     */
    private BigDecimal getDeductCommodityPrice(CalculateIntegralDeductQO qo, HsaIntegralConsumeRule hsaIntegralConsumeRule, BigDecimal cumulativeUnitPrice) {
        //适用商品
        Map<String, DeductCommodityDTO> deductCommodityDTOMap = getGradeCommodityBase(hsaIntegralConsumeRule, qo);
        for (MemberPriceCommodityQO memberPriceCommodityQO : qo.getMemberPriceCommodityQOS()) {
            if (isContainsKey(hsaIntegralConsumeRule, deductCommodityDTOMap, memberPriceCommodityQO)) {

                if (hsaIntegralConsumeRule.getApplyGoodsType() == ApplyCommodityTypeEnum.APPLY_COMMODITY.getCode()
                        && memberPriceCommodityQO.getGoodsType() == 2) {
                    log.info("适用商品类型下,无码商品不可用:{}", memberPriceCommodityQO.getCommodityName());
                    continue;
                }
                //可用
                memberPriceCommodityQO.setUsable(BooleanEnum.TRUE.getCode());
                cumulativeUnitPrice = addCumulativeUnitPrice(cumulativeUnitPrice, memberPriceCommodityQO);
            }
        }
        return cumulativeUnitPrice;
    }

    private static boolean isContainsKey(HsaIntegralConsumeRule hsaIntegralConsumeRule,
                                         Map<String, DeductCommodityDTO> deductCommodityDTOMap,
                                         MemberPriceCommodityQO memberPriceCommodityQO) {
        return (CollUtil.isNotEmpty(deductCommodityDTOMap)
                && deductCommodityDTOMap.containsKey(memberPriceCommodityQO.getCommodityCode()))
                || hsaIntegralConsumeRule.getApplyGoodsType() == ApplyCommodityTypeEnum.ALL_APPLY_COMMODITY.getCode();
    }


    private static BigDecimal addCumulativeUnitPrice(BigDecimal cumulativeUnitPrice, MemberPriceCommodityQO memberPriceCommodityQO) {
        //优先判断是否有前置优惠
        if (Objects.nonNull(memberPriceCommodityQO.getAfterDiscountTotalPrice())) {
            cumulativeUnitPrice = cumulativeUnitPrice.add(memberPriceCommodityQO.getAfterDiscountTotalPrice());
        } else {
            //商品总价
            BigDecimal total = memberPriceCommodityQO.getDiscountTotalPriceInShopCar();
            //减去其他优惠项 已优惠金额
            if (Objects.nonNull(memberPriceCommodityQO.getCommodityMinistryPrice())
                    && memberPriceCommodityQO.getCommodityMinistryPrice().compareTo(BigDecimal.ZERO) > 0) {

                total = total
                        .subtract(memberPriceCommodityQO.getCommodityMinistryPrice());
            }
            cumulativeUnitPrice = cumulativeUnitPrice.add(total);
        }
        return cumulativeUnitPrice;
    }

    private Map<String, DeductCommodityDTO> getGradeCommodityBase(HsaIntegralConsumeRule hsaIntegralConsumeRule, CalculateIntegralDeductQO qo) {

        List<HsaIntegralDeductCommodity> hsaGradeRightsCommodityRules = hsaIntegralDeductCommodityMapper.selectList(new LambdaQueryWrapper<HsaIntegralDeductCommodity>()
                .eq(HsaIntegralDeductCommodity::getOperSubjectGuid, hsaIntegralConsumeRule.getOperSubjectGuid())
                //查询：商城0 pos1
                .eq(HsaIntegralDeductCommodity::getBusinessType, ThreadLocalCache.getSystem()));

        List<DeductCommodityDTO> deductCommodityDTOS = Lists.newArrayList();
        if (hsaIntegralConsumeRule.getApplyGoodsType() == ApplyCommodityTypeEnum.ALL_APPLY_COMMODITY.getCode()) {
            return new HashMap<>();
        } else if (hsaIntegralConsumeRule.getApplyGoodsType() == ApplyCommodityTypeEnum.APPLY_COMMODITY.getCode()) {
            return getGradeCommodityBaseQOMap(hsaGradeRightsCommodityRules);
        } else {
            //都用businessType = 0
            return getGradeCommodityBaseQOMap(qo, hsaGradeRightsCommodityRules, deductCommodityDTOS);
        }
    }


    private Map<String, DeductCommodityDTO> getGradeCommodityBaseQOMap(CalculateIntegralDeductQO qo, List<HsaIntegralDeductCommodity> hsaGradeRightsCommodityRules, List<DeductCommodityDTO> deductCommodityDTOS) {
        //都用 getCommodityCode
        Map<String, HsaIntegralDeductCommodity> hsaIntegralDeductCommodityMap = getHsaIntegralDeductCommodityMap(0, hsaGradeRightsCommodityRules);
        qo.getMemberPriceCommodityQOS().forEach(in -> {
            if (CollUtil.isNotEmpty(hsaIntegralDeductCommodityMap) && !hsaIntegralDeductCommodityMap.containsKey(in.getCommodityCode())) {
                deductCommodityDTOS.add(
                        getCommodityBase(in.getProductTemplateId(), in.getCommodityCode(), in.getCommodityName()));
            }
        });
        return deductCommodityDTOS.stream().collect(Collectors.toMap(DeductCommodityDTO::getDeductCommodityCode, Function.identity(), (entity1, entity2) -> entity1));
    }

    private Map<String, DeductCommodityDTO> getGradeCommodityBaseQOMap(List<HsaIntegralDeductCommodity> hsaIntegralDeductCommodityList) {
        return hsaIntegralDeductCommodityList
                .stream()
                .map(deductCommodity -> getCommodityBase(deductCommodity.getCommodityId(), deductCommodity.getCommodityCode(), deductCommodity.getCommodityName()))
                .collect(Collectors.toList())
                .stream()
                .collect(Collectors.toMap(DeductCommodityDTO::getDeductCommodityCode, Function.identity(), (entity1, entity2) -> entity1));
    }

    private static Map<String, HsaIntegralDeductCommodity> getHsaIntegralDeductCommodityMap(int businessType, List<HsaIntegralDeductCommodity> hsaGradeRightsCommodityRules) {
        return hsaGradeRightsCommodityRules
                .stream()
                .collect(Collectors.toMap(businessType == 0 ? HsaIntegralDeductCommodity::getCommodityCode : HsaIntegralDeductCommodity::getCommodityId, Function.identity(), (entity1, entity2) -> entity1));
    }


    private DeductCommodityDTO getCommodityBase(String productTemplateId, String commodityCode, String commodityName) {
        return new DeductCommodityDTO()
                .setCommodityId(productTemplateId)
                .setDeductCommodityCode(commodityCode)
                .setDeductCommodityName(commodityName);
    }
}
