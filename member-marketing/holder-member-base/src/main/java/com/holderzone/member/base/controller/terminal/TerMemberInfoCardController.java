package com.holderzone.member.base.controller.terminal;


import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.base.dto.MemberCardRechargeNewDTO;
import com.holderzone.member.base.service.card.HsaCardInfoService;
import com.holderzone.member.base.service.card.HsaMemberInfoCardService;
import com.holderzone.member.base.service.card.HsaPhysicalCardService;
import com.holderzone.member.base.service.card.impl.PayServiceImpl;
import com.holderzone.member.base.service.coupon.IHsaMemberCouponLinkService;
import com.holderzone.member.base.service.member.HsaCardBalanceRuleService;
import com.holderzone.member.base.service.member.HsaOperationMemberInfoService;
import com.holderzone.member.base.service.pay.MemberCardPayService;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.card.CardInfoDTO;
import com.holderzone.member.common.dto.card.CreateSecretDTO;
import com.holderzone.member.common.dto.member.EndConsumptionInfoDTO;
import com.holderzone.member.common.dto.member.MemberCardPayCallbackDTO;
import com.holderzone.member.common.dto.member.MemberCardPayDTO;
import com.holderzone.member.common.dto.member.MemberCardRefundDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.enums.card.CreateCardResultEnum;
import com.holderzone.member.common.qo.card.MemberFaceQO;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.equities.MemberCalculatePreMoneyQO;
import com.holderzone.member.common.qo.gift.TerCardRechargeDataVO;
import com.holderzone.member.common.qo.gift.TerRechargeActivityQO;
import com.holderzone.member.common.qo.member.EndConsumptionInfoQO;
import com.holderzone.member.common.vo.card.*;
import com.holderzone.member.common.vo.coupon.CouponGiveVO;
import com.holderzone.member.common.vo.coupon.MemberCouponLinkVO;
import com.holderzone.member.common.vo.member.HsaCardBalanceRuleVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 一体机会员卡相关
 */
@Slf4j
@RestController
@RequestMapping("/ter-card")
public class TerMemberInfoCardController {

    @Resource
    private HsaCardInfoService hsaCardInfoService;

    @Resource
    private IHsaMemberCouponLinkService memberCouponLinkService;

    @Resource
    private HsaPhysicalCardService hsaPhysicalCardService;

    @Resource
    private HsaMemberInfoCardService memberInfoCardService;

    @Resource
    private HsaOperationMemberInfoService hsaOperationMemberInfoService;

    @Resource
    private PayServiceImpl payService;

    @Resource
    private MemberCardPayService memberCardPayService;

    @Resource
    private HsaCardBalanceRuleService hsaCardBalanceRuleService;

    /**
     * 校验卡是否存在
     *
     * @param cardInfoDTO cardInfoDTO
     */
    @ApiOperation("校验卡是否存在(一体机)")
    @PostMapping(value = "/check_is_card_exist", produces = "application/json;charset=utf-8")
    Result<Boolean> isCardExist(@RequestBody CardInfoDTO cardInfoDTO) {
        return Result.success(hsaCardInfoService.isCardExist(cardInfoDTO.getCardNum()));
    }

    @ApiOperation("生成实体卡卡号")
    @PostMapping(value = "/produce_physical_card_number", produces = "application/json;charset=utf-8")
    public Result<CreateSecretDTO> producePhysicalCardNumber(@RequestBody ProducePhysicalCardQO request) {
        CreateSecretDTO produceSecretDTO = hsaCardInfoService.producePhysicalCardNumber(request);
        if (produceSecretDTO.getResultEnum() == CreateCardResultEnum.CREATE_RESULT_FAIL) {
            return Result.error(CreateCardResultEnum.CREATE_RESULT_FAIL.getDes());
        }
        return Result.success(produceSecretDTO);
    }

    @ApiOperation("重新生成实体卡号")
    @PostMapping(value = "/regenerate_physical_card_number", produces = "application/json;charset=utf-8")
    public Result<Void> regeneratePhysicalCardNumber(@RequestBody RegeneratePhysicalCardQO request) {
        hsaCardInfoService.regeneratePhysicalCardNumber(request);
        return Result.success();
    }

    /**
     * 校验卡是否绑定用户
     *
     * @param cardInfoDTO cardInfoDTO
     */
    @ApiOperation("校验卡是否绑定用户(一体机)")
    @PostMapping(value = "/is_binding_member_card", produces = "application/json;charset=utf-8")
    Result<BindingMemberCardVO> isBindingMemberCard(@RequestBody CardInfoDTO cardInfoDTO) {
        return Result.success(hsaCardInfoService.isBindingMemberCard(cardInfoDTO.getCardNum()));
    }

    /**
     * 实体卡激活
     *
     * @param cardInfoDTO cardInfoDTO
     */
    @ApiOperation("实体卡激活(一体机)")
    @PostMapping(value = "/card_activate", produces = "application/json;charset=utf-8")
    Result isCardActivate(@RequestBody CardInfoDTO cardInfoDTO) {
        return Result.success(hsaCardInfoService.cardActivate(cardInfoDTO));
    }

    /**
     * 实体卡更改卡状态
     *
     * @param cardInfoDTO cardInfoDTO
     */
    @ApiOperation("实体卡更改卡状态(一体机)")
    @PostMapping(value = "/update_card_status", produces = "application/json;charset=utf-8")
    public Result<CardActivateVO> updateCardStatus(@RequestBody CardInfoDTO cardInfoDTO) {
        CardActivateVO cardActivateVO = hsaCardInfoService.updateCardStatus(cardInfoDTO.getOperationType(), cardInfoDTO.getCardNum(), cardInfoDTO.getCardBindingNum());
        if (ObjectUtils.isEmpty(cardActivateVO)) {
            return Result.success();
        } else {
            return Result.success(cardActivateVO);
        }
    }

    @ApiOperation("更新实体卡写卡状态信息(一体机)")
    @PutMapping(value = "/update_write_card_status", produces = "application/json;charset=utf-8")
    public Result<Boolean> updateWriteCard(@RequestBody WriteCardQO writeCardQO) {
        return Result.isSuccess(hsaCardInfoService.updateWriteCardStatus(writeCardQO));
    }

    /**
     * 实体卡绑定账户
     *
     * @param bindingMemberInfoQO bindingMemberInfoQO
     * @return Boolean
     */
    @ApiOperation("实体卡绑定账户")
    @PostMapping(value = "/binding_member_info", produces = "application/json;charset=utf-8")
    Result<Boolean> bindingMemberInfo(@RequestBody BindingMemberInfoQO bindingMemberInfoQO) {
        return Result.success(memberInfoCardService.bindingMemberInfo(bindingMemberInfoQO));
    }

    /**
     * 会员充值登录
     *
     * @param terLoginMemberCardQO terLoginMemberCardQO
     * @return Result
     */
    @ApiOperation("登录获取账号卡信息")
    @PostMapping(value = "/login_member_card", produces = "application/json;charset=utf-8")
    Result<TerBaseLoginMemberCardVO> loginMemberCard(@RequestBody TerLoginMemberCardQO terLoginMemberCardQO) {
        log.info("[登录获取账号卡信息]terLoginMemberCardQO={}", JacksonUtils.writeValueAsString(terLoginMemberCardQO));
        return Result.success(hsaCardInfoService.loginMemberCard(terLoginMemberCardQO));
    }

    /**
     * 会员实体卡支付
     *
     * @param request
     * @return
     */
    @ApiOperation("会员实体卡支付")
    @PostMapping(value = "/card_pay_order", produces = "application/json;charset=utf-8")
    Result<ConsumptionRespVO> payOrder(@RequestBody RequestConfirmPayVO request) {
        return Result.success(payService.payOrder(request));
    }

    /**
     * 密码校验
     *
     * @return
     */
    @ApiOperation("密码校验")
    @PostMapping(value = "/check_card_pay_password", produces = "application/json;charset=utf-8")
    Result<Boolean> checkCardPayPassword(@RequestBody TerCheckPassword terCheckPassword) {
        return Result.success(hsaCardInfoService.checkCardPayPassword(terCheckPassword));
    }

    /**
     * 密码规则校验
     *
     * @return
     */
    @ApiOperation("密码规则校验")
    @GetMapping(value = "/check_card_pay_password_rule", produces = "application/json;charset=utf-8")
    Result<Boolean> checkCardPayPasswordRule() {
        return Result.success(hsaCardInfoService.checkCardPayPasswordRule());
    }


    /**
     * 获取会员卡余额规则
     * @return 操作结果
     */
    @GetMapping("/get/balance/rule")
    public Result<HsaCardBalanceRuleVO> getBalanceRule() {
        return Result.success(hsaCardBalanceRuleService.getBalanceRule());
    }

    /**
     * 会员卡充值赠送页面
     *
     * @return Result
     */
    @ApiOperation("会员卡充值赠送页面")
    @PostMapping(value = "/getTerRechargeList", produces = "application/json;charset=utf-8")
    public Result<TerCardRechargeDataVO> getTerRechargeList(@RequestBody TerRechargeActivityQO terRechargeActivityQO) {
        return Result.success(hsaCardInfoService.getTerRechargeList(terRechargeActivityQO));
    }

    @ApiOperation("根据会员卡金额查询预计到账金额")
    @PostMapping(value = "/calculate_pre_money")
    Result<RechargeThresholdVO> calculatePreMoney(@RequestBody MemberCalculatePreMoneyQO preMoneyQO) {
        return Result.success(memberInfoCardService.calculatePreMoney(preMoneyQO));
    }

    /**
     * 通过人脸，找到这个人所有的会员、亲属信息
     *
     * @param faceQO 人脸
     * @return Result
     */
    @ApiOperation("通过人脸，找到这个人所有的会员、亲属信息")
    @PostMapping(value = "/query_member_by_face", produces = "application/json;charset=utf-8")
    Result<List<MemberFaceVO>> queryMemberByFace(@RequestBody MemberFaceQO faceQO) {
        return Result.success(hsaCardInfoService.queryMemberByFace(faceQO));
    }

    /**
     * 会员卡充值
     *
     * @param terMemberCardRechargeQO terMemberCardRechargeQO
     * @return Result
     */
    @ApiOperation("会员卡充值")
    @PostMapping(value = "/recharge", produces = "application/json;charset=utf-8")
    Result<RechargeRespVO> memberCardRecharge(@RequestBody TerMemberCardRechargeQO terMemberCardRechargeQO) {
        MemberCardRechargeNewDTO memberCardRecharge = hsaCardInfoService.memberCardRecharge(terMemberCardRechargeQO);
        RechargeRespVO recharge = hsaCardInfoService.getTerRechargeRespVO(memberCardRecharge);
        return Result.success(recharge);
    }

    /**
     * 会员卡充值退款
     *
     * @param refundDTO
     * @return Result
     */
    @ApiOperation("一体机：会员卡充值退款")
    @PostMapping(value = "/recharge/refund", produces = "application/json;charset=utf-8")
    Result<AggRefundResult> rechargeRefund(@RequestBody RechargeRefundDTO refundDTO) {
        return Result.success(hsaCardInfoService.rechargeRefund(refundDTO));
    }


    /**
     * 现金支付记录
     *
     * @param request
     * @return
     */
    @ApiOperation("会员支付记录")
    @PostMapping(value = "/cash_pay_order", produces = "application/json;charset=utf-8")
    Result<ConsumptionRespVO> cashPayOrder(@RequestBody RequestConfirmPayVO request) {
        return Result.success(payService.cashPayOrder(request));
    }


    @ApiOperation("一体机会员卡预下单")
    @PostMapping(value = "/card_per_Pay")
    public Result<MemberCardPerPayVO> memberCardPerPay(@RequestBody MemberCardAggPayQO memberCardAggPayQO) {
        return Result.success(memberCardPayService.memberCardPerPay(memberCardAggPayQO));
    }

    @ApiOperation("聚合支付工具类回调")
    @PostMapping(value = "/callBack")
    public Result<Void> initializeSubjectData(@RequestBody @Validated AggPayCallbackDTO aggPayCallbackDTO) {
        memberCardPayService.callBack(aggPayCallbackDTO);
        return Result.success();
    }

    @ApiOperation("获取结果")
    @PostMapping(value = "/polling")
    public Result<MemberCardPollingPayVO> memberCardPolling(MemberCardPayPollingQO memberCardPayPollingQO) {
        return Result.success(memberCardPayService.memberCardPolling(memberCardPayPollingQO));
    }

    /**
     * 消费权益回调
     *
     * @param terOrderCallbackQO memberConsumptionGuid
     */
    @ApiOperation("消费权益回调")
    @PostMapping(value = "/order_rights_callback", produces = "application/json;charset=utf-8")
    void payOrderRightsCallback(@RequestBody TerOrderCallbackQO terOrderCallbackQO) {
        Integer isBoolean = hsaCardInfoService.payOrderRightsCallback(terOrderCallbackQO);
        log.info("消费权益回调结果：{}", isBoolean);
    }


    /**
     *  订单完成 优惠活动记录回调
     */
    @ApiOperation("优惠活动记录回调")
    @PostMapping(value = "/order_discount_callback", produces = "application/json;charset=utf-8")
    void afterOrderDiscountCallback(@RequestBody AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO) {
        hsaCardInfoService.afterOrderDiscountCallback(afterOrderDiscountCallbackQO);
    }


    /**
     * 订单退款 优惠活动记录回调
     */
    @ApiOperation("订单退款 优惠活动记录回调")
    @PostMapping(value = "/bark_order_discount_callback", produces = "application/json;charset=utf-8")
    void barkOrderDiscountCallback(@RequestBody BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO) {
        hsaCardInfoService.barkOrderDiscountCallback(barkOrderDiscountCallbackQO);
    }

    @GetMapping(value = "/updateData", produces = "application/json;charset=utf-8")
    void updateData() {
        //初始化会员等级
        hsaOperationMemberInfoService.initMemberGrade();
    }

    /**
     * 一体机查询会员卡详情
     *
     * @param memberInfoGuid 会员guid
     */
    @ApiOperation("一体机查询会员卡详情")
    @GetMapping(value = "/query_card_details", produces = "application/json;charset=utf-8")
    public Result<List<TerCardDetailsVO>> queryCardDetails(String memberInfoGuid, String storeGuid) {
        return Result.success(hsaCardInfoService.queryCardDetails(memberInfoGuid, storeGuid));
    }

    @ApiOperation("分页查询")
    @PostMapping(value = "/getMemberUploadPage", produces = "application/json;charset=utf-8")
    public Result<PageResult> getMemberUploadPage(@RequestBody MemberCardExcelQO cardName) {
        return Result.success(hsaCardInfoService.getMemberUploadPage(cardName));
    }

    /**
     * 功能描述：查询卡密
     */
    @ApiOperation("查询卡密")
    @PostMapping(value = "/getUploadCardSecretPage", produces = "application/json;charset=utf-8")
    public Result<PageResult> getUploadCardSecret(@RequestBody MemberCardExcelQO memberCardExcelQO) {
        return Result.success(hsaPhysicalCardService.getUploadCardSecret(memberCardExcelQO));
    }


    /**
     * 功能描述：查询未过期订单
     */
    @ApiOperation("查询未过期订单")
    @PostMapping(value = "/queryMemberNotOrder", produces = "application/json;charset=utf-8")
    public Result<List<EndConsumptionInfoDTO>> queryMemberNotOrder(@RequestBody EndConsumptionInfoQO request) {
        return Result.success(hsaCardInfoService.queryMemberOrder(request));
    }


    /**
     * 会员卡绑定uid
     *
     * @param terCardUidQO
     */
    @ApiOperation("会员卡绑定uid")
    @PostMapping(value = "/cardBindingUid", produces = "application/json;charset=utf-8")
    public Result cardBindingUid(@RequestBody TerCardUidQO terCardUidQO) {
        return Result.success(hsaCardInfoService.cardBindingUid(terCardUidQO));
    }

    /**
     * 通过uid获取实体卡号
     *
     * @param uid
     * @return
     */
    @ApiOperation("通过uid获取实体卡号")
    @GetMapping(value = "/getCardNumByUid", produces = "application/json;charset=utf-8")
    public Result getCardNumByUid(String uid) {
        return Result.success(hsaCardInfoService.getCardNumByUid(uid));
    }


    /**
     * 会员卡支付
     *
     * @param request 支付实体
     * @return 支付结果
     */
    @ApiOperation("会员卡支付")
    @PostMapping(value = "/member_card_pay", produces = "application/json;charset=utf-8")
    Result<ConsumptionRespVO> memberCardPay(@RequestBody RequestMemberCardPayVO request) {
        return Result.success(payService.memberCardPay(request));
    }

    /**
     * 商城会员卡支付
     *
     * @param dtoList 支付实体
     * @return 支付结果
     */
    @ApiOperation("会员卡支付")
    @PostMapping(value = "/member_card_pay_request", produces = "application/json;charset=utf-8")
    Result<Boolean> memberCardPayRequest(@RequestBody List<MemberCardPayDTO> dtoList) {
        return Result.success(payService.memberCardPayRequest(dtoList));
    }

    /**
     * 商城会员卡支付回调
     *
     * @param dtoList 支付实体
     * @return 支付结果
     */
    @ApiOperation("会员卡支付")
    @PostMapping(value = "/member_card_pay_callback", produces = "application/json;charset=utf-8")
    Result<Boolean> memberCardPayCallback(@RequestBody List<MemberCardPayCallbackDTO> dtoList) {
        return Result.success(payService.memberCardPayCallback(dtoList));
    }

    /**
     * 会员消费退款
     *
     * @param request
     * @return
     */
    @ApiOperation("会员消费退款")
    @PostMapping(value = "/member_order_refund", produces = "application/json;charset=utf-8")
    Result<ConsumptionRespVO> memberOrderRefund(@RequestBody RequestMemberOrderRefundVO request) {
        return Result.success(payService.memberOrderRefund(request));
    }

    /**
     * 商城会员卡退款
     *
     * @param dto 退款参数
     * @return 退款结果
     */
    @ApiOperation("会员消费退款")
    @PostMapping(value = "/member_card_refund_request", produces = "application/json;charset=utf-8")
    Result<Boolean> memberCardRefundRequest(@RequestBody MemberCardRefundDTO dto) {
        return Result.success(payService.memberCardRefundRequest(dto));
    }

    /**
     * 手机号后四位匹配
     *
     * @param phoneNum
     * @return
     */
    @ApiOperation("手机号后四位匹配")
    @GetMapping(value = "/member_phone_match", produces = "application/json;charset=utf-8")
    Result<List<MemberPhoneMathVO>> memberPhoneMatch(@RequestParam String phoneNum) {
        return Result.success(hsaCardInfoService.memberPhoneMatch(phoneNum));
    }

    /**
     * 会员支付记录
     *
     * @param request
     * @return
     */
    @ApiOperation("会员支付记录")
    @PostMapping(value = "/member_order_record", produces = "application/json;charset=utf-8")
    Result<ConsumptionRespVO> memberOrderRecord(@RequestBody RequestMemberCardPayVO request) {
        return Result.success(hsaCardInfoService.memberOrderRecord(request));
    }

    /**
     * 会员支付记录批量
     *
     * @param dtoList
     * @return
     */
    @ApiOperation("会员支付记录")
    @PostMapping(value = "/member_order_record_list", produces = "application/json;charset=utf-8")
    public Result<Boolean> memberCardPayRecordRequest(@RequestBody List<MemberCardPayDTO> dtoList) {
        return Result.success(payService.memberCardPayRecordRequest(dtoList));
    }

    /**
     * 校验卡号是否匹配
     * @param request
     * @return
     */
    @ApiOperation("校验卡号是否匹配")
    @PostMapping(value = "/check_matching_card_num", produces = "application/json;charset=utf-8")
    Result<Boolean> checkMatchingCardNum(@RequestBody RequestCheckMatchingCardVO request) {
        return Result.success(hsaCardInfoService.checkMatchingCardNum(request));
    }

    /**
     * 查询卡余额
     * @param memberInfoCardGuid
     * @return
     */
    @GetMapping(value = "/queryCardBalance", produces = "application/json;charset=utf-8")
    Result<BigDecimal> queryCardBalance(@RequestParam(value = "memberInfoCardGuid") String memberInfoCardGuid) {
        return Result.success(hsaCardInfoService.queryCardBalance(memberInfoCardGuid));
    }

    /**
     * 查询会员卡信息
     * @param requestMemberCardQO
     * @return
     */
    @PostMapping(value = "/queryCardByMember", produces = "application/json;charset=utf-8")
    Result<List<TerLoginMemberCardVO>> queryCardByMember(@RequestBody RequestMemberCardQO requestMemberCardQO) {
        return Result.success(hsaCardInfoService.queryCardByMember(requestMemberCardQO));
    }

    /**
     * 会员卡支付 前置校验
     */
    @PostMapping(value = "/preCheckMemberCardPay")
    Result<CheckMemberCardPayVO> preCheckMemberCardPay(@RequestBody CheckMemberCardPayQO checkMemberCardPayQO) {
        return Result.success(hsaCardInfoService.preCheckMemberCardPay(checkMemberCardPayQO));
    }

    /**
     * 会员卡支付 前置校验
     */
    @PostMapping(value = "/preCheckMemberCardPayNew", produces = "application/json;charset=utf-8")
    public String preCheckMemberCardPayNew(@RequestBody CheckMemberCardPayQO checkMemberCardPayQO) {
        return JSON.toJSONString(hsaCardInfoService.preCheckMemberCardPay(checkMemberCardPayQO));
    }

    /**
     * 获取会员当前可用积分
     * @param memberInfoGuid 会员guid
     * @return 可用积分
     */
    @GetMapping(value = "/getUsableIntegral", produces = "application/json;charset=utf-8")
    Result<Integer> getUsableIntegral(@RequestParam(value = "memberInfoGuid") String memberInfoGuid) {
        return Result.success(hsaCardInfoService.getUsableIntegral(memberInfoGuid));
    }

    /**
     * 根据券码查询会员手机号
     *
     * @param couponCode 券码
     * @return 手机号
     */
    @GetMapping(value = "/getMemberPhoneByCouponCode", produces = "application/json;charset=utf-8")
    Result<MemberCouponLinkVO> getMemberPhoneByCouponCode(@RequestParam(value = "couponCode") String couponCode) {
        return Result.success(memberCouponLinkService.getMemberPhoneByCouponCode(couponCode));
    }

    /**
     * 根据券码查询优惠券信息
     *
     * @param couponCode 券码
     */
    @GetMapping("/getByCouponCode")
    public Result<CouponGiveVO> getCouponGiveVOByCouponCode(@RequestParam(value = "couponCode") String couponCode) {
        return Result.success(memberCouponLinkService.getCouponGiveVOByCouponCode(couponCode));
    }
}
