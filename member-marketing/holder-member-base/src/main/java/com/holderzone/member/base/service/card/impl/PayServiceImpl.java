package com.holderzone.member.base.service.card.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.credit.HsaCreditInfo;
import com.holderzone.member.base.entity.credit.HsaCreditUser;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberConsumptionPayWay;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.base.event.MemberGradeChangeEvent;
import com.holderzone.member.base.mapper.credit.HsaCreditInfoMapper;
import com.holderzone.member.base.mapper.credit.HsaCreditUserMapper;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionMapper;
import com.holderzone.member.base.mapper.member.HsaMemberConsumptionPayWayMapper;
import com.holderzone.member.base.mapper.member.HsaOperationMemberInfoMapper;
import com.holderzone.member.base.mapper.purchase.HsaPurchaseOrderMapper;
import com.holderzone.member.base.service.card.CardPayService;
import com.holderzone.member.base.service.card.HsaCardInfoService;
import com.holderzone.member.base.service.card.business.TerCardCheckBusinessService;
import com.holderzone.member.base.service.credit.HsaCreditInfoService;
import com.holderzone.member.base.service.member.HsaLabelSettingService;
import com.holderzone.member.base.service.member.HsaMemberLabelService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.RequestPayInfoDTO;
import com.holderzone.member.common.dto.event.SendMemberGradeChangeEvent;
import com.holderzone.member.common.dto.member.MemberCardPayCallbackDTO;
import com.holderzone.member.common.dto.member.MemberCardPayDTO;
import com.holderzone.member.common.dto.member.MemberCardRefundDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.card.CardTypeEnum;
import com.holderzone.member.common.enums.mall.order.PayTypeEnum;
import com.holderzone.member.common.enums.member.LabelTriggerTypeEnum;
import com.holderzone.member.common.enums.member.MemberTerminalExceptionEnum;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.qo.card.RequestMemberCardPayVO;
import com.holderzone.member.common.qo.card.RequestMemberOrderRefundVO;
import com.holderzone.member.common.qo.card.TerOrderCallbackQO;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.card.ConsumptionRespVO;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.holderzone.member.common.constant.NumberConstant.NUMBER_0;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PayServiceImpl {

    private final HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Autowired
    @Lazy
    private HsaCardInfoService hsaCardInfoService;

    @Resource
    private TerCardCheckBusinessService terCardCheckBusinessService;

    @Autowired
    private SystemRoleHelper systemRoleHelper;

    @Autowired
    @Lazy
    private HsaCreditInfoService hsaCreditInfoService;

    @Autowired
    @Lazy
    private HsaLabelSettingService hsaLabelSettingService;

    @Resource
    private HsaCreditInfoMapper hsaCreditInfoMapper;

    @Resource
    private HsaCreditUserMapper hsaCreditUserMapper;

    @Resource
    private Executor memberBaseThreadExecutor;

    @Resource
    private HsaMemberLabelService hsaMemberLabelService;

    @Resource
    private HsaPurchaseOrderMapper hsaPurchaseOrderMapper;

    @Resource
    private CardPayService cardPayService;

    private final StringRedisTemplate stringRedisTemplate;

    private final MemberGradeChangeEvent memberGradeChangeEvent;

    private final HsaMemberConsumptionMapper hsaMemberConsumptionMapper;

    private final HsaMemberConsumptionPayWayMapper hsaMemberConsumptionPayWayMapper;

    private static final String CONSUMPTION_KEY = "CONSUMPTION_KEY";

    private static final String CONSUMPTION_VALUE = "1";

    public PayServiceImpl(HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper,
                          HsaCardInfoService hsaCardInfoService, StringRedisTemplate stringRedisTemplate,
                          MemberGradeChangeEvent memberGradeChangeEvent, HsaMemberConsumptionMapper hsaMemberConsumptionMapper, HsaMemberConsumptionPayWayMapper hsaMemberConsumptionPayWayMapper) {
        this.hsaOperationMemberInfoMapper = hsaOperationMemberInfoMapper;
        this.hsaCardInfoService = hsaCardInfoService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.memberGradeChangeEvent = memberGradeChangeEvent;
        this.hsaMemberConsumptionMapper = hsaMemberConsumptionMapper;
        this.hsaMemberConsumptionPayWayMapper = hsaMemberConsumptionPayWayMapper;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void cancelPay(List<HsaOperationMemberInfo> hsaOperationMemberInfos) {
        hsaOperationMemberInfoMapper.batchUpdateGrowth(hsaOperationMemberInfos);

    }

    public ConsumptionRespVO cashPayOrder(RequestConfirmPayVO request) {
        ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
        if (CollectionUtil.isNotEmpty(request.getRequestConfirmPayVOS())) {
            for (RequestConfirmPayVO requestConfirmPayVO : request.getRequestConfirmPayVOS()) {
                consumptionRespVO = hsaCardInfoService.cashPayOrder(requestConfirmPayVO);
            }
        } else {
            consumptionRespVO = hsaCardInfoService.cashPayOrder(request);
        }
        return consumptionRespVO;
    }

    @Resource
    private RedissonClient redissonClient;

    private final static String PAY_ORDER = "PAY_ORDER:";

    private static final String CARD_PAY_ORDER_MEMBER = "CARD_PAY_ORDER_MEMBER:";

    private static final String REFUND_ORDER_MEMBER = "REFUND_ORDER_MEMBER:";

    //@RedissonLock(lockName = "PAY_ORDER", tryLock = true, leaseTime = 5)
    public ConsumptionRespVO payOrder(RequestConfirmPayVO request) {
        ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
        //防重复提交
        RLock lock = redissonClient.getLock(PAY_ORDER + JSON.toJSONString(request));
        try {
            if (lock.isLocked()) {
                log.error("重复发起支付或退款，请求参数{}", JSON.toJSONString(request));
                return consumptionRespVO;
            }
            lock.lock();

            if (CollectionUtil.isNotEmpty(request.getRequestConfirmPayVOS())) {
                consumptionRespVO = dealBatchOrder(request, consumptionRespVO);
            } else {
                consumptionRespVO = dealOrder(request, consumptionRespVO);

            }
            log.info("payOrder返回值consumptionRespVO：--->{}", JSON.toJSONString(consumptionRespVO));
            dealRefresh(consumptionRespVO);
            return consumptionRespVO;
        } catch (Exception e) {
            log.error("会员支付或退款发生异常，请求参数{}", JSON.toJSONString(request), e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    public void dealRefresh(ConsumptionRespVO consumptionRespVO) {
        if (Objects.nonNull(consumptionRespVO) && !StringUtils.isEmpty(consumptionRespVO.getRedisConsumptionGuid())) {
            String isRefresh = stringRedisTemplate.opsForValue().get(CONSUMPTION_KEY + consumptionRespVO.getRedisConsumptionGuid());
            log.info("isRefresh:----->{}", isRefresh);
            if (Objects.nonNull(isRefresh) && CONSUMPTION_VALUE.equals(isRefresh)) {
                //刷新会员等级变更信息
                SendMemberGradeChangeEvent changeEvent = getMemberGradeChangeEvent(
                        consumptionRespVO.getOperSubjectGuid(), consumptionRespVO.getMemberInfoGuid());
                memberGradeChangeEvent.send(changeEvent);
            }
        }
        if (Objects.nonNull(consumptionRespVO) && StringUtils.isNotBlank(consumptionRespVO.getMemberInfoGuid())) {
            //刷新标签
            hsaLabelSettingService.refreshLabel(Lists.newArrayList(consumptionRespVO.getMemberInfoGuid()), null,
                    BooleanEnum.FALSE.getCode(), null, LabelTriggerTypeEnum.CONSUMPTION_INFO.getCode());
        }
    }

    /**
     * 订单单笔支付
     *
     * @param request           request
     * @param consumptionRespVO consumptionRespVO
     * @return ConsumptionRespVO
     */
    private ConsumptionRespVO dealOrder(RequestConfirmPayVO request, ConsumptionRespVO consumptionRespVO) {
        if (StringUtils.isEmpty(request.getMemberConsumptionGuid())
                && request.getRequestPayInfoList().size() == NumberConstant.NUMBER_1
                && (request.getRequestPayInfoList().get(NumberConstant.NUMBER_0).getPayWay() == NumberConstant.NUMBER_0
                || request.getRequestPayInfoList().get(NumberConstant.NUMBER_0).getPayWay() == NumberConstant.NUMBER_1)) {
            this.cashPayOrder(request);
        } else {
            consumptionRespVO = hsaCardInfoService.payOrder(request);
        }
        return consumptionRespVO;
    }

    /**
     * 订单批量支付
     *
     * @param request           request
     * @param consumptionRespVO consumptionRespVO
     * @return ConsumptionRespVO
     */
    private ConsumptionRespVO dealBatchOrder(RequestConfirmPayVO request, ConsumptionRespVO consumptionRespVO) {
        log.info("批量订单日志======>{}", JSONObject.toJSONString(request));
        //挂账支付总额
        BigDecimal creditPayAmount = request.getCreditPayAmount();
        //卡支付总额
        BigDecimal cardBalancePayAmount = request.getCardBalancePayAmount();
        //获取持卡对象
        BigDecimal total = null;

        if (StringUtils.isNotBlank(request.getRequestBaseInfo().getMemberInfoCardGuid())) {
            HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(request.getRequestBaseInfo().getMemberInfoCardGuid());
            HsaMemberInfoCard memberInfoCard = hsaCardInfoService.getHsaMemberInfoCard(request, BooleanEnum.FALSE.getCode(), hsaOperationMemberInfo);
            total = memberInfoCard.getCardAmount().add(memberInfoCard.getGiftAmount().add(memberInfoCard.getSubsidyAmount()));
        }

        if (Objects.nonNull(total) && Objects.nonNull(cardBalancePayAmount) && total.compareTo(cardBalancePayAmount) < 0) {
            throw new MemberBaseException(systemRoleHelper.getReplace(MemberTerminalExceptionEnum.ERROR_CARD_MONEY_LACK.getDes() + StringConstant.CURRENT_BALANCE + cardBalancePayAmount.subtract(total)
                    , ThreadLocalCache.getOperSubjectGuid()));
        }
        if (Objects.nonNull(request.getIsCredit())
                && request.getIsCredit() == BooleanEnum.TRUE.getCode()
                && Objects.nonNull(creditPayAmount)) {
            checkCreditPay(request, creditPayAmount);
        }
        long seconds = 0;
        for (RequestConfirmPayVO requestConfirmPayVO : request.getRequestConfirmPayVOS()) {
            LocalDateTime now = LocalDateTime.now().plusSeconds(seconds);
            requestConfirmPayVO.getRequestOrderInfo().setOrderTime(now);
            consumptionRespVO = hsaCardInfoService.payOrder(requestConfirmPayVO);
            seconds = seconds + 10L;
        }
        return consumptionRespVO;
    }

    /**
     * 校验挂账支付金额
     *
     * @param request         支付实体
     * @param creditPayAmount 挂账金额
     */
    private void checkCreditPay(RequestConfirmPayVO request, BigDecimal creditPayAmount) {
        HsaCreditUser hsaCreditUser = hsaCreditUserMapper.queryByGuid(request.getCreditUserGuid());
        log.info("当前挂账用户信息======>{}", JSONObject.toJSONString(hsaCreditUser));
        if (ObjectUtils.isEmpty(hsaCreditUser))
            throw new MemberBaseException("挂账用户信息不存在");
        HsaCreditInfo hsaCreditInfo = hsaCreditInfoMapper.queryByGuid(hsaCreditUser.getCreditInfoGuid());
        log.info("当前挂账信息======>{}", JSONObject.toJSONString(hsaCreditInfo));
        HsaOperationMemberInfo hsaOperationMemberInfo = hsaOperationMemberInfoMapper.queryByGuid(hsaCreditUser.getMemberInfoGuid());
        terCardCheckBusinessService.checkCreditUserCondition(request, hsaCreditUser, hsaCreditInfo, hsaOperationMemberInfo);
        //可用金额
        BigDecimal remainingAmount;
        if (hsaCreditUser.getSinglePersonUpperLimit().compareTo(BigDecimal.ZERO) == NumberConstant.NUMBER_0) {
            remainingAmount = hsaCreditInfoService.getRemainingAmount(hsaCreditInfo.getGuid(), hsaCreditInfo.getCreditLimitedAmount(), hsaCreditInfo.getCreditLimitedSet());
        } else {
            remainingAmount = hsaCreditUser.getSinglePersonUpperLimit().subtract(hsaCreditUser.getTotalCredit());
        }
        //余额判断
        checkRemainingAmount(creditPayAmount, remainingAmount);
        //单笔上限判断
        if (hsaCreditUser.getSingleCountUpperLimit().compareTo(BigDecimal.ZERO) > NumberConstant.NUMBER_0) {
            for (RequestConfirmPayVO requestConfirmPayVO : request.getRequestConfirmPayVOS()) {
                if (requestConfirmPayVO.getCreditPayAmount().compareTo(hsaCreditUser.getSingleCountUpperLimit()) > NUMBER_0) {
                    log.error("===========>单笔超过挂账上限：" + hsaCreditUser.getSingleCountUpperLimit());
                    throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_SINGLE_OVER.getCode(),
                            MemberTerminalExceptionEnum.ERROR_CREDIT_SINGLE_OVER.getDes() + "账户单笔上限 ¥" + hsaCreditUser.getSingleCountUpperLimit());
                }
            }
        }
    }

    private void checkRemainingAmount(BigDecimal creditPayAmount, BigDecimal remainingAmount) {
        if (remainingAmount.compareTo(new BigDecimal("-1")) != NUMBER_0 && creditPayAmount.compareTo(remainingAmount) > NUMBER_0) {
            log.error("===========>剩余挂账金额不足：" + remainingAmount);
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING.getCode(), MemberTerminalExceptionEnum.ERROR_CREDIT_INSUFFICIENT_AMOUNT_REMAINING.getDes() + "剩余挂账金额 ¥" + remainingAmount);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public HsaMemberConsumption rightsCallback(TerOrderCallbackQO terOrderCallbackQO) {
        log.info("消费回调==============> terOrderCallbackQO={}", terOrderCallbackQO);
        if (Objects.isNull(terOrderCallbackQO)) {
            log.info("缺少必要参数，memberConsumptionGuid={}", terOrderCallbackQO);
            return null;
        }
        HsaMemberConsumption hsaMemberConsumption;
        if (!StringUtils.isEmpty(terOrderCallbackQO.getMemberConsumptionGuid())) {
            hsaMemberConsumption = hsaMemberConsumptionMapper.queryByGuid(terOrderCallbackQO.getMemberConsumptionGuid());
        } else {
            hsaMemberConsumption = hsaMemberConsumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                    .eq(HsaMemberConsumption::getOrderNumber, terOrderCallbackQO.getOrderNum()));
        }
        if (Objects.isNull(hsaMemberConsumption)) {
            log.info("消费记录不存在，memberConsumptionGuid={}", terOrderCallbackQO.getMemberConsumptionGuid());
            return null;
        }
        if (StringUtils.isEmpty(hsaMemberConsumption.getMemberInfoGuid())) {
            log.info("非会员消费，memberConsumptionGuid={}", terOrderCallbackQO.getMemberConsumptionGuid());
            return null;
        }
        if (hsaMemberConsumption.getIsComplete() == BooleanEnum.TRUE.getCode()) {
            log.info("此订单已完成成长值触发，memberConsumptionGuid={}", hsaMemberConsumption);
            return null;
        }
        hsaMemberConsumption.setIsComplete(BooleanEnum.TRUE.getCode());
        hsaMemberConsumptionMapper.updateByGuid(hsaMemberConsumption);
        return hsaMemberConsumption;
    }

    private SendMemberGradeChangeEvent getMemberGradeChangeEvent(String operSubjectGuid, String memberInfoGuid) {
        SendMemberGradeChangeEvent event = new SendMemberGradeChangeEvent();
        event.setOperSubjectGuid(operSubjectGuid);
        event.setMemberGuidList(Collections.singletonList(memberInfoGuid));
        event.setSourceType(SourceTypeEnum.ADD_BACKGROUND.getCode());
        event.setIsRefresh(BooleanEnum.FALSE.getCode());
        log.info("等级变更消息体：{}", event);
        return event;
    }


    /**
     * 会员卡支付
     */
    public ConsumptionRespVO memberCardPay(RequestMemberCardPayVO request) {
        ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();

        //防重复提交
        RLock lock = redissonClient.getLock(CARD_PAY_ORDER_MEMBER + request.getOrderNumber());
        try {
            if (lock.isLocked()) {
                log.error("重复发起支付，请求订单号{}", request.getOrderNumber());
                return consumptionRespVO;
            }
            lock.lock();

            consumptionRespVO = cardPayService.memberCardPay(request);

            log.info("会员支付返回值：--->{}", JSON.toJSONString(consumptionRespVO));
            dealRefresh(consumptionRespVO);
            return consumptionRespVO;
        } catch (Exception e) {
            log.error("会员支付发生异常，请求参数{}", JSON.toJSONString(request), e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 构建会员卡支付参数
     *
     * @param dto dto
     * @return 消费记录
     */
    private RequestMemberCardPayVO buildMemberCardPayRequest(MemberCardPayDTO dto) {
        RequestMemberCardPayVO request = new RequestMemberCardPayVO();
        request.setIsCredit(NumberConstant.NUMBER_0);
        request.setCardType(CardTypeEnum.CARD_TYPE_EQUITY.getCode());
        request.setMemberInfoGuid(dto.getMemberInfoGuid());
        request.setMemberInfoCardGuid(dto.getMemberInfoCardGuid());
        request.setOrderNumber(dto.getOrderNumber());
        request.setOrderType(dto.getOrderType());
        request.setStoreGuid(dto.getStoreGuid());
        request.setStoreName(dto.getStoreName());
        request.setOrderTime(dto.getOrderTime());
        // 设置支付信息
        RequestPayInfoDTO requestPayInfoDTO = new RequestPayInfoDTO();
        requestPayInfoDTO.setPayWay(PayTypeEnum.getByPlatform(dto.getPayType()));
        requestPayInfoDTO.setPayAmount((dto.getCardBalancePayAmount()));
        request.setRequestPayInfoList(Collections.singletonList(requestPayInfoDTO));

        // 订单金额
        request.setOrderPaymentAmount(dto.getOrderAmount());
        // 卡支付金额
        request.setCardBalancePayAmount(dto.getCardBalancePayAmount());
        // 优惠金额
        request.setOrderDiscountAmount(dto.getOrderDiscountAmount());
        // 订单实付金额
        request.setOrderRealPaymentAmount(dto.getCardBalancePayAmount());
        return request;
    }

    public Boolean memberCardPayRecordRequest(List<MemberCardPayDTO> dtoList) {
        log.info("批量会员同步支付记录请求参数：--->{}", JSON.toJSONString(dtoList));
        List<RequestMemberCardPayVO> requestMemberCardPayVOS = dtoList.stream().map(this::buildMemberCardPayRequest).collect(Collectors.toList());

        try {
            for (RequestMemberCardPayVO request : requestMemberCardPayVOS) {
                ConsumptionRespVO result = hsaCardInfoService.memberOrderRecord(request);
                log.info("批量会员同步支付记录返回值：--->{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("批量会员同步支付记录支付失败", e);
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        }
        return Boolean.TRUE;
    }

    /**
     * 商城会员卡支付
     *
     * @param dtoList 请求参数
     * @return 支付结果
     */
    public Boolean memberCardPayRequest(List<MemberCardPayDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            log.info("会员卡支付请求参数为空");
            return Boolean.FALSE;
        }

        String[] orderNumberSplit = dtoList.get(0).getOrderNumber().split("-");
        RLock lock = redissonClient.getLock(CARD_PAY_ORDER_MEMBER + orderNumberSplit[0]);
        try {
            if (lock.isLocked()) {
                log.info("重复发起支付，请求订单号{}", orderNumberSplit[0]);
                return Boolean.FALSE;
            }
            lock.lock();

            List<ConsumptionRespVO> consumptionRespVOS = cardPayService.batchMemberCardPay(dtoList.stream().map(this::buildMemberCardPayRequest).collect(Collectors.toList()));

            log.info("会员支付返回值：--->{}", JSON.toJSONString(consumptionRespVOS));
            consumptionRespVOS.forEach(this::dealRefresh);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("商城会员卡支付发生异常，请求参数{}", JSON.toJSONString(dtoList), e);
            return Boolean.FALSE;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 商城会员卡支付回调
     *
     * @param dtoList 支付实体
     * @return 支付结果
     */
    public Boolean memberCardPayCallback(List<MemberCardPayCallbackDTO> dtoList) {
        //消费权益回调
        dtoList.forEach(dto -> {
            TerOrderCallbackQO terOrderCallbackQO = new TerOrderCallbackQO();
            terOrderCallbackQO.setOrderNum(dto.getOrderNumber());
            hsaCardInfoService.payOrderRightsCallback(terOrderCallbackQO);
        });

        return Boolean.TRUE;
    }

    /**
     * 会员消费退款
     */
    public ConsumptionRespVO memberOrderRefund(RequestMemberOrderRefundVO request) {
        ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();

        //防重复提交
        RLock lock = redissonClient.getLock(REFUND_ORDER_MEMBER + request.getOrderNumber());
        try {
            if (lock.isLocked()) {
                log.error("重复发起退款，请求订单号{}", request.getOrderNumber());
                return consumptionRespVO;
            }
            lock.lock();

            consumptionRespVO = cardPayService.memberOrderRefund(request);

            log.info("会员退款返回参数：--->{}", JSON.toJSONString(consumptionRespVO));
            dealRefresh(consumptionRespVO);
            return consumptionRespVO;
        } catch (Exception e) {
            log.error("会员退款发生异常，请求参数{},异常原因={}", JSON.toJSONString(request), e.getMessage());
            throw new MemberBaseException(((MemberBaseException) e).getCode(), ((MemberBaseException) e).getDes());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 商城退款
     *
     * @param dto 请求参数
     * @return 支付结果
     */
    public Boolean memberCardRefundRequest(MemberCardRefundDTO dto) {
        try {
            memberOrderRefund(buildMemberOrderRefundRequest(dto));
        } catch (Exception e) {
            log.info("商城会员退款发生异常，请求参数{},异常原因={}", JSON.toJSONString(dto), e.getMessage());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 构建商城退款参数
     *
     * @param dto 退款请求参数
     * @return 退款请求对象
     */
    private RequestMemberOrderRefundVO buildMemberOrderRefundRequest(MemberCardRefundDTO dto) {
        // 1. 查找消费记录
        String actualOrderNumber = findActualOrderNumber(dto.getOrderNumber());
        HsaMemberConsumption consumption = getHsaMemberConsumption(actualOrderNumber);

        // 2. 创建退款请求对象
        RequestMemberOrderRefundVO request = createRefundRequest(actualOrderNumber);

        // 3. 判断退款类型并设置支付信息
        determineRefundTypeAndSetPayInfo(dto, consumption, request);

        // 4. 设置退款金额信息
        setRefundAmountInfo(dto, request);

        return request;
    }

    /**
     * 查找实际订单号
     *
     * @param orderNumber 原始订单号
     * @return 实际订单号
     */
    private String findActualOrderNumber(String orderNumber) {
        String actualOrderNumber = orderNumber;
        HsaMemberConsumption consumption = getHsaMemberConsumption(actualOrderNumber);

        if (Objects.isNull(consumption)) {
            actualOrderNumber = handleMallOrderNotFound(orderNumber);
            consumption = getHsaMemberConsumption(actualOrderNumber);
            if (Objects.isNull(consumption)) {
                throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_MEMBER_ORDER_NOT_EXIT);
            }
        }

        return actualOrderNumber;
    }

    /**
     * 处理商城订单未找到的情况
     *
     * @param orderNumber 原始订单号
     * @return 处理后的订单号
     */
    private String handleMallOrderNotFound(String orderNumber) {
        if (SourceTypeEnum.isMallSource(ThreadLocalCache.getSource())) {
            String mallOrderNumber = orderNumber + "-1";
            log.info("尝试查询商城订单，订单号：{}", mallOrderNumber);
            return mallOrderNumber;
        } else {
            throw new MemberBaseException(MemberTerminalExceptionEnum.ERROR_MEMBER_ORDER_NOT_EXIT);
        }
    }

    /**
     * 创建退款请求对象
     *
     * @param orderNumber 订单号
     * @return 退款请求对象
     */
    private RequestMemberOrderRefundVO createRefundRequest(String orderNumber) {
        RequestMemberOrderRefundVO request = new RequestMemberOrderRefundVO();
        request.setOrderNumber(orderNumber);
        // 默认整单退款
        request.setRefundType(NumberConstant.NUMBER_1);
        return request;
    }

    /**
     * 判断退款类型并设置支付信息
     *
     * @param dto         退款请求参数
     * @param consumption 消费记录
     * @param request     退款请求对象
     */
    private void determineRefundTypeAndSetPayInfo(MemberCardRefundDTO dto,
                                                HsaMemberConsumption consumption,
                                                RequestMemberOrderRefundVO request) {
        log.info("退款订单金额：{}，订单金额：{}", dto.getOrderAmount(), consumption.getOrderAmount());

        if (isPartialRefund(dto, consumption)) {
            handlePartialRefund(dto, request);
        } else {
            handleFullRefund(dto, consumption, request);
        }
    }

    /**
     * 判断是否为部分退款
     *
     * @param dto         退款请求参数
     * @param consumption 消费记录
     * @return 是否为部分退款
     */
    private boolean isPartialRefund(MemberCardRefundDTO dto, HsaMemberConsumption consumption) {
        return dto.getRefundAmount().compareTo(consumption.getCardBalancePayAmount()) < 0
                || (Objects.nonNull(dto.getOrderAmount()) && dto.getOrderAmount().compareTo(consumption.getOrderAmount()) < 0);
    }

    /**
     * 处理部分退款
     *
     * @param dto     退款请求参数
     * @param request 退款请求对象
     */
    private void handlePartialRefund(MemberCardRefundDTO dto, RequestMemberOrderRefundVO request) {
        // 部分退款
        request.setRefundType(NUMBER_0);

        // 设置支付信息
        RequestPayInfoDTO requestPayInfoDTO = createPartialRefundPayInfo(dto);
        request.setRequestPayInfoList(Collections.singletonList(requestPayInfoDTO));
    }

    /**
     * 创建部分退款支付信息
     *
     * @param dto 退款请求参数
     * @return 支付信息
     */
    private RequestPayInfoDTO createPartialRefundPayInfo(MemberCardRefundDTO dto) {
        RequestPayInfoDTO requestPayInfoDTO = new RequestPayInfoDTO();
        requestPayInfoDTO.setPayWay(dto.getPayWay() == null ? PayWayEnum.CARD_BALANCE_PAY.getCode() : dto.getPayWay());
        requestPayInfoDTO.setPayName(StrUtil.isEmpty(dto.getPayName()) ? PayWayEnum.CARD_BALANCE_PAY.getDes() : dto.getPayName());
        requestPayInfoDTO.setPayAmount(dto.getRefundAmount());
        return requestPayInfoDTO;
    }

    /**
     * 处理整单退款
     *
     * @param dto         退款请求参数
     * @param consumption 消费记录
     * @param request     退款请求对象
     */
    private void handleFullRefund(MemberCardRefundDTO dto,
                                HsaMemberConsumption consumption,
                                RequestMemberOrderRefundVO request) {
        List<HsaMemberConsumptionPayWay> hsaMemberConsumptionPayWays = buildFullRefundPayWays(dto, consumption);
        List<RequestPayInfoDTO> requestPayInfoList = getRequestPayInfoDTOS(hsaMemberConsumptionPayWays);
        request.setRequestPayInfoList(requestPayInfoList);
    }

    /**
     * 构建整单退款支付方式列表
     *
     * @param dto         退款请求参数
     * @param consumption 消费记录
     * @return 支付方式列表
     */
    private List<HsaMemberConsumptionPayWay> buildFullRefundPayWays(MemberCardRefundDTO dto,
                                                                  HsaMemberConsumption consumption) {
        List<HsaMemberConsumptionPayWay> hsaMemberConsumptionPayWays = new ArrayList<>();

        if (dto.getPayWay() == null) {
            // 查询原始支付方式
            hsaMemberConsumptionPayWays = hsaMemberConsumptionPayWayMapper.selectList(
                new LambdaQueryWrapper<HsaMemberConsumptionPayWay>()
                    .eq(HsaMemberConsumptionPayWay::getConsumptionGuid, consumption.getGuid())
            );
        } else {
            // 自定义退款支付方式
            HsaMemberConsumptionPayWay customPayWay = createCustomRefundPayWay(dto);
            hsaMemberConsumptionPayWays.add(customPayWay);
        }

        return hsaMemberConsumptionPayWays;
    }

    /**
     * 创建自定义退款支付方式
     *
     * @param dto 退款请求参数
     * @return 自定义支付方式
     */
    private HsaMemberConsumptionPayWay createCustomRefundPayWay(MemberCardRefundDTO dto) {
        HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay = new HsaMemberConsumptionPayWay();
        hsaMemberConsumptionPayWay.setPayName(dto.getPayName());
        hsaMemberConsumptionPayWay.setPayWay(dto.getPayWay());
        hsaMemberConsumptionPayWay.setPayAmount(dto.getRefundAmount());
        return hsaMemberConsumptionPayWay;
    }

    /**
     * 设置退款金额信息
     *
     * @param dto     退款请求参数
     * @param request 退款请求对象
     */
    private void setRefundAmountInfo(MemberCardRefundDTO dto, RequestMemberOrderRefundVO request) {
        request.setOrderAmount(dto.getOrderAmount());
        request.setRefundAmount(dto.getRefundAmount());
        request.setDiscountAmount(dto.getDiscountAmount());
    }



    private static void wrapRequestPayInfoDTO (MemberCardRefundDTO dto, RequestPayInfoDTO requestPayInfoDTO, RequestMemberOrderRefundVO request) {
        requestPayInfoDTO.setPayWay(dto.getPayWay() == null ? PayWayEnum.CARD_BALANCE_PAY.getCode() : dto.getPayWay());
        requestPayInfoDTO.setPayName(StrUtil.isEmpty(dto.getPayName()) ? PayWayEnum.CARD_BALANCE_PAY.getDes() : dto.getPayName());
        requestPayInfoDTO.setPayAmount(dto.getRefundAmount());
        request.setRequestPayInfoList(Collections.singletonList(requestPayInfoDTO));
    }

    private HsaMemberConsumption getHsaMemberConsumption(String orderNumber) {
        return hsaMemberConsumptionMapper.selectOne(new LambdaQueryWrapper<HsaMemberConsumption>()
                .eq(HsaMemberConsumption::getOrderNumber, orderNumber)
                .eq(HsaMemberConsumption::getIsCancel, BooleanEnum.FALSE.getCode()));
    }

    private static List<RequestPayInfoDTO> getRequestPayInfoDTOS(List<HsaMemberConsumptionPayWay> hsaMemberConsumptionPayWays) {
        List<RequestPayInfoDTO> requestPayInfoList = Lists.newArrayList();
        for (HsaMemberConsumptionPayWay hsaMemberConsumptionPayWay : hsaMemberConsumptionPayWays) {
            // 设置支付信息
            RequestPayInfoDTO requestPayInfoDTO = new RequestPayInfoDTO();
            requestPayInfoDTO.setPayWay(hsaMemberConsumptionPayWay.getPayWay());
            requestPayInfoDTO.setPayName(hsaMemberConsumptionPayWay.getPayName());
            requestPayInfoDTO.setPayAmount(hsaMemberConsumptionPayWay.getPayAmount());
            requestPayInfoList.add(requestPayInfoDTO);
        }
        return requestPayInfoList;
    }
}
