package com.holderzone.member.base.service.card;


import com.holderzone.member.base.dto.MemberCardRechargeNewDTO;
import com.holderzone.member.base.entity.card.HsaCardBaseInfo;
import com.holderzone.member.base.entity.card.HsaMemberInfoCard;
import com.holderzone.member.base.entity.member.HsaMemberConsumption;
import com.holderzone.member.base.entity.member.HsaMemberConsumptionPayWay;
import com.holderzone.member.base.entity.member.HsaMemberFundingDetail;
import com.holderzone.member.base.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.common.dto.card.CardInfoDTO;
import com.holderzone.member.common.dto.card.CreateSecretDTO;
import com.holderzone.member.common.dto.member.EndConsumptionInfoDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.qo.card.MemberFaceQO;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.gift.TerCardRechargeDataVO;
import com.holderzone.member.common.qo.gift.TerRechargeActivityQO;
import com.holderzone.member.common.qo.member.EndConsumptionInfoQO;
import com.holderzone.member.common.vo.card.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

public interface HsaCardInfoService {

    Boolean isCardExist(String cardGuid);

    BindingMemberCardVO isBindingMemberCard(String cardNum);

    CardActivateVO cardActivate(CardInfoDTO cardInfoDTO);

    CardActivateVO queryPhysicalCardInfo(CardInfoDTO cardInfoDTO);

    CardActivateVO updateCardStatus(Integer type, String cardNum, String cardBindingNum);

    boolean updateWriteCardStatus(WriteCardQO writeCardQO);

    TerBaseLoginMemberCardVO loginMemberCard(TerLoginMemberCardQO terLoginMemberCardQO);

    TerCardRechargeDataVO getTerRechargeList(TerRechargeActivityQO terRechargeActivityQO);

    /**
     * 通过人脸，找到这个人所有的会员、亲属信息
     * <p>
     * 通过人脸查询到会员或亲属，再通过结果的手机号查询会员、亲属
     *
     * @param memberFaceQO
     * @return
     */
    List<MemberFaceVO> queryMemberByFace(MemberFaceQO memberFaceQO);

    /**
     * 一体机会员卡充值
     *
     * @param terMemberCardRechargeQO
     * @return
     */
    MemberCardRechargeNewDTO memberCardRecharge(TerMemberCardRechargeQO terMemberCardRechargeQO);

    RechargeRespVO getTerRechargeRespVO(MemberCardRechargeNewDTO memberCardRechargeNewDTO);

    /**
     * 超额处理
     *
     * @param terMemberCardRechargeQO terMemberCardRechargeQO
     * @param hsaMemberInfoCard       hsaMemberInfoCard
     * @param hsaCardBaseInfo         hsaCardBaseInfo
     */
    void excessProcessor(TerMemberCardRechargeQO terMemberCardRechargeQO, HsaMemberInfoCard hsaMemberInfoCard, HsaCardBaseInfo hsaCardBaseInfo);

    /**
     * 新会员卡通用充值
     *
     * @param terMemberCardRechargeQO
     * @return
     */
    MemberCardRechargeNewDTO memberCardRechargeNew(TerMemberCardRechargeQO terMemberCardRechargeQO);

    AggPayRespDTO rechargePay(TerMemberCardRechargeQO terMemberCardRechargeQO, HttpServletRequest request);

    void rechargeCallback(SaasNotifyDTO saasNotifyDTO);

    RechargeRespVO rechargePreOrder(TerMemberCardRechargeQO terMemberCardRechargeQO);

    RechargeRespVO getRechargeRespVO(MemberCardRechargeNewDTO cardRechargeNewDTO);

    void rechargeTaskBusiness(HeaderUserInfo header, HsaMemberInfoCard hsaMemberInfoCard, HsaMemberConsumption memberConsumptionGuid);

    HsaMemberConsumption getMemberRechargeConsumption(HeaderUserInfo headerUserInfo, TerMemberCardRechargeQO
            terMemberCardRechargeQO, HsaMemberInfoCard memberInfoCard);

    HsaMemberConsumptionPayWay getRechargePayWay(TerMemberCardRechargeQO terMemberCardRechargeQO, String memberConsumptionGuid);

    CreateSecretDTO createPhysicalCardByStrategy(ProducePhysicalCardQO producePhysicalCardQO);

    CreateSecretDTO callbackCreatePhysicalCard(ProducePhysicalCardQO producePhysicalCardQO);

    CreateSecretDTO producePhysicalCardNumber(ProducePhysicalCardQO producePhysicalCardQO);

    void physicalMarkResult(RegeneratePhysicalCardQO request);

    void regeneratePhysicalCardNumber(RegeneratePhysicalCardQO request);

    /**
     * 实体卡支付
     *
     * @param request
     * @return
     */
    ConsumptionRespVO payOrder(RequestConfirmPayVO request);

    /**
     * 获取卡
     *
     * @param request request
     * @return HsaMemberInfoCard
     */
    HsaMemberInfoCard getHsaMemberInfoCard(RequestConfirmPayVO request, Integer isCredit, HsaOperationMemberInfo hsaOperationMemberInfo);

    /**
     * 补贴扣减规则
     *
     * @param memberInfoCard      memberInfoCard
     * @param subsidyReduceAmount subsidyReduceAmount
     */
    void subsidyMoneyReduceProcessor(HsaMemberInfoCard memberInfoCard, BigDecimal subsidyReduceAmount);

    /**
     * 密码校验
     *
     * @return
     */
    Boolean checkCardPayPassword(TerCheckPassword terCheckPassword);

    /**
     * 密码规则校验
     *
     * @return
     */
    Boolean checkCardPayPasswordRule();

    /**
     * 现金支付记录
     *
     * @param request
     * @return
     */
    ConsumptionRespVO cashPayOrder(RequestConfirmPayVO request);

    /**
     * 消费权益回调
     *
     * @param terOrderCallbackQO
     */
    Integer payOrderRightsCallback(TerOrderCallbackQO terOrderCallbackQO);

    /**
     * 订单完成后 优惠活动记录回调
     */
    void afterOrderDiscountCallback(AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO);

    /**
     * 订单退款 优惠活动记录回调
     */
    void barkOrderDiscountCallback(BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO);

    /**
     * 一体机查询会员卡详情列表
     *
     * @param memberInfoGuid 会员guid
     * @param storeGuid      门店guid
     * @return 会员卡详情信息List
     */
    List<TerCardDetailsVO> queryCardDetails(String memberInfoGuid, String storeGuid);

    PageResult getMemberUploadPage(MemberCardExcelQO qo);

    List<EndConsumptionInfoDTO> queryMemberOrder(EndConsumptionInfoQO request);

    /**
     * 会员卡绑定uid
     *
     * @param terCardUidQO
     */
    boolean cardBindingUid(TerCardUidQO terCardUidQO);

    /**
     * 通过uid获取卡号
     *
     * @param uid
     * @return
     */
    String getCardNumByUid(String uid);

    /**
     * 充值退款
     *
     * @param refundDTO
     */
    AggRefundResult rechargeRefund(RechargeRefundDTO refundDTO);

    AggRefundResult rechargeRefundCallback(RechargeRefundCallbackDTO refundDTO);

    /**
     * 手机号后四位搜索
     * @param phoneNum
     * @return
     */
    List<MemberPhoneMathVO> memberPhoneMatch(String phoneNum);

    /**
     * 批量会员卡支付记录
     * @param requestList
     * @return
     */
    List<ConsumptionRespVO> batchMemberCardPayRecord(List<RequestMemberCardPayVO> requestList);

    /**
     * 会员消费记录
     * @param request
     * @return
     */
    ConsumptionRespVO memberOrderRecord(RequestMemberCardPayVO request);

    /**
     * 校验卡号是否匹配
     * @param request
     * @return
     */
    Boolean checkMatchingCardNum(RequestCheckMatchingCardVO request);

    /**
     * 查询卡余额
     * @param memberInfoCardGuid
     * @return
     */
    BigDecimal queryCardBalance(String memberInfoCardGuid);


    /**
     * 查询会员卡信息
     * @param requestMemberCardQO
     * @return
     */
    List<TerLoginMemberCardVO> queryCardByMember(RequestMemberCardQO requestMemberCardQO);


    /**
     * 会员卡支付 前置校验
     */
    CheckMemberCardPayVO preCheckMemberCardPay(CheckMemberCardPayQO checkMemberCardPayQO);

    /**
     * 获取会员当前可用积分
     * @param memberInfoGuid 会员guid
     * @return 可用积分
     */
    Integer getUsableIntegral(String memberInfoGuid);


    void sendChangeCardAmountShortMessage(HsaMemberInfoCard memberInfoCard,
                                          HsaMemberConsumption consumption,
                                          HsaOperationMemberInfo hsaOperationMemberInfo,
                                          HsaMemberFundingDetail hsaMemberFundingDetail);


    void sendChangeCardAmountMsg(HsaMemberInfoCard memberInfoCard,
                                 HsaMemberConsumption consumption,
                                 HsaOperationMemberInfo hsaOperationMemberInfo,
                                 HsaMemberFundingDetail hsaMemberFundingDetail);
}
