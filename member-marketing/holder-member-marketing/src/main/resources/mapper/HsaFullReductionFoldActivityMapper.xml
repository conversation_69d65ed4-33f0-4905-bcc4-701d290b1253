<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityMapper">

    <select id="query" resultType="com.holderzone.member.common.vo.activity.FullReductionFoldActivityPageVO">
        select
        guid,
        activity_name,
        activity_code,
        start_time,
        end_time,
        discount_sill_type,
        full_reduction_fold_json,
        full_reduction_tactics_type,
        apply_business_json AS activityBusiness,
        0 AS activityItem,
        0 AS activityOrder,
        state
        from
        hsa_full_reduction_fold_activity
        <where>
            is_delete = 0
            and oper_subject_guid = #{query.operSubjectGuid}
            <if test="query.keywords != null and query.keywords != ''">
                and (activity_name like concat('%',#{query.keywords},'%')
                or activity_code like concat('%',#{query.keywords},'%'))
            </if>
            <if test="query.startTime != null and query.endTime != null">
                <![CDATA[ AND (
                    ( start_time <= #{query.startTime} AND end_time >= #{query.startTime} )
                    or
                    ( start_time <= #{query.endTime} AND end_time >= #{query.endTime} )
                    or
                    ( start_time >= #{query.startTime} AND start_time <= #{query.endTime} )
                    or
                    ( end_time >= #{query.startTime} AND end_time <= #{query.endTime} )
                ) ]]>
            </if>
            <if test="query.business != null">
                AND apply_business_json like concat('%',#{query.business},'%')
            </if>

            <if test="query.state != null">
                <choose>
                    <when test="query.state == 4">
                        AND state = 1
                        <![CDATA[AND start_time > now()]]>
                    </when>
                    <when test="query.state == 1">
                        AND state = 1
                        AND <![CDATA[ start_time <= now() ]]>
                        AND <![CDATA[ end_time >= now() ]]>
                    </when>
                    <when test="query.state == 3">
                        AND <![CDATA[ state <> 0 ]]>
                        AND <![CDATA[ end_time < now() ]]>
                    </when>
                    <when test="query.state == 2">
                        AND <![CDATA[ state = 2 ]]>
                        AND <![CDATA[ end_time > now() ]]>
                    </when>
                    <otherwise>
                        AND state = #{query.state}
                    </otherwise>
                </choose>
            </if>
        </where>
        order by gmt_modified desc
    </select>

    <select id="checkActivity"
            resultType="com.holderzone.member.common.vo.activity.FullReductionFoldActivityPublishVO">
        select DISTINCT
        lsa.activity_code,
        lsa.limit_period_json,
        lsa.is_limit_period,
        lsa.limit_period_type,
        lsa.start_time,
        lsa.end_time
        from
        hsa_full_reduction_fold_activity lsa
        left join hsa_full_reduction_fold_activity_store lsas on lsa.guid = lsas.activity_guid
        <where>
            lsa.is_delete = 0
            and lsa.guid <![CDATA[ <> ]]> #{query.guid}
            and lsa.oper_subject_guid = #{query.operSubjectGuid}
            and lsa.state = 1
            and lsa.end_time > now()
            <![CDATA[ AND (
                    ( lsa.start_time <= #{query.startTime} AND lsa.end_time >= #{query.startTime} )
                    or
                    ( lsa.start_time <= #{query.endTime} AND lsa.end_time >= #{query.endTime} )
                    or
                    ( lsa.start_time >= #{query.startTime} AND lsa.start_time <= #{query.endTime} )
                    or
                    ( lsa.end_time >= #{query.startTime} AND lsa.end_time <= #{query.endTime} )
                ) ]]>
            <if test="query.storeIdList != null and query.storeIdList.size()>0">
                and (lsa.is_all_store = 1
                or
                lsas.store_guid in
                <foreach collection="query.storeIdList" item="storeId" open="(" close=")" separator=",">
                    #{storeId}
                </foreach>
                )
            </if>
            <if test="query.applyBusinessList != null and query.applyBusinessList.size()>0">
                and
                <foreach collection="query.applyBusinessList" item="applyBusiness" open="(" close=")" separator="or">
                    lsa.apply_business_json like concat('%',#{applyBusiness},'%')
                </foreach>

            </if>
        </where>
    </select>

    <select id="queryActivityByGuid"
            resultType="com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivity">
        SELECT
            <include refid="limitSpecialsActivityField"/>
        FROM
            `hsa_full_reduction_fold_activity` sa
        WHERE
            sa.is_delete = 0
            AND sa.guid = #{guid}
    </select>

    <select id="queryActivityItemByMember"
            resultType="com.holderzone.member.common.vo.specials.LimitSpecialsActivityItemVO">
        SELECT
            sai.commodity_id,
            sai.commodity_code,
            sai.specials_type,
            sai.specials_number,
            sai.limit_number,
            sa.guid activityGuid,
            sa.`name` activityName,
            sa.activity_code,
            sa.start_time,
            sa.end_time,
            sa.`state`,
            sa.is_limit_period,
            sa.limit_period_type,
            sa.limit_period_json,
            sa.relation_rule
        FROM
            `hsa_full_reduction_fold_activity` sa
            left join hsa_full_reduction_fold_activity_item sai on sai.activity_guid = sa.guid
            left join hsa_full_reduction_fold_activity_store sas on sas.activity_guid = sa.guid
        WHERE
            <include refid="queryActivity"/>
            <!-- 适用门店 -->
            and (
                    sa.is_all_store = 1
                    or
                    ( sa.is_all_store = 0 and sas.store_guid = #{query.storeGuid} )
                )
            <!-- 适用场景 -->
            and (
                    sa.apply_business = 0
                    or
                    ( sa.apply_business = 1
                        and sa.apply_business_json like concat('%',#{query.applyBusiness},'%')
                    )
                )
            <if test="query.itemGuidList != null and query.itemGuidList.size() > 0">
                and sai.commodity_id in
                <foreach collection="query.itemGuidList" open="(" separator="," close=")" item="itemGuid">
                    #{itemGuid}
                </foreach>
            </if>
            <if test="query.activityGuidList != null and query.activityGuidList.size() > 0">
                and sa.guid in
                <foreach collection="query.activityGuidList" open="(" separator="," close=")" item="activityGuid">
                    #{activityGuid}
                </foreach>
            </if>
    </select>

    <select id="queryActivityByMember"
            resultType="com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivity">
        SELECT
            <include refid="limitSpecialsActivityField"/>
        FROM
            `hsa_full_reduction_fold_activity` sa
        WHERE
            <include refid="queryActivity"/>
    </select>

    <select id="queryActivityByOperSubjectGuid"
            resultType="com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivity">
        SELECT
            <include refid="limitSpecialsActivityField"/>
        FROM
            `hsa_full_reduction_fold_activity` sa
        WHERE
            sa.is_delete = 0
            AND sa.state = 1
            AND sa.end_time > now()
            AND sa.oper_subject_guid = #{operSubjectGuid}
    </select>

    <select id="queryActivityByRun"
            resultType="com.holderzone.member.common.vo.activity.QueryFullReductionFoldActivityVO">
        SELECT
        distinct
        <include refid="limitSpecialsActivityField"/>
        FROM
        hsa_full_reduction_fold_activity sa
        left join
        hsa_full_reduction_fold_activity_store sas on sas.activity_guid = sa.guid
        WHERE
        sa.is_delete = 0
        and sa.state = 1
        and sa.end_time > now()
        and sa.start_time <![CDATA[ <= ]]> now()
        AND sa.oper_subject_guid = #{query.operSubjectGuid}

        <if test="query.discountGuidList != null and query.discountGuidList.size()>0">
            and sa.activity_code in
            <foreach collection="query.discountGuidList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>

        <!-- 门店查询条件：支持单个门店和门店列表 -->
        <if test="(query.storeGuid != null and query.storeGuid != '') or (query.storeGuidList != null and query.storeGuidList.size() > 0)">
        and (sa.is_all_store = 1 or (sa.is_all_store = 0 and 
        <choose>
            <when test="query.storeGuidList != null and query.storeGuidList.size() > 0">
                sas.store_guid in 
                <foreach collection="query.storeGuidList" item="storeGuid" open="(" close=")" separator=",">
                    #{storeGuid}
                </foreach>
            </when>
            <when test="query.storeGuid != null and query.storeGuid != ''">
                sas.store_guid = #{query.storeGuid}
            </when>
        </choose>
        ))
        </if>
        <if test="query.applyBusiness != null">
        and FIND_IN_SET(#{query.applyBusiness}, sa.apply_business_json) > 0;
        </if>
    </select>

    <select id="queryEndTimeActivity"
            resultType="com.holderzone.member.common.vo.specials.QueryEndTimeActivityVO">
        SELECT
        <include refid="limitSpecialsActivityField"/>
        FROM
        hsa_full_reduction_fold_activity sa
        WHERE
        state in (1,2)
        and sa.is_delete = 0
        and now() >= sa.end_time
    </select>

    <select id="queryRunInfo"
            resultType="com.holderzone.member.common.vo.activity.FullReductionFoldActivityRunVO">
        SELECT
            distinct
            <include refid="limitSpecialsActivityField"/>
        FROM
            hsa_full_reduction_fold_activity sa
        WHERE
            sa.is_delete = 0
            and sa.state = 1
            and sa.end_time > now()
            and sa.start_time <![CDATA[ <= ]]> now()
            AND sa.oper_subject_guid = #{query.operSubjectGuid}
            <if test="query.discountGuidList != null and query.discountGuidList.size()>0">
                and sa.activity_code in
                <foreach collection="query.discountGuidList" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
            </if>
    </select>

    <update id="updateState">
        UPDATE
            `hsa_full_reduction_fold_activity`
        SET
            state = #{state},
            is_publish = 1
        WHERE
            guid = #{activityGuid}
            AND oper_subject_guid = #{operSubjectGuid}
    </update>

    <update id="logicDelete">
        UPDATE
            `hsa_full_reduction_fold_activity`
        SET
            is_delete = 1
        WHERE
            guid = #{activityGuid}
            AND oper_subject_guid = #{operSubjectGuid}
    </update>

    <update id="updateRelationRuleActivity">
        <foreach collection="list" item="activity">
            UPDATE hsa_full_reduction_fold_activity
            SET relation_rule = #{activity.relationRule}
            WHERE activity_code = #{activity.discountGuid};
        </foreach>
    </update>

    <update id="updateStateEnd">
        UPDATE hsa_full_reduction_fold_activity
        SET state = 3
        WHERE
        activity_code in
        <foreach collection="list" open="(" separator="," close=")" item="code">
            #{code}
        </foreach>
    </update>

    <sql id="limitSpecialsActivityField">
        sa.id,
        sa.guid,
        sa.oper_subject_guid,
        sa.`activity_name`,
        sa.activity_code,
        sa.start_time,
        sa.end_time,
        sa.state,
        sa.is_limit_period,
        sa.limit_period_type,
        sa.limit_period_json,
        sa.label_guid_json,
        sa.relation_rule,
        sa.apply_business,
        sa.apply_business_json,
        sa.is_all_store,
        sa.group_type,
        sa.apply_commodity,
        sa.limit_count,
        sa.condition_label_json,
        sa.condition_grade_json,
        sa.condition_member_json,
        sa.discount_sill_type,
        sa.full_reduction_fold_json,
        sa.full_reduction_tactics_type,
        sa.gmt_create,
        sa.gmt_modified,
        sa.is_publish
    </sql>

    <sql id="queryActivity">
            sa.is_delete = 0
            and sa.state = 1
            and sa.end_time > now()
            and sa.start_time <![CDATA[ <= ]]> now()
            AND sa.oper_subject_guid = #{query.operSubjectGuid}
        <!-- 适用人群 -->
        <choose>
                <!-- 未注册 -->
            <when test="query.isRegister == 0">
                    and sa.group_type = 0
                </when>
                <otherwise>
                    AND (
                        sa.group_type in ( 0,1 )
                        or
                    <!-- 指定人群 -->
                    ( sa.group_type = 2
                            and (
                    <!-- 指定标签会员 -->
                    sa.condition_label_json regexp #{query.labelGuidListStr}
                                or
                    <!-- 指定等级会员 -->
                    sa.condition_grade_json regexp #{query.gradeGuidListStr}
                                or
                    <!-- 指定会员 -->
                    sa.condition_member_json regexp #{query.memberInfoGuid}
                            )
                        )
                    )
                </otherwise>
            </choose>
    </sql>

</mapper>