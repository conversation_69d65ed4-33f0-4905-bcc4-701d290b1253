<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.marketing.mapper.nth.HsaNthActivityMapper">

    <select id="query" resultType="com.holderzone.member.common.vo.nth.NthActivityVO">
        select
            guid,
            name,
            activity_code,
            start_time,
            end_time,
            apply_business_json AS activityBusiness,
            0 AS activityItem,
            0 AS activityOrder,
            state
        from
            hsa_nth_activity
        <where>
            is_delete = 0
            and oper_subject_guid = #{query.operSubjectGuid}
            <if test="query.keywords != null and query.keywords != ''">
                and (name like concat('%',#{query.keywords},'%') or activity_code like concat('%',#{query.keywords},'%'))
            </if>
            <if test="query.startTime != null and query.endTime != null">
                <![CDATA[ AND (
                    ( start_time <= #{query.startTime} AND end_time >= #{query.startTime} )
                    or
                    ( start_time <= #{query.endTime} AND end_time >= #{query.endTime} )
                    or
                    ( start_time >= #{query.startTime} AND start_time <= #{query.endTime} )
                    or
                    ( end_time >= #{query.startTime} AND end_time <= #{query.endTime} )
                ) ]]>
            </if>
            <if test="query.business != null">
                AND apply_business_json like concat('%',#{query.business},'%')
            </if>

            <if test="query.state != null">
                <choose>
                    <when test="query.state == 4">
                        AND state = 1
                        <![CDATA[AND start_time > now()]]>
                    </when>
                    <when test="query.state == 1">
                        AND state = 1
                        AND <![CDATA[ start_time <= now() ]]>
                        AND <![CDATA[ end_time >= now() ]]>
                    </when>
                    <when test="query.state == 3">
                        AND <![CDATA[ state <> 0 ]]>
                        AND <![CDATA[ end_time < now() ]]>
                    </when>
                    <when test="query.state == 2">
                        AND <![CDATA[ state = 2 ]]>
                        AND <![CDATA[ end_time > now() ]]>
                    </when>
                    <otherwise>
                        AND state = #{query.state}
                    </otherwise>
                </choose>
            </if>
        </where>
        order by gmt_modified desc
    </select>

    <update id="updateRelationRuleActivity">
        <foreach collection="list" item="activity">
            UPDATE hsa_nth_activity
            SET relation_rule = #{activity.relationRule}
            WHERE activity_code = #{activity.discountGuid};
        </foreach>
    </update>

</mapper>