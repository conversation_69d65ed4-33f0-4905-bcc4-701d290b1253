<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.marketing.mapper.specials.HsaLimitSpecialsActivityStoreMapper">

    <select id="queryByActivityGuid"
            resultType="com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityStore">
        SELECT
            guid,
            activity_guid,
            store_guid,
            store_name,
            store_code,
            system
        FROM
            `hsa_limit_specials_activity_store`
        WHERE
            is_delete = 0
            AND activity_guid = #{activityGuid}
            AND oper_subject_guid = #{operSubjectGuid}
    </select>

    <select id="queryByActivityGuidList"
            resultType="com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityStore">
        SELECT
            guid,
            activity_guid,
            store_guid,
            store_name,
            store_code
        FROM
            `hsa_limit_specials_activity_store`
        WHERE
            is_delete = 0
            AND oper_subject_guid = #{operSubjectGuid}
            <if test="activityGuidList != null and activityGuidList.size > 0">
                  AND activity_guid IN
                  <foreach collection="activityGuidList" open="(" close=")" separator="," item="activityGuid">
                      #{activityGuid}
                  </foreach>
            </if>
    </select>

</mapper>