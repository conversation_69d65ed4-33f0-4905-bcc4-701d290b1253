package com.holderzone.member.marketing.service.certificate.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.assembler.CertifiedAssembler;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.VolumeInfoUpdateDTO;
import com.holderzone.member.common.dto.certificate.CertifiedCouponDTO;
import com.holderzone.member.common.dto.certificate.GiftInfoDTO;
import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.label.LabelBaseDataDTO;
import com.holderzone.member.common.dto.label.MemberCertifiedLabel;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.certificate.MemberCouponSourceTypeEnum;
import com.holderzone.member.common.enums.certificate.VolumeStateEnum;
import com.holderzone.member.common.enums.gift.GiftTypeEnum;
import com.holderzone.member.common.enums.wechat.WechatMsgSendType;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.feign.ZhuanCanFeign;
import com.holderzone.member.common.qo.activity.RequestLabelBaseQO;
import com.holderzone.member.common.qo.activity.RequestLabelDTO;
import com.holderzone.member.common.qo.certificate.VolumeInfoQO;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.util.StringHandlerUtil;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.repast.RepastUtils;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.PlatFormUserDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;
import com.holderzone.member.common.vo.certificate.RequestMemberInfoVolumeSave;
import com.holderzone.member.common.vo.certificate.ResponseAppletsMemberInfo;
import com.holderzone.member.common.vo.certificate.SendVolumeVO;
import com.holderzone.member.common.vo.certificate.VolumeInfoVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;
import com.holderzone.member.marketing.config.RedisDelayedQueueT;
import com.holderzone.member.marketing.entity.certificate.HsaAuditStep;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedActivity;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedActivityRecord;
import com.holderzone.member.marketing.mapper.HsaCertifiedActivityMapper;
import com.holderzone.member.marketing.mapper.HsaCertifiedActivityRecordMapper;
import com.holderzone.member.marketing.service.certificate.HsaCertifiedActivityRecordService;
import com.holderzone.member.marketing.service.certificate.HsaCertifiedExternalService;
import com.holderzone.member.marketing.util.message.WeChatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import com.holderzone.member.common.feign.MemberBaseFeign;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 认证有礼
 * @date 2022/4/12 16:56
 */
@Service
@Slf4j
public class HsaCertifiedExternalServiceImpl implements HsaCertifiedExternalService {

    public final static Integer ONE = 1;

    public final static Integer MAX_PAGE = 9999;

    @Resource
    private ZhuanCanFeign zhuancanFeign;

    @Resource
    private SaasStoreFeign saasStoreFeign;

    @Resource
    private HsaCertifiedActivityRecordMapper hsaCertifiedActivityRecordMapper;

    @Resource
    @Lazy
    private HsaCertifiedActivityRecordService certifiedActivityRecordService;

    @Resource
    private HsaCertifiedActivityMapper hsaCertifiedActivityMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private Executor marketingThreadExecutor;

    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private WeChatUtil weChatUtil;


    @Override
    public List<IslandCouponDTO> getIslandCouponsList(List<String> couponId) {
        if (Objects.nonNull(couponId) && CollectionUtil.isNotEmpty(couponId)) {
            List<Long> couponLongId = couponId.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            return zhuancanFeign.getIslandCouponDetailsNew(couponLongId).getBody();
        }
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("headerUserInfo：{}", JSON.toJSONString(headerUserInfo));
        ResponseEntity<PlatFormUserDTO> userDTOResponseEntity = zhuancanFeign.getPlatFormThirdNo(headerUserInfo.getEnterpriseGuid(), headerUserInfo.getOperSubjectGuid());
        if (Objects.isNull(userDTOResponseEntity.getBody())) {
            log.info("未查询到平台数据");
            return Lists.newArrayList();
        }
        Long platFormId = userDTOResponseEntity.getBody().getId();
        ResponseEntity<List<IslandCouponDTO>> listResponseEntity = zhuancanFeign.getIslandCouponsList(
                NumberConstant.NUMBER_LONG_9999999,
                platFormId,
                null,
                null,
                Boolean.FALSE);
        if (Objects.isNull(userDTOResponseEntity.getBody())) {
            log.info("此平台未查询到优惠券：{}", JSON.toJSONString(platFormId));
            return Lists.newArrayList();
        }
        return listResponseEntity.getBody()
                .stream()
                .sorted(Comparator.comparing(IslandCouponDTO::getUpdateDate)
                        .reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<ResponseOperationLabel> getLabelList(RequestLabelDTO requestLabelDTO) {
        RequestLabelBaseQO requestLabelBaseQO = new RequestLabelBaseQO();
        BeanUtils.copyProperties(requestLabelDTO, requestLabelBaseQO);
        return externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).getLabelList(requestLabelBaseQO);
    }

    @Override
    public boolean saveAutomaticLabel(RequestLabelSetting req) {
        return externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).saveAutomaticLabel(req);
    }

    @Override
    public MemberBasicInfoVO getMemberInfo(MemberQueryDTO memberQueryDTO) {
        return externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).getMemberInfo(memberQueryDTO);
    }

    @Override
    public String getMemberName(MemberQueryDTO memberQueryDTO) {
        return externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).getMemberName(memberQueryDTO);
    }

    @Async("marketingThreadExecutor")
    public <T> void sendLabel(T t) {
        MemberCertifiedLabel memberCertifiedLabel = JSON.parseObject(Objects.requireNonNull(t).toString(), MemberCertifiedLabel.class);
        HsaCertifiedActivity hsaCertifiedActivity = hsaCertifiedActivityMapper.queryByGuid(memberCertifiedLabel.getCertifiedActivityGuid());
        List<String> labelGuidList = JSON.parseArray(hsaCertifiedActivity.getActivityLabelGuids(), LabelBaseDataDTO.class)
                .stream().map(LabelBaseDataDTO::getGuid)
                .collect(Collectors.toList());

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        headerUserInfo.setOperSubjectGuid(memberCertifiedLabel.getOperSubjectGuid());
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));

        RemoveMemberLabelCorrelationQO removeMemberLabelCorrelationQO = new RemoveMemberLabelCorrelationQO();
        removeMemberLabelCorrelationQO.setMemberInfoGuid(Lists.newArrayList(memberCertifiedLabel.getMemberInfoGuid()));
        removeMemberLabelCorrelationQO.setLabelGuid(labelGuidList);
        log.info("删除会员标签：{}", JSON.toJSONString(memberCertifiedLabel));
        externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).removeMemberInfoLabel(removeMemberLabelCorrelationQO);
    }

    @Override
    public void memberLabelRefresh(MemberCertifiedLabel memberCertifiedLabel) {
        HsaCertifiedActivity hsaCertifiedActivity = hsaCertifiedActivityMapper.queryByGuid(memberCertifiedLabel.getCertifiedActivityGuid());
        if (Objects.isNull(hsaCertifiedActivity) || StringUtils.isEmpty(hsaCertifiedActivity.getActivityLabelGuids())) {
            log.info("活动或标签不存在：{}", JSON.toJSONString(hsaCertifiedActivity));
            return;
        }

        // 查询会员
        MemberQueryDTO memberQueryDTO = new MemberQueryDTO();
        memberQueryDTO.setPhoneNum(memberCertifiedLabel.getPhoneNumber());
        MemberBasicInfoVO memberInfo = getMemberInfo(memberQueryDTO);
        log.info("会员信息：{}", JSON.toJSONString(memberInfo));

        if (Objects.isNull(memberInfo) || StringUtils.isEmpty(memberInfo.getGuid())) {
            log.info("会员不存在：{}", JSON.toJSONString(memberCertifiedLabel.getPhoneNumber()));
            return;
        }

        if (Objects.nonNull(memberCertifiedLabel.getCertificateValidity()) && memberCertifiedLabel.getCertificateValidity().isBefore(LocalDateTime.now())) {
            log.info("时间在之前不关联：{}", JSON.toJSONString(memberCertifiedLabel));
            return;
        }
        List<String> labelGuidList = JSONObject.parseArray(hsaCertifiedActivity.getActivityLabelGuids(), LabelBaseDataDTO.class)
                .stream().map(LabelBaseDataDTO::getGuid)
                .collect(Collectors.toList());

        AddMemberLabelCorrelationQO addMemberLabelCorrelationQO = new AddMemberLabelCorrelationQO();
        addMemberLabelCorrelationQO.setMemberInfoGuid(Lists.newArrayList(memberInfo.getGuid()));
        addMemberLabelCorrelationQO.setLabelGuid(labelGuidList);
        log.info("标签关联开始：{}", JSON.toJSONString(addMemberLabelCorrelationQO));
        externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).addMemberInfoLabel(addMemberLabelCorrelationQO);

        if (Objects.nonNull(memberCertifiedLabel.getCertificateValidity()) && memberCertifiedLabel.getCertificateValidity().isAfter(LocalDateTime.now())) {
            memberCertifiedLabel.setMemberInfoGuid(memberInfo.getGuid());
            long time = Timestamp.valueOf(memberCertifiedLabel.getCertificateValidity()).getTime();
            RedisDelayedQueueT<String> redisDelayedQueue = new RedisDelayedQueueT<>(StringConstant.LABEL_UPDATE, String.class, this::sendLabel);
            redisDelayedQueue.setStringRedisTemplate(marketingThreadExecutor, stringRedisTemplate);
            log.info("执行结果：{}", redisDelayedQueue.putForDelayedTime(JSONObject.toJSONString(memberCertifiedLabel), time));

        }
    }

    @Override
    public void memberCouponSend(SendCertifiedStampsDTO sendCertifiedStampsDTO) {
        List<CertifiedCouponDTO> certifiedCouponDTOS = sendCertifiedStampsDTO.getCertifiedCouponDTOS().stream()
                .filter(in -> Objects.isNull(in.getGiftType()) || in.getGiftType() == GiftTypeEnum.GIFT_MEMBER_COUPON.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(certifiedCouponDTOS)) {
            return;
        }
        log.info("发券执行开始：{}", JacksonUtils.writeValueAsString(sendCertifiedStampsDTO));
        externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).sendMemberCoupon(sendCertifiedStampsDTO);
    }

    @Override
    public List<IslandCouponDTO> queryVolumeList() {
        VolumeInfoQO volumeInfoQO = new VolumeInfoQO();
        volumeInfoQO.setVolumeState(VolumeStateEnum.NOT_EXPIRED.getCode());
        volumeInfoQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        FeignModel<List<VolumeInfoVO>> pageFeignModel = saasStoreFeign.queryUsableVolumeList(volumeInfoQO);
        List<VolumeInfoVO> infoVOList = pageFeignModel.getData();
        return CertifiedAssembler.toIslandCouponDTO(infoVOList);
    }

    @Override
    public void batchSendVolume(SendVolumeVO sendVolumeVO) {
        if (CollectionUtils.isEmpty(sendVolumeVO.getRecordGuidList())) {
            return;
        }
        List<HsaCertifiedActivityRecord> activityRecordList = hsaCertifiedActivityRecordMapper.queryReplenishSendRecord(
                sendVolumeVO.getRecordGuidList());
        if (CollectionUtils.isEmpty(activityRecordList)) {
            log.warn("[批量补发优惠券]活动记录未查询到");
            return;
        }

        // 以记录为准循环发，避免活动变更
        List<HsaCertifiedActivityRecord> updateList = new ArrayList<>();
        activityRecordList.forEach(activityRecord -> {
            if (com.holderzone.framework.util.StringUtils.isEmpty(activityRecord.getDiscount())) {
                return;
            }
            List<GiftInfoDTO> giftInfoDTOList = JacksonUtils.toObjectList(GiftInfoDTO.class, activityRecord.getDiscount());
            if (CollectionUtils.isEmpty(giftInfoDTOList)) {
                return;
            }
            RequestMemberInfoVolumeSave volumeSave = buildMemberInfoVolumeSaveRequest(activityRecord, giftInfoDTOList);

            FeignModel<List<VolumeInfoUpdateDTO>> feignModel = saasStoreFeign.batchSendVolume(volumeSave);
            log.info("[批量补发优惠券]feignModel={}", JacksonUtils.writeValueAsString(feignModel));
            List<VolumeInfoUpdateDTO> responseVolumeInfoList = feignModel.getData();
            if (!CollectionUtils.isEmpty(responseVolumeInfoList)) {
                activityRecord.setIssuanceStatus(BooleanEnum.TRUE.getCode());
                activityRecord.setGmtModified(LocalDateTime.now());
                updateList.add(activityRecord);
            }
        });
        if (!CollectionUtils.isEmpty(updateList)) {
            certifiedActivityRecordService.updateBatchByGuid(updateList);
        }
    }

    @Override
    public void updateGrowthValue(RequestMemberGrowthValue request) {
        log.info("会员成长值调整开始：{}", JSON.toJSONString(request));
        externalSupport.memberServer(ThreadLocalCache.getOperSubjectGuid()).updateGrowthValue(request);
    }

    @Override
    public void sendAppletMessage(HsaCertifiedActivityRecord activityRecord, HsaAuditStep hsaAuditStep) {
        log.info("发送小程序订阅消息：activityRecord:{}, hsaAuditStep:{}",
                JacksonUtils.writeValueAsString(activityRecord), JacksonUtils.writeValueAsString(hsaAuditStep));

        final List<Object> dataParams = getDataParams(activityRecord, hsaAuditStep);

        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();

        if (!RepastUtils.isHasRepastAuth(operSubjectGuid)) {
            String[] pageParams = hsaAuditStep.getStatus() == 1 ? null :
                    new String[]{activityRecord.getCertifiedActivityGuid(), activityRecord.getGuid()};
            weChatUtil.send(activityRecord.getOpenid(),
                    pageParams,
                    dataParams);
        } else {
            MessagesSendQO messagesSendQO = new MessagesSendQO();
            messagesSendQO.setMemberGuid(activityRecord.getMemberGuid());
            if (hsaAuditStep.getStatus() != 1) {
                messagesSendQO.setActivityGuid(activityRecord.getCertifiedActivityGuid());
                messagesSendQO.setActivityRecordGuid(activityRecord.getGuid());
            }
            messagesSendQO.setOperSubjectGuid(operSubjectGuid);
            messagesSendQO.setTemplateName(WechatMsgSendType.ACTIVITY_AUDIT.getMsgTitle());
            messagesSendQO.setOpenId(activityRecord.getOpenid());
            messagesSendQO.setPhone(activityRecord.getPhoneNumber());

            // 参数
            Map<String, String> prams = Maps.newHashMap();
            prams.put("thing1", dataParams.get(0).toString());
            prams.put("thing2", dataParams.get(1).toString());
            prams.put("thing3", dataParams.get(2).toString());
            prams.put("time4", dataParams.get(3).toString());
            prams.put("thing5", dataParams.get(4).toString());
            messagesSendQO.setPrams(prams);

            memberBaseFeign.wechatMessageSend(messagesSendQO);
        }
    }

    /**
     * 小程序订阅消息参数封装
     *
     * @param activityRecord 申请活动记录信息
     * @param hsaAuditStep   审核结果信息
     * @return 参数
     */
    private List<Object> getDataParams(HsaCertifiedActivityRecord activityRecord, HsaAuditStep hsaAuditStep) {

        List<Object> dataList = new ArrayList<>();
        //活动名称
        dataList.add(activityRecord.getActivityName());
        //状态
        String status = hsaAuditStep.getStatus() == 1 ? "审核成功" : "审核失败";
        dataList.add(status);
        //审核情况
        String auditResult = hsaAuditStep.getStatus() == 1 ? "您的申请已通过审核" : hsaAuditStep.getContent();
        dataList.add(auditResult);
        //时间
        dataList.add(DateUtil.getDateString(hsaAuditStep.getGmtCreate()));
        //备注
        String content = hsaAuditStep.getStatus() == 1 ? "优惠券礼品稍后将为您放入卡包中" : "可修改信息重新提交申请";
        dataList.add(content);
        return dataList;
    }

    private RequestMemberInfoVolumeSave buildMemberInfoVolumeSaveRequest(HsaCertifiedActivityRecord activityRecord,
                                                                         List<GiftInfoDTO> giftInfoDTOList) {
        RequestMemberInfoVolumeSave volumeSave = new RequestMemberInfoVolumeSave();
        FeignModel<ResponseAppletsMemberInfo> memberInfoFeignModel = saasStoreFeign.getAppletsMemberInfoByUserId(activityRecord.getMemberGuid());
        ResponseAppletsMemberInfo data = memberInfoFeignModel.getData();
        if (Objects.isNull(data)) {
            log.warn("[批量补发优惠券][通过userId获取会员基础信息]异常,userId={}", activityRecord.getMemberGuid());
            return volumeSave;
        }
        List<String> volumeInfoGuidList = giftInfoDTOList.stream()
                .map(GiftInfoDTO::getGiveawayGuid)
                .collect(Collectors.toList());
        List<Integer> volumeNumberList = giftInfoDTOList.stream()
                .map(GiftInfoDTO::getGiveawayNum)
                .collect(Collectors.toList());

        volumeSave.setMemberInfoGuid(data.getMemberInfoGuid());
        volumeSave.setVolumeInfoGuid(StringHandlerUtil.listToString(volumeInfoGuidList, StringConstant.COMMA));
        volumeSave.setVolumeNumber(StringHandlerUtil.listToString(volumeNumberList, StringConstant.COMMA));
        volumeSave.setSendEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        volumeSave.setCollectionType(MemberCouponSourceTypeEnum.ALIPAY_APPLET.getType());
        volumeSave.setVolumeSourceCode(MemberCouponSourceTypeEnum.ACCURATE_PUSH.getType());
        return volumeSave;
    }

}
