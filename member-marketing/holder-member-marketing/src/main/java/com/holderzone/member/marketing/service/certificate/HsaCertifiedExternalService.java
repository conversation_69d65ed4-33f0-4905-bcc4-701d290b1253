package com.holderzone.member.marketing.service.certificate;


import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.label.MemberCertifiedLabel;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.qo.activity.RequestLabelDTO;
import com.holderzone.member.common.qo.member.RequestLabelSetting;
import com.holderzone.member.common.qo.member.RequestMemberGrowthValue;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;
import com.holderzone.member.common.vo.certificate.SendVolumeVO;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;
import com.holderzone.member.marketing.entity.certificate.HsaAuditStep;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedActivityRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 认证有礼 外部服务 Service
 * @date 2022/4/12 16:59
 */
public interface HsaCertifiedExternalService {

    /**
     * 获取优惠券
     *
     * @return List<IslandCouponDTO>
     */
    List<IslandCouponDTO> getIslandCouponsList(List<String> couponId);

    /**
     * 获取标签列表
     *
     * @param requestLabelDTO requestLabelDTO
     * @return List<ResponseOperationLabel>
     */
    List<ResponseOperationLabel> getLabelList(RequestLabelDTO requestLabelDTO);

    /**
     * 新增手动标签
     *
     * @param req
     */
    boolean saveAutomaticLabel(RequestLabelSetting req);

    /**
     * 获取会员信息
     *
     * @param memberQueryDTO memberQueryDTO
     * @return MemberBasicInfoVO
     */
    MemberBasicInfoVO getMemberInfo(MemberQueryDTO memberQueryDTO);

    /**
     * 获取会员名称
     *
     */
    String getMemberName(MemberQueryDTO memberQueryDTO);

    /**
     * 会员标签刷新
     *
     * @param memberCertifiedLabel memberCertifiedLabel
     */
    void memberLabelRefresh(MemberCertifiedLabel memberCertifiedLabel);

    List<IslandCouponDTO> queryVolumeList();

    /**
     * 发券
     *
     * @param sendCertifiedStampsDTO 发券
     */
    void memberCouponSend(SendCertifiedStampsDTO sendCertifiedStampsDTO);

    /**
     * 批量补发
     * 目前只有支付宝认证有补发操作，并且看实现只补发了券，成长值没有处理
     */
    void batchSendVolume(SendVolumeVO sendVolumeVO);

    /**
     * 调整成长值
     *
     * @param request request
     */
    void updateGrowthValue(RequestMemberGrowthValue request);


    /**
     * 发送小程序消息
     */
    void sendAppletMessage(HsaCertifiedActivityRecord activityRecord, HsaAuditStep hsaAuditStep);

}
