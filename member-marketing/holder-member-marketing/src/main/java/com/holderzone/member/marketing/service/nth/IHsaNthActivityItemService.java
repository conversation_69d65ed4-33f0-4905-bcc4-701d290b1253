package com.holderzone.member.marketing.service.nth;

import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynDiscountDTO;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityItem;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;

import java.util.List;

/**
 * <p>
 * 第N份优惠活动适用商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface IHsaNthActivityItemService extends IHolderBaseService<HsaNthActivityItem> {

    List<HsaNthActivityItem> listByActivityGuid(String activityGuid);

    List<HsaNthActivityItem> listByActivityGuids(List<String> activityGuids);

    void removeByActivityGuid(String activityGuid);
}
