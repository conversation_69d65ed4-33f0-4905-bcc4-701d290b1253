package com.holderzone.member.marketing.manage.specials;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageInfo;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.LimitSpecialsActivityDTO;
import com.holderzone.member.common.dto.activity.SettleApplyCommodityDTO;
import com.holderzone.member.common.dto.base.QueryArrayShopBase;
import com.holderzone.member.common.dto.base.ResCommodityBase;
import com.holderzone.member.common.dto.nth.NthActivityItemDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.specials.DateTimeHandleBO;
import com.holderzone.member.common.dto.specials.LimitSpecialsActivityItemDTO;
import com.holderzone.member.common.dto.specials.LimitSpecialsActivityReqDTO;
import com.holderzone.member.common.dto.specials.LimitSpecialsActivityStoreDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum;
import com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum;
import com.holderzone.member.common.enums.member.MarketConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.order.OrderStateEnum;
import com.holderzone.member.common.enums.specials.ActivityStateEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.activity.SynOrderDiscountQO;
import com.holderzone.member.common.qo.card.BarkOrderDiscountCallbackQO;
import com.holderzone.member.common.qo.gift.ConsumptionGiftDetailsDTO;
import com.holderzone.member.common.qo.gift.RequestQueryMemberBaseQO;
import com.holderzone.member.common.qo.member.ResponseOperationMemberInfo;
import com.holderzone.member.common.qo.specials.CheckActivityQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsActivityItemQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsActivityQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsActivityRunQO;
import com.holderzone.member.common.support.SettlementSupport;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.card.OperationMemberInfoVO;
import com.holderzone.member.common.vo.grade.EffectiveTimeVO;
import com.holderzone.member.common.vo.specials.*;
import com.holderzone.member.marketing.assembler.LimitSpecialsAssembler;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityItem;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivity;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityItem;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityRecord;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityStore;
import com.holderzone.member.marketing.manage.specials.bo.LimitSpecialsBO;
import com.holderzone.member.marketing.helper.ActivityTimeHelper;
import com.holderzone.member.marketing.manage.voting.helper.ActivityStatusHelper;
import com.holderzone.member.marketing.service.cache.CacheService;
import com.holderzone.member.marketing.service.specials.IHsaLimitSpecialsActivityItemService;
import com.holderzone.member.marketing.service.specials.IHsaLimitSpecialsActivityRecordService;
import com.holderzone.member.marketing.service.specials.IHsaLimitSpecialsActivityService;
import com.holderzone.member.marketing.service.specials.IHsaLimitSpecialsActivityStoreService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/14
 * @description 限时特价处理器
 */
@Slf4j
@Component
@AllArgsConstructor
public class LimitSpecialsManage {

    /**
     * 限时特价活动锁
     */
    public static final String LIMIT_SPECIALS_ACTIVITY_LOCK = "LIMIT_SPECIALS_ACTIVITY_LOCK:";

    public static final long WAIT_TIME = 5000;

    public static final long LEASE_TIME = 1000;
    public static final String NOT_ACTIVITY = "活动不存在";
    public static final String INVALID_TIME_PERIODS = "无效时间段time1={},time2={}";

    private static final String SPECIALS_TYPE_DES = "指定商品特价活动";

    private final IHsaLimitSpecialsActivityService activityService;

    private final IHsaLimitSpecialsActivityStoreService activityStoreService;

    private final IHsaLimitSpecialsActivityItemService activityItemService;

    private final IHsaLimitSpecialsActivityRecordService activityRecordService;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final MemberBaseFeign memberBaseFeign;

    private final RedissonClient redissonClient;

    private final CacheService cacheService;

    private final ExternalSupport externalSupport;

    private final Executor marketingThreadExecutor;

    private final SettlementSupport settlementSupport;

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(LimitSpecialsActivityReqDTO reqDTO) {
        checkRequestParam(reqDTO);

        RLock lock = redissonClient.getLock(LIMIT_SPECIALS_ACTIVITY_LOCK + ThreadLocalCache.getOperSubjectGuid());
        try {
            if (!lock.tryLock(WAIT_TIME, LEASE_TIME, TimeUnit.MILLISECONDS)) {
                throw new MemberMarketingException("网络繁忙，请稍后重试");
            }
            HsaLimitSpecialsActivity specialsActivity = LimitSpecialsAssembler.activityReqDTO2Activity(reqDTO);
            if (StringUtils.isEmpty(specialsActivity.getGuid())) {
                String activityGuid = guidGeneratorUtil.getStringGuid(HsaLimitSpecialsActivity.class.getSimpleName());
                specialsActivity.setGuid(activityGuid);
                specialsActivity.setActivityCode(activityGuid.substring(activityGuid.length() - 6));
                specialsActivity.setIsDelete((byte) 0);
            } else {
                HsaLimitSpecialsActivity hsaLimitSpecialsActivity = activityService.queryByGuid(specialsActivity.getGuid());
                specialsActivity.setState(ActivityStateEnum.NOT_PUBLISH.getCode());
                specialsActivity.setActivityCode(hsaLimitSpecialsActivity.getActivityCode());

                LimitSpecialsActivityDTO limitSpecialsActivityDTO = new LimitSpecialsActivityDTO();
                BeanUtils.copyProperties(specialsActivity, limitSpecialsActivityDTO);
                sendActivity(limitSpecialsActivityDTO, Boolean.TRUE);
            }
            activityService.saveOrUpdate(specialsActivity);

            // 门店关联
            saveActivityStore(reqDTO, specialsActivity);

            // 商品关联
            saveActivityItem(reqDTO, specialsActivity);
        } catch (Exception e) {
            log.error("[新增or编辑限时特价活动]失败,eMessage={},e={}", e.getMessage(), e);
            Thread.currentThread().interrupt();
            throw new MemberMarketingException(e.getMessage());
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 推送活动
     *
     * @param hsaLimitSpecialsActivity 积分规则
     */
    public void sendActivity(LimitSpecialsActivityDTO hsaLimitSpecialsActivity, Boolean isDelete) {

        //推送到优惠结算台
        SettlementDiscountDTO discountDTO = new SettlementDiscountDTO();
        discountDTO.setOperSubjectGuid(hsaLimitSpecialsActivity.getOperSubjectGuid());
        final SettlementDiscountOptionEnum anEnum = SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL;
        discountDTO.setOption(anEnum.getCode());
        if (Boolean.TRUE.equals(isDelete)) {
            //删除
            discountDTO.setDelDiscountGuids(Collections.singletonList(hsaLimitSpecialsActivity.getActivityCode()));
        } else {
            //新增、更新
            SettlementDiscountDTO.Discount discount = new SettlementDiscountDTO.Discount().
                    setDiscountType(anEnum.getType().getCode())
                    .setDiscountNum(anEnum.getLimitNum())
                    .setDiscountGuid(hsaLimitSpecialsActivity.getActivityCode())
                    .setDiscountDynamic(SPECIALS_TYPE_DES)
                    .setRelationRule(hsaLimitSpecialsActivity.getRelationRule())
                    .setDiscountName(hsaLimitSpecialsActivity.getName())
                    .setIsFirstAdd(hsaLimitSpecialsActivity.getIsPublish() == BooleanEnum.TRUE.getCode() ?
                            BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode());
            discountDTO.setDiscountList(Collections.singletonList(discount));
        }
        settlementSupport.syncSettlementDiscount(discountDTO);
    }

    private void saveActivityItem(LimitSpecialsActivityReqDTO reqDTO, HsaLimitSpecialsActivity specialsActivity) {
        List<LimitSpecialsActivityItemDTO> itemDTOList = reqDTO.getItemDTOList();
        List<HsaLimitSpecialsActivityItem> itemDOList = Lists.newArrayList();
        List<String> guids = guidGeneratorUtil.getGuids(HsaLimitSpecialsActivityItem.class.getSimpleName(), itemDTOList.size());
        for (int i = 0, itemDTOListSize = itemDTOList.size(); i < itemDTOListSize; i++) {
            LimitSpecialsActivityItemDTO itemDTO = itemDTOList.get(i);
            HsaLimitSpecialsActivityItem item = new HsaLimitSpecialsActivityItem();
            item.setGuid(guids.get(i));
            item.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            item.setActivityGuid(specialsActivity.getGuid());
            item.setCommodityId(itemDTO.getCommodityId());
            item.setCommodityCode(itemDTO.getCommodityCode());
            item.setCommodityType(itemDTO.getCommodityType());
            item.setCommodityName(itemDTO.getCommodityName());
            item.setSpecialsType(itemDTO.getSpecialsType());
            item.setSpecialsNumber(itemDTO.getSpecialsNumber());
            item.setLimitNumber(itemDTO.getLimitNumber());
            item.setChannel(itemDTO.getChannel());
            item.setSystem(itemDTO.getSystem());
            itemDOList.add(item);
        }
        activityItemService.remove(new LambdaQueryWrapper<HsaLimitSpecialsActivityItem>()
                .eq(HsaLimitSpecialsActivityItem::getActivityGuid, specialsActivity.getGuid())
                .eq(HsaLimitSpecialsActivityItem::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
        );
        activityItemService.saveBatch(itemDOList);
    }

    private void checkRequestParam(LimitSpecialsActivityReqDTO reqDTO) {
        // 当前时间 ＜结束时间
        if (!LocalDateTime.now().isBefore(reqDTO.getEndTime())) {
            throw new MemberMarketingException("活动结束时间已到，请重新设置");
        }
        if (GoodsApplicableStoreEnum.PORTION_STORE.getCode() == reqDTO.getIsAllStore() && CollectionUtils.isEmpty(reqDTO.getStoreDTOList())) {
            throw new MemberMarketingException("请选择活动门店");
        }
        List<LimitSpecialsActivityItemDTO> itemDTOList = reqDTO.getItemDTOList();
        if (CollectionUtils.isEmpty(itemDTOList)) {
            throw new MemberMarketingException("请选择活动商品");
        }
        if (!StringUtils.isEmpty(reqDTO.getGuid())) {
            Boolean checkActivityState = checkActivityState(reqDTO.getGuid(), reqDTO.getState());
            if (Boolean.TRUE.equals(checkActivityState)) {
                throw new MemberMarketingException("操作失败，活动状态已变更，请重试");
            }
        }
    }

    private void saveActivityStore(LimitSpecialsActivityReqDTO reqDTO, HsaLimitSpecialsActivity specialsActivity) {
        if (GoodsApplicableStoreEnum.PORTION_STORE.getCode() == reqDTO.getIsAllStore() && !CollectionUtils.isEmpty(reqDTO.getStoreDTOList())) {
            List<LimitSpecialsActivityStoreDTO> storeDTOList = reqDTO.getStoreDTOList();
            List<HsaLimitSpecialsActivityStore> storeDOList = new ArrayList<>();
            List<String> guids = guidGeneratorUtil.getGuids(HsaLimitSpecialsActivityStore.class.getSimpleName(), storeDTOList.size());
            for (int i = 0; i < storeDTOList.size(); i++) {
                LimitSpecialsActivityStoreDTO storeDTO = storeDTOList.get(i);
                HsaLimitSpecialsActivityStore activityStore = new HsaLimitSpecialsActivityStore();
                activityStore.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
                activityStore.setActivityGuid(specialsActivity.getGuid());
                activityStore.setStoreGuid(storeDTO.getStoreGuid());
                activityStore.setStoreName(storeDTO.getStoreName());
                activityStore.setStoreCode(storeDTO.getStoreNumber());
                activityStore.setGuid(guids.get(i));
                activityStore.setSystem(storeDTO.getSystem());
                storeDOList.add(activityStore);
            }
            activityStoreService.remove(new LambdaQueryWrapper<HsaLimitSpecialsActivityStore>()
                    .eq(HsaLimitSpecialsActivityStore::getActivityGuid, specialsActivity.getGuid())
                    .eq(HsaLimitSpecialsActivityStore::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
            );
            activityStoreService.saveBatch(storeDOList);
        }
    }

    public PageResult<LimitSpecialsActivityVO> pageQuery(LimitSpecialsActivityQO qo) {
        PageResult<LimitSpecialsActivityVO> pageResult = activityService.pageQuery(qo);
        List<LimitSpecialsActivityVO> activityVOList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(activityVOList)) {
            log.warn("限时特价活动列表为空");
            return PageUtil.pageResult(new PageInfo<>(activityVOList));
        }
        List<String> activityGuidList = activityVOList.stream()
                .map(LimitSpecialsActivityVO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        // 活动商品
        Map<String, Long> activityItemMap = queryActivityItem(activityGuidList);
        // 活动订单数
        Map<String, Long> activityRecrodMap = queryActivityRecord(activityGuidList);
        for (LimitSpecialsActivityVO activityVO : activityVOList) {
            activityVO.setState(ActivityStatusHelper.handleLimitSpecialsActivityState(
                    activityVO.getState(), activityVO.getStartTime(), activityVO.getEndTime()));
            activityVO.setActivityItem(activityItemMap.getOrDefault(activityVO.getGuid(), 0L));
            activityVO.setActivityOrder(activityRecrodMap.getOrDefault(activityVO.getGuid(), 0L));
        }
        return pageResult;
    }

    /**
     * 查询活动商品
     */
    private Map<String, Long> queryActivityItem(List<String> activityGuidList) {
        List<HsaLimitSpecialsActivityItem> activityItemList = activityItemService.list(
                new LambdaQueryWrapper<HsaLimitSpecialsActivityItem>()
                        .in(HsaLimitSpecialsActivityItem::getActivityGuid, activityGuidList)
        );
        Map<String, Long> activityItemMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(activityItemList)) {
            activityItemMap = activityItemList.stream()
                    .collect(Collectors.groupingBy(HsaLimitSpecialsActivityItem::getActivityGuid, Collectors.counting()));
        }
        return activityItemMap;
    }


    /**
     * 查询活动订单数
     */
    private Map<String, Long> queryActivityRecord(List<String> activityGuidList) {
        List<LimitSpecialsActivityStaticsVO> staticsList = activityRecordService.staticsOrderCountByActivityGuid(activityGuidList);
        return staticsList.stream()
                .collect(Collectors.toMap(LimitSpecialsActivityStaticsVO::getActivityGuid, LimitSpecialsActivityStaticsVO::getOrderCount, (key1, key2) -> key1));
    }

    public LimitSpecialsActivityDetailsVO get(String guid) {
        HsaLimitSpecialsActivity activity = activityService.queryActivityByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }
        LimitSpecialsActivityDetailsVO detailsVO = LimitSpecialsAssembler.activity2ActivityDetailsVO(activity);
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        // 使子线程也能获取到 requestAttributes
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(requestAttributes, true);

        // 适用于部分门店查询门店关联
        CompletableFuture<List<LimitSpecialsActivityStoreDTO>> storeFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleStoreInfo(detailsVO.getIsAllStore(), detailsVO.getGuid());
        }, marketingThreadExecutor).exceptionally(throwable -> {
            log.error("查询适用门店失败", throwable);
            throw new MemberMarketingException("无法获取适用门店");
        });

        // 指定会员
        CompletableFuture<ConsumptionGiftDetailsDTO> memberFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            UserContextUtils.put(JacksonUtils.writeValueAsString(headerUserInfo));
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleMemberInfo(detailsVO.getMemberDTO());
        }, marketingThreadExecutor).exceptionally(throwable -> {
            log.error("查询指定会员失败", throwable);
            throw new MemberMarketingException("无法获取指定会员");
        });

        // 活动商品
        CompletableFuture<List<LimitSpecialsActivityItemDTO>> itemFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            UserContextUtils.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleItemInfo(guid);
        }, marketingThreadExecutor).exceptionally(throwable -> {
            log.error("查询活动商品失败", throwable);
            throw new MemberMarketingException("无法获取活动商品");
        });

        CompletableFuture<Void> all = CompletableFuture.allOf(storeFuture, memberFuture, itemFuture);
        try {
            all.get();
            detailsVO.setStoreDTOList(storeFuture.get());
            detailsVO.setMemberDTO(memberFuture.get());
            detailsVO.setItemDTOList(itemFuture.get());
        } catch (Exception e) {
            log.error("查询活动详情失败", e);
            Thread.currentThread().interrupt();
            throw new MemberMarketingException("系统繁忙稍后再试");
        }

        return detailsVO;
    }

    private List<LimitSpecialsActivityItemDTO> handleItemInfo(String guid) {
        List<HsaLimitSpecialsActivityItem> itemList = activityItemService.queryByActivityGuid(guid);
        if (CollUtil.isEmpty(itemList)) {
            return Collections.emptyList();
        }

        // 按 system 分组，分别查详情并合并
        Map<String, List<HsaLimitSpecialsActivityItem>> systemMap = itemList.stream()
                .filter(c -> c.getSystem() != null)
                .collect(Collectors.groupingBy(HsaLimitSpecialsActivityItem::getSystem));

        List<ResCommodityBase> allCommodityDetails = systemMap.entrySet().stream()
                .flatMap(entry -> {
                    String systemName = entry.getKey();
                    List<String> ids = entry.getValue().stream()
                            .map(HsaLimitSpecialsActivityItem::getCommodityId)
                            .distinct()
                            .collect(Collectors.toList());
                    List<ResCommodityBase> details = externalSupport
                            .itemServer(SystemEnum.getSystemCodeBySystemName(systemName))
                            .listCommodityByDetail(new QueryArrayShopBase().setCommodityIdList(ids));
                    return CollUtil.isEmpty(details) ? java.util.stream.Stream.empty() : details.stream();
                })
                .collect(Collectors.toList());

        // 组装返回结果
        return itemList.stream()
                .map(item -> getResponseCouponCommodityVO(item, allCommodityDetails))
                .collect(Collectors.toList());
    }

    private LimitSpecialsActivityItemDTO getResponseCouponCommodityVO(HsaLimitSpecialsActivityItem item, List<ResCommodityBase> allCommodityDetails) {
        LimitSpecialsActivityItemDTO limitSpecialsActivityItemDTO = new LimitSpecialsActivityItemDTO();
        if (CollUtil.isEmpty(allCommodityDetails)) {
            buildRespCommodityByNull(item, limitSpecialsActivityItemDTO);
        } else {
            Map<String, ResCommodityBase> commodityBaseMap = allCommodityDetails.stream()
                    .collect(Collectors.toMap(ResCommodityBase::getCommodityId, resCommodityBase -> resCommodityBase, (k1, k2) -> k1));
            ResCommodityBase commodityBase = commodityBaseMap.get(item.getCommodityId());
            if (commodityBase == null) {
                buildRespCommodityByNull(item, limitSpecialsActivityItemDTO);
            } else {
                buildRespCommodityByBase(item, limitSpecialsActivityItemDTO, commodityBase);
            }
        }
        return limitSpecialsActivityItemDTO;
    }

    private void buildRespCommodityByNull(HsaLimitSpecialsActivityItem item,
                                          LimitSpecialsActivityItemDTO limitSpecialsActivityItemDTO) {
        limitSpecialsActivityItemDTO.setActivityGuid(item.getActivityGuid());
        limitSpecialsActivityItemDTO.setCommodityCode(item.getCommodityCode());
        limitSpecialsActivityItemDTO.setCommodityId(item.getCommodityId());
        limitSpecialsActivityItemDTO.setCommodityName(item.getCommodityName());
        limitSpecialsActivityItemDTO.setCommodityType(item.getCommodityType());
        limitSpecialsActivityItemDTO.setChannel(item.getChannel());
        limitSpecialsActivityItemDTO.setIsExist(BooleanEnum.FALSE.getCode());
        limitSpecialsActivityItemDTO.setSystem(item.getSystem());
        limitSpecialsActivityItemDTO.setSpecialsType(item.getSpecialsType());
        limitSpecialsActivityItemDTO.setSpecialsNumber(item.getSpecialsNumber());
        limitSpecialsActivityItemDTO.setLimitNumber(item.getLimitNumber());
    }

    private void buildRespCommodityByBase(HsaLimitSpecialsActivityItem item,
                                          LimitSpecialsActivityItemDTO limitSpecialsActivityItemDTO,
                                          ResCommodityBase commodityBase) {
        limitSpecialsActivityItemDTO.setActivityGuid(item.getActivityGuid());
        limitSpecialsActivityItemDTO.setCommodityCode(commodityBase.getCommodityCode());
        limitSpecialsActivityItemDTO.setCommodityId(item.getCommodityId());
        limitSpecialsActivityItemDTO.setCommodityName(commodityBase.getName());
        limitSpecialsActivityItemDTO.setCommodityType(item.getCommodityType());
        limitSpecialsActivityItemDTO.setChannel(item.getChannel());
        limitSpecialsActivityItemDTO.setIsExist(BooleanEnum.TRUE.getCode());
        limitSpecialsActivityItemDTO.setSystem(item.getSystem());
        limitSpecialsActivityItemDTO.setSpecialsType(item.getSpecialsType());
        limitSpecialsActivityItemDTO.setSpecialsNumber(item.getSpecialsNumber());
        limitSpecialsActivityItemDTO.setLimitNumber(item.getLimitNumber());
    }

    private List<LimitSpecialsActivityStoreDTO> handleStoreInfo(Integer isAllStore, String guid) {
        List<LimitSpecialsActivityStoreDTO> storeDTOList = new ArrayList<>();
        if (GoodsApplicableStoreEnum.PORTION_STORE.getCode() == isAllStore) {
            List<HsaLimitSpecialsActivityStore> storeList = activityStoreService.queryByActivityGuid(guid);
            if (!CollectionUtils.isEmpty(storeList)) {
                storeList.forEach(store -> {
                    LimitSpecialsActivityStoreDTO storeDTO = new LimitSpecialsActivityStoreDTO();
                    storeDTO.setStoreGuid(store.getStoreGuid());
                    storeDTO.setStoreNumber(store.getStoreCode());
                    storeDTO.setStoreName(store.getStoreName());
                    storeDTO.setActivityGuid(store.getActivityGuid());
                    storeDTO.setSystem(store.getSystem());
                    storeDTOList.add(storeDTO);
                });
                return storeDTOList;
            }
        }
        return storeDTOList;
    }

    private ConsumptionGiftDetailsDTO handleMemberInfo(ConsumptionGiftDetailsDTO memberDTO) {
        if (!ObjectUtils.isEmpty(memberDTO)) {
            List<String> guidList = memberDTO.getGuidList();
            RequestQueryMemberBaseQO memberBaseQO = new RequestQueryMemberBaseQO();
            memberBaseQO.setMemberGuidList(guidList);
            memberBaseQO.setPageSize(guidList.size());
            memberBaseQO.setCurrentPage(1);
            com.holderzone.framework.util.Page<ResponseOperationMemberInfo> memberInfoPage =
                    externalSupport.memberServer(ThreadLocalCache.getSystem()).getOperationMemberInfoPage(memberBaseQO);
            log.info("[限时特价活动]查询会员信息={}", JacksonUtils.writeValueAsString(memberInfoPage));
            List<ResponseOperationMemberInfo> memberInfoList = memberInfoPage.getData();
            if (CollUtil.isNotEmpty(memberInfoList)) {
                List<OperationMemberInfoVO> memberInfoGuidList = getMemberInfoVOList(memberInfoList);
                memberDTO.setMemberInfoGuidList(memberInfoGuidList);
            }
        }
        return memberDTO;
    }

    private List<OperationMemberInfoVO> getMemberInfoVOList(List<ResponseOperationMemberInfo> memberInfoList) {
        List<OperationMemberInfoVO> memberInfoGuidList = Lists.newArrayList();
        for (ResponseOperationMemberInfo memberInfo : memberInfoList) {
            OperationMemberInfoVO operationMemberInfoVO = new OperationMemberInfoVO();
            operationMemberInfoVO.setMemberAccount(memberInfo.getMemberAccount());
            operationMemberInfoVO.setPhoneNum(memberInfo.getPhoneNum());
            operationMemberInfoVO.setUserName(memberInfo.getUserName());
            operationMemberInfoVO.setGuid(memberInfo.getGuid());
            memberInfoGuidList.add(operationMemberInfoVO);
        }
        return memberInfoGuidList;
    }

    public void delete(String guid) {
        HsaLimitSpecialsActivity activity = activityService.queryActivityByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }
        if (Objects.equals(BooleanEnum.TRUE.getCode(), activity.getIsPublish())) {
            throw new MemberMarketingException("已发布过的活动不可删除");
        }
        activityService.logicDelete(guid);

        //同步结算台
        activity.setIsDelete((byte) BooleanEnum.TRUE.getCode());
        LimitSpecialsActivityDTO limitSpecialsActivityDTO = new LimitSpecialsActivityDTO();
        BeanUtils.copyProperties(activity, limitSpecialsActivityDTO);
        sendActivity(limitSpecialsActivityDTO, Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<LimitSpecialsActivityPublishVO> updateState(String guid, Integer state) {
        HsaLimitSpecialsActivity activity = activityService.queryActivityByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }
        if (state == ActivityStateEnum.RUNNING.getCode()) {
            List<LimitSpecialsActivityPublishVO> activityPublishVOList = checkLimitSpecialsActivityPublish(guid, activity);
            if (!CollectionUtils.isEmpty(activityPublishVOList)) {
                return activityPublishVOList;
            }
            LimitSpecialsActivityDTO limitSpecialsActivityDTO = new LimitSpecialsActivityDTO();
            limitSpecialsActivityDTO.setIsPublish(activity.getIsPublish());
            activity.setState(state);
            if (activity.getIsPublish() == BooleanEnum.FALSE.getCode()) {
                activity.setIsPublish(BooleanEnum.TRUE.getCode());
            }
            activityService.updateByGuid(activity);
            // 加入缓存
            LimitSpecialsActivityDetailsVO detailsVO = get(guid);
            cacheService.saveLimitSpecialsActivityCache(detailsVO);
            //同步结算台
            limitSpecialsActivityDTO.setActivityCode(activity.getActivityCode());
            limitSpecialsActivityDTO.setOperSubjectGuid(activity.getOperSubjectGuid());
            limitSpecialsActivityDTO.setName(activity.getName());
            limitSpecialsActivityDTO.setRelationRule(activity.getRelationRule());
            sendActivity(limitSpecialsActivityDTO, Boolean.FALSE);
        } else if (state == ActivityStateEnum.STOP.getCode()) {
            // 暂停
            activity.setState(state);
            activityService.updateByGuid(activity);
            // 清除缓存
            cacheService.deleteLimitSpecialsActivityCache(guid);
        } else {
            throw new MemberMarketingException("状态类型错误");
        }
        return Lists.newArrayList();
    }

    private List<LimitSpecialsActivityPublishVO> checkLimitSpecialsActivityPublish(String guid, HsaLimitSpecialsActivity activity) {
        // 发布/开启 判断当前活动中，是否存在相同时间内、相同场景、相同门店、相同商品组合重复
        CheckActivityQO checkActivityQO = getCheckActivityQO(guid, activity);
        log.info("[校验活动发布]checkActivityQO={}", JacksonUtils.writeValueAsString(checkActivityQO));
        List<LimitSpecialsActivityPublishVO> activityPublishList = activityService.checkActivity(checkActivityQO);
        log.info("[校验活动发布]activityPublishList={}", JacksonUtils.writeValueAsString(activityPublishList));
        if (!CollectionUtils.isEmpty(activityPublishList)) {
            // 校验活动时段
            return activityPublishList.stream().filter(pubActivity -> {
                if (Objects.equals(BooleanEnum.FALSE.getCode(), activity.getIsLimitPeriod())
                        || Objects.equals(BooleanEnum.FALSE.getCode(), pubActivity.getIsLimitPeriod())) {
                    log.warn("有活动为不限制活动时段则不限制");
                    return true;
                }
                String limitPeriodJson = activity.getLimitPeriodJson();
                String pubLimitPeriodJson = pubActivity.getLimitPeriodJson();
                if (StringUtils.isEmpty(limitPeriodJson) || StringUtils.isEmpty(pubLimitPeriodJson)) {
                    log.warn("限制时段限制类型json为空,当作不限制");
                    return true;
                }

                DateTimeHandleBO handleBO = getHandleBO(activity, limitPeriodJson);
                List<Pair<LocalDateTime, LocalDateTime>> currentActivityTimeList = ActivityTimeHelper.getDateTimePairs(handleBO);

                DateTimeHandleBO handlePubBO = getHandlePubBO(pubActivity, pubLimitPeriodJson);
                List<Pair<LocalDateTime, LocalDateTime>> dbActivityTimeList = ActivityTimeHelper.getDateTimePairs(handlePubBO);

                // 时间交集循环判断
                return ActivityTimeHelper.checkDateTimeIntersection(currentActivityTimeList, dbActivityTimeList);
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private DateTimeHandleBO getHandlePubBO(LimitSpecialsActivityPublishVO pubActivity,
                                            String pubLimitPeriodJson) {
        DateTimeHandleBO handlePubBO = new DateTimeHandleBO();
        handlePubBO.setTimeVOList(JacksonUtils.toObjectList(EffectiveTimeVO.class, pubLimitPeriodJson));
        handlePubBO.setLimitPeriodType(pubActivity.getLimitPeriodType());
        handlePubBO.setStartTime(pubActivity.getStartTime());
        handlePubBO.setEndTime(pubActivity.getEndTime());
        return handlePubBO;
    }

    private DateTimeHandleBO getHandleBO(HsaLimitSpecialsActivity activity,
                                         String limitPeriodJson) {
        DateTimeHandleBO handleBO = new DateTimeHandleBO();
        handleBO.setTimeVOList(JacksonUtils.toObjectList(EffectiveTimeVO.class, limitPeriodJson));
        handleBO.setLimitPeriodType(activity.getLimitPeriodType());
        handleBO.setStartTime(activity.getStartTime());
        handleBO.setEndTime(activity.getEndTime());
        return handleBO;
    }


    private CheckActivityQO getCheckActivityQO(String guid, HsaLimitSpecialsActivity activity) {
        CheckActivityQO checkActivityQO = new CheckActivityQO();
        checkActivityQO.setGuid(guid);
        checkActivityQO.setStartTime(activity.getStartTime());
        checkActivityQO.setEndTime(activity.getEndTime());
        checkActivityQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<String> commodityIdList = getCommodityIdList(guid);
        checkActivityQO.setCommodityIdList(commodityIdList);
        if (Objects.equals(GoodsApplicableStoreEnum.PORTION_STORE.getCode(), activity.getIsAllStore())) {
            List<String> storeIdList = getStoreIdList(guid);
            checkActivityQO.setStoreIdList(storeIdList);
        }
        if (Objects.equals(ApplyBusinessTypeEnum.APPOINT_BUSINESS.getCode(), activity.getApplyBusiness())) {
            List<String> applyBusinessList = com.google.common.collect.Lists.newArrayList(activity.getApplyBusinessJson().split(","));
            checkActivityQO.setApplyBusinessList(applyBusinessList);
        }
        return checkActivityQO;
    }

    private List<String> getStoreIdList(String guid) {
        List<HsaLimitSpecialsActivityStore> activityStoreList = activityStoreService.queryByActivityGuid(guid);
        List<String> storeIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(activityStoreList)) {
            storeIdList = activityStoreList.stream()
                    .map(HsaLimitSpecialsActivityStore::getStoreGuid)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return storeIdList;
    }

    private List<String> getCommodityIdList(String guid) {
        List<HsaLimitSpecialsActivityItem> itemList = activityItemService.queryByActivityGuid(guid);
        List<String> commodityIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemList)) {
            commodityIdList = itemList.stream()
                    .map(HsaLimitSpecialsActivityItem::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return commodityIdList;
    }

    public Boolean checkActivityState(String guid, Integer state) {
        HsaLimitSpecialsActivity activity = activityService.queryActivityByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            return Boolean.TRUE;
        }
        Integer activityState = ActivityStatusHelper.handleLimitSpecialsActivityState(
                activity.getState(), activity.getStartTime(), activity.getEndTime());
        return !Objects.equals(activityState, state);
    }

    public List<LimitSpecialsActivityItemVO> queryActivityCommodity(LimitSpecialsActivityItemQO query) {
        replenishQueryParam(query);
        log.info("[queryActivityByMember]query={}", JacksonUtils.writeValueAsString(query));
        List<HsaLimitSpecialsActivity> activityList = activityService.queryActivityByMember(query);
        log.info("[queryActivityByMember]activityList={}", JacksonUtils.writeValueAsString(activityList));
        if (CollectionUtils.isEmpty(activityList)) {
            log.warn("未查询到匹配活动");
            return Lists.newArrayList();
        }
        LocalDateTime now = LocalDateTime.now();
        log.info("活动查询时间={}", now);
        List<HsaLimitSpecialsActivity> specialsActivityList = activityList.stream().filter(activity -> {
            if (Objects.equals(BooleanEnum.FALSE.getCode(), activity.getIsLimitPeriod())) {
                log.warn("活动为不限制活动时段则不限制");
                return true;
            }
            String limitPeriodJson = activity.getLimitPeriodJson();
            if (StringUtils.isEmpty(limitPeriodJson)) {
                log.warn("限制时段限制类型json为空,当作不限制");
                return true;
            }

            DateTimeHandleBO handleBO = getHandleBO(activity, limitPeriodJson);
            List<Pair<LocalDateTime, LocalDateTime>> currentActivityTimeList = ActivityTimeHelper.getDateTimePairs(handleBO);
            log.info("解析时段={}", JacksonUtils.writeValueAsString(currentActivityTimeList));

            // 时间命中判断
            return ActivityTimeHelper.checkDateTimeHit(currentActivityTimeList, now);
        }).collect(Collectors.toList());
        log.info("[命中判断后活动]specialsActivityList={}", JacksonUtils.writeValueAsString(specialsActivityList));
        if (CollectionUtils.isEmpty(specialsActivityList)) {
            log.warn("时间校验未命中");
            return Lists.newArrayList();
        }
        List<String> activityGuidList = specialsActivityList.stream()
                .map(HsaLimitSpecialsActivity::getGuid)
                .distinct()
                .collect(Collectors.toList());
        query.setActivityGuidList(activityGuidList);
        log.info("[queryActivityItemByMember]query={}", JacksonUtils.writeValueAsString(query));
        return activityService.queryActivityItemByMember(query);
    }

    private void replenishQueryParam(LimitSpecialsActivityItemQO query) {
        if (StringUtils.isEmpty(query.getOperSubjectGuid())) {
            throw new MemberMarketingException("运营主体为空");
        }
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        headerUserInfo.setOperSubjectGuid(query.getOperSubjectGuid());
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
    }


    /**
     * 下单
     */
    public void pay(LimitSpecialsBO biz) {
        // 查询活动
        HsaLimitSpecialsActivity limitSpecialsActivity = activityService.queryByGuid(biz.getLimitSpecialsActivity().getGuid());
        if (Objects.isNull(limitSpecialsActivity)) {
            log.error("限时特价活动查询不存在, guid:{}", biz.getLimitSpecialsActivity().getGuid());
            return;
        }
        HsaLimitSpecialsActivityRecord limitSpecialsActivityRecord = biz.getLimitSpecialsActivityRecord();
        limitSpecialsActivityRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaLimitSpecialsActivityRecord.class.getSimpleName()));
        limitSpecialsActivityRecord.setName(limitSpecialsActivity.getName());
        limitSpecialsActivityRecord.setOrderState(OrderStateEnum.FINISH.getCode());
        limitSpecialsActivityRecord.setActivityCode(limitSpecialsActivity.getActivityCode());
        limitSpecialsActivityRecord.setOperSubjectGuid(limitSpecialsActivity.getOperSubjectGuid());
        // save
        activityRecordService.saveRecord(limitSpecialsActivityRecord);
    }


    /**
     * 退款
     */
    public void refund(LimitSpecialsBO biz) {
        activityRecordService.updateRefundStateByOrderGuid(biz.getLimitSpecialsActivityRecord().getOrderGuid());
    }

    public List<LimitSpecialsActivityDetailsVO> listByOperSubjectGuid(String operSubjectGuid) {
        List<HsaLimitSpecialsActivity> activityList = activityService.queryActivityByOperSubjectGuid(operSubjectGuid);
        if (CollectionUtils.isEmpty(activityList)) {
            return Lists.newArrayList();
        }
        List<LimitSpecialsActivityDetailsVO> detailsVOList = LimitSpecialsAssembler.activityList2ActivityDetailsVOList(activityList);

        // 适用于部分门店查询门店关联
        List<LimitSpecialsActivityStoreDTO> storeDTOList = handleStoreInfoList(detailsVOList);
        Map<String, List<LimitSpecialsActivityStoreDTO>> storeMap = storeDTOList.stream()
                .collect(Collectors.groupingBy(LimitSpecialsActivityStoreDTO::getActivityGuid));


        // 活动商品
        List<LimitSpecialsActivityItemDTO> activityItemDTOList = handleItemInfoList(detailsVOList);
        Map<String, List<LimitSpecialsActivityItemDTO>> itemMap = activityItemDTOList.stream()
                .collect(Collectors.groupingBy(LimitSpecialsActivityItemDTO::getActivityGuid));

        handleActivityDetailsList(detailsVOList, storeMap, itemMap);

        return detailsVOList;
    }

    private void handleActivityDetailsList(List<LimitSpecialsActivityDetailsVO> detailsVOList,
                                           Map<String, List<LimitSpecialsActivityStoreDTO>> storeMap,
                                           Map<String, List<LimitSpecialsActivityItemDTO>> itemMap) {
        detailsVOList.forEach(detailsVO -> {
            // 适用门店
            List<LimitSpecialsActivityStoreDTO> storeList = storeMap.get(detailsVO.getGuid());
            if (CollectionUtils.isEmpty(storeList)) {
                storeList = Lists.newArrayList();
            }
            detailsVO.setStoreDTOList(storeList);

            // 活动商品
            List<LimitSpecialsActivityItemDTO> itemDTOList = itemMap.get(detailsVO.getGuid());
            if (CollectionUtils.isEmpty(itemDTOList)) {
                itemDTOList = Lists.newArrayList();
            }
            detailsVO.setItemDTOList(itemDTOList);
        });
    }

    private List<LimitSpecialsActivityItemDTO> handleItemInfoList(List<LimitSpecialsActivityDetailsVO> detailsVOList) {
        List<String> activityGuidList = detailsVOList.stream()
                .map(LimitSpecialsActivityDetailsVO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaLimitSpecialsActivityItem> itemList = activityItemService.queryByActivityGuidList(activityGuidList);
        if (CollectionUtils.isEmpty(itemList)) {
            log.warn("活动关联商品为空,activityGuidList={}", activityGuidList);
            return Lists.newArrayList();
        }
        return LimitSpecialsAssembler.activityItemList2ActivityitemDTOList(itemList);
    }

    private List<LimitSpecialsActivityStoreDTO> handleStoreInfoList(List<LimitSpecialsActivityDetailsVO> detailsVOList) {
        List<String> activityGuidList = detailsVOList.stream()
                .filter(a -> Objects.equals(a.getIsAllStore(), GoodsApplicableStoreEnum.PORTION_STORE.getCode()))
                .map(LimitSpecialsActivityDetailsVO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<LimitSpecialsActivityStoreDTO> storeDTOList = new ArrayList<>();
        List<HsaLimitSpecialsActivityStore> storeList = activityStoreService.queryByActivityGuidList(activityGuidList);
        if (!CollectionUtils.isEmpty(storeList)) {
            storeList.forEach(store -> {
                LimitSpecialsActivityStoreDTO storeDTO = new LimitSpecialsActivityStoreDTO();
                storeDTO.setStoreGuid(store.getStoreGuid());
                storeDTO.setStoreNumber(store.getStoreCode());
                storeDTO.setStoreName(store.getStoreName());
                storeDTO.setActivityGuid(store.getActivityGuid());
                storeDTOList.add(storeDTO);
            });
        }
        return storeDTOList;
    }

    public List<LimitSpecialsActivityRunVO> queryMarketActivityByRun(LimitSpecialsActivityRunQO query) {
        return activityService.queryMarketActivityByRun(query);
    }

    public List<LimitSpecialsActivityRunVO> querySettleCommodityByRun(LimitSpecialsActivityRunQO query) {
        return activityService.querySettleCommodityByRun(query);
    }

    public void limitSpecialsSynState() {
        log.info("限时特价活动状态同步,当前时间={}", LocalDateTime.now());
        List<QueryEndTimeActivityVO> queryEndTimeActivityList = activityService.queryEndTimeActivity();

        if (CollUtil.isNotEmpty(queryEndTimeActivityList)) {
            SettlementDiscountDTO discountDTO = new SettlementDiscountDTO();
            queryEndTimeActivityList.stream()
                    .collect(Collectors.groupingBy(QueryEndTimeActivityVO::getOperSubjectGuid))
                    .forEach((operSubjectGuid, activityList) -> {
                        List<String> delDiscountGuids = activityList.stream()
                                .map(QueryEndTimeActivityVO::getActivityCode)
                                .collect(Collectors.toList());
                        discountDTO.setDelDiscountGuids(delDiscountGuids);
                        discountDTO.setOperSubjectGuid(operSubjectGuid);
                        discountDTO.setOption(SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode());
                        settlementSupport.syncSettlementDiscount(discountDTO);
                    });

            List<String> delDiscountGuids = queryEndTimeActivityList.stream()
                    .map(QueryEndTimeActivityVO::getActivityCode)
                    .collect(Collectors.toList());
            //批量修改状态
            activityService.updateStateEnd(delDiscountGuids);
        }
    }

    public void saveRecord(List<SynOrderDiscountQO> recordQO) {

        List<String> discountGuidList = recordQO.stream().map(SynOrderDiscountQO::getDiscountGuid).collect(Collectors.toList());
        Map<String, HsaLimitSpecialsActivity> limitSpecialsActivityMap = activityService.queryActivityByCode(discountGuidList)
                .stream()
                .collect(Collectors.toMap(HsaLimitSpecialsActivity::getActivityCode, Function.identity(), (obj1, obj2) -> obj1));

        log.info("限时特价活动记录批量新增,recordQO={}", JSON.toJSONString(recordQO));
        List<HsaLimitSpecialsActivityRecord> hsaLimitSpecialsActivityRecords = Lists.newArrayList();
        for (SynOrderDiscountQO synOrderDiscountQO : recordQO) {
            HsaLimitSpecialsActivityRecord activityRecord = new HsaLimitSpecialsActivityRecord();
            HsaLimitSpecialsActivity hsaLimitSpecialsActivity = limitSpecialsActivityMap.get(synOrderDiscountQO.getDiscountGuid());
            if (Objects.isNull(hsaLimitSpecialsActivity)) {
                log.info("限时特价活动不存在,订单={}", synOrderDiscountQO.getOrderNo());
                continue;
            }
            getLimitSpecialsActivityRecord(synOrderDiscountQO, activityRecord, hsaLimitSpecialsActivity, hsaLimitSpecialsActivityRecords);
        }

        if (CollUtil.isNotEmpty(hsaLimitSpecialsActivityRecords))
            activityRecordService.saveBatch(hsaLimitSpecialsActivityRecords);
    }


    public void updateRecord(BarkOrderDiscountCallbackQO callbackQO) {
        log.info("限时特价活动统计记录更新,callbackQO={}", JSON.toJSONString(callbackQO));
        HsaLimitSpecialsActivityRecord hsaLimitSpecialsActivityRecord = activityRecordService.queryByOrderGuid(callbackQO.getOperSubjectGuid(), callbackQO.getOrderNo());

        if (Objects.nonNull(hsaLimitSpecialsActivityRecord)) {
            hsaLimitSpecialsActivityRecord.setOrderBackFee(hsaLimitSpecialsActivityRecord.getOrderBackFee().add(callbackQO.getRefundAmount()));
            if (Objects.nonNull(callbackQO.getDiscountAmount())){
                hsaLimitSpecialsActivityRecord.setOrderDiscountFee(hsaLimitSpecialsActivityRecord.getOrderDiscountFee().subtract(callbackQO.getDiscountAmount()));
            }
            //若是全额退款 则修改订单状态为已退款
            if (callbackQO.getRefundType() == BooleanEnum.TRUE.getCode()) {
                hsaLimitSpecialsActivityRecord.setOrderState(OrderStateEnum.REFUND.getCode());
            }

            activityRecordService.updateByGuid(hsaLimitSpecialsActivityRecord);
        }
    }

    private void getLimitSpecialsActivityRecord(SynOrderDiscountQO synOrderDiscountQO,
                                                HsaLimitSpecialsActivityRecord activityRecord,
                                                HsaLimitSpecialsActivity hsaLimitSpecialsActivity,
                                                List<HsaLimitSpecialsActivityRecord> hsaLimitSpecialsActivityRecords) {
        activityRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaLimitSpecialsActivityRecord.class.getSimpleName()));
        activityRecord.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //活动信息
        activityRecord.setActivityCode(hsaLimitSpecialsActivity.getActivityCode());
        activityRecord.setActivityGuid(hsaLimitSpecialsActivity.getGuid());
        activityRecord.setName(hsaLimitSpecialsActivity.getName());

        //订单信息
        activityRecord.setStoreName(synOrderDiscountQO.getStoreName());
        activityRecord.setOrderActuallyFee(synOrderDiscountQO.getOrderActuallyFee());
        activityRecord.setOrderNo(synOrderDiscountQO.getOrderNo());
        activityRecord.setOrderType(MarketConsumptionOrderTypeEnum.STORE_ORDER.getCode());
        activityRecord.setOrderFee(synOrderDiscountQO.getOrderFee());
        activityRecord.setOrderDiscountFee(synOrderDiscountQO.getOrderDiscountFee());
        activityRecord.setOrderTime(synOrderDiscountQO.getOrderTime());
        activityRecord.setOrderSource(synOrderDiscountQO.getOrderSource());
        activityRecord.setOrderState(synOrderDiscountQO.getOrderState());
        activityRecord.setOrderGuid(synOrderDiscountQO.getOrderGuid());

        //会员数据
        activityRecord.setMemberGuid(synOrderDiscountQO.getMemberGuid());
        activityRecord.setMemberPhone(synOrderDiscountQO.getPhoneNum());
        activityRecord.setMemberName(synOrderDiscountQO.getUserName());

        hsaLimitSpecialsActivityRecords.add(activityRecord);
    }

    public List<LimitSpecialsActivityRunVO> queryRunInfo(LimitSpecialsActivityRunQO query) {
        List<QueryLimitSpecialsActivityVO> activityList = activityService.queryRunInfo(query);
        if (CollUtil.isEmpty(activityList)) {
            log.warn("未查询到匹配活动");
            return Lists.newArrayList();
        }

        // 组装活动数据,此处的处理只处理了此时需要的
        List<LimitSpecialsActivityRunVO> activityRunVOList = Lists.newArrayList();
        for (QueryLimitSpecialsActivityVO activityVO : activityList) {
            LimitSpecialsActivityRunVO activityRunVO = new LimitSpecialsActivityRunVO();
            BeanUtils.copyProperties(activityVO, activityRunVO);
            activityRunVO.setActivityName(activityVO.getName());
            activityRunVO.setActivityCode(activityVO.getActivityCode());
            activityRunVO.setIsEnabled(activityVO.getIsEnabled());
            if (!StringUtils.isEmpty(activityVO.getLabelGuidJson())) {
                List<String> labelGuidList = com.google.common.collect.Lists.newArrayList(activityVO.getLabelGuidJson().split(","));
                activityRunVO.setLabelGuidList(labelGuidList);
            }
            activityRunVOList.add(activityRunVO);
        }
        return activityRunVOList;
    }
}
