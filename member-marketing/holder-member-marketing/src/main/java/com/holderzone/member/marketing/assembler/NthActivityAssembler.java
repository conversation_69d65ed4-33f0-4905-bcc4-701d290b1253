package com.holderzone.member.marketing.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.nth.NthActivityItemDTO;
import com.holderzone.member.common.dto.nth.NthActivityReqDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.nth.NthActivityGroupTypeEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.qo.gift.ConsumptionGiftDetailsDTO;
import com.holderzone.member.common.vo.nth.NthActivityDetailsVO;
import com.holderzone.member.marketing.entity.nth.HsaNthActivity;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;


/**
 * 第N份活动转换
 */
@Slf4j
public class NthActivityAssembler {

    private NthActivityAssembler() {
    }


    public static HsaNthActivity activityReqDTO2Activity(NthActivityReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO)) {
            throw new MemberMarketingException("参数为空");
        }
        HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid(reqDTO.getGuid());
        nthActivity.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        nthActivity.setName(reqDTO.getName());
        nthActivity.setStartTime(reqDTO.getStartTime());
        nthActivity.setEndTime(reqDTO.getEndTime());
        nthActivity.setIsLimitPeriod(reqDTO.getIsLimitPeriod());
        nthActivity.setLimitPeriodType(reqDTO.getEquitiesTimeLimitedType());
        nthActivity.setLimitPeriodJson(reqDTO.getEquitiesTimeLimitedJson());
        nthActivity.setDiscountRule(reqDTO.getPerCount() + "," + reqDTO.getPerDiscount().stripTrailingZeros().toPlainString());
        nthActivity.setRelationRule(reqDTO.getRelationRule());
        nthActivity.setApplyBusiness(reqDTO.getApplyBusiness());
        if (CollUtil.isNotEmpty(reqDTO.getApplyBusinessList())) {
            String applyBusinessJson = String.join(",", reqDTO.getApplyBusinessList());
            nthActivity.setApplyBusinessJson(applyBusinessJson);
        }
        nthActivity.setIsAllStore(reqDTO.getIsAllStore());
        nthActivity.setGroupType(reqDTO.getGroupType());

        // 指定标签
        if (NthActivityGroupTypeEnum.SPECIFIED_MEMBER.getCode() == reqDTO.getGroupType() && !ObjectUtils.isEmpty(reqDTO.getLabelDTO())) {
            nthActivity.setConditionLabelJson(JacksonUtils.writeValueAsString(reqDTO.getLabelDTO()));
        } else {
            nthActivity.setConditionLabelJson(null);
        }

        // 指定等级
        if (NthActivityGroupTypeEnum.SPECIFIED_MEMBER.getCode() == reqDTO.getGroupType() && !ObjectUtils.isEmpty(reqDTO.getGradeDTO())) {
            nthActivity.setConditionGradeJson(JacksonUtils.writeValueAsString(reqDTO.getGradeDTO()));
        } else {
            nthActivity.setConditionGradeJson(null);
        }

        // 指定会员
        if (NthActivityGroupTypeEnum.SPECIFIED_MEMBER.getCode() == reqDTO.getGroupType() && !ObjectUtils.isEmpty(reqDTO.getMemberDTO())) {
            nthActivity.setConditionMemberJson(JacksonUtils.writeValueAsString(reqDTO.getMemberDTO()));
        } else {
            nthActivity.setConditionMemberJson(null);
        }

        return nthActivity;
    }

    public static NthActivityDetailsVO activity2ActivityDetailsVO(HsaNthActivity activity) {
        NthActivityDetailsVO nthActivityDetails = new NthActivityDetailsVO();
        nthActivityDetails.setGuid(activity.getGuid());
        nthActivityDetails.setName(activity.getName());
        nthActivityDetails.setStartTime(activity.getStartTime());
        nthActivityDetails.setEndTime(activity.getEndTime());
        nthActivityDetails.setIsLimitPeriod(activity.getIsLimitPeriod());
        nthActivityDetails.setEquitiesTimeLimitedType(activity.getLimitPeriodType());
        nthActivityDetails.setEquitiesTimeLimitedJson(activity.getLimitPeriodJson());
        nthActivityDetails.setRelationRule(activity.getRelationRule());
        String[] discountRuleSplit = activity.getDiscountRule().split(",");
        if (discountRuleSplit.length == 2) {
            nthActivityDetails.setPerCount(Integer.valueOf(discountRuleSplit[0]));
            nthActivityDetails.setPerDiscount(new BigDecimal(discountRuleSplit[1]));
        } else {
            log.error("优惠规则数据异常:{}", JacksonUtils.writeValueAsString(activity));
        }
        nthActivityDetails.setApplyBusiness(activity.getApplyBusiness());
        if (StrUtil.isNotEmpty(activity.getApplyBusinessJson())) {
            List<String> applyBusinessList = Lists.newArrayList(activity.getApplyBusinessJson().split(","));
            nthActivityDetails.setApplyBusinessList(applyBusinessList);
        }


        nthActivityDetails.setIsAllStore(activity.getIsAllStore());
        nthActivityDetails.setGroupType(activity.getGroupType());

        if (NthActivityGroupTypeEnum.SPECIFIED_MEMBER.getCode() == activity.getGroupType()) {
            // 指定标签
            if (StrUtil.isNotEmpty(activity.getConditionLabelJson())) {
                ConsumptionGiftDetailsDTO labelDTO = JacksonUtils.toObject(ConsumptionGiftDetailsDTO.class, activity.getConditionLabelJson());
                nthActivityDetails.setLabelDTO(labelDTO);
            }

            // 指定等级
            if (StrUtil.isNotEmpty(activity.getConditionGradeJson())) {
                ConsumptionGiftDetailsDTO gradeDTO = JacksonUtils.toObject(ConsumptionGiftDetailsDTO.class, activity.getConditionGradeJson());
                nthActivityDetails.setGradeDTO(gradeDTO);
            }

            // 指定会员
            if (StrUtil.isNotEmpty(activity.getConditionMemberJson())) {
                ConsumptionGiftDetailsDTO memberDTO = JacksonUtils.toObject(ConsumptionGiftDetailsDTO.class, activity.getConditionMemberJson());
                nthActivityDetails.setMemberDTO(memberDTO);
            }
        }

        return nthActivityDetails;
    }

    public static List<NthActivityItemDTO> activityItemList2ActivityitemDTOList(List<HsaNthActivityItem> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return Lists.newArrayList();
        }
        List<NthActivityItemDTO> itemDTOList = Lists.newArrayList();
        for (HsaNthActivityItem activityItem : itemList) {
            NthActivityItemDTO itemDTO = new NthActivityItemDTO();
            itemDTO.setCommodityId(activityItem.getCommodityId());
            itemDTO.setCommodityCode(activityItem.getCommodityCode());
            itemDTO.setCommodityName(activityItem.getCommodityName());
            itemDTO.setCommodityType(activityItem.getCommodityType());
            itemDTO.setChannel(activityItem.getChannel());
            itemDTO.setActivityGuid(activityItem.getActivityGuid());
            itemDTO.setIsExist(BooleanEnum.TRUE.getCode());
            itemDTO.setSystem(activityItem.getSystem());
            itemDTOList.add(itemDTO);
        }
        return itemDTOList;
    }

    public static List<NthActivityDetailsVO> activityList2ActivityDetailsVOList(List<HsaNthActivity> activityList) {
        if (CollectionUtils.isEmpty(activityList)) {
            return Lists.newArrayList();
        }
        List<NthActivityDetailsVO> activityDetailsVOList = Lists.newArrayList();
        activityList.forEach(activity -> {
            NthActivityDetailsVO detailsVO = activity2ActivityDetailsVO(activity);
            activityDetailsVOList.add(detailsVO);
        });
        return activityDetailsVOList;
    }
}
