package com.holderzone.member.marketing.controller.redeem;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.redeem.*;
import com.holderzone.member.marketing.service.redeem.HsaRedeemCodeDtlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 兑换码兑换码管理
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/redeem/code/dtl")
public class HsaRedeemCodeDtlController {

    @Resource
    private HsaRedeemCodeDtlService hsaRedeemCodeDtlService;

    /**
     * 分页查询兑换码
     */
    @PostMapping("/queryPage")
    public Result<PageResult<RespondRedeemCodePageVO>> queryPage(@RequestBody RequestRedeemCodeDtlPageQO request) {
        return Result.success(hsaRedeemCodeDtlService.queryPage(request));
    }

    /**
     * 根据会员查询兑换记录
     */
    @PostMapping("/queryPageByMember")
    public Result<PageResult<RespondMemberRedeemPageVO>> queryPageByMember(@RequestBody RequestRedeemCodeDtlPageQO request){
        return Result.success(hsaRedeemCodeDtlService.queryPageByMember(request));
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void export(@RequestBody RequestRedeemCodeDtlPageQO request, HttpServletResponse response) {
        hsaRedeemCodeDtlService.export(request, response);
    }

    /**
     * 统计兑换码
     */
    @PostMapping("/queryStatistics")
    public Result<RespondStatisticsVO> queryStatistics(@RequestBody RequestRedeemCodeDtlPageQO request) {
        return Result.success(hsaRedeemCodeDtlService.queryStatistics(request));
    }

    /**
     * 批量作废兑换码
     */
    @PostMapping("/batchCancelRedeemCode")
    public Result<Boolean> batchCancelRedeemCode(@RequestBody List<String> codeGuidList) {
        hsaRedeemCodeDtlService.batchCancelRedeemCode(codeGuidList);
        return Result.success();
    }

    /**
     * 批量修改兑换码会员信息
     */
    @PostMapping("/batchRedeemCodeMemberInfo")
    public Result<Void> batchRedeemCodeMemberInfo(@RequestBody RequestUpdateDtlQO request) {
        hsaRedeemCodeDtlService.batchRedeemCodeMemberInfo(request);
        return Result.success();
    }
}
