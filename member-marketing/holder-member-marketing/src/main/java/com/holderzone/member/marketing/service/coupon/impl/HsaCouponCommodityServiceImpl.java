package com.holderzone.member.marketing.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryArrayShopBase;
import com.holderzone.member.common.dto.base.ResCommodityBase;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.coupon.RequestCouponCommodityQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.coupon.ResponseCouponCommodityVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityBaseVO;
import com.holderzone.member.common.vo.growth.GrowthCommodityPageVO;
import com.holderzone.member.marketing.entity.coupon.HsaCouponCommodity;
import com.holderzone.member.marketing.entity.coupon.HsaCouponStore;
import com.holderzone.member.marketing.mapper.coupon.HsaCouponCommodityMapper;
import com.holderzone.member.marketing.service.coupon.IHsaCouponCommodityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Service
@Slf4j
public class HsaCouponCommodityServiceImpl extends HolderBaseServiceImpl<HsaCouponCommodityMapper, HsaCouponCommodity> implements IHsaCouponCommodityService {

    @Resource
    private HsaCouponCommodityMapper hsaCouponCommodityMapper;

    @Resource
    private GuidGeneratorUtil getGuidGenerator;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private ExternalSupport externalSupport;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCommodityByCoupon(String couPonGuid, List<RequestCouponCommodityQO> requestCouponCommodityQOList) {
        List<HsaCouponCommodity> hsaCouponCommodityList = hsaCouponCommodityMapper.selectList(new LambdaQueryWrapper<HsaCouponCommodity>()
                .eq(HsaCouponCommodity::getCouponGuid, couPonGuid));

        if (CollUtil.isNotEmpty(hsaCouponCommodityList)) {
            hsaCouponCommodityList.forEach(in -> hsaCouponCommodityMapper.removeByGuid(in.getGuid()));
        }

        List<HsaCouponCommodity> hsaCouponCommodities = Lists.newArrayList();
        if (CollUtil.isNotEmpty(requestCouponCommodityQOList)) {
            for (RequestCouponCommodityQO requestCouponCommodityQO : requestCouponCommodityQOList) {
                HsaCouponCommodity couponCommodity = new HsaCouponCommodity();
                couponCommodity.setGuid(getGuidGenerator.getStringGuid(HsaCouponStore.class.getSimpleName()));
                couponCommodity.setCommodityId(requestCouponCommodityQO.getCommodityId());
                couponCommodity.setCommodityName(requestCouponCommodityQO.getCommodityName());
                couponCommodity.setCommodityPrice(requestCouponCommodityQO.getCommodityPrice());
                couponCommodity.setChannel(requestCouponCommodityQO.getChannel());
                couponCommodity.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
                couponCommodity.setComboType(requestCouponCommodityQO.getComboType());
                couponCommodity.setCouponGuid(couPonGuid);
                couponCommodity.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
                couponCommodity.setSystem(requestCouponCommodityQO.getSystem());
                hsaCouponCommodities.add(couponCommodity);
            }
            this.saveBatch(hsaCouponCommodities);
        }

        return true;
    }

    @Override
    public List<HsaCouponCommodity> getCommodityByCouponGuids(List<String> couponGuids) {
        if (CollectionUtils.isEmpty(couponGuids)) {
            return Lists.newArrayList();
        }
        return hsaCouponCommodityMapper.selectList(
                new LambdaQueryWrapper<HsaCouponCommodity>()
                        .in(HsaCouponCommodity::getCouponGuid, couponGuids)
        );
    }

    @Override
    public List<ResponseCouponCommodityVO> getCommodityByCoupon(String couPonGuid) {
        List<HsaCouponCommodity> couponCommodities = hsaCouponCommodityMapper.selectList(
                new LambdaQueryWrapper<HsaCouponCommodity>().eq(HsaCouponCommodity::getCouponGuid, couPonGuid)
        );
        // 组装返回结果
        return getCommodityByCoupon(couponCommodities);
    }


    private ResponseCouponCommodityVO getResponseCouponCommodityVO(HsaCouponCommodity couponCommodity, List<ResCommodityBase> allCommodityDetails) {
        ResponseCouponCommodityVO couponCommodityVO = new ResponseCouponCommodityVO();
        if (CollUtil.isEmpty(allCommodityDetails)) {
            buildRespCommodityByNull(couponCommodity, couponCommodityVO);
        } else {
            Map<String, ResCommodityBase> commodityBaseMap = allCommodityDetails.stream()
                    .collect(Collectors.toMap(ResCommodityBase::getCommodityId, resCommodityBase -> resCommodityBase, (k1, k2) -> k1));
            ResCommodityBase commodityBase = commodityBaseMap.get(couponCommodity.getCommodityId());
            if (commodityBase == null) {
                buildRespCommodityByNull(couponCommodity, couponCommodityVO);
            } else {
                buildRespCommodityByBase(couponCommodity, couponCommodityVO, commodityBase);
            }
        }
        return couponCommodityVO;
    }

    @Override
    public List<ResponseCouponCommodityVO> getCommodityByCoupon(List<HsaCouponCommodity> couponCommodities) {
        if (CollUtil.isEmpty(couponCommodities)) {
            return Collections.emptyList();
        }
        // 按 system 分组，分别查详情并合并
        Map<String, List<HsaCouponCommodity>> systemMap = couponCommodities.stream()
                .filter(c -> c.getSystem() != null)
                .collect(Collectors.groupingBy(HsaCouponCommodity::getSystem));

        List<ResCommodityBase> allCommodityDetails = systemMap.entrySet().stream()
                .flatMap(entry -> {
                    String systemName = entry.getKey();
                    List<String> ids = entry.getValue().stream()
                            .map(HsaCouponCommodity::getCommodityId)
                            .distinct()
                            .collect(Collectors.toList());
                    List<ResCommodityBase> details = externalSupport
                            .itemServer(SystemEnum.getSystemCodeBySystemName(systemName))
                            .listCommodityByDetail(new QueryArrayShopBase().setCommodityIdList(ids));
                    return CollUtil.isEmpty(details) ? java.util.stream.Stream.empty() : details.stream();
                })
                .collect(Collectors.toList());

        // 组装返回结果
        return couponCommodities.stream()
                .map(couponCommodity -> getResponseCouponCommodityVO(couponCommodity, allCommodityDetails))
                .collect(Collectors.toList());
    }

    private void buildRespCommodityByNull(HsaCouponCommodity couponCommodity,
                                          ResponseCouponCommodityVO couponCommodityVO) {
        couponCommodityVO.setCouPonGuid(couponCommodity.getCouponGuid());
        couponCommodityVO.setCommodityCode(couponCommodity.getCommodityCode());
        couponCommodityVO.setComboType(couponCommodity.getComboType());
        couponCommodityVO.setCommodityId(couponCommodity.getCommodityId());
        couponCommodityVO.setCommodityName(couponCommodity.getCommodityName());
        couponCommodityVO.setCommodityPrice(couponCommodity.getCommodityPrice());
        couponCommodityVO.setChannel(couponCommodity.getChannel());
        couponCommodityVO.setIsExist(BooleanEnum.FALSE.getCode());
        couponCommodityVO.setSystem(couponCommodity.getSystem());
    }

    private void buildRespCommodityByBase(HsaCouponCommodity couponCommodity,
                                          ResponseCouponCommodityVO couponCommodityVO,
                                          ResCommodityBase commodityBase) {
        couponCommodityVO.setCouPonGuid(couponCommodity.getCouponGuid());
        couponCommodityVO.setCommodityCode(commodityBase.getCommodityCode());
        couponCommodityVO.setComboType(Integer.valueOf(commodityBase.getCommodityComboType()));
        couponCommodityVO.setCommodityId(couponCommodity.getCommodityId());
        couponCommodityVO.setCommodityName(commodityBase.getName());
        couponCommodityVO.setCommodityPrice(commodityBase.getBasePrice());
        couponCommodityVO.setChannel(couponCommodity.getChannel());
        couponCommodityVO.setIsExist(BooleanEnum.TRUE.getCode());
        couponCommodityVO.setSystem(couponCommodity.getSystem());
    }
}
