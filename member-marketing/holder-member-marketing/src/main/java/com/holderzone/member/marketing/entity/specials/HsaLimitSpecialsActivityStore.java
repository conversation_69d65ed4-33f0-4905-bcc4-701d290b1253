package com.holderzone.member.marketing.entity.specials;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 限时特价活动门店
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-16
 */
@Data
@Accessors(chain = true)
public class HsaLimitSpecialsActivityStore implements Serializable {

    private static final long serialVersionUID = -2850585912909575860L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0-否 1-是
     */
    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Byte isDelete;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动guid
     */
    private String activityGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店id
     */
    private String storeCode;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private String system;

}
