package com.holderzone.member.marketing.service.redeem.impl;


import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.redeem.RequestRedeemApplyDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.coupon.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.redeem.RequestCheckRedeemApplyQO;
import com.holderzone.member.common.qo.redeem.RespondCheckRedeemApplyVO;
import com.holderzone.member.common.qo.redeem.RespondEditActiveVO;
import com.holderzone.member.common.qo.system.AppletBaseInfoQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.system.AppletBaseInfoVO;
import com.holderzone.member.marketing.assembler.CouponPackageAssembler;
import com.holderzone.member.marketing.entity.redeem.HsaRedeemCodeDtl;
import com.holderzone.member.marketing.mapper.redeem.HsaRedeemCodeDtlMapper;
import com.holderzone.member.marketing.service.redeem.HsaRedeemCodeActiveService;
import com.holderzone.member.marketing.service.redeem.HsaRedeemCodeDtlService;
import com.holderzone.member.marketing.service.redeem.RedeemActiveApplyService;
import com.holderzone.member.marketing.service.redeem.util.RedeemCodeDtlCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;

@Service
@Slf4j
public class RedeemActiveApplyServiceImpl implements RedeemActiveApplyService {

    @Resource
    private HsaRedeemCodeDtlMapper hsaRedeemCodeDtlMapper;

    @Resource
    private HsaRedeemCodeDtlService hsaRedeemCodeDtlService;

    @Resource
    private HsaRedeemCodeActiveService hsaRedeemCodeActiveService;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private Executor marketingThreadExecutor;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private RedeemCodeDtlCheckUtil redeemCodeDtlCheckUtil;

    /**
     * 请输入正确兑换码
     */
    private static final String REDEEM_CODE_ERROR = "请输入正确兑换码";


    /**
     * 兑换码已使用
     */
    private static final String REDEEM_CODE_ALREADY_REDEEM = "兑换码已使用";

    /**
     * 兑换码已失效
     */
    private static final String REDEEM_CODE_EXPIRED = "兑换码已失效";

    /**
     * 兑换校验
     * 1、兑换码错误
     * 2、兑换码活动未开始
     * 3、活动已暂停/已结束
     * 4、兑换码状态兑换成功
     * 5、兑换码状态已作废
     * 6、通用码兑换库存已达上限
     * 7、当前会员账户不符合兑换对象
     * 8、当前会员兑换数量超过上限
     * 9、兑换优惠为“余额”，当前会员账户无有效会员卡（电子卡/实体卡任 一状态“正常”）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RespondCheckRedeemApplyVO redeem(RequestCheckRedeemApplyQO request) {
        ThreadLocalCache.getHeaderUserInfo();
        RespondCheckRedeemApplyVO response = new RespondCheckRedeemApplyVO();
        // 根据活动码查询活动
        Pair<RespondEditActiveVO, HsaRedeemCodeDtl> respondEditActiveVOHsaRedeemCodeDtlPair = queryPairByRedeemCode(request);
        RespondEditActiveVO hsaRedeemCodeActive = respondEditActiveVOHsaRedeemCodeDtlPair.getLeft();
        HsaRedeemCodeDtl hsaRedeemCode = respondEditActiveVOHsaRedeemCodeDtlPair.getRight();

        response.setHsaRedeemCodeActive(hsaRedeemCodeActive);
        //活动规则判断
        redeemCodeDtlCheckUtil.checkRedeemCodeActive(request, hsaRedeemCodeActive);
        //增加余额的卡判断
        redeemCodeDtlCheckUtil.checkCardMoney(request, hsaRedeemCodeActive, response);
        //优惠券过期判断
        redeemCodeDtlCheckUtil.checkCoupon(hsaRedeemCodeActive);

        redeemCodeDtlUpdate(request, hsaRedeemCode, hsaRedeemCodeActive, response);

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        headerUserInfo.setStoreGuid(request.getStoreGuid());
        headerUserInfo.setStoreName(request.getStoreName());
        marketingThreadExecutor.execute(() -> {
            ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
            RequestRedeemApplyDTO dto = new RequestRedeemApplyDTO()
                    .setHsaRedeemCodeActive(hsaRedeemCodeActive)
                    .setDtlGuid(response.getDtlGuid())
                    .setMemberGuid(request.getMemberGuid())
                    .setOrderGuid(request.getOrderGuid());

            dto.setCardBaseInfoVO(response.getCardBaseInfoVO());
            dto.setMemberInfoCardGuid(response.getMemberInfoCardGuid());
            boolean isSuccess = memberBaseFeign.redeem(dto);
            log.info("兑换会员卡异步调用结果：{}", isSuccess);
        });

        // 查询积分/成长值名称
        buildBaseInfo(response);
        return response;
    }

    @Override
    public RespondEditActiveVO queryByRedeemCode(RequestCheckRedeemApplyQO request) {
        Pair<RespondEditActiveVO, HsaRedeemCodeDtl> respondEditActiveVOHsaRedeemCodeDtlPair = queryPairByRedeemCode(request);
        return respondEditActiveVOHsaRedeemCodeDtlPair.getLeft();
    }


    /**
     * 根据活动码查询活动
     */
    private Pair<RespondEditActiveVO, HsaRedeemCodeDtl> queryPairByRedeemCode(RequestCheckRedeemApplyQO request) {
        RespondEditActiveVO hsaRedeemCodeActive = hsaRedeemCodeActiveService.detailsQuery(request.getRedeemCode());
        HsaRedeemCodeDtl hsaRedeemCode = null;
        if (StringUtils.isEmpty(hsaRedeemCodeActive.getGuid())) {
            hsaRedeemCode = hsaRedeemCodeDtlService.queryByCodeAndState(request.getRedeemCode(), null);

            if (Objects.isNull(hsaRedeemCode)) {
                throw new MemberBaseException(REDEEM_CODE_ERROR);
            }

            if (hsaRedeemCode.getCodeState() == RedeemCodeStateEnum.ALREADY_REDEEM.getCode()) {
                throw new MemberBaseException(REDEEM_CODE_ALREADY_REDEEM);
            }

            if (hsaRedeemCode.getCodeState() == RedeemCodeStateEnum.CANCEL.getCode()) {
                throw new MemberBaseException(REDEEM_CODE_EXPIRED);
            }
            hsaRedeemCodeActive = hsaRedeemCodeActiveService.detailsQuery(hsaRedeemCode.getRedeemCodeActiveGuid());
        }
        return Pair.of(hsaRedeemCodeActive, hsaRedeemCode);
    }

    private void buildBaseInfo(RespondCheckRedeemApplyVO response) {
        Integer activeRedeemType = response.getHsaRedeemCodeActive().getActiveRedeemType();
        if (!Objects.equals(RedeemActiveTypeEnum.REDEEM_INTEGRATION_VALUE.getCode(), activeRedeemType)
                && !Objects.equals(RedeemActiveTypeEnum.REDEEM_GROWTH_VALUE.getCode(), activeRedeemType)) {
            return;
        }
        AppletBaseInfoQO baseInfoQO = new AppletBaseInfoQO();
        baseInfoQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        try {
            Result<AppletBaseInfoVO> baseInfoResult = memberBaseFeign.getBaseInfo(baseInfoQO);
            log.info("查询积分/成长值名称：{}", JacksonUtils.writeValueAsString(baseInfoResult));
            AppletBaseInfoVO baseInfoVO = baseInfoResult.getData();
            response.setGrowthValueName(baseInfoVO.getGrowthValueName());
            response.setIntegralName(baseInfoVO.getIntegralName());
        } catch (Exception e) {
            log.error("查询积分/成长值名称失败：", e);
            response.setGrowthValueName(StringConstant.GROWTH_VALUE);
            response.setIntegralName(StringConstant.INTEGRAL_VALUE);
        }
    }


    private void redeemCodeDtlUpdate(RequestCheckRedeemApplyQO request,
                                     HsaRedeemCodeDtl hsaRedeemCode,
                                     RespondEditActiveVO hsaRedeemCodeActive,
                                     RespondCheckRedeemApplyVO response) {

        if (Objects.nonNull(hsaRedeemCode)) {
            CouponPackageAssembler.setRedeemCodeActive(request, hsaRedeemCodeActive, hsaRedeemCode, response);
            response.setDtlGuid(hsaRedeemCode.getGuid());
            hsaRedeemCodeDtlMapper.updateByGuid(hsaRedeemCode);
        } else {
            HsaRedeemCodeDtl hsaRedeemCodeDtl = new HsaRedeemCodeDtl();
            hsaRedeemCodeDtl.setGuid(guidGeneratorUtil.getStringGuid(HsaRedeemCodeDtl.class.getSimpleName()));
            hsaRedeemCodeDtl.setRedeemCodeSn(hsaRedeemCodeActive.getRedeemCodeVal());
            hsaRedeemCodeDtl.setRedeemCodeActiveGuid(hsaRedeemCodeActive.getGuid());
            hsaRedeemCodeDtl.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            CouponPackageAssembler.setRedeemCodeActive(request, hsaRedeemCodeActive, hsaRedeemCodeDtl, response);
            response.setDtlGuid(hsaRedeemCodeDtl.getGuid());
            hsaRedeemCodeDtlMapper.insert(hsaRedeemCodeDtl);
        }
    }

}
