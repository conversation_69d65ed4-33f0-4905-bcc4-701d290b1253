package com.holderzone.member.marketing.service.certificate.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.assembler.CertifiedAssembler;
import com.holderzone.member.common.config.AliPayPassConfig;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.VolumeInfoUpdateDTO;
import com.holderzone.member.common.dto.certificate.CertifiedCouponDTO;
import com.holderzone.member.common.dto.certificate.GiftInfoDTO;
import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.label.MemberCertifiedLabel;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.page.ApplyActivityPageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.activity.ActivityAuditTypeEnum;
import com.holderzone.member.common.enums.certificate.*;
import com.holderzone.member.common.enums.gift.GiftTypeEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.member.AmountSourceTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.MemberIdentityEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.qo.certificate.ApplyCertifiedActivityQO;
import com.holderzone.member.common.qo.certificate.CertifiedActivityRecordQO;
import com.holderzone.member.common.qo.certificate.DisposeActivityAudit;
import com.holderzone.member.common.qo.certificate.WeChatMsgAuthorizeQO;
import com.holderzone.member.common.qo.growth.RequestCertifiedGrowthValue;
import com.holderzone.member.common.qo.member.RequestMemberGrowthValue;
import com.holderzone.member.common.util.StringHandlerUtil;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.util.transaction.SpringContextUtils;
import com.holderzone.member.common.util.verify.StrValidate;
import com.holderzone.member.common.vo.certificate.*;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;
import com.holderzone.member.marketing.assembler.AlipayPassAssembler;
import com.holderzone.member.marketing.entity.certificate.HsaAuditStep;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedActivity;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedActivityRecord;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedGift;
import com.holderzone.member.marketing.mapper.HsaAuditStepMapper;
import com.holderzone.member.marketing.mapper.HsaCertifiedActivityMapper;
import com.holderzone.member.marketing.mapper.HsaCertifiedActivityRecordMapper;
import com.holderzone.member.marketing.mapper.HsaCertifiedGiftMapper;
import com.holderzone.member.marketing.service.certificate.HsaCertifiedActivityRecordService;
import com.holderzone.member.marketing.service.certificate.HsaCertifiedActivityService;
import com.holderzone.member.marketing.service.certificate.HsaCertifiedExternalService;
import com.holderzone.member.marketing.util.message.WeChatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-04-12 09:51
 */
@Slf4j
@Service
public class HsaCertifiedActivityRecordServiceImpl extends HolderBaseServiceImpl<HsaCertifiedActivityRecordMapper,
        HsaCertifiedActivityRecord> implements HsaCertifiedActivityRecordService {

    private static final Integer STATUS_CODE_SUCCEEDED = 0;

    @Resource
    private HsaCertifiedActivityRecordMapper hsaCertifiedActivityRecordMapper;

    @Resource
    private HsaCertifiedActivityMapper hsaCertifiedActivityMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private WeChatUtil weChatUtil;

    @Resource
    private HsaAuditStepMapper hsaAuditStepMapper;

    @Resource
    private HsaCertifiedGiftMapper hsaCertifiedGiftMapper;

    @Resource
    private HsaCertifiedExternalService hsaCertifiedExternalService;

    @Resource
    private Executor marketingCertifiedThreadExecutor;

    @Resource
    private HsaCertifiedActivityService hsaCertifiedActivityService;

    @Resource
    private SaasStoreFeign saasStoreFeign;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @Resource
    private AliPayPassConfig aliPayPassConfig;

    @Override
    public CertifiedActivityRecordStatisticsVO getCertifiedActivityRecordStatistics(String certifiedActivityGuid) {

        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();

        return new CertifiedActivityRecordStatisticsVO()
                .setAwaitAuditNumber(hsaCertifiedActivityRecordMapper.queryAwaitAuditNumber(certifiedActivityGuid,
                        CertifiedStatusEnum.AWAIT_AUDIT.getCode(), operSubjectGuid))
                .setAuditPassNumber(hsaCertifiedActivityRecordMapper.queryAwaitAuditNumber(certifiedActivityGuid,
                        CertifiedStatusEnum.AUDIT_PASS.getCode(), operSubjectGuid))
                .setAuditRejectedNumber(hsaCertifiedActivityRecordMapper.queryAwaitAuditNumber(certifiedActivityGuid,
                        CertifiedStatusEnum.AUDIT_REJECTED.getCode(), operSubjectGuid));
    }

    @Override
    public ApplyActivityPageResult queryCertifiedActivityRecord(CertifiedActivityRecordQO request) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        request.setOperSubjectGuid(headerUserInfo.getOperSubjectGuid());
        request.setSource(ThreadLocalCache.getHeaderUserInfo().getSource());
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<CertifiedActivityRecordVO> list = hsaCertifiedActivityRecordMapper.queryCertifiedActivityRecord(request);
        for (CertifiedActivityRecordVO certifiedActivityRecordVO : list) {
            certifiedActivityRecordVO.setVoucher(JSONObject.parseArray(certifiedActivityRecordVO.getVoucherJsonStr(), String.class));
            //待审核并且已过期，展示已过期
            if (certifiedActivityRecordVO.getStatus() == CertifiedStatusEnum.AWAIT_AUDIT.getCode()
                    && certifiedActivityRecordVO.getActivityEndTime().isBefore(LocalDateTime.now())) {
                certifiedActivityRecordVO.setStatus(CertifiedStatusEnum.OVERDUE.getCode());
            }

            // 权益内容
            handleBenefitsContent(certifiedActivityRecordVO);
        }
        Integer waitingDisposeNumber = 0;
        //商户小程序，查看待处理数量
        if (headerUserInfo.getSource() == SourceTypeEnum.ADD_ZHUANCAN.getCode()) {
            waitingDisposeNumber = hsaCertifiedActivityRecordMapper.queryAwaitAuditNumber(null,
                    CertifiedStatusEnum.AWAIT_AUDIT.getCode(), headerUserInfo.getOperSubjectGuid());
        }
        return PageUtil.getApplyActivityPageResult(new PageInfo<>(list), waitingDisposeNumber);
    }

    private void handleBenefitsContent(CertifiedActivityRecordVO certifiedActivityRecordVO) {
        if (StringUtils.hasText(certifiedActivityRecordVO.getDiscount())) {
            List<GiftInfoDTO> giftInfoDTOList = JSON.parseArray(certifiedActivityRecordVO.getDiscount(), GiftInfoDTO.class);
            if (!CollectionUtils.isEmpty(giftInfoDTOList)) {
                StringBuilder builder = new StringBuilder();
                int size = giftInfoDTOList.size() - 1;
                for (int i = 0; i <= size; i++) {
                    GiftInfoDTO giftInfoDTO = giftInfoDTOList.get(i);
                    setCoupon(giftInfoDTO, builder, i, size);

                    setGrowth(giftInfoDTO, builder, i, size);

                }
                certifiedActivityRecordVO.setBenefitsContent(builder.toString());
            }
        }
    }

    private static void setCoupon(GiftInfoDTO giftInfoDTO, StringBuilder builder, int i, int size) {
        if (Objects.isNull(giftInfoDTO.getGiftType())
                || giftInfoDTO.getGiftType() == GiftTypeEnum.GIFT_MEMBER_COUPON.getCode()) {
            builder.append(giftInfoDTO.getGiveawayName())
                    .append("*")
                    .append(giftInfoDTO.getGiveawayNum());
            if (i != size) {
                builder.append("、");
            }
        }
    }

    private static void setGrowth(GiftInfoDTO giftInfoDTO, StringBuilder builder, int i, int size) {
        if (Objects.nonNull(giftInfoDTO.getGiftType())
                && giftInfoDTO.getGiftType() == GiftTypeEnum.GIFT_MEMBER_GROWTH.getCode()) {
            builder.append(GiftTypeEnum.GIFT_MEMBER_GROWTH.getDes())
                    .append("*")
                    .append(giftInfoDTO.getGiftGrowthValue());
            if (i != size) {
                builder.append("、");
            }
        }
    }

    @Override
    public Integer queryWaitingDisposeNumber() {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        log.info("查询待审核数量运营主体===========>{}", operSubjectGuid);
        Integer result = hsaCertifiedActivityRecordMapper.queryAwaitAuditNumber(null,
                CertifiedStatusEnum.AWAIT_AUDIT.getCode(), operSubjectGuid);
        log.info("查询待审核数量返回结果===========>{}", result);
        return result;
    }

    @Override
    public ApplicationInfoVO applyCertifiedActivity(ApplyCertifiedActivityQO request) {
        ApplicationInfoVO applicationInfoVO = new ApplicationInfoVO();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        log.info("申请认证活动HeaderUserInfo===========>{}", JSON.toJSONString(headerUserInfo));
        if (Objects.isNull(request.getMemberGuid())) {
            MemberQueryDTO memberQueryDTO = new MemberQueryDTO();
            memberQueryDTO.setPhoneNum(request.getPhoneNumber());
            memberQueryDTO.setQueryZhuanCanFlag(true);
            MemberBasicInfoVO memberInfo = hsaCertifiedExternalService.getMemberInfo(memberQueryDTO);
            if (Objects.isNull(memberInfo)) {
                log.warn("当前会员未注册，请通过【微信小程序】注册会员再参与活动：{}", request.getPhoneNumber());
                applicationInfoVO.setApplicationStatus(ApplyCertifiedResultEnum.QUERY_PHONE_UNREGISTERED_ERROR.getCode());
                return applicationInfoVO;
            }
            log.info("用户信息：{}", JSON.toJSON(memberInfo));
            request.setMemberGuid(memberInfo.getGuid());
        }

        HsaCertifiedActivity hsaCertifiedActivity = hsaCertifiedActivityMapper.queryByGuid(request.getCertifiedActivityGuid());
        HsaCertifiedActivityRecord hsaCertifiedActivityRecord = getHsaCertifiedActivityRecord(request, hsaCertifiedActivity);
        hsaCertifiedActivityRecord.setCertifiedActivityGuid(hsaCertifiedActivity.getGuid());
        //校验活动状态
        int result = checkActivityStatus(request, hsaCertifiedActivity, hsaCertifiedActivityRecord);
        if (result > 0) {
            applicationInfoVO.setApplicationStatus(result);
            return applicationInfoVO;
        }
        getCertifiedActivityRecord(request, hsaCertifiedActivityRecord, hsaCertifiedActivity);
        checkCouponSend(request, hsaCertifiedActivityRecord, hsaCertifiedActivity);
        this.saveOrUpdate(hsaCertifiedActivityRecord);
        applicationInfoVO.setApplicationStatus(ApplyCertifiedResultEnum.SUCCEED.getCode());
        applicationInfoVO.setGuid(hsaCertifiedActivityRecord.getGuid());

        request.setOperSubjectGuid(hsaCertifiedActivity.getOperSubjectGuid());
        if (applicationInfoVO.getApplicationStatus() == ApplyCertifiedResultEnum.SUCCEED.getCode()
                && hsaCertifiedActivity.getAuditType() == ActivityAuditTypeEnum.AUTO_AUDIT.getCode()) {
            // 将RequestAttributes对象设置为子线程共享
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            RequestContextHolder.setRequestAttributes(sra, true);
            marketingCertifiedThreadExecutor.execute(() -> {
                ThreadLocalCache.put(JSON.toJSONString(headerUserInfo));
                sendActivityAudit(hsaCertifiedActivity, request, hsaCertifiedActivityRecord);
            });
        }
        return applicationInfoVO;

    }

    /**
     * 处理活动审核
     *
     * @param request
     * @param hsaCertifiedActivityRecord
     */
    private void sendActivityAudit(HsaCertifiedActivity hsaCertifiedActivity, ApplyCertifiedActivityQO request, HsaCertifiedActivityRecord hsaCertifiedActivityRecord) {
        DisposeActivityAudit disposeActivityAudit = new DisposeActivityAudit();
        disposeActivityAudit.setActivityRecordGuid(hsaCertifiedActivityRecord.getGuid());
        disposeActivityAudit.setDisposeResult(BooleanEnum.TRUE.getCode());
        disposeActivityAudit.setPhoneNumber(request.getPhoneNumber());
        disposeActivityAudit.setCertificateValidity(hsaCertifiedActivityRecord.getCertificateValidity());
        disposeActivityAudit.setOperSubjectGuid(hsaCertifiedActivity.getOperSubjectGuid());
        HsaCertifiedActivityRecordService hsaOperationMemberInfoService = SpringContextUtils.getBean(HsaCertifiedActivityRecordService.class);
        hsaOperationMemberInfoService.disposeActivityAudit(disposeActivityAudit);
    }

    private static void getCertifiedActivityRecord(ApplyCertifiedActivityQO request,
                                                   HsaCertifiedActivityRecord hsaCertifiedActivityRecord,
                                                   HsaCertifiedActivity hsaCertifiedActivity) {
        hsaCertifiedActivityRecord.setPhoneNumber(request.getPhoneNumber());
        hsaCertifiedActivityRecord.setActivityCode(hsaCertifiedActivity.getActivityCode());
        hsaCertifiedActivityRecord.setActivityName(hsaCertifiedActivity.getActivityName());
        if (hsaCertifiedActivity.getCertifiedType() == CertifiedTypeEnum.TRIP_TICKET.getCode()) {
            hsaCertifiedActivityRecord.setCertifiedType(CertifiedTypeEnum.TRIP_TICKET.getDes());
        } else {
            hsaCertifiedActivityRecord.setCertifiedType(hsaCertifiedActivity.getCertificateName());
        }
        hsaCertifiedActivityRecord.setGmtCreate(LocalDateTime.now());
        hsaCertifiedActivityRecord.setGmtModified(LocalDateTime.now());
        hsaCertifiedActivityRecord.setTripTicketType(request.getTripTicketType());
        if (!CollectionUtils.isEmpty(request.getVoucherList())) {
            hsaCertifiedActivityRecord.setVoucher(JSONUtil.toJsonStr(request.getVoucherList()));
        }
        hsaCertifiedActivityRecord.setArriveTime(request.getArriveTime());
        hsaCertifiedActivityRecord.setJourneyType(request.getJourneyType());
        hsaCertifiedActivityRecord.setJourneyAddress(request.getJourneyAddress());
        hsaCertifiedActivityRecord.setCertificateNumber(request.getCertificateNumber());
        hsaCertifiedActivityRecord.setCertificateDate(request.getCertificateDate());
        hsaCertifiedActivityRecord.setMemberGuid(request.getMemberGuid());
        hsaCertifiedActivityRecord.setOpenid(request.getOpenid());
        hsaCertifiedActivityRecord.setIsAgreeSendMsg(Optional.ofNullable(request.getIsAgreeSendMsg()).orElse(BooleanEnum.FALSE.getCode()));
        //来源 用于判断是微信小程序申请还是点餐宝申请的
        hsaCertifiedActivityRecord.setSource(request.getSource());
    }

    private void checkCouponSend(ApplyCertifiedActivityQO request,
                                 HsaCertifiedActivityRecord hsaCertifiedActivityRecord,
                                 HsaCertifiedActivity hsaCertifiedActivity) {
        if (request.getSource() == SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode()
                || request.getSource() == SourceTypeEnum.MALL_WECHAT_APPLET.getCode()
                || request.getSource() == SourceTypeEnum.MALL_H5.getCode()) {
            hsaCertifiedActivityRecord.setStatus(CertifiedStatusEnum.AWAIT_AUDIT.getCode());
            hsaCertifiedActivityRecord.setUsername(request.getUsername());
        } else if (request.getSource() == SourceTypeEnum.ADD_ZHUANCAN.getCode()) {
            if (hsaCertifiedActivity.getMerchantsEntrance() == BooleanEnum.FALSE.getCode()) {
                throw new MemberBaseException(MemberAccountExceptionEnum.QUERY_PHONE_ERROR);
            }
            hsaCertifiedActivityRecord.setStatus(CertifiedStatusEnum.AUDIT_PASS.getCode());
            if (StringUtils.isEmpty(request.getPhoneNumber())) {
                throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
            }
            String memberName = hsaCertifiedExternalService.getMemberName(new MemberQueryDTO()
                    .setPhoneNum(request.getPhoneNumber()));
            log.info("根据手机号查询会员返回数据==========>{}", memberName);
            if (Objects.isNull(memberName)) {
                throw new MemberBaseException(MemberAccountExceptionEnum.QUERY_PHONE_ERROR);
            }
            hsaCertifiedActivityRecord.setUsername(memberName);
            hsaCertifiedActivityRecord.setMemberGuid(request.getMemberGuid());
            hsaCertifiedActivityRecord.setAuditTime(LocalDateTime.now());
            //审核人员
            String auditUser = request.getUsername() + "/" + request.getLoginPhoneNum();
            hsaCertifiedActivityRecord.setAuditUser(auditUser);
            SendCertifiedStampsDTO sendCertifiedStampsDTO = getSendCertifiedStampsDTO(NumberConstant.NUMBER_6, null, null, hsaCertifiedActivityRecord);
            // 发放赠品
            sendCertifiedGift(sendCertifiedStampsDTO, hsaCertifiedActivityRecord);
        }
    }

    /**
     * 认证活动记录查询
     *
     * @param request              request
     * @param hsaCertifiedActivity hsaCertifiedActivity
     * @return HsaCertifiedActivityRecord
     */
    private HsaCertifiedActivityRecord getHsaCertifiedActivityRecord(ApplyCertifiedActivityQO request, HsaCertifiedActivity hsaCertifiedActivity) {
        if (Objects.isNull(hsaCertifiedActivity)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.CERTIFIED_ACTIVITY_NOT_EXIST);
        }
        HsaCertifiedActivityRecord hsaCertifiedActivityRecord;
        if (!StringUtils.isEmpty(request.getGuid())) {
            hsaCertifiedActivityRecord = hsaCertifiedActivityRecordMapper.queryByGuid(request.getGuid());
            if (hsaCertifiedActivityRecord.getStatus() != CertifiedStatusEnum.AUDIT_REJECTED.getCode()) {
                throw new MemberBaseException(MemberAccountExceptionEnum.CERTIFIED_STATUS_ERROR);
            }
        } else {
            hsaCertifiedActivityRecord = new HsaCertifiedActivityRecord();
            hsaCertifiedActivityRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaCertifiedActivityRecord.class.getSimpleName()));
            hsaCertifiedActivityRecord.setPhoneNumber(request.getPhoneNumber());
        }
        return hsaCertifiedActivityRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @RedissonLock(lockName = "DISPOSE_ACTIVITY_AUDIT", tryLock = true, leaseTime = 10)
    public boolean disposeActivityAudit(DisposeActivityAudit request) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        if (!StringUtils.isEmpty(operSubjectGuid)) {
            request.setOperSubjectGuid(operSubjectGuid);
        }

        log.info("处理审核参数==========>{}", JSON.toJSONString(request));

        HsaCertifiedActivityRecord hsaCertifiedActivityRecord = hsaCertifiedActivityRecordMapper.queryByGuid(request.
                getActivityRecordGuid());
        if (hsaCertifiedActivityRecord.getStatus() != CertifiedStatusEnum.AWAIT_AUDIT.getCode()) {
            throw new MemberBaseException(MemberAccountExceptionEnum.DATA_CHANGE);
        }
        HsaAuditStep hsaAuditStep = new HsaAuditStep();
        if (request.getDisposeResult() == CertifiedStatusEnum.AUDIT_PASS.getCode()) {
            if (Objects.nonNull(request.getCertificateValidity())) {
                hsaCertifiedActivityRecord.setCertificateValidity(request.getCertificateValidity());
            }
            if (Objects.nonNull(request.getDiscountValidityStart()) && Objects.nonNull(request.getDiscountValidityEnd())) {
                String discountValidity = DateUtil.formatLocalDate(request.getDiscountValidityStart(), DateUtil.PATTERN_DATE) +
                        "至" + DateUtil.formatLocalDate(request.getDiscountValidityEnd(), DateUtil.PATTERN_DATE);
                hsaCertifiedActivityRecord.setDiscountValidity(discountValidity);
            }
        } else {
            hsaAuditStep.setContent(request.getContent());
        }
        hsaCertifiedActivityRecord.setStatus(request.getDisposeResult());
        hsaCertifiedActivityRecord.setAuditTime(LocalDateTime.now());
        String auditUser = StringUtils.isEmpty(request.getUsername()) ? request.getPhoneNumber() : request.getUsername()
                + "/" + request.getPhoneNumber();
        hsaCertifiedActivityRecord.setAuditUser(auditUser);
        this.saveOrUpdate(hsaCertifiedActivityRecord);
        hsaAuditStep.setGuid(guidGeneratorUtil.getStringGuid(HsaAuditStep.class.getSimpleName()));
        hsaAuditStep.setAuditUser(auditUser);
        hsaAuditStep.setStatus(request.getDisposeResult());
        hsaAuditStep.setType(0);
        hsaAuditStep.setTypeGuid(request.getActivityRecordGuid());
        hsaAuditStep.setGmtCreate(LocalDateTime.now());
        hsaAuditStep.setGmtModified(LocalDateTime.now());
        hsaAuditStepMapper.insert(hsaAuditStep);
        //审核通过并且符合发放赠品条件
        if (request.getDisposeResult() == CertifiedStatusEnum.AUDIT_PASS.getCode() && !StringUtils.isEmpty(hsaCertifiedActivityRecord.getDiscount())) {
            //校验并发送认证有礼赠品
            checkSendCertified(request, hsaCertifiedActivityRecord);
        }
        //给用户推审核结果消息
        String headerUserInfo = ThreadLocalCache.get();
        marketingCertifiedThreadExecutor.execute(() -> {
            ThreadLocalCache.put(headerUserInfo);
            sendMsg(hsaCertifiedActivityRecord, hsaAuditStep);
        });
        return Boolean.TRUE;

    }

    private void checkSendCertified(DisposeActivityAudit request, HsaCertifiedActivityRecord hsaCertifiedActivityRecord) {
        SendCertifiedStampsDTO sendCertifiedStampsDTO = getSendCertifiedStampsDTO(NumberConstant.NUMBER_7,
                request.getDiscountValidityStart(), request.getDiscountValidityEnd(), hsaCertifiedActivityRecord);
        // 发放赠品
        sendCertifiedGift(sendCertifiedStampsDTO, hsaCertifiedActivityRecord);
    }

    /**
     * 调整成长值
     */
    private void updateGrowthValue(CertifiedCouponDTO certifiedCouponDTO, HsaCertifiedActivityRecord hsaCertifiedActivityRecord) {
        RequestMemberGrowthValue request = new RequestMemberGrowthValue();
        request.setGrowthValue(certifiedCouponDTO.getGiftGrowthValue());
        request.setGrowthValueType(NumberConstant.NUMBER_0);
        request.setSource(hsaCertifiedActivityRecord.getSource());
        request.setSourceType(AmountSourceTypeEnum.CERTIFIED.getCode());
        request.setPhoneNum(hsaCertifiedActivityRecord.getPhoneNumber());
        request.setOperatorAccountName(hsaCertifiedActivityRecord.getAuditUser());

        String remark = String.format("认证有礼%s:%s", hsaCertifiedActivityRecord.getActivityName(), hsaCertifiedActivityRecord.getActivityCode());
        request.setRemark(remark);

        String headerUserInfo = ThreadLocalCache.get();
        marketingCertifiedThreadExecutor.execute(() -> {
            ThreadLocalCache.put(headerUserInfo);
            hsaCertifiedExternalService.updateGrowthValue(request);
        });
    }


    /**
     * 发送优惠券
     */
    private void sendMemberCoupon(SendCertifiedStampsDTO sendCertifiedStampsDTO) {
        String headerUserInfo = ThreadLocalCache.get();
        marketingCertifiedThreadExecutor.execute(() -> {
            ThreadLocalCache.put(headerUserInfo);
            hsaCertifiedExternalService.memberCouponSend(sendCertifiedStampsDTO);
        });
    }

    /**
     * 发放认证有礼赠品
     */
    private void sendCertifiedGift(SendCertifiedStampsDTO sendCertifiedStampsDTO, HsaCertifiedActivityRecord hsaCertifiedActivityRecord) {
        List<CertifiedCouponDTO> certifiedCouponDTOS = sendCertifiedStampsDTO.getCertifiedCouponDTOS().stream()
                .filter(in -> Objects.isNull(in.getGiftType()) || in.getGiftType() == GiftTypeEnum.GIFT_MEMBER_COUPON.getCode())
                .collect(Collectors.toList());
        // 判断如果存在优惠券，就发券
        if (CollUtil.isNotEmpty(certifiedCouponDTOS)) {
            log.info("发券执行开始：{}", JSON.toJSONString(sendCertifiedStampsDTO));
            sendMemberCoupon(sendCertifiedStampsDTO);
        }

        List<CertifiedCouponDTO> certifiedGrowthValues = sendCertifiedStampsDTO.getCertifiedCouponDTOS().stream()
                .filter(in -> Objects.nonNull(in.getGiftType()) && in.getGiftType() == GiftTypeEnum.GIFT_MEMBER_GROWTH.getCode())
                .collect(Collectors.toList());
        // 判断如果存在成长值，就调整
        if (CollUtil.isNotEmpty(certifiedGrowthValues)) {
            // 调整成长值
            updateGrowthValue(certifiedGrowthValues.get(0), hsaCertifiedActivityRecord);
        }
        // 标签
        String headerUserInfo = ThreadLocalCache.get();
        marketingCertifiedThreadExecutor.execute(() -> {
            ThreadLocalCache.put(headerUserInfo);
            hsaCertifiedExternalService.memberLabelRefresh(new MemberCertifiedLabel()
                    .setCertifiedActivityGuid(sendCertifiedStampsDTO.getActivityGuid())
                    .setCertificateValidity(sendCertifiedStampsDTO.getCertificateValidity())
                    .setPhoneNumber(sendCertifiedStampsDTO.getPhoneNumber()));
        });
    }


    private void sendMsg(HsaCertifiedActivityRecord activityRecord, HsaAuditStep hsaAuditStep) {
        if (BooleanEnum.FALSE.getCode() == activityRecord.getIsAgreeSendMsg()) {
            log.info("用户openid:{},未授权订阅消息。所以审核结果未推送", activityRecord.getOpenid());
            return;
        }
        //微信小程序订阅消息推送，需要用户授权,每次授权只能推送一条消息（一次性订阅消息模式）
        activityRecord.setIsAgreeSendMsg(BooleanEnum.FALSE.getCode());
        hsaCertifiedActivityRecordMapper.updateMsgAuthorize(activityRecord);

        // 发送小程序订阅消息
        hsaCertifiedExternalService.sendAppletMessage(activityRecord, hsaAuditStep);
    }


    private SendCertifiedStampsDTO getSendCertifiedStampsDTO(Integer source, LocalDate start, LocalDate end, HsaCertifiedActivityRecord hsaCertifiedActivityRecord) {
        HsaCertifiedActivity hsaCertifiedActivity = hsaCertifiedActivityMapper.queryByGuid(hsaCertifiedActivityRecord.getCertifiedActivityGuid());
        List<CertifiedCouponDTO> giftInfoDTOS = JSON.parseArray(hsaCertifiedActivityRecord.getDiscount(), GiftInfoDTO.class)
                .stream()
                .map(in -> {
                    CertifiedCouponDTO certifiedCouponDTO = new CertifiedCouponDTO();
                    if (!StringUtils.isEmpty(in.getGiveawayGuid())) {
                        certifiedCouponDTO.setCouponId(Long.parseLong(in.getGiveawayGuid()));
                    }
                    certifiedCouponDTO.setCouponNum(in.getGiveawayNum());
                    certifiedCouponDTO.setGiftType(in.getGiftType());
                    certifiedCouponDTO.setGiftGrowthValue(in.getGiftGrowthValue());
                    return certifiedCouponDTO;
                }).collect(Collectors.toList());
        SendCertifiedStampsDTO dto = new SendCertifiedStampsDTO();
        dto.setActivityGuid(hsaCertifiedActivity.getGuid())
                .setActivityName(hsaCertifiedActivity.getActivityName())
                .setActivityNumber(hsaCertifiedActivity.getActivityCode())
                .setAuditStepGuid(hsaCertifiedActivityRecord.getGuid())
                .setStartTime(start)
                .setEndTime(end)
                .setMemberSource(hsaCertifiedActivityRecord.getSource())
                .setSource(source)
                .setUserId(StringUtils.isEmpty(hsaCertifiedActivityRecord.getMemberGuid()) ? null : Long.parseLong(hsaCertifiedActivityRecord.getMemberGuid()))
                .setCertifiedCouponDTOS(giftInfoDTOS)
                .setPhoneNumber(hsaCertifiedActivityRecord.getPhoneNumber());
        if (Objects.nonNull(hsaCertifiedActivityRecord.getCertificateValidity())) {
            dto.setCertificateValidity(getTime(hsaCertifiedActivityRecord.getCertificateValidity()));
        }
        return dto;
    }

    private LocalDateTime getTime(LocalDate time) {
        return Objects.nonNull(time) ? time.atTime(NumberConstant.NUMBER_0, NumberConstant.NUMBER_0, NumberConstant.NUMBER_0).plusDays(NumberConstant.NUMBER_1) : null;
    }

    @Override
    public AuditRecordVO queryAuditStep(String recordGuid) {
        Integer source = ThreadLocalCache.getHeaderUserInfo().getSource();
        List<HsaAuditStep> list = hsaAuditStepMapper.selectList(new LambdaQueryWrapper<HsaAuditStep>()
                .eq(HsaAuditStep::getType, 0)
                .eq(HsaAuditStep::getTypeGuid, recordGuid)
                .orderByDesc(HsaAuditStep::getId));
        AuditRecordVO auditRecordVO = new AuditRecordVO();
        if (CollectionUtils.isEmpty(list)) {
            return auditRecordVO;
        }
        if (source != SourceTypeEnum.ADD_BACKGROUND.getCode()) {
            HsaCertifiedActivityRecord hsaCertifiedActivityRecord = hsaCertifiedActivityRecordMapper.queryByGuid(recordGuid);
            auditRecordVO.setCertificateValidity(hsaCertifiedActivityRecord.getCertificateValidity());
            auditRecordVO.setDiscountValidity(hsaCertifiedActivityRecord.getDiscountValidity());
            //商户小程序查询最新一条
            list = list.stream().limit(1).collect(Collectors.toList());
        }
        List<AuditStepVO> auditStepVOList = Lists.newArrayList();
        for (HsaAuditStep hsaAuditStep : list) {
            AuditStepVO auditStepVO = new AuditStepVO();
            auditStepVO.setAuditTime(hsaAuditStep.getGmtCreate());
            auditStepVO.setAuditUser(hsaAuditStep.getAuditUser());
            auditStepVO.setContent(hsaAuditStep.getContent());
            auditStepVO.setStatus(hsaAuditStep.getStatus());
            auditStepVO.setSource(ThreadLocalCache.getHeaderUserInfo().getSource());
            auditStepVOList.add(auditStepVO);
        }
        auditRecordVO.setAuditStepVOList(auditStepVOList);

        return auditRecordVO;

    }


    @Override
    public CertifiedActivityRecordVO queryActivityRecordDetail(String recordGuid) {
        CertifiedActivityRecordVO certifiedActivityRecordVO = hsaCertifiedActivityRecordMapper.queryActivityRecordByGuid(recordGuid);
        certifiedActivityRecordVO.setVoucher(JSONObject.parseArray(certifiedActivityRecordVO.getVoucherJsonStr(), String.class));
        return certifiedActivityRecordVO;
    }

    @Override
    public Boolean isAgreeSend(WeChatMsgAuthorizeQO weChatMsgAuthorizeQO) {
        HsaCertifiedActivityRecord hsaCertifiedActivityRecord = hsaCertifiedActivityRecordMapper
                .selectOne(new LambdaQueryWrapper<HsaCertifiedActivityRecord>()
                        .eq(HsaCertifiedActivityRecord::getGuid, weChatMsgAuthorizeQO.getActivityRecordGuid()));
        if (Objects.isNull(hsaCertifiedActivityRecord)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.CERTIFIED_RECORD_NOT_EXIST);
        }
        hsaCertifiedActivityRecord.setIsAgreeSendMsg(weChatMsgAuthorizeQO.getIsAgreeSendMsg());
        return hsaCertifiedActivityRecordMapper.updateByGuid(hsaCertifiedActivityRecord);
    }

    @Override
    public CertifiedPupilActivityVO studentVerified(CertifiedStudentVerifiedVO request) {
        CertifiedPupilActivityVO activityVO = new CertifiedPupilActivityVO();
        String certifiedActivityGuid = request.getCertifiedActivityGuid();

        // 查询企业关联
        OperSubjectCloudVO operSubjectVO = memberMallToolFeign.queryByOperSubiectGuid(ThreadLocalCache.getOperSubjectGuid());
        if (Objects.isNull(operSubjectVO)) {
            log.warn("[学生验证通过]未查询到企业关联,operSubiectGuid={}", ThreadLocalCache.getOperSubjectGuid());
            return activityVO;
        }

        // 设置老会员头部信息
        HeaderUserInfo userInfo = ThreadLocalCache.getHeaderUserInfo();
        userInfo.setEnterpriseGuid(operSubjectVO.getMultiEnterpriseGuid());
        userInfo.setOperSubjectGuid(operSubjectVO.getMultiOperSubiectGuid());
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(userInfo));

        // 组装发券参数进行发券
        HsaCertifiedActivity certifiedActivity = hsaCertifiedActivityMapper.queryByGuid(certifiedActivityGuid);
        if (Objects.isNull(certifiedActivity)) {
            log.warn("[学生验证通过]认证活动不存在,certifiedActivityGuid={}", certifiedActivityGuid);
            return activityVO;
        }
        HsaCertifiedGift certifiedGift = hsaCertifiedGiftMapper.queryByActivityGuid(certifiedActivityGuid);
        if (Objects.isNull(certifiedGift) || com.holderzone.framework.util.StringUtils.isEmpty(certifiedGift.getGiveawayInfoJson())) {
            log.warn("[学生验证通过]认证活动赠品信息异常,certifiedActivityGuid={}", certifiedActivityGuid);
            return activityVO;
        }
        List<GiftInfoDTO> giftInfoDTOList = JacksonUtils.toObjectList(GiftInfoDTO.class, certifiedGift.getGiveawayInfoJson());
        if (CollectionUtils.isEmpty(giftInfoDTOList)) {
            log.warn("[学生验证通过]认证活动赠品信息异常,certifiedGift={}", JacksonUtils.writeValueAsString(certifiedGift));
            return activityVO;
        }

        FeignModel<ResponseAppletsMemberInfo> memberInfoFeignModel = saasStoreFeign.getAppletsMemberInfoByUserId(request.getUserId());
        ResponseAppletsMemberInfo data = memberInfoFeignModel.getData();
        if (Objects.isNull(data)) {
            log.warn("[学生验证通过][通过userId获取会员基础信息]异常,userId={}", request.getUserId());
            return activityVO;
        }

        // 再次认证会更新身份信息，但是不会重新发放奖品 --理论上只能认证一次
        HsaCertifiedActivityRecord activityRecord = hsaCertifiedActivityRecordMapper.selectOne(
                new LambdaQueryWrapper<HsaCertifiedActivityRecord>()
                        .eq(HsaCertifiedActivityRecord::getMemberGuid, request.getUserId())
                        .eq(HsaCertifiedActivityRecord::getCertifiedActivityGuid, certifiedActivityGuid)
        );
        boolean isSave = Objects.isNull(activityRecord);
        List<VolumeInfoUpdateDTO> responseVolumeInfoList = new ArrayList<>();

        //若存在券 则发券
        List<GiftInfoDTO> giftInfoCouponDTOS = giftInfoDTOList
                .stream()
                .filter(in -> Objects.isNull(in.getGiftType()) || in.getGiftType() == GiftTypeEnum.GIFT_MEMBER_COUPON.getCode())
                .collect(Collectors.toList());

        //若存在成长值 则发放
        List<GiftInfoDTO> giftInfoGrowthDTOS = giftInfoDTOList
                .stream()
                .filter(in -> Objects.nonNull(in.getGiftType()) && in.getGiftType() == GiftTypeEnum.GIFT_MEMBER_GROWTH.getCode())
                .collect(Collectors.toList());

        //发放结果
        Boolean isLabelRefresh = Boolean.FALSE;
        if (isSave) {
            if (CollUtil.isNotEmpty(giftInfoCouponDTOS)) {
                // 发券
                List<VolumeInfoUpdateDTO> respVolumeInfoList = sendVolume(data, giftInfoCouponDTOS);
                responseVolumeInfoList = buildVolumeInfo(respVolumeInfoList, giftInfoCouponDTOS);
                if (CollUtil.isNotEmpty(respVolumeInfoList)) {
                    isLabelRefresh = Boolean.TRUE;
                }
            }
            //发成长值
            isLabelRefresh = sendGrowth(giftInfoGrowthDTOS, certifiedActivity, data, isLabelRefresh);
            activityRecord = new HsaCertifiedActivityRecord();
            activityRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaCertifiedActivityRecord.class.getSimpleName()));
            activityRecord.setCertifiedActivityGuid(certifiedActivityGuid);
            activityRecord.setGmtCreate(LocalDateTime.now());
            activityRecord.setSource(ThreadLocalCache.getSource());
            activityRecord.setActivityCode(certifiedActivity.getActivityCode());
            activityRecord.setDiscount(certifiedGift.getGiveawayInfoJson());
            activityRecord.setMemberGuid(request.getUserId());
        }
        activityRecord.setActivityName(certifiedActivity.getActivityName());
        activityRecord.setCertifiedType(CertifiedTypeEnum.ALIPAY_STUDENT.getDes());
        activityRecord.setStatus(CertifiedStatusEnum.AUDIT_PASS.getCode());
        activityRecord.setAuditTime(LocalDateTime.now());
        activityRecord.setUsername(data.getNickName());
        activityRecord.setPhoneNumber(data.getPhoneNum());
        activityRecord.setGmtModified(LocalDateTime.now());

        dealProcessor(isLabelRefresh, activityRecord, certifiedActivity, isSave);

        setAlipayVolumeGift(responseVolumeInfoList, activityVO);
        return activityVO;
    }

    private void dealProcessor(Boolean isLabelRefresh, HsaCertifiedActivityRecord activityRecord, HsaCertifiedActivity certifiedActivity, boolean isSave) {
        if (Boolean.FALSE.equals(isLabelRefresh)) {
            activityRecord.setIssuanceStatus(BooleanEnum.FALSE.getCode());
        } else {
            activityRecord.setIssuanceStatus(BooleanEnum.TRUE.getCode());

            //标签关联
            MemberCertifiedLabel memberCertifiedLabel = new MemberCertifiedLabel()
                    .setCertifiedActivityGuid(certifiedActivity.getGuid())
                    .setPhoneNumber(activityRecord.getPhoneNumber());
            log.info("[学生验证通过][会员标签关联]开始,memberCertifiedLabel={}", JacksonUtils.writeValueAsString(memberCertifiedLabel));
            hsaCertifiedExternalService.memberLabelRefresh(memberCertifiedLabel);
        }

        if (isSave) {
            hsaCertifiedActivityRecordMapper.insert(activityRecord);
        } else {
            hsaCertifiedActivityRecordMapper.updateByGuid(activityRecord);
        }
    }

    private void setAlipayVolumeGift(List<VolumeInfoUpdateDTO> responseVolumeInfoList, CertifiedPupilActivityVO activityVO) {
        if (CollUtil.isNotEmpty(responseVolumeInfoList)) {
            // 优惠券列表
            activityVO.setIslandPupilCouponDTOList(CertifiedAssembler.responseVolumeInfoList2IslandPupilCouponDTOList(responseVolumeInfoList));
            // 支付宝券包参数
            activityVO.setAlipayGiftPage(AlipayPassAssembler.buildAlipayGiftPageVO(aliPayPassConfig.getLogo(),
                    aliPayPassConfig.getMcallbackUrl(), responseVolumeInfoList));
        }
    }

    private Boolean sendGrowth(List<GiftInfoDTO> giftInfoGrowthDTOS, HsaCertifiedActivity certifiedActivity, ResponseAppletsMemberInfo data, Boolean isLabelRefresh) {
        if (CollUtil.isNotEmpty(giftInfoGrowthDTOS)) {
            //成长值只存在一个
            GiftInfoDTO giftInfoDTO = giftInfoGrowthDTOS.get(0);
            RequestCertifiedGrowthValue growthValue = new RequestCertifiedGrowthValue();
            growthValue.setCertifiedName(certifiedActivity.getActivityName());
            growthValue.setGrowthValue(giftInfoDTO.getGiftGrowthValue());
            growthValue.setMemberInfoGuid(data.getMemberInfoGuid());
            growthValue.setOperSubjectGuid(certifiedActivity.getOperSubjectGuid());
            log.info("[学生验证通过]成长值调整请求,growthValue={}", JacksonUtils.writeValueAsString(growthValue));
            FeignModel<Boolean> feignModel = saasStoreFeign.certifiedGrowthAdjust(growthValue);
            log.info("[学生验证通过]成长值调整结果,result={}", JacksonUtils.writeValueAsString(feignModel));
            return feignModel.getData();
        }
        return isLabelRefresh;
    }

    private List<VolumeInfoUpdateDTO> buildVolumeInfo(List<VolumeInfoUpdateDTO> respVolumeInfoList, List<GiftInfoDTO> giftInfoDTOList) {
        List<VolumeInfoUpdateDTO> responseVolumeInfoList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(respVolumeInfoList)) {
            Map<String, VolumeInfoUpdateDTO> respVolumeInfoMap = respVolumeInfoList.stream()
                    .collect(Collectors.toMap(VolumeInfoUpdateDTO::getGuid, Function.identity(), (o, n) -> n));
            giftInfoDTOList.forEach(giftInfoDTO -> {
                VolumeInfoUpdateDTO volumeInfoUpdateDTO = respVolumeInfoMap.get(giftInfoDTO.getGiveawayGuid());
                if (Objects.isNull(volumeInfoUpdateDTO)) {
                    return;
                }
                volumeInfoUpdateDTO.setCouponNumber(giftInfoDTO.getGiveawayNum());
                responseVolumeInfoList.add(volumeInfoUpdateDTO);
            });
        }
        return responseVolumeInfoList;
    }

    @Override
    public void updateBatchByGuid(List<HsaCertifiedActivityRecord> updateList) {
        this.updateBatchById(updateList);
    }

    private List<VolumeInfoUpdateDTO> sendVolume(ResponseAppletsMemberInfo data, List<GiftInfoDTO> giftInfoDTOList) {
        RequestMemberInfoVolumeSave volumeSave = new RequestMemberInfoVolumeSave();
        volumeSave.setMemberInfoGuid(data.getMemberInfoGuid());
        List<String> volumeInfoGuidList = giftInfoDTOList.stream()
                .map(GiftInfoDTO::getGiveawayGuid)
                .collect(Collectors.toList());
        volumeSave.setVolumeInfoGuid(StringHandlerUtil.listToString(volumeInfoGuidList, StringConstant.COMMA));
        List<Integer> volumeNumberList = giftInfoDTOList.stream()
                .map(GiftInfoDTO::getGiveawayNum)
                .collect(Collectors.toList());
        volumeSave.setVolumeNumber(StringHandlerUtil.listToString(volumeNumberList, StringConstant.COMMA));
        volumeSave.setSendEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        volumeSave.setCollectionType(MemberCouponSourceTypeEnum.ALIPAY_APPLET.getType());
        volumeSave.setVolumeSourceCode(MemberCouponSourceTypeEnum.ACCURATE_PUSH.getType());
        volumeSave.setIdentity(MemberIdentityEnum.STUDENT.getCode());

        FeignModel<List<VolumeInfoUpdateDTO>> feignModel = saasStoreFeign.batchSendVolume(volumeSave);
        log.info("[学生验证通过]feignModel={}", JacksonUtils.writeValueAsString(feignModel));
        if (STATUS_CODE_SUCCEEDED != feignModel.getCode()) {
            return new ArrayList<>();
        }
        return feignModel.getData();
    }

    /**
     * 校验活动状态
     *
     * @param request              申请活动请求QO
     * @param hsaCertifiedActivity 认证活动信息
     * @return 校验结果
     */
    private int checkActivityStatus(ApplyCertifiedActivityQO request, HsaCertifiedActivity hsaCertifiedActivity,
                                    HsaCertifiedActivityRecord hsaCertifiedActivityRecord) {
        if (ActivityStateEnum.ACTIVITY_STATE_PROGRESS.getCode() != hsaCertifiedActivityService.getActivityState(hsaCertifiedActivity.
                getActivityStartTime(), hsaCertifiedActivity.getActivityEndTime(), hsaCertifiedActivity.getActivityState())) {
            return ApplyCertifiedResultEnum.GAME_OVER.getCode();
        }
        //判断活动是否结束
        if (hsaCertifiedActivity.getActivityEndTime().isBefore(LocalDateTime.now())) {
            return ApplyCertifiedResultEnum.GAME_OVER.getCode();
        }
        Integer activityJoinCount = hsaCertifiedActivityRecordMapper.selectCount(new LambdaQueryWrapper<HsaCertifiedActivityRecord>()
                .eq(HsaCertifiedActivityRecord::getCertifiedActivityGuid, request.getCertifiedActivityGuid())
                .eq(HsaCertifiedActivityRecord::getPhoneNumber, request.getPhoneNumber())
                .ne(!StringUtils.isEmpty(request.getGuid()), HsaCertifiedActivityRecord::getGuid, request.getGuid()));
        //判断当前用户参与次数
        if (hsaCertifiedActivity.getParticipateCertificationNum() != 0 &&
                activityJoinCount >= hsaCertifiedActivity.getParticipateCertificationNum()) {
            return ApplyCertifiedResultEnum.ALREADY_JOIN_THE_ACTIVITY.getCode();
        }
        //根据行程票类型或者证件类型查询
        List<HsaCertifiedGift> hsaCertifiedGifts = hsaCertifiedGiftMapper
                .selectList(new LambdaQueryWrapper<HsaCertifiedGift>()
                        .eq(HsaCertifiedGift::getCertifiedActivityGuid, hsaCertifiedActivity.getGuid())
                        .like(HsaCertifiedGift::getCertifiedConditions, "\"" + request.getTripTicketType() + "\""));
        if (CollectionUtils.isEmpty(hsaCertifiedGifts)) {
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();
        }
        int rs = checkCertificationRestriction(request, hsaCertifiedActivity);
        if (rs > 0) {
            return rs;
        }
        //满足条件的认证有礼赠送礼品对象
        HsaCertifiedGift certifiedGift = null;
        //行程票
        if (CertifiedTypeEnum.TRIP_TICKET.getCode() == hsaCertifiedActivity.getCertifiedType()) {
            Integer exchangeCount = hsaCertifiedActivityRecordMapper.queryTripTicketChangeCount(request);
            if (exchangeCount > 0) {
                return ApplyCertifiedResultEnum.TRIP_TICKET_ALREADY_EXCHANGE.getCode();
            }
            for (HsaCertifiedGift hsaCertifiedGift : hsaCertifiedGifts) {
                if (!checkCertifiedGift(request, hsaCertifiedGift)) {
                    certifiedGift = hsaCertifiedGift;
                    break;
                }
            }
        } else {//自定义证件
            Integer certifiedCount = hsaCertifiedActivityRecordMapper.queryCertificateChangeCount(request);
            if (certifiedCount > 0) {
                return ApplyCertifiedResultEnum.CERTIFICATE_ALREADY_EXCHANGE.getCode();
            }
            //认证条件是限制
            if (Objects.nonNull(hsaCertifiedActivity.getCertificationConditionLimit()) &&
                    hsaCertifiedActivity.getCertificationConditionLimit() == BooleanEnum.TRUE.getCode()) {
                int checkResult = checkCertifiedNumberLength(request, hsaCertifiedActivity);
                if (checkResult > 0) {
                    return checkResult;
                }
            }
            //两长一短取最长，两个都长取最近
            //满足条件的认证有礼赠送礼品对象集合（自定义证件可能有多个，取条件长的那个，最长的又多个，取第一个）
            List<HsaCertifiedGift> list = Lists.newArrayList();
            for (HsaCertifiedGift hsaCertifiedGift : hsaCertifiedGifts) {
                //不限制
                if (Objects.isNull(hsaCertifiedGift.getFixedNumberStart()) || StringUtils.isEmpty(request.getCertificateNumber())) {
                    certifiedGift = hsaCertifiedGift;
                    continue;
                }
                //证件编号大于固定编号结束位
                if (hsaCertifiedGift.getFixedNumberEnd() > request.getCertificateNumber().length()) {
                    continue;
                }
                String content = request.getCertificateNumber().substring(hsaCertifiedGift.
                        getFixedNumberStart() - 1, hsaCertifiedGift.getFixedNumberEnd());
                //满足限制条件
                if (Objects.equals(content, hsaCertifiedGift.getFixedNumberInfo())) {
                    hsaCertifiedGift.setFixedNumberInfoLength(hsaCertifiedGift.getFixedNumberInfo().length());
                    list.add(hsaCertifiedGift);
                }
            }
            if (!list.isEmpty()) {
                certifiedGift = list
                        .stream()
                        .sorted(Comparator.comparing(HsaCertifiedGift::getFixedNumberInfoLength)
                                .reversed()
                                .thenComparing(HsaCertifiedGift::getId))
                        .collect(Collectors.toList())
                        .get(0);
            }
        }
        log.info("满足条件的认证有礼赠送礼品对象===========>{}", certifiedGift);

        if (Objects.isNull(certifiedGift)) {
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();
        }
        hsaCertifiedActivityRecord.setDiscount(certifiedGift.getGiveawayInfoJson());
        return ApplyCertifiedResultEnum.SUCCEED.getCode();
    }

    private static boolean checkCertifiedGift(ApplyCertifiedActivityQO request, HsaCertifiedGift hsaCertifiedGift) {
        List<String> conditions = JSON.parseArray(hsaCertifiedGift.getCertifiedConditions(), String.class);
        if (!conditions.contains(request.getTripTicketType())) {
            return true;
        }
        List<String> addressList = JSON.parseArray(hsaCertifiedGift.getCertifiedConditionsInfo(), String.class);
        return Objects.nonNull(addressList) && !addressList.contains(request.getJourneyAddress());
    }

    /**
     * 校验到达日期、证件日期
     */
    private int checkCertificationRestriction(ApplyCertifiedActivityQO request, HsaCertifiedActivity hsaCertifiedActivity) {

        //认证条件不限制或者认证限制时间范围为0
        if (hsaCertifiedActivity.getCertificationRestrictionsType() != BooleanEnum.TRUE.getCode() ||
                hsaCertifiedActivity.getTimeRestrictionsScope() == 0) {
            return ApplyCertifiedResultEnum.SUCCEED.getCode();
        }
        //校验时间
        LocalDate checkTime;
        if (hsaCertifiedActivity.getCertifiedType() == CertifiedTypeEnum.TRIP_TICKET.getCode()) {
            checkTime = request.getArriveTime();
        } else {
            checkTime = request.getCertificateDate();
        }
        if (Objects.isNull(checkTime)) {
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();
        }
        DataUnitEnum dataUnitEnum = DataUnitEnum.Enum(hsaCertifiedActivity.getTimeRestrictionsUnit());

        switch (Objects.requireNonNull(dataUnitEnum)) {
            case DAY:
                checkTime = checkTime.plusDays(hsaCertifiedActivity.getTimeRestrictionsScope());
                break;
            case WEEK:
                checkTime = checkTime.plusWeeks(hsaCertifiedActivity.getTimeRestrictionsScope());
                break;
            case MONTH:
                checkTime = checkTime.plusMonths(hsaCertifiedActivity.getTimeRestrictionsScope());
                break;
            case YEAR:
                checkTime = checkTime.plusYears(hsaCertifiedActivity.getTimeRestrictionsScope());
                break;
            default:
                return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();
        }
        if (checkTime.isBefore(LocalDate.now())) {
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();
        }
        return ApplyCertifiedResultEnum.SUCCEED.getCode();
    }

    private Integer checkCertifiedNumberLength(ApplyCertifiedActivityQO request, HsaCertifiedActivity hsaCertifiedActivity) {


        if (request.getCertificateNumber().length() != hsaCertifiedActivity.getCertificationConditionNum()) {
            log.info("证件编号位数不符合认证条件限制位数");
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();
        }
        if (hsaCertifiedActivity.getCertificationConditionType() == CertificationConditionTypeEnum.PURE_NUMBERS.getCode()
                && (!StrValidate.isDigit(request.getCertificateNumber()))) {
            log.info("证件编号不是纯数字");
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();

        }
        if (hsaCertifiedActivity.getCertificationConditionType() == CertificationConditionTypeEnum.PURE_LETTERS.getCode()
                && (!StrValidate.isLetter(request.getCertificateNumber()))) {
            log.info("证件编号不是纯字母");
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();

        }
        if (hsaCertifiedActivity.getCertificationConditionType() == CertificationConditionTypeEnum.LETTER_AND_NUMBER.getCode()
                && (!StrValidate.numberAndLetter(request.getCertificateNumber()))) {
            log.info("证件编号不是数字加字母");
            return ApplyCertifiedResultEnum.APPLY_INFO_NOT_ACCORD_CONDITION.getCode();

        }
        return ApplyCertifiedResultEnum.SUCCEED.getCode();
    }


}
