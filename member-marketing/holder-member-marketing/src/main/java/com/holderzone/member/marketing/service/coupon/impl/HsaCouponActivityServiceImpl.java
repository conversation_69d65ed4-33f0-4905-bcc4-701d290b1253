package com.holderzone.member.marketing.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.coupon.CouponActivityStateEnum;
import com.holderzone.member.common.enums.gift.MarketingGiftActivityStateEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynMarketingDTO;
import com.holderzone.member.common.qo.coupon.CouponListCountQO;
import com.holderzone.member.common.qo.coupon.CouponPageQO;
import com.holderzone.member.common.qo.coupon.RequestCouponActivityQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.number.NumberUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.coupon.CouponListCountVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponActivityVO;
import com.holderzone.member.marketing.entity.coupon.HsaCouponActivity;
import com.holderzone.member.marketing.entity.coupon.HsaCouponCommodity;
import com.holderzone.member.marketing.entity.coupon.HsaCouponStore;
import com.holderzone.member.marketing.mapper.coupon.HsaCouponActivityMapper;
import com.holderzone.member.marketing.service.coupon.IHsaCouponActivityService;
import com.holderzone.member.marketing.service.coupon.IHsaCouponCommodityService;
import com.holderzone.member.marketing.service.coupon.IHsaCouponStoreService;
import com.holderzone.member.marketing.support.SettlementMarketingSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 充值赠送活动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Service
@Slf4j
public class HsaCouponActivityServiceImpl extends HolderBaseServiceImpl<HsaCouponActivityMapper, HsaCouponActivity> implements IHsaCouponActivityService {

    @Resource
    private HsaCouponActivityMapper hsaCouponActivityMapper;

    @Resource
    private IHsaCouponCommodityService hsaCouponCommodityService;

    @Resource
    private IHsaCouponStoreService hsaCouponStoreService;

    @Resource
    private GuidGeneratorUtil getGuidGeneratorUtils;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private SettlementMarketingSupport settlementMarketingSupport;


    @Override
    public boolean editCouponActivity(RequestCouponActivityQO request) {
        HsaCouponActivity hsaCouponActivity;
        if (StringUtils.isNotBlank(request.getGuid())) {
            hsaCouponActivity = queryByGuid(request.getGuid());
            BeanUtils.copyProperties(request, hsaCouponActivity);
            dealCouponActivity(request, hsaCouponActivity);
            hsaCouponActivity.setGmtModified(LocalDateTime.now());
            //结算中心：发布过的券（变未发布）不删除
            updateByGuid(hsaCouponActivity);
        } else {
            hsaCouponActivity = new HsaCouponActivity();

            BeanUtils.copyProperties(request, hsaCouponActivity);
            hsaCouponActivity.setGuid(getGuidGeneratorUtils.getStringGuid(HsaCouponActivity.class.getSimpleName()));
            hsaCouponActivity.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            hsaCouponActivity.setCouponCode(createCouponCode());
            dealCouponActivity(request, hsaCouponActivity);

            hsaCouponActivityMapper.insert(hsaCouponActivity);
        }
        return true;
    }

    /**
     * 生成编码
     *
     * @return String
     */
    public String createCouponCode() {
        String activityCode = NumberUtil.buildNumToStr(6);
        LambdaQueryWrapper<HsaCouponActivity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(HsaCouponActivity::getCouponCode, activityCode);
        HsaCouponActivity one = this.getOne(lambdaQueryWrapper);
        if (ObjectUtils.isEmpty(one)) {
            return activityCode;
        } else {
            return createCouponCode();
        }
    }

    @Override
    public ResponseCouponActivityVO detailsCouponActivity(String guid) {
        ResponseCouponActivityVO response = new ResponseCouponActivityVO();
        HsaCouponActivity hsaCouponActivity = queryByGuid(guid);
        dealCouponActivityVO(response, hsaCouponActivity);
        return response;
    }

    @Override
    public PageResult<ResponseCouponActivityVO> pageQuery(CouponPageQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        qo.setIsDelete(BooleanEnum.FALSE.getCode());
        List<ResponseCouponActivityVO> activityVOList = hsaCouponActivityMapper.pageQuery(qo);
        if (CollUtil.isNotEmpty(activityVOList)) {
            List<String> couponGuids = activityVOList.stream()
                    .map(ResponseCouponActivityVO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<HsaCouponCommodity> commodities = hsaCouponCommodityService.getCommodityByCouponGuids(couponGuids);
            List<HsaCouponStore> stores = hsaCouponStoreService.getStoreByCouponGuids(couponGuids);

            List<String> couponCodes = new ArrayList<>();
            for (ResponseCouponActivityVO responseCouponActivityVO : activityVOList) {
                responseCouponActivityVO.setApplyBusinessList(JSON.parseArray(responseCouponActivityVO.getApplyBusinessJson(), String.class));
                responseCouponActivityVO.setApplyTerminalList(JSON.parseArray(responseCouponActivityVO.getApplyTerminalJson(), String.class));
                couponCodes.add(responseCouponActivityVO.getCouponCode());

                List<HsaCouponCommodity> couponCommodities = commodities.stream()
                        .filter(e -> Objects.equals(responseCouponActivityVO.getGuid(), e.getCouponGuid()))
                        .collect(Collectors.toList());
                responseCouponActivityVO.setRequestCouponCommodityQOList(hsaCouponCommodityService.getCommodityByCoupon(couponCommodities));

                List<HsaCouponStore> couponStores = stores.stream()
                        .filter(e -> Objects.equals(responseCouponActivityVO.getGuid(), e.getCouponGuid()))
                        .collect(Collectors.toList());
                responseCouponActivityVO.setRequestCouponStoreQOList(hsaCouponStoreService.getStoreByCoupon(couponStores));
            }
            //填充发放数核销数
            putActivityCouponNum(activityVOList, couponCodes);
        }
        return PageUtil.pageResult(new PageInfo<>(activityVOList));
    }

    /**
     * 填充优惠券数量
     *
     * @param activityVOList 活动 h
     * @param couponCodes    券码
     */
    private void putActivityCouponNum(List<ResponseCouponActivityVO> activityVOList, List<String> couponCodes) {
        final CouponListCountQO listCountQo = new CouponListCountQO()
                .setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid())
                .setCouponCodes(couponCodes);
        final Map<String, CouponListCountVO> couponNumMap = memberBaseFeign.countListCoupon(listCountQo);
        if (CollUtil.isNotEmpty(couponNumMap)) {
            //发放数
            activityVOList.forEach(vo -> {
                final CouponListCountVO countVO = couponNumMap.get(vo.getCouponCode());
                Optional.ofNullable(countVO).ifPresent(c -> {
                    vo.setGrantNum(c.getGivenCount());
                    vo.setWriteNum(c.getUseCount());
                });
            });
        }
    }

    private static final String STATE_ERROR = "操作失败，优惠券状态已变更，请刷新重试";

    @Override
    public String updateActivityState(String guid, Integer activityState) {
        HsaCouponActivity hsaCouponActivity = queryByGuid(guid);

        if (Objects.isNull(hsaCouponActivity) || hsaCouponActivity.deleted()) {
            throw new MemberMarketingException("数据不存在！");
        }
        if (activityState == CouponActivityStateEnum.REMOVE.getCode()) {
            hsaCouponActivity.deleteTime();
            updateCouponActivity(hsaCouponActivity);
            //推送到结算台
            settlementMarketingSupport.sendCoupon(hsaCouponActivity.getCouponType(), Collections.singletonList(hsaCouponActivity));
            return StringConstant.SUCCESS;
        }
        LocalDateTime now = LocalDateTime.now();
        if (activityState == CouponActivityStateEnum.UNDER_WAY.getCode()) {
            String stateError = validateState(activityState, hsaCouponActivity, now);
            if (stateError != null) {
                return stateError;
            }
            //推送到结算台
            HsaCouponActivity activity = new HsaCouponActivity();
            hsaCouponActivity.setActivityState(activityState);
            BeanUtils.copyProperties(hsaCouponActivity, activity);
            settlementMarketingSupport.sendCoupon(hsaCouponActivity.getCouponType(), Collections.singletonList(activity));

            hsaCouponActivity.setIsPublished(Boolean.TRUE);
        } else {
            //只有进行中能暂停
            if (activityState == CouponActivityStateEnum.STOP.getCode() &&
                    hsaCouponActivity.getActivityState() != CouponActivityStateEnum.UNDER_WAY.getCode()) {
                return STATE_ERROR;
            }
            //推送到结算台
            settlementMarketingSupport.sendCoupon(hsaCouponActivity.getCouponType(), Collections.singletonList(hsaCouponActivity));
            hsaCouponActivity.setActivityState(activityState);
        }
        updateCouponActivity(hsaCouponActivity);
        return StringConstant.SUCCESS;
    }

    private void updateCouponActivity(HsaCouponActivity hsaCouponActivity) {
        updateByGuid(hsaCouponActivity);
    }

    private String validateState(Integer activityState, HsaCouponActivity hsaCouponActivity, LocalDateTime now) {
        //只有进行中能暂停
        if (!CouponActivityStateEnum.underWay(hsaCouponActivity.getActivityState())) {
            return STATE_ERROR;
        }
        if (hsaCouponActivity.getEffectiveType() == 0
                || hsaCouponActivity.getEffectiveType() == 2) {
            if (hsaCouponActivity.getCouponEffectiveEndTime().compareTo(now) <= 0) {
                hsaCouponActivity.setActivityState(CouponActivityStateEnum.OVER.getCode());
                updateCouponActivity(hsaCouponActivity);
                //推送到结算台
                settlementMarketingSupport.sendCoupon(hsaCouponActivity.getCouponType(), Collections.singletonList(hsaCouponActivity));
                return "券结束时间已到，请确认券时间";
            }

            long duration = Duration.between(now, hsaCouponActivity.getCouponEffectiveEndTime()).toMinutes();
            if (duration <= 4320) {
                hsaCouponActivity.setActivityState(activityState);
                hsaCouponActivity.setIsPublished(Boolean.TRUE);
                updateCouponActivity(hsaCouponActivity);
                //推送到结算台
                settlementMarketingSupport.sendCoupon(hsaCouponActivity.getCouponType(), Collections.singletonList(hsaCouponActivity));
                return "活动时间即将结束，请确认券时间";
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActivityState() {
        List<HsaCouponActivity> hsaCouponActivityList = baseMapper.selectList(new LambdaQueryWrapper<HsaCouponActivity>()
                .eq(HsaBaseEntity::getIsDelete, BooleanEnum.FALSE.getCode())
                .in(HsaCouponActivity::getActivityState, CouponActivityStateEnum.UNDER_WAY.getCode(), CouponActivityStateEnum.STOP.getCode())
                .le(HsaCouponActivity::getCouponEffectiveEndTime, LocalDateTime.now())
                .in(HsaCouponActivity::getEffectiveType, 0, 2));

        log.info("处理优惠券过期数据：{}", JacksonUtils.writeValueAsString(hsaCouponActivityList));

        if (CollUtil.isEmpty(hsaCouponActivityList)) {
            return;
        }
        //优惠券过期
        List<String> overCouponList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (HsaCouponActivity hsaCouponActivity : hsaCouponActivityList) {
            if (!hsaCouponActivity.getCouponEffectiveEndTime().isAfter(now)) {
                hsaCouponActivity.setActivityState(CouponActivityStateEnum.OVER.getCode());
                this.updateByGuid(hsaCouponActivity);
                overCouponList.add(hsaCouponActivity.getGuid());
            }
        }
        //推送到优惠结算台
        settlementMarketingSupport.sendCoupon(overCouponList);
    }

    @Override
    public void synCouponRelationRule(SettlementSynMarketingDTO synMarketingDTO) {
        if (CollUtil.isEmpty(synMarketingDTO.getSynDiscounts())) {
            return;
        }
        hsaCouponActivityMapper.synCouponRelationRule(synMarketingDTO.getSynDiscounts());
        // 结算台同步优惠券叠加使用上限
        hsaCouponActivityMapper.synCouponLimit(synMarketingDTO.getSynDiscounts());
    }

    private void dealCouponActivityVO(ResponseCouponActivityVO response, HsaCouponActivity hsaCouponActivity) {
        BeanUtils.copyProperties(hsaCouponActivity, response);

        if (StringUtils.isNotBlank(hsaCouponActivity.getApplyBusinessJson())) {
            response.setApplyBusinessList(JSON.parseArray(hsaCouponActivity.getApplyBusinessJson(), String.class));
        }

        if (StringUtils.isNotBlank(hsaCouponActivity.getApplyLabelGuidJson())) {
            response.setApplyLabelGuidList(JSON.parseArray(hsaCouponActivity.getApplyLabelGuidJson(), String.class));
        }

        if (StringUtils.isNotBlank(hsaCouponActivity.getApplyTerminalJson())) {
            response.setApplyTerminalList(JSON.parseArray(hsaCouponActivity.getApplyTerminalJson(), String.class));
        }
        response.setRequestCouponCommodityQOList(hsaCouponCommodityService.getCommodityByCoupon(hsaCouponActivity.getGuid()));
        response.setRequestCouponStoreQOList(hsaCouponStoreService.getCommodityByCoupon(hsaCouponActivity.getGuid()));
    }

    private void dealCouponActivity(RequestCouponActivityQO request, HsaCouponActivity hsaCouponActivity) {


        hsaCouponActivity.setActivityState(MarketingGiftActivityStateEnum.UN_PUBLISHED.getCode());

        hsaCouponActivity.setApplyBusinessJson(JSON.toJSONString(request.getApplyBusinessList()))
                .setApplyTerminalJson(JSON.toJSONString(request.getApplyTerminalList()));
        if (CollUtil.isNotEmpty(request.getApplyLabelGuidList())) {
            hsaCouponActivity.setApplyLabelGuidJson(JSON.toJSONString(request.getApplyLabelGuidList()));
        } else {
            hsaCouponActivity.setApplyLabelGuidJson(null);
        }

        //处理商品
        hsaCouponCommodityService.updateCommodityByCoupon(hsaCouponActivity.getGuid(), request.getRequestCouponCommodityQOList());


        //处理门店
        hsaCouponStoreService.updateStoreByCoupon(hsaCouponActivity.getGuid(), request.getRequestCouponStoreQOList());

    }
}
