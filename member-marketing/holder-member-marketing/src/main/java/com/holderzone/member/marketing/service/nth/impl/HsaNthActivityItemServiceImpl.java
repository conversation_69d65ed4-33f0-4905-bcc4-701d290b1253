package com.holderzone.member.marketing.service.nth.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynDiscountDTO;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityItem;
import com.holderzone.member.marketing.mapper.nth.HsaNthActivityItemMapper;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityItemService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 第N份优惠活动适用商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class HsaNthActivityItemServiceImpl extends HolderBaseServiceImpl<HsaNthActivityItemMapper, HsaNthActivityItem> implements IHsaNthActivityItemService {

    @Override
    public List<HsaNthActivityItem> listByActivityGuid(String activityGuid) {
        LambdaQueryWrapper<HsaNthActivityItem> qw = new LambdaQueryWrapper<HsaNthActivityItem>()
                .eq(HsaNthActivityItem::getActivityGuid, activityGuid);
        return list(qw);
    }

    @Override
    public List<HsaNthActivityItem> listByActivityGuids(List<String> activityGuids) {
        if (CollectionUtils.isEmpty(activityGuids)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HsaNthActivityItem> qw = new LambdaQueryWrapper<HsaNthActivityItem>()
                .in(HsaNthActivityItem::getActivityGuid, activityGuids);
        return list(qw);
    }

    @Override
    public void removeByActivityGuid(String activityGuid) {
        LambdaUpdateWrapper<HsaNthActivityItem> uw = new UpdateWrapper<HsaNthActivityItem>()
                .lambda()
                .eq(HsaNthActivityItem::getActivityGuid, activityGuid);
        remove(uw);
    }
}
