package com.holderzone.member.marketing.service.coupon;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.coupon.RequestCouponCommodityQO;
import com.holderzone.member.common.vo.coupon.ResponseCouponCommodityVO;
import com.holderzone.member.marketing.entity.coupon.HsaCouponCommodity;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
public interface IHsaCouponCommodityService extends IHolderBaseService<HsaCouponCommodity> {

    boolean updateCommodityByCoupon(String couPonGuid, List<RequestCouponCommodityQO> requestCouponCommodityQOList);

    List<HsaCouponCommodity> getCommodityByCouponGuids(List<String> couponGuids);

    List<ResponseCouponCommodityVO> getCommodityByCoupon(String couPonGuid);

    List<ResponseCouponCommodityVO> getCommodityByCoupon(List<HsaCouponCommodity> couponCommodities);

}
