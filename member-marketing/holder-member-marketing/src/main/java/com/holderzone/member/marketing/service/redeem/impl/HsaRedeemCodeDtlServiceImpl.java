package com.holderzone.member.marketing.service.redeem.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.common.base.HsaBaseEntity;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.coupon.ResponseCouponDTO;
import com.holderzone.member.common.dto.excel.RedeemCodeUploadExcel;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.redeem.RedeemRightInfosDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponUse;
import com.holderzone.member.common.enums.coupon.CouponActivityStateEnum;
import com.holderzone.member.common.enums.coupon.RedeemActiveTypeEnum;
import com.holderzone.member.common.enums.coupon.RedeemCodeStateEnum;
import com.holderzone.member.common.enums.coupon.RedeemCouponCodeEnum;
import com.holderzone.member.common.enums.gift.MarketingGiftActivityStateEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.coupon.CouponDtlQO;
import com.holderzone.member.common.qo.redeem.*;
import com.holderzone.member.common.util.date.DateUtil;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.card.CardBaseInfoVO;
import com.holderzone.member.common.vo.card.SubsidyRecordDetailVO;
import com.holderzone.member.common.vo.excel.ExcelSubsidyRecordVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRecordExcelVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRecordVO;
import com.holderzone.member.common.vo.specials.RedeemCodeRecordExcelVO;
import com.holderzone.member.marketing.assembler.CouponPackageAssembler;
import com.holderzone.member.marketing.entity.redeem.HsaRedeemCodeActive;
import com.holderzone.member.marketing.entity.redeem.HsaRedeemCodeDtl;
import com.holderzone.member.marketing.mapper.redeem.HsaRedeemCodeActiveMapper;
import com.holderzone.member.marketing.mapper.redeem.HsaRedeemCodeDtlMapper;
import com.holderzone.member.marketing.service.redeem.HsaRedeemCodeActiveService;
import com.holderzone.member.marketing.service.redeem.HsaRedeemCodeDtlService;
import com.holderzone.member.marketing.service.redeem.util.GenerateRedeemCodeHandle;
import com.holderzone.member.marketing.service.redeem.util.RedeemCodeUploadExcelUtil;
import com.holderzone.member.marketing.util.excel.ExportDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 优惠兑换码 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaRedeemCodeDtlServiceImpl
        extends HolderBaseServiceImpl<HsaRedeemCodeDtlMapper, HsaRedeemCodeDtl> implements HsaRedeemCodeDtlService {

    @Resource
    private GuidGeneratorUtil getGuidGeneratorUtils;

    /**
     * 线程池
     */
    @Resource
    private Executor marketingThreadExecutor;

    @Resource
    private HsaRedeemCodeActiveMapper hsaRedeemCodeActiveMapper;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Override
    public PageResult<RespondRedeemCodePageVO> queryPage(RequestRedeemCodeDtlPageQO request) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //通用码特殊处理
        HsaRedeemCodeActive hsaRedeemCodeActive = hsaRedeemCodeActiveMapper.queryByGuid(request.getRedeemCodeActiveGuid());
        if (hsaRedeemCodeActive.getRedeemCodeType() == RedeemCouponCodeEnum.COMMON_USED_CODE.getCode()) {
            request.setRedeemCodeType(RedeemCouponCodeEnum.COMMON_USED_CODE.getCode());
        }

        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        List<RespondRedeemCodePageVO> activityVOList = baseMapper.pageQuery(request);

        if (CollUtil.isNotEmpty(activityVOList)) {
            Map<String, Integer> codeMap = memberBaseFeign
                    .listByCodeRecord(new CouponDtlQO().setDtlList(activityVOList.stream()
                            .map(RespondRedeemCodePageVO::getGuid)
                            .collect(Collectors.toList())));
            for (RespondRedeemCodePageVO respondMemberRedeemPageVO : activityVOList) {
                CouponPackageAssembler.dealRightInfoJson(respondMemberRedeemPageVO, codeMap);
            }
        }
        return PageUtil.pageResult(new PageInfo<>(activityVOList));
    }

    @Override
    public PageResult<RespondMemberRedeemPageVO> queryPageByMember(RequestRedeemCodeDtlPageQO request) {
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        List<RespondMemberRedeemPageVO> activityVOList = baseMapper.queryPageByMember(request);

        CouponPackageAssembler.setRightInfoJson(activityVOList);
        return PageUtil.pageResult(new PageInfo<>(activityVOList));
    }



    @Override
    public void export(RequestRedeemCodeDtlPageQO request, HttpServletResponse response) {
        request.setPageSize(NumberConstant.NUMBER_20000 + 1);
        PageMethod.startPage(request.getCurrentPage(), request.getPageSize());
        PageResult<RespondRedeemCodePageVO> activityRecordList = this.queryPage(request);
        List<RespondRedeemCodePageVO> resultList = activityRecordList.getRecords();
        if (resultList.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        // 导出数据处理
        List<RedeemCodeRecordExcelVO> recordExcelList = CouponPackageAssembler.getRedeemCodeRecordExcelVOS(resultList);
        HsaRedeemCodeActive hsaRedeemCodeActive = hsaRedeemCodeActiveMapper.queryByGuid(request.getRedeemCodeActiveGuid());
        // 导出数据
        exportData(recordExcelList, response, hsaRedeemCodeActive);
    }

    private void exportData(List<RedeemCodeRecordExcelVO> recordExcelList, HttpServletResponse response, HsaRedeemCodeActive hsaRedeemCodeActive) {
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String fileName = String.format(hsaRedeemCodeActive.getActiveName(), timeStr);
        if (CollectionUtils.isEmpty(recordExcelList)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        try {
            response.setContentType(ExportDataUtils.CONTENT_TYPE);
            response.setCharacterEncoding(ExportDataUtils.UTF_8);
            fileName = URLEncoder.encode(fileName, ExportDataUtils.UTF_8);
            response.setHeader(ExportDataUtils.HEADER, ExportDataUtils.ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
            EasyExcelFactory.write(response.getOutputStream(), RedeemCodeRecordExcelVO.class)
                    .sheet("sheet1")
                    .doWrite(recordExcelList);
        } catch (IOException e) {
            log.info(hsaRedeemCodeActive.getActiveName() + "导出报错!" + e.getMessage());
        }
    }

    @Override
    public RespondStatisticsVO queryStatistics(RequestRedeemCodeDtlPageQO request) {
        RespondStatisticsVO respondStatisticsVO = new RespondStatisticsVO();

        HsaRedeemCodeActive hsaRedeemCodeActive = hsaRedeemCodeActiveMapper.queryByGuid(request.getRedeemCodeActiveGuid());

        //总数量
        request.setPageSize(NumberConstant.NUMBER_9999999);
        request.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        List<RespondRedeemCodePageVO> activityVOList = this.queryPage(request).getRecords();

        if (hsaRedeemCodeActive.getRedeemCodeType() == RedeemCouponCodeEnum.COMMON_USED_CODE.getCode()
                && Objects.isNull(request.getRedeemCodeState())) {
            respondStatisticsVO.setTotalNumber(hsaRedeemCodeActive.getStockCount());
        } else {
            respondStatisticsVO.setTotalNumber(activityVOList.size());
        }
        if (CollUtil.isEmpty(activityVOList)) {
            return respondStatisticsVO;
        }
        //兑换数量
        List<RespondRedeemCodePageVO> alreadyNum = activityVOList
                .stream()
                .filter(item -> Objects.equals(item.getCodeState(), RedeemCodeStateEnum.ALREADY_REDEEM.getCode()))
                .collect(Collectors.toList());
        respondStatisticsVO.setRedeemSuccessNumber(alreadyNum.size());

        //兑换率 = 兑换成功数 / 总库存 x 100%，四舍五入保留两位小数；
        BigDecimal redeemRate = getRedeemRate(request, hsaRedeemCodeActive, alreadyNum, activityVOList);
        respondStatisticsVO.setRedeemRate(redeemRate);


        //券核销数量
        dealCouponRate(activityVOList, respondStatisticsVO);
        return respondStatisticsVO;
    }

    private static BigDecimal getRedeemRate(RequestRedeemCodeDtlPageQO request, HsaRedeemCodeActive hsaRedeemCodeActive, List<RespondRedeemCodePageVO> alreadyNum, List<RespondRedeemCodePageVO> activityVOList) {
        BigDecimal redeemRate;
        if (hsaRedeemCodeActive.getRedeemCodeType() == RedeemCouponCodeEnum.COMMON_USED_CODE.getCode()
                && Objects.isNull(request.getRedeemCodeState())) {
            redeemRate = new BigDecimal(alreadyNum.size())
                    .divide(new BigDecimal(hsaRedeemCodeActive.getStockCount()), 2, RoundingMode.DOWN)
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
        } else {
            redeemRate = new BigDecimal(alreadyNum.size())
                    .divide(new BigDecimal(activityVOList.size()), 2, RoundingMode.DOWN)
                    .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
        }
        return redeemRate;
    }

    private static void dealCouponRate(List<RespondRedeemCodePageVO> activityVOList, RespondStatisticsVO respondStatisticsVO) {
        List<RespondRedeemCodePageVO> result = activityVOList.stream()
                .filter(item -> Objects.equals(item.getActiveRedeemType(), RedeemActiveTypeEnum.REDEEM_COUPON.getCode())
                        && Objects.equals(item.getCodeState(), RedeemCodeStateEnum.ALREADY_REDEEM.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(result)) {
            int toNum = result.stream().mapToInt(RespondRedeemCodePageVO::getUsedCount).sum();
            respondStatisticsVO.setCouponUseCount(toNum);
            //券核销率：统计筛选条件下，= 券核销数量 / 券兑换数量  x 100%，四舍五入保留两位小数；

            //总兑换数
            BigDecimal redeemCount = BigDecimal.ZERO;
            for (RespondRedeemCodePageVO respondRedeemCodePageVO : result) {
                for (ResponseCouponDTO responseCouponDTO : respondRedeemCodePageVO.getCouponGuidList()) {
                    redeemCount = redeemCount.add(new BigDecimal(responseCouponDTO.getNum()));
                }
            }
            BigDecimal totalCount = new BigDecimal(toNum);
            BigDecimal couponUseRate;
            if (totalCount.compareTo(BigDecimal.ZERO) == 0) {
                couponUseRate = BigDecimal.ZERO;
            } else {
                couponUseRate = totalCount.divide(redeemCount, 2, RoundingMode.DOWN).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            }
            respondStatisticsVO.setCouponUseRate(couponUseRate);
        }
    }

    @Override
    public void batchCancelRedeemCode(List<String> codeGuidList) {
        batchCancelRedeemCode(codeGuidList, RedeemCodeStateEnum.CANCEL.getCode());
    }

    @Override
    public void batchRedeemCodeState(String guid, Integer codeState) {
        List<HsaRedeemCodeDtl> hsaRedeemCodeDtlList = getHsaRedeemCodeDtls(guid, codeState);

        if (CollUtil.isNotEmpty(hsaRedeemCodeDtlList)) {
            HeaderUserInfo header = ThreadLocalCache.getHeaderUserInfo();
            marketingThreadExecutor.execute(() -> {
                ThreadLocalCache.put(JSON.toJSONString(header));
                List<String> codeGuidList = hsaRedeemCodeDtlList.stream().map(HsaRedeemCodeDtl::getGuid).collect(Collectors.toList());
                int pointsDataLimit = 5000;//限制条数
                while (!codeGuidList.isEmpty()) {
                    List<String> listPage;
                    if (codeGuidList.size() >= pointsDataLimit) {
                        listPage = codeGuidList.subList(0, pointsDataLimit);
                    } else {
                        listPage = codeGuidList.subList(0, codeGuidList.size());
                    }

                    if (codeState == CouponActivityStateEnum.OVER.getCode()) {
                        batchCancelRedeemCode(codeGuidList, RedeemCodeStateEnum.EXPIRED.getCode());
                    } else if (codeState == CouponActivityStateEnum.UN_START.getCode()) {
                        batchCancelRedeemCode(codeGuidList, RedeemCodeStateEnum.NOT_REDEEM.getCode());
                    }
                    //剔除
                    codeGuidList.subList(0, listPage.size()).clear();
                }
            });
        }
    }

    @Override
    public void batchRedeemCodeMemberInfo(RequestUpdateDtlQO request) {
        if (CollectionUtils.isEmpty(request.getGuids())) {
            return;
        }
        baseMapper.update(new HsaRedeemCodeDtl().setRedeemMemberGuid(request.getMemberGuid())
                        .setRedeemMemberName(request.getMemberName())
                        .setRedeemMemberPhone(request.getMemberPhone()),
                new LambdaQueryWrapper<HsaRedeemCodeDtl>()
                        .in(HsaRedeemCodeDtl::getGuid, request.getGuids()));
    }

    private List<HsaRedeemCodeDtl> getHsaRedeemCodeDtls(String guid, Integer codeState) {
        List<HsaRedeemCodeDtl> hsaRedeemCodeDtlList;

        if (codeState == CouponActivityStateEnum.OVER.getCode()) {
            hsaRedeemCodeDtlList = baseMapper.selectList(new LambdaQueryWrapper<HsaRedeemCodeDtl>()
                    .eq(HsaRedeemCodeDtl::getRedeemCodeActiveGuid, guid)
                    .eq(HsaRedeemCodeDtl::getCodeState, RedeemCodeStateEnum.NOT_REDEEM.getCode()));
        } else {
            hsaRedeemCodeDtlList = baseMapper.selectList(new LambdaQueryWrapper<HsaRedeemCodeDtl>()
                    .eq(HsaRedeemCodeDtl::getRedeemCodeActiveGuid, guid)
                    .eq(HsaRedeemCodeDtl::getCodeState, RedeemCodeStateEnum.EXPIRED.getCode()));
        }
        return hsaRedeemCodeDtlList;
    }


    private void batchCancelRedeemCode(List<String> codeGuidList, Integer codeState) {
        int num = baseMapper.batchCancelRedeemCode(codeGuidList, codeState);
        log.info("批量作废兑换码，成功数量：{}", num);
    }


    @Override
    public void saveIslandRedeemCodeDtl(String redeemActiveGuid, List<String> cods) {
        log.info("保存兑换码，兑换码数量：{}", cods.size());
        List<HsaRedeemCodeDtl> dtls = new ArrayList<>(cods.size());
        List<String> guids = getGuidGeneratorUtils.getGuids(HsaRedeemCodeDtl.class.getSimpleName(), cods.size());
        for (int i = 0; i < cods.size(); i++) {
            HsaRedeemCodeDtl dtl = new HsaRedeemCodeDtl();
            dtl.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            dtl.setGuid(guids.get(i));
            dtl.setRedeemCodeSn(cods.get(i));
            dtl.setRedeemCodeActiveGuid(redeemActiveGuid);
            dtl.setCodeState(RedeemCodeStateEnum.NOT_REDEEM.getCode());    //0-未兑换
            dtls.add(dtl);
        }
        this.saveBatch(dtls);
    }

    @Override
    public HsaRedeemCodeDtl queryByCodeAndState(String code, Integer state) {
        List<HsaRedeemCodeDtl> hsaRedeemCodeDtlList = baseMapper.selectList(new LambdaQueryWrapper<HsaRedeemCodeDtl>()
                .eq(HsaRedeemCodeDtl::getRedeemCodeSn, code)
                .eq(Objects.nonNull(state), HsaRedeemCodeDtl::getCodeState, state)
                .eq(HsaRedeemCodeDtl::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));

        if (CollUtil.isNotEmpty(hsaRedeemCodeDtlList)) {
            return hsaRedeemCodeDtlList.get(0);
        }
        return null;
    }

    @Override
    public int queryMemberRedeemCount(String memberGuid, String redeemActiveGuid) {
        return baseMapper.selectCount(new LambdaQueryWrapper<HsaRedeemCodeDtl>()
                .eq(StringUtils.isNotEmpty(memberGuid), HsaRedeemCodeDtl::getRedeemMemberGuid, memberGuid)
                .eq(StringUtils.isNotEmpty(redeemActiveGuid), HsaRedeemCodeDtl::getRedeemCodeActiveGuid, redeemActiveGuid));
    }
}
