package com.holderzone.member.marketing.entity.reduction;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class HsaFullReductionFoldActivityItem implements Serializable {

    private static final long serialVersionUID = -320179672700108361L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * guid
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动guid
     */
    private String activityGuid;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 商品类型
     */
    private Integer commodityType;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品渠道
     */
    private String channel;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0-否 1-是
     */
    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Byte isDelete;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private String system;

}
