package com.holderzone.member.marketing.controller;

import cn.hutool.core.util.StrUtil;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.ResponseCardInfoDTO;
import com.holderzone.member.common.dto.excel.ItemUploadVO;
import com.holderzone.member.common.dto.excel.MemberUploadExcelVO;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.activity.RequestLabelBaseQO;
import com.holderzone.member.common.qo.gift.RequestQueryMemberBaseQO;
import com.holderzone.member.common.qo.gift.RequestQueryVolumeBaseQO;
import com.holderzone.member.common.qo.member.RequestLabelSetting;
import com.holderzone.member.common.qo.member.ResponseOperationMemberInfo;
import com.holderzone.member.common.util.repast.RepastUtils;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.grade.QueryMemberGradeVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础数据
 * @date 2021/8/26 14:18
 */
@RestController
@RequestMapping("/hsa-base")
@Slf4j
public class OperatingSubjectController {

    @Lazy
    @Resource
    private ExternalSupport externalSupport;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    /**
     * 获取基础数据
     *
     * @return Result
     */
    @ApiOperation("获取基础数据")
    @GetMapping(value = "/getOperatingSubjectInfo", produces = "application/json;charset=utf-8")
    public Result getOperatingSubjectInfo() {
        return Result.success(externalSupport.baseServer(ThreadLocalCache.getSystem()).getOperatingSubjectInfo());
    }

    @ApiOperation("获取门店数据")
    @GetMapping(value = "/queryStore", produces = "application/json;charset=utf-8")
    public Result queryStore(@RequestParam(required = false) String name) {
        return Result.success(externalSupport.storeServer(ThreadLocalCache.getSystem()).queryStore(name));
    }

    @ApiOperation("查询桌台数据")
    @GetMapping(value = "/queryTable", produces = "application/json;charset=utf-8")
    public Result queryTable(String storeGuid) {

        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).queryTable(storeGuid, null));
    }

    @ApiOperation("查询所有会员卡")
    @GetMapping("/getCardList")
    public Result<List<ResponseCardInfoDTO>> getAllCardList() {
        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).getAllCardList());
    }


    /**
     * 会员等级列表
     */
    @ApiOperation("会员等级列表")
    @PostMapping("/member_grade/query_member_grade_list")
    Result<QueryMemberGradeVO> queryGrowthValueTaskDetail(@RequestParam(value = "roleType", required = false) String roleType,
                                                          @RequestParam(value = "gradeType", required = false) Integer gradeType) {
        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).queryGrowthValueTaskDetail(roleType, gradeType));
    }

    /**
     * 获取标签列表
     *
     * @param requestLabelDTO requestLabelDTO
     * @return List<ResponseOperationLabel>
     */
    @ApiOperation("获取标签列表")
    @PostMapping(value = "/get_label_list", produces = "application/json;charset=utf-8")
    Result<List<ResponseOperationLabel>> getLabelList(@RequestBody RequestLabelBaseQO requestLabelDTO) {
        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).getLabelList(requestLabelDTO));
    }

    /**
     * 新增手动标签
     */
    @ApiOperation("新增手动标签")
    @PostMapping(value = "/save_automatic_label", produces = "application/json;charset=utf-8")
    Result<Boolean> saveAutomaticLabel(@RequestBody RequestLabelSetting req) {
        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).saveAutomaticLabel(req));
    }

    @ApiOperation(value = "查询优惠券列表")
    @PostMapping(value = "/query_volume_list")
    public Result<Page<IslandCouponDTO>> queryVolumeList(@RequestBody RequestQueryVolumeBaseQO request) {
        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).queryVolumeList(request));
    }

    @ApiOperation(value = "查询会员列表")
    @PostMapping(value = "/get_operation_member_info_page")
    public Result<Page<ResponseOperationMemberInfo>> getOperationMemberInfoPage(@RequestBody RequestQueryMemberBaseQO request) {
        Integer system = ThreadLocalCache.getSystem();
        // 餐饮云需要判断是否授权
        if (Objects.equals(system, SystemEnum.REPAST.getCode()) && RepastUtils.isHasRepastAuth(ThreadLocalCache.getOperSubjectGuid())) {
            system = SystemEnum.RETAIL.getCode();
        }

        return Result.success(externalSupport.memberServer(system).getOperationMemberInfoPage(request));
    }

    @ApiOperation(value = "导入会员列表")
    @GetMapping(value = "/member_upload_excel_url")
    Result<MemberUploadExcelVO> memberUploadExcelUrl(@RequestParam(value = "fileUrl") String fileUrl) {
        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).memberUploadExcelUrl(fileUrl));
    }


    @ApiOperation(value = "查询活动业务")
    @GetMapping(value = "/get_business_scene")
    Result<List<Integer>> getBusinessScene() {
        return Result.success(externalSupport.memberServer(ThreadLocalCache.getSystem()).getBusinessScene());
    }

    @ApiOperation(value = "导入商品")
    @GetMapping(value = "/item_upload_excel_url")
    public Result<ItemUploadVO> itemUploadExcelUrl(@RequestParam(value = "fileUrl") String fileUrl,
                                                   @RequestParam(value = "activityType", required = false) Integer activityType) {
        return Result.success(externalSupport.itemServer(ThreadLocalCache.getSystem()).itemUploadExcelUrl(fileUrl, activityType));
    }
}
