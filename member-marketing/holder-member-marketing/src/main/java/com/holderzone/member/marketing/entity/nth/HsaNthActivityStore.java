package com.holderzone.member.marketing.entity.nth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 第N份优惠活动适用门店
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
public class HsaNthActivityStore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 活动guid
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动guid
     */
    private String activityGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店id
     */
    private String storeCode;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0-否 1-是
     */
    @TableLogic
    private Byte isDelete;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private String system;

}
