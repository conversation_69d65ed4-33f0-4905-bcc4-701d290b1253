package com.holderzone.member.marketing.service.coupon.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.qo.coupon.RequestCouponStoreQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.coupon.ResponseCouponStoreVO;
import com.holderzone.member.marketing.entity.coupon.HsaCouponStore;
import com.holderzone.member.marketing.mapper.coupon.HsaCouponStoreMapper;
import com.holderzone.member.marketing.service.coupon.IHsaCouponStoreService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 充值赠送活动门店 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Service
public class HsaCouponStoreServiceImpl extends HolderBaseServiceImpl<HsaCouponStoreMapper, HsaCouponStore> implements IHsaCouponStoreService {

    @Resource
    private HsaCouponStoreMapper hsaCouponStoreMapper;

    @Resource
    private GuidGeneratorUtil getGuidGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStoreByCoupon(String couponGuid, List<RequestCouponStoreQO> requestCouponStoreQOList) {
        List<HsaCouponStore> hsaCouponStore = hsaCouponStoreMapper.selectList(new LambdaQueryWrapper<HsaCouponStore>()
                .eq(HsaCouponStore::getCouponGuid, couponGuid));

        if (CollUtil.isNotEmpty(hsaCouponStore)) {
            hsaCouponStore.forEach(in -> hsaCouponStoreMapper.removeByGuid(in.getGuid()));
        }

        List<HsaCouponStore> hsaCouponStoreList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(requestCouponStoreQOList)){
            for (RequestCouponStoreQO requestCouponStoreQO : requestCouponStoreQOList) {
                HsaCouponStore couponStore = new HsaCouponStore();
                couponStore.setGuid(getGuidGenerator.getStringGuid(HsaCouponStore.class.getSimpleName()));
                couponStore.setStoreGuid(requestCouponStoreQO.getStoreGuid());
                couponStore.setStoreName(requestCouponStoreQO.getStoreName());
                couponStore.setCouponGuid(couponGuid);
                couponStore.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
                couponStore.setStoreNumber(requestCouponStoreQO.getStoreNumber());
                couponStore.setChannel(requestCouponStoreQO.getChannel());
                couponStore.setSystem(requestCouponStoreQO.getSystem());
                hsaCouponStoreList.add(couponStore);
            }
            this.saveBatch(hsaCouponStoreList);
        }

        return true;
    }

    @Override
    public List<HsaCouponStore> getStoreByCouponGuids(List<String> couponGuids) {
        if (CollectionUtils.isEmpty(couponGuids)) {
            return Lists.newArrayList();
        }
        return hsaCouponStoreMapper.selectList(new LambdaQueryWrapper<HsaCouponStore>()
                .in(HsaCouponStore::getCouponGuid, couponGuids));
    }

    @Override
    public List<ResponseCouponStoreVO> getCommodityByCoupon(String couPonGuid) {
        List<HsaCouponStore> hsaCouponStoreList = hsaCouponStoreMapper.selectList(new LambdaQueryWrapper<HsaCouponStore>()
                .eq(HsaCouponStore::getCouponGuid, couPonGuid));
        return getStoreByCoupon(hsaCouponStoreList);
    }

    @Override
    public List<ResponseCouponStoreVO> getStoreByCoupon(List<HsaCouponStore> hsaCouponStoreList) {
        if (CollectionUtils.isEmpty(hsaCouponStoreList)) {
            return Lists.newArrayList();
        }
        List<ResponseCouponStoreVO> responseCouponStoreVO = Lists.newArrayList();
        for (HsaCouponStore couponStore : hsaCouponStoreList) {
            ResponseCouponStoreVO response = new ResponseCouponStoreVO();
            BeanUtils.copyProperties(couponStore, response);
            responseCouponStoreVO.add(response);
        }
        return responseCouponStoreVO;
    }
}
