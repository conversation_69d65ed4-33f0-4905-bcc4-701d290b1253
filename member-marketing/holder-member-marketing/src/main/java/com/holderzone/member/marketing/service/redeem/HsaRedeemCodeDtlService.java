package com.holderzone.member.marketing.service.redeem;


import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.redeem.*;
import com.holderzone.member.marketing.entity.redeem.HsaRedeemCodeDtl;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface HsaRedeemCodeDtlService extends IHolderBaseService<HsaRedeemCodeDtl> {

    /**
     * 分页查询兑换码
     */
    PageResult<RespondRedeemCodePageVO> queryPage(RequestRedeemCodeDtlPageQO request);

    /**
     * 根据会员查询兑换记录
     */
    PageResult<RespondMemberRedeemPageVO> queryPageByMember(RequestRedeemCodeDtlPageQO request);

    /**
     * 导出
     */
    void export(RequestRedeemCodeDtlPageQO request, HttpServletResponse response);

    /**
     * 统计兑换码
     */
    RespondStatisticsVO queryStatistics(RequestRedeemCodeDtlPageQO request);

    /**
     * 批量作废兑换码
     */
    void batchCancelRedeemCode(List<String> codeGuidList);

    /**
     * 批量修改兑换码状态
     */
    void batchRedeemCodeState(String guid, Integer codeState);

    /**
     * 批量修改兑换码会员信息
     */
    void batchRedeemCodeMemberInfo(RequestUpdateDtlQO request);

    /**
     * 保存优惠券兑换码
     */
    void saveIslandRedeemCodeDtl(String redeemActiveGuid, List<String> cods);

    /**
     * 根据兑换码和状态查询
     */
    HsaRedeemCodeDtl queryByCodeAndState(String code, Integer state);

    /**
     * 查询用户兑换次数
     */
    int queryMemberRedeemCount(String memberGuid, String redeemActiveGuid);
}
