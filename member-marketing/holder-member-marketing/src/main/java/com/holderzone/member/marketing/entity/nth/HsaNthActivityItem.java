package com.holderzone.member.marketing.entity.nth;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 第N份优惠活动适用商品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
public class HsaNthActivityItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 活动guid
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动guid
     */
    private String activityGuid;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 商品类型
     */
    private Integer commodityType;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品渠道
     */
    private String channel;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0-否 1-是
     */
    @TableLogic
    private Byte isDelete;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private String system;

}
