package com.holderzone.member.marketing.entity.nth;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.holderzone.member.common.enums.nth.NthActivityGroupTypeEnum;
import lombok.Data;

/**
 * <p>
 * 第N份优惠活动
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
public class HsaNthActivity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 活动guid
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动ID
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动状态 0-未发布 1-已发布 2-已暂停
     *
     * @see com.holderzone.member.common.enums.nth.ActivityStateEnum
     */
    private Integer state;

    /**
     * 优惠规则 2,0
     */
    private String discountRule;

    /**
     * 门槛条件 1:购买任意商品
     */
    private Integer thresholdType;

    /**
     * 计算规则 1:从价格最低开始优惠
     */
    private Integer calculateRule;

    /**
     * 是否限制活动时段 0-否 1-是
     */
    private Integer isLimitPeriod;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer limitPeriodType;

    /**
     * 限制时段限制类型json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String limitPeriodJson;

    /**
     * 活动标签guid集合json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String labelGuidJson;

    /**
     * 活动规则 共享互斥关系 0-互斥 1-共享
     */
    private Integer relationRule;

    /**
     * 适用场景 0:全部业务 1：部分业务
     */
    private Integer applyBusiness;

    /**
     * 适用场景json
     */
    private String applyBusinessJson;

    /**
     * 是否适用于所有门店 1:全部门店；0:部分门店(外关联表)
     */
    private Integer isAllStore;

    /**
     * 适用人群类型 0不限制 1所有注册会员 2指定人群
     *
     * @see NthActivityGroupTypeEnum
     */
    private Integer groupType;

    /**
     * 指定标签json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String conditionLabelJson;

    /**
     * 指定等级json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String conditionGradeJson;

    /**
     * 指定会员json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String conditionMemberJson;

    /**
     * 是否发布过 0-否 1-是
     */
    private Integer isPublish;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0-否 1-是
     */
    @TableLogic
    private Byte isDelete;
}
