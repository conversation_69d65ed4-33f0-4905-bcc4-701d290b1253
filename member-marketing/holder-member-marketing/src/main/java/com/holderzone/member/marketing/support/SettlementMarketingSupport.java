package com.holderzone.member.marketing.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.enums.coupon.CouponActivityStateEnum;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountTypeEnum;
import com.holderzone.member.common.support.SettlementSupport;
import com.holderzone.member.marketing.entity.coupon.HsaCouponActivity;
import com.holderzone.member.marketing.service.coupon.IHsaCouponActivityService;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 优惠结算台支持：营销中心
 *
 * <AUTHOR>
 * @date 2023/9/7
 * @since 1.8
 */
@Component
@Async("marketingThreadExecutor")
public class SettlementMarketingSupport {

    @Lazy
    @Resource
    IHsaCouponActivityService couponActivityService;

    @Resource
    SettlementSupport settlementSupport;

    /**
     * 批量推送优惠券（适用于过期..）
     * 场景：发布、停止、过期
     *
     * @param couponGuids 优惠券guid
     */
    public void sendCoupon(List<String> couponGuids) {
        if (CollUtil.isEmpty(couponGuids)) {
            return;
        }
        //优惠券列表
        final List<HsaCouponActivity> couponActivities = couponActivityService.list(
                new LambdaQueryWrapper<HsaCouponActivity>()
                        .select(HsaCouponActivity::getGuid,
                                HsaCouponActivity::getOperSubjectGuid,
                                HsaCouponActivity::getActivityState,
                                HsaCouponActivity::getCouponCode,
                                HsaCouponActivity::getCouponName,
                                HsaCouponActivity::getCouponType,
                                HsaCouponActivity::getThresholdType,
                                HsaCouponActivity::getThresholdAmount,
                                HsaCouponActivity::getDiscountAmount,
                                HsaCouponActivity::getCouponEffectiveStartTime,
                                HsaCouponActivity::getCouponEffectiveEndTime)
                        .in(HsaCouponActivity::getGuid, couponGuids)
        );
        if (CollUtil.isEmpty(couponActivities)) {
            return;
        }
        final Map<String, List<HsaCouponActivity>> operSubjectGuidCouponMap = couponActivities.stream()
                .collect(Collectors.groupingBy(HsaCouponActivity::getOperSubjectGuid));
        //主体分组
        for (Map.Entry<String, List<HsaCouponActivity>> operSubjectEntry : operSubjectGuidCouponMap.entrySet()) {
            //优惠券类型分组
            final Map<Integer, List<HsaCouponActivity>> couponTypeMap = operSubjectEntry.getValue()
                    .stream().collect(Collectors.groupingBy(HsaCouponActivity::getCouponType));
            for (Map.Entry<Integer, List<HsaCouponActivity>> entry : couponTypeMap.entrySet()) {
                //推送不同类型优惠券
                sendCoupon(entry.getKey(), entry.getValue());
            }
        }
    }


    /**
     * 推送优惠券
     * 场景：发布、停止、过期
     *
     * @param couponType 优惠券类型
     * @param couponList 优惠券列表
     */
    public void sendCoupon(int couponType, List<HsaCouponActivity> couponList) {
        final SettlementDiscountOptionEnum optionEnum = CouponTypeEnum.convert(couponType);
        if (optionEnum == null || CollUtil.isEmpty(couponList)) {
            return;
        }
        final HsaCouponActivity firstCoupon = couponList.get(0);
        //推送到优惠结算台
        SettlementDiscountDTO discountDTO = new SettlementDiscountDTO();
        discountDTO.setOperSubjectGuid(firstCoupon.getOperSubjectGuid());
        //优惠券类型
        discountDTO.setOption(optionEnum.getCode());

        //未发布优惠券
        final List<HsaCouponActivity> unPublishedCoupons = couponList.stream()
                .filter(c -> !CouponActivityStateEnum.settlement(c.getActivityState()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(unPublishedCoupons)) {
            //删除
            final List<String> delCouponGuids = unPublishedCoupons.stream().map(HsaCouponActivity::getCouponCode).collect(Collectors.toList());
            discountDTO.setDelDiscountGuids(delCouponGuids);
        }
        //已发布优惠券(包含暂停)
        final List<HsaCouponActivity> publishedCoupons = couponList.stream()
                .filter(c -> CouponActivityStateEnum.settlement(c.getActivityState()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(publishedCoupons)) {
            settlementSupport.syncSettlementDiscount(discountDTO);
            return;
        }

        //优惠券更新
        sendCouponForUpdate(discountDTO, publishedCoupons);
    }

    /**
     * 推送优惠券
     * 场景：更新
     *
     * @param discountDTO 优惠券更新
     * @param publishedCoupons 已发布优惠券
     */
    private void sendCouponForUpdate(SettlementDiscountDTO discountDTO, List<HsaCouponActivity> publishedCoupons) {
        //优惠项更新
        List<SettlementDiscountDTO.Discount> discountList = new ArrayList<>(publishedCoupons.size());
        for (HsaCouponActivity couponActivity : publishedCoupons) {

            //eg: 代金券：满100元减20元  不同的券展示不同
            final String discountDynamics = couponActivity.showDiscountDynamics();
            SettlementDiscountDTO.Discount discount = new SettlementDiscountDTO.Discount()
                    //订单级
                    .setDiscountType(SettlementDiscountTypeEnum.ORDER.getCode())
                    .setDiscountNum(couponActivity.getSingleOrderUsedLimit())
                    .setRelationRule(couponActivity.getShareRelation())
                    .setDiscountGuid(couponActivity.getCouponCode())
                    .setDiscountDynamic(discountDynamics)
                    .setDiscountName(couponActivity.getCouponName());

            if (couponActivity.getIsPublished() == Boolean.FALSE) {
                discount.setIsFirstAdd(1);
            }
            discountList.add(discount);
        }

        discountDTO.setDiscountList(discountList);
        settlementSupport.syncSettlementDiscount(discountDTO);
    }
}
