package com.holderzone.member.marketing.service.coupon;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.coupon.RequestCouponStoreQO;
import com.holderzone.member.common.vo.coupon.ResponseCouponStoreVO;
import com.holderzone.member.marketing.entity.coupon.HsaCouponStore;

import java.util.List;

/**
 * <p>
 * 充值赠送活动门店 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
public interface IHsaCouponStoreService extends IHolderBaseService<HsaCouponStore> {

    /**
     * 修改优惠券关联门店
     * @param couPonGuid couPonGuid
     * @param requestCouponStoreQOList requestCouponStoreQOList
     * @return boolean
     */
    boolean updateStoreByCoupon(String couPonGuid, List<RequestCouponStoreQO> requestCouponStoreQOList);

    List<HsaCouponStore> getStoreByCouponGuids(List<String> couponGuids);

    List<ResponseCouponStoreVO> getCommodityByCoupon(String couPonGuid);

    List<ResponseCouponStoreVO> getStoreByCoupon(List<HsaCouponStore> hsaCouponStoreList);
}
