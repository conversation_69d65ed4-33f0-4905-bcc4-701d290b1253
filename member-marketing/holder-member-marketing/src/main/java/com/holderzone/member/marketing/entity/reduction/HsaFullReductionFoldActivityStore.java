package com.holderzone.member.marketing.entity.reduction;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class HsaFullReductionFoldActivityStore implements Serializable {

    private static final long serialVersionUID = -2850585912909575860L;

    /**
     * 自增主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0-否 1-是
     */
    @ApiModelProperty(value = "是否删除 0-否 1-是")
    private Byte isDelete;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动guid
     */
    private String activityGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店id
     */
    private String storeCode;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    private String system;

}
