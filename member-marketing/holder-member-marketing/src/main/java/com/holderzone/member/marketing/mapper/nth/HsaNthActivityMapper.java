package com.holderzone.member.marketing.mapper.nth;

import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynDiscountDTO;
import com.holderzone.member.common.qo.nth.NthActivityQO;
import com.holderzone.member.common.vo.nth.NthActivityVO;
import com.holderzone.member.marketing.entity.nth.HsaNthActivity;
import com.holderzone.member.common.config.mybatis.HolderBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 第N份优惠活动 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface HsaNthActivityMapper extends HolderBaseMapper<HsaNthActivity> {

    /**
     * 活动列表查询
     */
    List<NthActivityVO> query(@Param("query") NthActivityQO query);

    /**
     * 同步关联规则
     *
     * @param synDiscounts 同步关联规则
     */
    void updateRelationRuleActivity(List<SettlementSynDiscountDTO> synDiscounts);
}
