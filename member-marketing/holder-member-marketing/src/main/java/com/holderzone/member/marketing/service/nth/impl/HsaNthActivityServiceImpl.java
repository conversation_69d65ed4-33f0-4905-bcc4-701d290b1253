package com.holderzone.member.marketing.service.nth.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.nth.ActivityStateEnum;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynDiscountDTO;
import com.holderzone.member.common.qo.nth.NthActivityQO;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.nth.NthActivityVO;
import com.holderzone.member.marketing.entity.nth.HsaNthActivity;
import com.holderzone.member.marketing.mapper.nth.HsaNthActivityMapper;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityService;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 第N份优惠活动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class HsaNthActivityServiceImpl extends HolderBaseServiceImpl<HsaNthActivityMapper, HsaNthActivity> implements IHsaNthActivityService {

    @Override
    public List<HsaNthActivity> listRunningByOperSubjectGuid(String operSubjectGuid) {
        LambdaQueryWrapper<HsaNthActivity> qw = new LambdaQueryWrapper<HsaNthActivity>()
                .eq(HsaNthActivity::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaNthActivity::getState, ActivityStateEnum.RUNNING.getCode())
                .ge(HsaNthActivity::getEndTime, LocalDateTime.now());
        return list(qw);
    }

    @Override
    public PageResult<NthActivityVO> pageQuery(NthActivityQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<NthActivityVO> activityVOList = baseMapper.query(qo);
        return PageUtil.getPageResult(new PageInfo<>(activityVOList));
    }

    @Override
    public void synActivityRelationRule(List<SettlementSynDiscountDTO> synDiscounts) {
        baseMapper.updateRelationRuleActivity(synDiscounts);
    }
}
