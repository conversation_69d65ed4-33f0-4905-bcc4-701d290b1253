package com.holderzone.member.marketing.service.reduction.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.MarketConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.order.OrderStateEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRecordQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityStaticsQO;
import com.holderzone.member.common.qo.activity.SynOrderDiscountQO;
import com.holderzone.member.common.qo.card.BarkOrderDiscountCallbackQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRecordVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityStaticsVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRecordExcelVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityStaticsVO;
import com.holderzone.member.marketing.entity.reduction.HsaFullReductionFoldActivity;
import com.holderzone.member.marketing.entity.reduction.HsaFullReductionFoldActivityRecord;
import com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityMapper;
import com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityRecordMapper;
import com.holderzone.member.marketing.service.reduction.HsaFullReductionFoldActivityRecordService;
import com.holderzone.member.marketing.service.reduction.HsaFullReductionFoldActivityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 满减满折活动记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaFullReductionFoldActivityRecordServiceImpl
        extends HolderBaseServiceImpl<HsaFullReductionFoldActivityRecordMapper, HsaFullReductionFoldActivityRecord>
        implements HsaFullReductionFoldActivityRecordService {
    @Resource
    private ExternalSupport externalSupport;


    private static final String CONTENT_TYPE = "application/vnd.ms-excel";

    private static final String UTF_8 = "utf-8";

    private static final String HEADER = "Content-disposition";

    private static final String ATTACHMENT = "attachment;filename=";

    private static final String SHEET_NAME = "活动统计";

    @Resource
     private  GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private HsaFullReductionFoldActivityMapper activityMapper;

    @Override
    public PageResult<FullReductionFoldActivityRecordVO> page(FullReductionFoldActivityStaticsQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<FullReductionFoldActivityRecordVO> activityRecordList = baseMapper.query(qo);
        return PageUtil.pageResult(new PageInfo<>(activityRecordList));
    }

    @Override
    public FullReductionFoldActivityStaticsVO statics(FullReductionFoldActivityStaticsQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        FullReductionFoldActivityStaticsVO fullReductionFoldActivityStaticsVO = baseMapper.statics(qo);

        //适配零售无会员情况
        long count = baseMapper.getNotMemberCount(qo).longValue();
        if (count > 0) {
            count = count + fullReductionFoldActivityStaticsVO.getMemberCount();
            BigDecimal decimal = fullReductionFoldActivityStaticsVO.getOrderActuallyFee()
                    .divide(new BigDecimal(count), 2, RoundingMode.DOWN);
            fullReductionFoldActivityStaticsVO.setMemberUnitPrice(decimal);
            fullReductionFoldActivityStaticsVO.setMemberCount(count);
        }
        return fullReductionFoldActivityStaticsVO;
    }

    @Override
    public void export(FullReductionFoldActivityStaticsQO qo, HttpServletResponse response) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        qo.setPageSize(NumberConstant.NUMBER_20000 + 1);
        PageMethod.startPage(qo.getCurrentPage(), qo.getPageSize());
        List<FullReductionFoldActivityRecordVO> activityRecordList = baseMapper.query(qo);
        if (activityRecordList.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        // 导出数据处理
        List<LimitSpecialsActivityRecordExcelVO> recordExcelList = excelDateHandler(activityRecordList);
        // 导出数据
        exportData(recordExcelList, response);
    }

    @Override
    public void updateRefundStateByOrderGuid(String orderGuid) {
        final LambdaUpdateWrapper<HsaFullReductionFoldActivityRecord> uw =
                new UpdateWrapper<HsaFullReductionFoldActivityRecord>()
                        .lambda()
                        .eq(HsaFullReductionFoldActivityRecord::getOrderGuid, orderGuid);
        List<HsaFullReductionFoldActivityRecord> hsaFullReductionFoldActivityRecords = baseMapper.selectList(uw);
        hsaFullReductionFoldActivityRecords.forEach(in -> {
            in.setOrderState(OrderStateEnum.REFUND.getCode());
            baseMapper.updateByGuid(in);
        });
    }

    @Override
    public HsaFullReductionFoldActivityRecord queryByOrderGuid(String operSubjectGuid, String orderNum) {
        return baseMapper.selectOne(new LambdaQueryWrapper<HsaFullReductionFoldActivityRecord>()
                .eq(HsaFullReductionFoldActivityRecord::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaFullReductionFoldActivityRecord::getOrderNo, orderNum));
    }

    @Override
    public void saveRecord(List<SynOrderDiscountQO> recordQO) {
        List<String> discountGuidList = recordQO.stream().map(SynOrderDiscountQO::getDiscountGuid).collect(Collectors.toList());
        Map<String, HsaFullReductionFoldActivity> limitSpecialsActivityMap = activityMapper.selectList(new LambdaQueryWrapper<HsaFullReductionFoldActivity>()
                .in(HsaFullReductionFoldActivity::getActivityCode, discountGuidList))
                .stream()
                .collect(Collectors.toMap(HsaFullReductionFoldActivity::getActivityCode, Function.identity(), (obj1, obj2) -> obj1));

        log.info("满减满折活动记录批量新增,recordQO={}", JSON.toJSONString(recordQO));
        List<HsaFullReductionFoldActivityRecord> fullReductionFoldActivityRecords =Lists.newArrayList();
        for (SynOrderDiscountQO synOrderDiscountQO : recordQO) {
            HsaFullReductionFoldActivityRecord activityRecord = new HsaFullReductionFoldActivityRecord();
            HsaFullReductionFoldActivity hsaLimitSpecialsActivity = limitSpecialsActivityMap.get(synOrderDiscountQO.getDiscountGuid());
            if (Objects.isNull(hsaLimitSpecialsActivity)) {
                log.info("满减满折活动不存在,订单={}", synOrderDiscountQO.getOrderNo());
                continue;
            }
            getFullReductionFoldActivityRecord(synOrderDiscountQO, activityRecord, hsaLimitSpecialsActivity, fullReductionFoldActivityRecords);
        }

        if (CollUtil.isNotEmpty(fullReductionFoldActivityRecords))
            this.saveBatch(fullReductionFoldActivityRecords);
    }

    @Override
    public void updateRecord(BarkOrderDiscountCallbackQO callbackQO) {
        log.info("满减满折回退记录更新,callbackQO={}", JSON.toJSONString(callbackQO));
        HsaFullReductionFoldActivityRecord hsaFullReductionFoldActivityRecord = baseMapper.selectOne(new LambdaQueryWrapper<HsaFullReductionFoldActivityRecord>()
                .eq(HsaFullReductionFoldActivityRecord::getOperSubjectGuid, callbackQO.getOperSubjectGuid())
                .eq(HsaFullReductionFoldActivityRecord::getOrderNo, callbackQO.getOrderNo()));

        if (Objects.nonNull(hsaFullReductionFoldActivityRecord)) {
            hsaFullReductionFoldActivityRecord.setOrderBackFee(hsaFullReductionFoldActivityRecord.getOrderBackFee().add(callbackQO.getRefundAmount()));

            if (Objects.nonNull(callbackQO.getDiscountAmount())){
                hsaFullReductionFoldActivityRecord.setOrderDiscountFee(hsaFullReductionFoldActivityRecord.getOrderDiscountFee().subtract(callbackQO.getDiscountAmount()));
            }
            //若是全额退款 则修改订单状态为已退款
            if (callbackQO.getRefundType() == BooleanEnum.TRUE.getCode()) {
                hsaFullReductionFoldActivityRecord.setOrderState(OrderStateEnum.REFUND.getCode());
            }

            baseMapper.updateByGuid(hsaFullReductionFoldActivityRecord);
        }
    }

    @Override
    public List<FullReductionFoldActivityRecordVO> queryRecord(FullReductionFoldActivityRecordQO qo) {
        return baseMapper.queryRecord(qo);
    }

    @Override
    public List<FullReductionFoldActivityStaticsVO> staticsOrderCountByActivityGuid(List<String> activityGuidList) {
        if (CollectionUtils.isEmpty(activityGuidList)) {
            return Lists.newArrayList();
        }
        return baseMapper.staticsOrderCountByActivityGuid(activityGuidList);
    }

    private void getFullReductionFoldActivityRecord(SynOrderDiscountQO synOrderDiscountQO,
                                                HsaFullReductionFoldActivityRecord activityRecord,
                                                HsaFullReductionFoldActivity hsaFullReductionFoldActivity,
                                                List<HsaFullReductionFoldActivityRecord> hsaLimitSpecialsActivityRecords) {
        activityRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaFullReductionFoldActivityRecord.class.getSimpleName()));
        activityRecord.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //活动信息
        activityRecord.setActivityCode(hsaFullReductionFoldActivity.getActivityCode());
        activityRecord.setActivityGuid(hsaFullReductionFoldActivity.getGuid());
        activityRecord.setName(hsaFullReductionFoldActivity.getActivityName());

        //订单信息
        activityRecord.setStoreName(synOrderDiscountQO.getStoreName());
        activityRecord.setOrderActuallyFee(synOrderDiscountQO.getOrderActuallyFee());
        activityRecord.setOrderNo(synOrderDiscountQO.getOrderNo());
        activityRecord.setOrderType(synOrderDiscountQO.getOrderType() !=null ? synOrderDiscountQO.getOrderType() : MarketConsumptionOrderTypeEnum.STORE_ORDER.getCode());
        activityRecord.setOrderFee(synOrderDiscountQO.getOrderFee());
        activityRecord.setOrderDiscountFee(synOrderDiscountQO.getOrderDiscountFee());
        activityRecord.setOrderTime(synOrderDiscountQO.getOrderTime());
        activityRecord.setOrderSource(synOrderDiscountQO.getOrderSource());
        activityRecord.setOrderState(synOrderDiscountQO.getOrderState());
        activityRecord.setOrderGuid(synOrderDiscountQO.getOrderGuid());

        //会员数据
        activityRecord.setMemberGuid(synOrderDiscountQO.getMemberGuid());
        activityRecord.setMemberPhone(synOrderDiscountQO.getPhoneNum());
        activityRecord.setMemberName(synOrderDiscountQO.getUserName());

        hsaLimitSpecialsActivityRecords.add(activityRecord);
    }

    /**
     * 导出数据处理
     */
    private List<LimitSpecialsActivityRecordExcelVO> excelDateHandler(List<FullReductionFoldActivityRecordVO> activityRecordList) {
        if (CollectionUtils.isEmpty(activityRecordList)) {
            return Lists.newArrayList();
        }
        return activityRecordList.stream().map(e -> {
            LimitSpecialsActivityRecordExcelVO excelVO = new LimitSpecialsActivityRecordExcelVO();
            BeanUtils.copyProperties(e, excelVO);
            // 订单状态
            excelVO.setOrderState(OrderStateEnum.getByCode(e.getOrderState()));
            // 订单类型
            excelVO.setOrderType(externalSupport.baseServer(ThreadLocalCache.getSystem()).getOrderTypeEnum(e.getOrderType()));
            // 订单应付金额
            excelVO.setOrderFee(e.getOrderFee().stripTrailingZeros().toPlainString());
            // 订单优惠金额
            excelVO.setOrderDiscountFee(e.getOrderDiscountFee().stripTrailingZeros().toPlainString());
            // 订单实收金额
            excelVO.setOrderActuallyFee(e.getOrderActuallyFee().stripTrailingZeros().toPlainString());
            // 订单退款金额
            excelVO.setOrderBackFee(e.getOrderBackFee().stripTrailingZeros().toPlainString());
            // 订单来源
            excelVO.setOrderSource(externalSupport.baseServer(ThreadLocalCache.getSystem()).getOrderSourceEnum(e.getOrderSource()));
            return excelVO;
        }).collect(Collectors.toList());
    }

    private void exportData(List<LimitSpecialsActivityRecordExcelVO> activityRecordList, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(activityRecordList)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_NOT_DATA);
        }
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(StringConstant.YYYY_MM_DD));
        String fileName = "满减满折活动统计" + timeStr;
        try {
            response.setContentType(CONTENT_TYPE);
            response.setCharacterEncoding(UTF_8);
            fileName = URLEncoder.encode(fileName, UTF_8);
            response.setHeader(HEADER, ATTACHMENT + fileName + ExcelTypeEnum.XLSX.getValue());
            EasyExcelFactory.write(response.getOutputStream(), LimitSpecialsActivityRecordExcelVO.class)
                    .sheet(SHEET_NAME)
                    .doWrite(activityRecordList);
        } catch (IOException e) {
            log.error("满减满折活动统计导出记录明细报错!" + e.getMessage());
        }
    }
}
