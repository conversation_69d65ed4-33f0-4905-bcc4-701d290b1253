package com.holderzone.member.marketing.manage;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynMarketingDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.marketing.mapper.HsaFullReductionFoldActivityMapper;
import com.holderzone.member.marketing.service.certificate.HsaCertifiedThemeService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityService;
import com.holderzone.member.marketing.service.specials.IHsaLimitSpecialsActivityService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 营销中心回调操作
 *
 * <AUTHOR>
 * @date 2023/9/5
 * @since 1.8
 */
@Component
@AllArgsConstructor
@Slf4j
public class MarketCallbackManage {


    private final HsaCertifiedThemeService certifiedThemeService;

    private final IHsaLimitSpecialsActivityService limitSpecialsActivityService;

    private final IHsaNthActivityService nthActivityService;

    private final HsaFullReductionFoldActivityMapper fullReductionFoldActivityMapper;

    public void initializeSubjectData(List<String> operSubjectGuids) {
        //init
        certifiedThemeService.init(operSubjectGuids);
    }

    public void synActivityRelationRule(SettlementSynMarketingDTO synMarketingDTO) {
        if (CollUtil.isNotEmpty(synMarketingDTO.getSynDiscounts())) {
            if (synMarketingDTO.getAnEnum() == SettlementDiscountOptionEnum.FULL_OFF) {
                fullReductionFoldActivityMapper.updateRelationRuleActivity(synMarketingDTO.getSynDiscounts());
            }

            if (synMarketingDTO.getAnEnum() == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL) {
                limitSpecialsActivityService.synActivityRelationRule(synMarketingDTO.getSynDiscounts());
            }

            if (synMarketingDTO.getAnEnum() == SettlementDiscountOptionEnum.NTH_DISCOUNT) {
                nthActivityService.synActivityRelationRule(synMarketingDTO.getSynDiscounts());
            }
        }
    }
}
