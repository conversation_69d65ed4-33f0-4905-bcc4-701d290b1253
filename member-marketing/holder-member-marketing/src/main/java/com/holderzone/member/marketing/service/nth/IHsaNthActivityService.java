package com.holderzone.member.marketing.service.nth;

import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynDiscountDTO;
import com.holderzone.member.common.qo.nth.NthActivityQO;
import com.holderzone.member.common.vo.nth.NthActivityVO;
import com.holderzone.member.marketing.entity.nth.HsaNthActivity;
import com.holderzone.member.common.config.mybatis.IHolderBaseService;

import java.util.List;

/**
 * <p>
 * 第N份优惠活动 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface IHsaNthActivityService extends IHolderBaseService<HsaNthActivity> {

    List<HsaNthActivity> listRunningByOperSubjectGuid(String operSubjectGuid);

    PageResult<NthActivityVO> pageQuery(NthActivityQO qo);

    void synActivityRelationRule(List<SettlementSynDiscountDTO> synDiscounts);
}
