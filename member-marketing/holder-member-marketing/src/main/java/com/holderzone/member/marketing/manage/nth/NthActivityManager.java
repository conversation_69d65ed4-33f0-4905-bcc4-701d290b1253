package com.holderzone.member.marketing.manage.nth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryArrayShopBase;
import com.holderzone.member.common.dto.base.ResCommodityBase;
import com.holderzone.member.common.dto.nth.NthActivityItemDTO;
import com.holderzone.member.common.dto.nth.NthActivityReqDTO;
import com.holderzone.member.common.dto.nth.NthActivityStoreDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum;
import com.holderzone.member.common.enums.order.OrderStateEnum;
import com.holderzone.member.common.enums.specials.ActivityStateEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementDiscountDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.gift.ConsumptionGiftDetailsDTO;
import com.holderzone.member.common.qo.gift.RequestQueryMemberBaseQO;
import com.holderzone.member.common.qo.member.ResponseOperationMemberInfo;
import com.holderzone.member.common.qo.nth.NthActivityQO;
import com.holderzone.member.common.support.SettlementSupport;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.card.OperationMemberInfoVO;
import com.holderzone.member.common.vo.nth.NthActivityDetailsVO;
import com.holderzone.member.common.vo.nth.NthActivityStaticsVO;
import com.holderzone.member.common.vo.nth.NthActivityVO;
import com.holderzone.member.marketing.assembler.NthActivityAssembler;
import com.holderzone.member.marketing.entity.nth.HsaNthActivity;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityItem;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityRecord;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityStore;
import com.holderzone.member.marketing.entity.specials.HsaLimitSpecialsActivityRecord;
import com.holderzone.member.marketing.manage.nth.bo.NthActivityBO;
import com.holderzone.member.marketing.manage.voting.helper.ActivityStatusHelper;
import com.holderzone.member.marketing.service.cache.CacheService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityItemService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityRecordService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityStoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class NthActivityManager {

    /**
     * 第N份优惠活动锁
     */
    public static final String NTH_ACTIVITY_LOCK = "NTH_ACTIVITY_LOCK:";

    public static final String NOT_ACTIVITY = "活动不存在";

    public static final long WAIT_TIME = 5000;

    public static final long LEASE_TIME = 1000;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private final RedissonClient redissonClient;

    private final IHsaNthActivityService nthActivityService;

    private final IHsaNthActivityStoreService nthActivityStoreService;

    private final IHsaNthActivityItemService nthActivityItemService;

    private final IHsaNthActivityRecordService nthActivityRecordService;

    private final Executor marketingNthActivityThreadExecutor;

    private final ExternalSupport externalSupport;

    private final CacheService cacheService;

    private final SettlementSupport settlementSupport;

    public PageResult<NthActivityVO> pageQuery(NthActivityQO qo) {
        PageResult<NthActivityVO> pageResult = nthActivityService.pageQuery(qo);
        List<NthActivityVO> activityVOList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(activityVOList)) {
            return PageUtil.pageResult(new PageInfo<>(activityVOList));
        }
        List<String> activityGuidList = activityVOList.stream()
                .map(NthActivityVO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        // 活动商品
        Map<String, Long> activityItemMap = queryActivityItem(activityGuidList);
        // 活动订单数
        Map<String, Long> activityRecrodMap = queryActivityRecord(activityGuidList);
        for (NthActivityVO activityVO : activityVOList) {
            activityVO.setState(ActivityStatusHelper.handleLimitSpecialsActivityState(
                    activityVO.getState(), activityVO.getStartTime(), activityVO.getEndTime()));
            activityVO.setActivityItem(activityItemMap.getOrDefault(activityVO.getGuid(), 0L));
            activityVO.setActivityOrder(activityRecrodMap.getOrDefault(activityVO.getGuid(), 0L));
        }
        return pageResult;
    }

    /**
     * 查询指定运营主体下进行中的活动列表
     */
    public List<NthActivityDetailsVO> listByOperSubjectGuid(String operSubjectGuid) {
        List<HsaNthActivity> nthActivities = nthActivityService.listRunningByOperSubjectGuid(operSubjectGuid);
        if (CollectionUtils.isEmpty(nthActivities)) {
            return Lists.newArrayList();
        }
        List<NthActivityDetailsVO> detailsVOList = NthActivityAssembler.activityList2ActivityDetailsVOList(nthActivities);
        // 关联门店
        List<NthActivityStoreDTO> storeDTOList = queryBatchStoreInfoHandler(detailsVOList);
        Map<String, List<NthActivityStoreDTO>> storeMap = storeDTOList.stream()
                .collect(Collectors.groupingBy(NthActivityStoreDTO::getActivityGuid));
        // 活动商品
        List<NthActivityItemDTO> activityItemDTOList = queryBatchItemInfoHandler(detailsVOList);
        Map<String, List<NthActivityItemDTO>> itemMap = activityItemDTOList.stream()
                .collect(Collectors.groupingBy(NthActivityItemDTO::getActivityGuid));
        // 批量查询返回结果合并
        activityDetailsListHandler(detailsVOList, storeMap, itemMap);
        return detailsVOList;
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(NthActivityReqDTO reqDTO) {
        checkRequestParam(reqDTO);
        RLock lock = redissonClient.getLock(NTH_ACTIVITY_LOCK + ThreadLocalCache.getOperSubjectGuid());
        try {
            if (!lock.tryLock(WAIT_TIME, LEASE_TIME, TimeUnit.MILLISECONDS)) {
                throw new MemberMarketingException("网络繁忙，请稍后重试");
            }
            HsaNthActivity nthActivity = NthActivityAssembler.activityReqDTO2Activity(reqDTO);
            if (StringUtils.isEmpty(nthActivity.getGuid())) {
                String activityGuid = guidGeneratorUtil.getStringGuid(HsaNthActivity.class.getSimpleName());
                nthActivity.setGuid(activityGuid);
                nthActivity.setActivityCode(activityGuid.substring(activityGuid.length() - 6));
            } else {
                nthActivity.setState(ActivityStateEnum.NOT_PUBLISH.getCode());
            }
            nthActivityService.saveOrUpdate(nthActivity);
            // 门店关联
            saveActivityStore(reqDTO, nthActivity);
            // 商品关联
            saveActivityItem(reqDTO, nthActivity);
        } catch (Exception e) {
            log.error("[新增/编辑第N份优惠活动]失败,eMessage={},e={}", e.getMessage(), e);
            throw new MemberMarketingException(e.getMessage());
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private void saveActivityItem(NthActivityReqDTO reqDTO, HsaNthActivity nthActivity) {
        List<NthActivityItemDTO> itemDTOList = reqDTO.getItemDTOList();
        List<HsaNthActivityItem> itemDOList = Lists.newArrayList();
        List<String> guids = guidGeneratorUtil.getGuids(HsaNthActivityItem.class.getSimpleName(), itemDTOList.size());
        for (int i = 0, itemDTOListSize = itemDTOList.size(); i < itemDTOListSize; i++) {
            NthActivityItemDTO itemDTO = itemDTOList.get(i);
            HsaNthActivityItem item = new HsaNthActivityItem();
            item.setGuid(guids.get(i));
            item.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            item.setActivityGuid(nthActivity.getGuid());
            item.setCommodityId(itemDTO.getCommodityId());
            item.setCommodityCode(itemDTO.getCommodityCode());
            item.setCommodityType(itemDTO.getCommodityType());
            item.setCommodityName(itemDTO.getCommodityName());
            item.setChannel(itemDTO.getChannel());
            item.setSystem(itemDTO.getSystem());
            itemDOList.add(item);
        }
        nthActivityItemService.removeByActivityGuid(nthActivity.getGuid());
        nthActivityItemService.saveBatch(itemDOList);
    }

    private void saveActivityStore(NthActivityReqDTO reqDTO, HsaNthActivity nthActivity) {
        if (GoodsApplicableStoreEnum.ALL_STORE.getCode() == reqDTO.getIsAllStore() || CollectionUtils.isEmpty(reqDTO.getStoreDTOList())) {
            return;
        }
        List<NthActivityStoreDTO> storeDTOList = reqDTO.getStoreDTOList();
        List<HsaNthActivityStore> storeDOList = new ArrayList<>();
        List<String> guids = guidGeneratorUtil.getGuids(HsaNthActivityStore.class.getSimpleName(), storeDTOList.size());
        for (int i = 0; i < storeDTOList.size(); i++) {
            NthActivityStoreDTO storeDTO = storeDTOList.get(i);
            HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
            nthActivityStore.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            nthActivityStore.setActivityGuid(nthActivity.getGuid());
            nthActivityStore.setStoreGuid(storeDTO.getStoreGuid());
            nthActivityStore.setStoreName(storeDTO.getStoreName());
            nthActivityStore.setStoreCode(storeDTO.getStoreNumber());
            nthActivityStore.setGuid(guids.get(i));
            nthActivityStore.setSystem(storeDTO.getSystem());
            storeDOList.add(nthActivityStore);
        }
        nthActivityStoreService.removeByActivityGuid(nthActivity.getGuid());
        nthActivityStoreService.saveBatch(storeDOList);
    }

    /**
     * 查询活动商品
     */
    private Map<String, Long> queryActivityItem(List<String> activityGuidList) {
        List<HsaNthActivityItem> activityItemList = nthActivityItemService.listByActivityGuids(activityGuidList);
        return activityItemList.stream()
                .collect(Collectors.groupingBy(HsaNthActivityItem::getActivityGuid, Collectors.counting()));
    }


    /**
     * 查询活动订单数
     */
    private Map<String, Long> queryActivityRecord(List<String> activityGuidList) {
        List<NthActivityStaticsVO> staticsList = nthActivityRecordService.staticsOrderCountByActivityGuid(activityGuidList);
        return staticsList.stream()
                .collect(Collectors.toMap(NthActivityStaticsVO::getActivityGuid,
                        NthActivityStaticsVO::getOrderCount, (key1, key2) -> key1));
    }

    public NthActivityDetailsVO get(String guid) {
        HsaNthActivity activity = nthActivityService.queryByGuid(guid);
        if (ObjectUtils.isEmpty(activity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }

        NthActivityDetailsVO detailsVO = NthActivityAssembler.activity2ActivityDetailsVO(activity);
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        // 使子线程也能获取到 requestAttributes
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(requestAttributes, true);

        // 适用于部分门店查询门店关联
        CompletableFuture<List<NthActivityStoreDTO>> storeFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return queryStoreInfoHandler(detailsVO.getIsAllStore(), detailsVO.getGuid());
        }, marketingNthActivityThreadExecutor).exceptionally(throwable -> {
            log.error("查询适用门店失败", throwable);
            throw new MemberMarketingException("无法获取适用门店");
        });

        // 指定会员
        CompletableFuture<ConsumptionGiftDetailsDTO> memberFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            UserContextUtils.put(JacksonUtils.writeValueAsString(headerUserInfo));
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleMemberInfo(detailsVO.getMemberDTO());
        }, marketingNthActivityThreadExecutor).exceptionally(throwable -> {
            log.error("查询指定会员失败", throwable);
            throw new MemberMarketingException("无法获取指定会员");
        });

        // 活动商品
        CompletableFuture<List<NthActivityItemDTO>> itemFuture = CompletableFuture.supplyAsync(() -> {
            RequestContextHolder.setRequestAttributes(requestAttributes, true);
            ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
            UserContextUtils.put(JacksonUtils.writeValueAsString(headerUserInfo));
            return handleItemInfo(guid);
        }, marketingNthActivityThreadExecutor).exceptionally(throwable -> {
            log.error("查询活动商品失败", throwable);
            throw new MemberMarketingException("无法获取活动商品");
        });
        CompletableFuture<Void> all = CompletableFuture.allOf(storeFuture, memberFuture, itemFuture);
        try {
            all.get();
            detailsVO.setStoreDTOList(storeFuture.get());
            detailsVO.setMemberDTO(memberFuture.get());
            detailsVO.setItemDTOList(itemFuture.get());
        } catch (Exception e) {
            log.error("查询活动详情失败", e);
            throw new MemberMarketingException("系统繁忙稍后再试");
        }
        return detailsVO;
    }

    private ConsumptionGiftDetailsDTO handleMemberInfo(ConsumptionGiftDetailsDTO memberDTO) {
        if (!ObjectUtils.isEmpty(memberDTO)) {
            List<String> guidList = memberDTO.getGuidList();
            RequestQueryMemberBaseQO memberBaseQO = new RequestQueryMemberBaseQO();
            memberBaseQO.setMemberGuidList(guidList);
            memberBaseQO.setPageSize(guidList.size());
            memberBaseQO.setCurrentPage(1);
            Page<ResponseOperationMemberInfo> memberInfoPage =
                    externalSupport.memberServer(ThreadLocalCache.getSystem()).getOperationMemberInfoPage(memberBaseQO);
            List<ResponseOperationMemberInfo> memberInfoList = memberInfoPage.getData();
            if (CollUtil.isNotEmpty(memberInfoList)) {
                List<OperationMemberInfoVO> memberInfoGuidList = getMemberInfoVOList(memberInfoList);
                memberDTO.setMemberInfoGuidList(memberInfoGuidList);
            }
        }
        return memberDTO;
    }

    private List<OperationMemberInfoVO> getMemberInfoVOList(List<ResponseOperationMemberInfo> memberInfoList) {
        List<OperationMemberInfoVO> memberInfoGuidList = Lists.newArrayList();
        for (ResponseOperationMemberInfo memberInfo : memberInfoList) {
            OperationMemberInfoVO operationMemberInfoVO = new OperationMemberInfoVO();
            operationMemberInfoVO.setMemberAccount(memberInfo.getMemberAccount());
            operationMemberInfoVO.setPhoneNum(memberInfo.getPhoneNum());
            operationMemberInfoVO.setUserName(memberInfo.getUserName());
            operationMemberInfoVO.setGuid(memberInfo.getGuid());
            memberInfoGuidList.add(operationMemberInfoVO);
        }
        return memberInfoGuidList;
    }

    private List<NthActivityItemDTO> handleItemInfo(String guid) {
        List<HsaNthActivityItem> nthActivityItems = nthActivityItemService.listByActivityGuid(guid);
        if (CollUtil.isEmpty(nthActivityItems)) {
            return Collections.emptyList();
        }

        // 按 system 分组，分别查详情并合并
        Map<String, List<HsaNthActivityItem>> systemMap = nthActivityItems.stream()
                .filter(c -> c.getSystem() != null)
                .collect(Collectors.groupingBy(HsaNthActivityItem::getSystem));

        List<ResCommodityBase> allCommodityDetails = systemMap.entrySet().stream()
                .flatMap(entry -> {
                    String systemName = entry.getKey();
                    List<String> ids = entry.getValue().stream()
                            .map(HsaNthActivityItem::getCommodityId)
                            .distinct()
                            .collect(Collectors.toList());
                    List<ResCommodityBase> details = externalSupport
                            .itemServer(SystemEnum.getSystemCodeBySystemName(systemName))
                            .listCommodityByDetail(new QueryArrayShopBase().setCommodityIdList(ids));
                    return CollUtil.isEmpty(details) ? java.util.stream.Stream.empty() : details.stream();
                })
                .collect(Collectors.toList());

        // 组装返回结果
        return nthActivityItems.stream()
                .map(nthActivityItem -> getResponseCouponCommodityVO(nthActivityItem, allCommodityDetails))
                .collect(Collectors.toList());
    }

    private NthActivityItemDTO getResponseCouponCommodityVO(HsaNthActivityItem hsaNthActivityItem, List<ResCommodityBase> allCommodityDetails) {
        NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        if (CollUtil.isEmpty(allCommodityDetails)) {
            buildRespCommodityByNull(hsaNthActivityItem, nthActivityItemDTO);
        } else {
            Map<String, ResCommodityBase> commodityBaseMap = allCommodityDetails.stream()
                    .collect(Collectors.toMap(ResCommodityBase::getCommodityId, resCommodityBase -> resCommodityBase, (k1, k2) -> k1));
            ResCommodityBase commodityBase = commodityBaseMap.get(hsaNthActivityItem.getCommodityId());
            if (commodityBase == null) {
                buildRespCommodityByNull(hsaNthActivityItem, nthActivityItemDTO);
            } else {
                buildRespCommodityByBase(hsaNthActivityItem, nthActivityItemDTO, commodityBase);
            }
        }
        return nthActivityItemDTO;
    }

    private void buildRespCommodityByNull(HsaNthActivityItem hsaNthActivityItem,
                                          NthActivityItemDTO nthActivityItemDTO) {
        nthActivityItemDTO.setActivityGuid(hsaNthActivityItem.getActivityGuid());
        nthActivityItemDTO.setCommodityCode(hsaNthActivityItem.getCommodityCode());
        nthActivityItemDTO.setCommodityId(hsaNthActivityItem.getCommodityId());
        nthActivityItemDTO.setCommodityName(hsaNthActivityItem.getCommodityName());
        nthActivityItemDTO.setCommodityType(hsaNthActivityItem.getCommodityType());
        nthActivityItemDTO.setChannel(hsaNthActivityItem.getChannel());
        nthActivityItemDTO.setIsExist(BooleanEnum.FALSE.getCode());
        nthActivityItemDTO.setSystem(hsaNthActivityItem.getSystem());
    }

    private void buildRespCommodityByBase(HsaNthActivityItem hsaNthActivityItem,
                                          NthActivityItemDTO nthActivityItemDTO,
                                          ResCommodityBase commodityBase) {
        nthActivityItemDTO.setActivityGuid(hsaNthActivityItem.getActivityGuid());
        nthActivityItemDTO.setCommodityCode(commodityBase.getCommodityCode());
        nthActivityItemDTO.setCommodityId(hsaNthActivityItem.getCommodityId());
        nthActivityItemDTO.setCommodityName(commodityBase.getName());
        nthActivityItemDTO.setCommodityType(hsaNthActivityItem.getCommodityType());
        nthActivityItemDTO.setChannel(hsaNthActivityItem.getChannel());
        nthActivityItemDTO.setIsExist(BooleanEnum.TRUE.getCode());
        nthActivityItemDTO.setSystem(hsaNthActivityItem.getSystem());
    }

    /**
     * 查询单个活动门店
     */
    private List<NthActivityStoreDTO> queryStoreInfoHandler(Integer isAllStore, String activityGuid) {
        List<NthActivityStoreDTO> storeDTOList = Lists.newArrayList();
        if (GoodsApplicableStoreEnum.PORTION_STORE.getCode() == isAllStore) {
            List<HsaNthActivityStore> storeList = nthActivityStoreService.listByActivityGuid(activityGuid);
            storeList.forEach(store -> {
                NthActivityStoreDTO storeDTO = new NthActivityStoreDTO();
                storeDTO.setStoreGuid(store.getStoreGuid());
                storeDTO.setStoreNumber(store.getStoreCode());
                storeDTO.setStoreName(store.getStoreName());
                storeDTO.setSystem(store.getSystem());
                storeDTOList.add(storeDTO);
            });
            return storeDTOList;
        }
        return storeDTOList;
    }

    /**
     * 批量查询活动门店
     */
    private List<NthActivityStoreDTO> queryBatchStoreInfoHandler(List<NthActivityDetailsVO> detailsVOList) {
        List<NthActivityDetailsVO> portionStoreActivityDetails = detailsVOList.stream()
                .filter(e -> GoodsApplicableStoreEnum.PORTION_STORE.getCode() == e.getIsAllStore())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(portionStoreActivityDetails)) {
            return Lists.newArrayList();
        }
        List<String> portionStoreActivityGuids = portionStoreActivityDetails.stream()
                .map(NthActivityDetailsVO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaNthActivityStore> nthActivityStores = nthActivityStoreService.listByActivityGuids(portionStoreActivityGuids);
        return nthActivityStores.stream().map(store -> {
            NthActivityStoreDTO storeDTO = new NthActivityStoreDTO();
            storeDTO.setActivityGuid(store.getActivityGuid());
            storeDTO.setStoreGuid(store.getStoreGuid());
            storeDTO.setStoreNumber(store.getStoreCode());
            storeDTO.setStoreName(store.getStoreName());
            return storeDTO;
        }).collect(Collectors.toList());
    }


    /**
     * 批量查询适用商品
     */
    private List<NthActivityItemDTO> queryBatchItemInfoHandler(List<NthActivityDetailsVO> detailsVOList) {
        List<String> activityGuidList = detailsVOList.stream()
                .map(NthActivityDetailsVO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<HsaNthActivityItem> activityItemList = nthActivityItemService.listByActivityGuids(activityGuidList);
        if (CollectionUtils.isEmpty(activityItemList)) {
            return Lists.newArrayList();
        }
        return NthActivityAssembler.activityItemList2ActivityitemDTOList(activityItemList);
    }

    /**
     * 批量查询返回结果合并
     */
    private void activityDetailsListHandler(List<NthActivityDetailsVO> detailsVOList,
                                            Map<String, List<NthActivityStoreDTO>> storeMap,
                                            Map<String, List<NthActivityItemDTO>> itemMap) {
        detailsVOList.forEach(detailsVO -> {
            // 适用门店
            List<NthActivityStoreDTO> storeList = storeMap.getOrDefault(detailsVO.getGuid(), Lists.newArrayList());
            detailsVO.setStoreDTOList(storeList);
            // 活动商品
            List<NthActivityItemDTO> itemDTOList = itemMap.getOrDefault(detailsVO.getGuid(), Lists.newArrayList());
            detailsVO.setItemDTOList(itemDTOList);
        });
    }


    public void delete(String guid) {
        HsaNthActivity nthActivity = nthActivityService.queryByGuid(guid);
        if (ObjectUtils.isEmpty(nthActivity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }
        if (Objects.equals(BooleanEnum.TRUE.getCode(), nthActivity.getIsPublish())) {
            throw new MemberMarketingException("已发布过的活动不可删除");
        }
        nthActivityService.removeByGuid(guid);

        // 同步结算台（删除）
        try {
            Integer isFirstAdd = nthActivity.getIsPublish() == BooleanEnum.TRUE.getCode() ?
                    BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode();
            sendSettlement(nthActivity, isFirstAdd, Boolean.TRUE);
        } catch (Exception e) {
            log.error("[第N份优惠] 同步结算台失败, guid={}, e=", guid, e);
            throw new MemberMarketingException("同步结算台失败，请稍后重试");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateState(String guid, Integer state) {
        HsaNthActivity nthActivity = nthActivityService.queryByGuid(guid);
        if (ObjectUtils.isEmpty(nthActivity)) {
            throw new MemberMarketingException(NOT_ACTIVITY);
        }
        if (state == ActivityStateEnum.RUNNING.getCode()) {
            Integer isFirstAdd = nthActivity.getIsPublish() == BooleanEnum.TRUE.getCode() ?
                    BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode();
            nthActivity.setState(state);
            if (nthActivity.getIsPublish() == BooleanEnum.FALSE.getCode()) {
                nthActivity.setIsPublish(BooleanEnum.TRUE.getCode());
            }
            nthActivityService.updateByGuid(nthActivity);
            // 加入缓存
            NthActivityDetailsVO details = get(guid);
            cacheService.saveNthActivityCacheByOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid(), Lists.newArrayList(details));

            // 同步结算台（发布/更新）
            try {
                sendSettlement(nthActivity, isFirstAdd, Boolean.FALSE);
            } catch (Exception e) {
                log.error("[第N份优惠] 同步结算台失败, guid={}, e=", guid, e);
                throw new MemberMarketingException("同步结算台失败，请稍后重试");
            }
        } else if (state == ActivityStateEnum.STOP.getCode()) {
            // 暂停
            nthActivity.setState(state);
            nthActivityService.updateByGuid(nthActivity);
            // 清除缓存
            cacheService.deleteNthActivityCache(guid);
        } else {
            throw new MemberMarketingException("状态类型错误");
        }
    }

    public Boolean checkActivityState(String guid, Integer state) {
        HsaNthActivity nthActivity = nthActivityService.queryByGuid(guid);
        if (ObjectUtils.isEmpty(nthActivity)) {
            return Boolean.TRUE;
        }
        Integer activityState = ActivityStatusHelper.handleLimitSpecialsActivityState(
                nthActivity.getState(), nthActivity.getStartTime(), nthActivity.getEndTime());
        return !Objects.equals(activityState, state);
    }


    /**
     * 下单
     */
    public void pay(NthActivityBO biz) {
        // 查询活动
        HsaNthActivity nthActivity = nthActivityService.queryByGuid(biz.getNthActivityRecord().getGuid());
        if (Objects.isNull(nthActivity)) {
            log.error("活动查询不存在, guid:{}", biz.getNthActivityRecord().getGuid());
            return;
        }
        HsaNthActivityRecord nthActivityRecord = biz.getNthActivityRecord();
        nthActivityRecord.setGuid(guidGeneratorUtil.getStringGuid(HsaLimitSpecialsActivityRecord.class.getSimpleName()));
        nthActivityRecord.setName(nthActivity.getName());
        nthActivityRecord.setOrderState(OrderStateEnum.FINISH.getCode());
        nthActivityRecord.setActivityCode(nthActivity.getActivityCode());
        nthActivityRecord.setOperSubjectGuid(nthActivity.getOperSubjectGuid());
        // save
        nthActivityRecordService.saveRecord(nthActivityRecord);
    }


    /**
     * 退款
     */
    public void refund(NthActivityBO biz) {
        nthActivityRecordService.updateRefundStateByOrderGuid(biz.getNthActivityRecord().getOrderGuid());
    }

    /**
     * 推送第N份优惠到结算台
     */
    private void sendSettlement(HsaNthActivity nthActivity, Integer isFirstAdd, Boolean isDelete) {
        SettlementDiscountDTO discountDTO = new SettlementDiscountDTO();
        discountDTO.setOperSubjectGuid(nthActivity.getOperSubjectGuid());
        final SettlementDiscountOptionEnum anEnum = SettlementDiscountOptionEnum.NTH_DISCOUNT;
        discountDTO.setOption(anEnum.getCode());
        if (Boolean.TRUE.equals(isDelete)) {
            discountDTO.setDelDiscountGuids(Collections.singletonList(nthActivity.getActivityCode()));
        } else {
            SettlementDiscountDTO.Discount discount = new SettlementDiscountDTO.Discount()
                    .setDiscountType(anEnum.getType().getCode())
                    .setDiscountNum(anEnum.getLimitNum())
                    .setDiscountGuid(nthActivity.getActivityCode())
                    .setRelationRule(nthActivity.getRelationRule())
                    .setDiscountName(nthActivity.getName())
                    .setIsFirstAdd(isFirstAdd);

            discount.setDiscountDynamic(buildNthDiscountDynamic(nthActivity.getDiscountRule()));
            discountDTO.setDiscountList(Collections.singletonList(discount));
        }
        settlementSupport.syncSettlementDiscount(discountDTO);
    }

    /**
     * 生成第N份优惠的动态描述
     * discountRule 形如：perCount,perDiscount
     */
    private String buildNthDiscountDynamic(String discountRule) {
        if (StrUtil.isEmpty(discountRule)) {
            return "";
        }
        try {
            String[] parts = discountRule.split(",");
            String perCount = parts.length > 0 ? parts[0] : "";
            String perDiscount = parts.length > 1 ? parts[1] : "";
            return "每买第" + perCount + "份享" + perDiscount + "折";
        } catch (Exception e) {
            log.error("解析第N份优惠 discountRule 失败, discountRule={}", discountRule, e);
            return discountRule;
        }
    }

    private void checkRequestParam(NthActivityReqDTO reqDTO) {
        // 当前时间 ＜结束时间
        if (!LocalDateTime.now().isBefore(reqDTO.getEndTime())) {
            throw new MemberMarketingException("活动结束时间已到，请重新设置");
        }
        if (GoodsApplicableStoreEnum.PORTION_STORE.getCode() == reqDTO.getIsAllStore() && CollectionUtils.isEmpty(reqDTO.getStoreDTOList())) {
            throw new MemberMarketingException("请选择活动门店");
        }
        List<NthActivityItemDTO> itemDTOList = reqDTO.getItemDTOList();
        if (CollectionUtils.isEmpty(itemDTOList)) {
            throw new MemberMarketingException("请选择活动商品");
        }
        if (!StringUtils.isEmpty(reqDTO.getGuid())) {
            Boolean checkActivityState = checkActivityState(reqDTO.getGuid(), reqDTO.getState());
            if (Boolean.TRUE.equals(checkActivityState)) {
                throw new MemberMarketingException("操作失败，活动状态已变更，请重试");
            }
        }
    }
}
