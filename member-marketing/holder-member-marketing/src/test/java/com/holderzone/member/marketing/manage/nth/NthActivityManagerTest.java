package com.holderzone.member.marketing.manage.nth;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.dto.nth.NthActivityItemDTO;
import com.holderzone.member.common.dto.nth.NthActivityReqDTO;
import com.holderzone.member.common.dto.nth.NthActivityStoreDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.nth.NthActivityQO;
import com.holderzone.member.common.support.SettlementSupport;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.nth.NthActivityDetailsVO;
import com.holderzone.member.common.vo.nth.NthActivityStaticsVO;
import com.holderzone.member.common.vo.nth.NthActivityVO;
import com.holderzone.member.marketing.entity.nth.HsaNthActivity;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityItem;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityRecord;
import com.holderzone.member.marketing.entity.nth.HsaNthActivityStore;
import com.holderzone.member.marketing.manage.nth.bo.NthActivityBO;
import com.holderzone.member.marketing.service.cache.CacheService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityItemService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityRecordService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityService;
import com.holderzone.member.marketing.service.nth.IHsaNthActivityStoreService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class NthActivityManagerTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private IHsaNthActivityService mockNthActivityService;
    @Mock
    private IHsaNthActivityStoreService mockNthActivityStoreService;
    @Mock
    private IHsaNthActivityItemService mockNthActivityItemService;
    @Mock
    private IHsaNthActivityRecordService mockNthActivityRecordService;
    @Mock
    private ExternalSupport mockExternalSupport;
    @Mock
    private CacheService mockCacheService;
    @Mock
    private SettlementSupport settlementSupport;

    private NthActivityManager nthActivityManagerUnderTest;

    @Before
    public void setUp() throws Exception {
        nthActivityManagerUnderTest = new NthActivityManager(mockGuidGeneratorUtil, mockRedissonClient,
                mockNthActivityService, mockNthActivityStoreService, mockNthActivityItemService,
                mockNthActivityRecordService, MoreExecutors.directExecutor(), mockExternalSupport, mockCacheService, settlementSupport);
    }

    @Test
    public void testPageQuery() throws Exception {
        // Setup
        final NthActivityQO qo = new NthActivityQO();
        qo.setOperSubjectGuid("operSubjectGuid");
        qo.setKeywords("keywords");
        qo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo.setBusiness(0);

        final PageResult<NthActivityVO> expectedResult = new PageResult<>(0, 0, 0);

        // Configure IHsaNthActivityService.pageQuery(...).
        final NthActivityQO qo1 = new NthActivityQO();
        qo1.setOperSubjectGuid("operSubjectGuid");
        qo1.setKeywords("keywords");
        qo1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo1.setBusiness(0);
        when(mockNthActivityService.pageQuery(qo1)).thenReturn(new PageResult<>(0, 0, 0));

        // Configure IHsaNthActivityItemService.listByActivityGuids(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuids(Arrays.asList("value"))).thenReturn(hsaNthActivityItems);

        // Configure IHsaNthActivityRecordService.staticsOrderCountByActivityGuid(...).
        final NthActivityStaticsVO nthActivityStaticsVO = new NthActivityStaticsVO();
        nthActivityStaticsVO.setActivityGuid("activityGuid");
        nthActivityStaticsVO.setOrderCount(0L);
        nthActivityStaticsVO.setOrderFee(new BigDecimal("0.00"));
        nthActivityStaticsVO.setOrderDiscountFee(new BigDecimal("0.00"));
        nthActivityStaticsVO.setOrderActuallyFee(new BigDecimal("0.00"));
        final List<NthActivityStaticsVO> nthActivityStaticsVOS = Arrays.asList(nthActivityStaticsVO);
        when(mockNthActivityRecordService.staticsOrderCountByActivityGuid(Arrays.asList("value")))
                .thenReturn(nthActivityStaticsVOS);

        // Run the test
        final PageResult<NthActivityVO> result = nthActivityManagerUnderTest.pageQuery(qo);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPageQuery_IHsaNthActivityItemServiceReturnsNoItems() {
        // Setup
        final NthActivityQO qo = new NthActivityQO();
        qo.setOperSubjectGuid("operSubjectGuid");
        qo.setKeywords("keywords");
        qo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo.setBusiness(0);

        final PageResult<NthActivityVO> expectedResult = new PageResult<>(0, 0, 0);

        // Configure IHsaNthActivityService.pageQuery(...).
        final NthActivityQO qo1 = new NthActivityQO();
        qo1.setOperSubjectGuid("operSubjectGuid");
        qo1.setKeywords("keywords");
        qo1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo1.setBusiness(0);
        when(mockNthActivityService.pageQuery(qo1)).thenReturn(new PageResult<>(0, 0, 0));

        when(mockNthActivityItemService.listByActivityGuids(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure IHsaNthActivityRecordService.staticsOrderCountByActivityGuid(...).
        final NthActivityStaticsVO nthActivityStaticsVO = new NthActivityStaticsVO();
        nthActivityStaticsVO.setActivityGuid("activityGuid");
        nthActivityStaticsVO.setOrderCount(0L);
        nthActivityStaticsVO.setOrderFee(new BigDecimal("0.00"));
        nthActivityStaticsVO.setOrderDiscountFee(new BigDecimal("0.00"));
        nthActivityStaticsVO.setOrderActuallyFee(new BigDecimal("0.00"));
        final List<NthActivityStaticsVO> nthActivityStaticsVOS = Arrays.asList(nthActivityStaticsVO);
        when(mockNthActivityRecordService.staticsOrderCountByActivityGuid(Arrays.asList("value")))
                .thenReturn(nthActivityStaticsVOS);

        // Run the test
        final PageResult<NthActivityVO> result = nthActivityManagerUnderTest.pageQuery(qo);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPageQuery_IHsaNthActivityRecordServiceReturnsNoItems() {
        // Setup
        final NthActivityQO qo = new NthActivityQO();
        qo.setOperSubjectGuid("operSubjectGuid");
        qo.setKeywords("keywords");
        qo.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo.setBusiness(0);

        final PageResult<NthActivityVO> expectedResult = new PageResult<>(0, 0, 0);

        // Configure IHsaNthActivityService.pageQuery(...).
        final NthActivityQO qo1 = new NthActivityQO();
        qo1.setOperSubjectGuid("operSubjectGuid");
        qo1.setKeywords("keywords");
        qo1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        qo1.setBusiness(0);
        when(mockNthActivityService.pageQuery(qo1)).thenReturn(new PageResult<>(0, 0, 0));

        // Configure IHsaNthActivityItemService.listByActivityGuids(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuids(Arrays.asList("value"))).thenReturn(hsaNthActivityItems);

        when(mockNthActivityRecordService.staticsOrderCountByActivityGuid(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult<NthActivityVO> result = nthActivityManagerUnderTest.pageQuery(qo);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListByOperSubjectGuid() {
        // Setup
        final NthActivityDetailsVO nthActivityDetailsVO = new NthActivityDetailsVO();
        nthActivityDetailsVO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityDetailsVO.setName("name");
        nthActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setIsLimitPeriod(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedType(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        nthActivityDetailsVO.setPerCount(0);
        nthActivityDetailsVO.setPerDiscount(new BigDecimal("0.00"));
        nthActivityDetailsVO.setRelationRule(0);
        nthActivityDetailsVO.setApplyBusiness(0);
        nthActivityDetailsVO.setApplyBusinessList(Arrays.asList("value"));
        nthActivityDetailsVO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        nthActivityDetailsVO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        nthActivityDetailsVO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        nthActivityDetailsVO.setItemDTOList(Arrays.asList(nthActivityItemDTO));
        final List<NthActivityDetailsVO> expectedResult = Arrays.asList(nthActivityDetailsVO);

        // Configure IHsaNthActivityService.listRunningByOperSubjectGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        final List<HsaNthActivity> hsaNthActivities = Arrays.asList(nthActivity);
        when(mockNthActivityService.listRunningByOperSubjectGuid("operSubjectGuid")).thenReturn(hsaNthActivities);

        // Configure IHsaNthActivityStoreService.listByActivityGuids(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> hsaNthActivityStores = Arrays.asList(nthActivityStore);
        when(mockNthActivityStoreService.listByActivityGuids(Arrays.asList("value"))).thenReturn(hsaNthActivityStores);

        // Configure IHsaNthActivityItemService.listByActivityGuids(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuids(Arrays.asList("value"))).thenReturn(hsaNthActivityItems);

        // Run the test
        final List<NthActivityDetailsVO> result = nthActivityManagerUnderTest.listByOperSubjectGuid("operSubjectGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListByOperSubjectGuid_IHsaNthActivityServiceReturnsNoItems() {
        // Setup
        when(mockNthActivityService.listRunningByOperSubjectGuid("operSubjectGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<NthActivityDetailsVO> result = nthActivityManagerUnderTest.listByOperSubjectGuid("operSubjectGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testListByOperSubjectGuid_IHsaNthActivityStoreServiceReturnsNoItems() {
        // Setup
        final NthActivityDetailsVO nthActivityDetailsVO = new NthActivityDetailsVO();
        nthActivityDetailsVO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityDetailsVO.setName("name");
        nthActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setIsLimitPeriod(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedType(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        nthActivityDetailsVO.setPerCount(0);
        nthActivityDetailsVO.setPerDiscount(new BigDecimal("0.00"));
        nthActivityDetailsVO.setRelationRule(0);
        nthActivityDetailsVO.setApplyBusiness(0);
        nthActivityDetailsVO.setApplyBusinessList(Arrays.asList("value"));
        nthActivityDetailsVO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        nthActivityDetailsVO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        nthActivityDetailsVO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        nthActivityDetailsVO.setItemDTOList(Arrays.asList(nthActivityItemDTO));
        final List<NthActivityDetailsVO> expectedResult = Arrays.asList(nthActivityDetailsVO);

        // Configure IHsaNthActivityService.listRunningByOperSubjectGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        final List<HsaNthActivity> hsaNthActivities = Arrays.asList(nthActivity);
        when(mockNthActivityService.listRunningByOperSubjectGuid("operSubjectGuid")).thenReturn(hsaNthActivities);

        when(mockNthActivityStoreService.listByActivityGuids(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure IHsaNthActivityItemService.listByActivityGuids(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuids(Arrays.asList("value"))).thenReturn(hsaNthActivityItems);

        // Run the test
        final List<NthActivityDetailsVO> result = nthActivityManagerUnderTest.listByOperSubjectGuid("operSubjectGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListByOperSubjectGuid_IHsaNthActivityItemServiceReturnsNoItems() {
        // Setup
        final NthActivityDetailsVO nthActivityDetailsVO = new NthActivityDetailsVO();
        nthActivityDetailsVO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityDetailsVO.setName("name");
        nthActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setIsLimitPeriod(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedType(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        nthActivityDetailsVO.setPerCount(0);
        nthActivityDetailsVO.setPerDiscount(new BigDecimal("0.00"));
        nthActivityDetailsVO.setRelationRule(0);
        nthActivityDetailsVO.setApplyBusiness(0);
        nthActivityDetailsVO.setApplyBusinessList(Arrays.asList("value"));
        nthActivityDetailsVO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        nthActivityDetailsVO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        nthActivityDetailsVO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        nthActivityDetailsVO.setItemDTOList(Arrays.asList(nthActivityItemDTO));
        final List<NthActivityDetailsVO> expectedResult = Arrays.asList(nthActivityDetailsVO);

        // Configure IHsaNthActivityService.listRunningByOperSubjectGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        final List<HsaNthActivity> hsaNthActivities = Arrays.asList(nthActivity);
        when(mockNthActivityService.listRunningByOperSubjectGuid("operSubjectGuid")).thenReturn(hsaNthActivities);

        // Configure IHsaNthActivityStoreService.listByActivityGuids(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> hsaNthActivityStores = Arrays.asList(nthActivityStore);
        when(mockNthActivityStoreService.listByActivityGuids(Arrays.asList("value"))).thenReturn(hsaNthActivityStores);

        when(mockNthActivityItemService.listByActivityGuids(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<NthActivityDetailsVO> result = nthActivityManagerUnderTest.listByOperSubjectGuid("operSubjectGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveOrUpdate() {
        // Setup
        final NthActivityReqDTO reqDTO = new NthActivityReqDTO();
        reqDTO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        reqDTO.setState(0);
        reqDTO.setName("name");
        reqDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsLimitPeriod(0);
        reqDTO.setEquitiesTimeLimitedType(0);
        reqDTO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        reqDTO.setPerCount(0);
        reqDTO.setPerDiscount(new BigDecimal("0.00"));
        reqDTO.setRelationRule(0);
        reqDTO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        reqDTO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        reqDTO.setApplyBusiness(0);
        reqDTO.setApplyBusinessList(Arrays.asList("value"));
        reqDTO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        reqDTO.setItemDTOList(Arrays.asList(nthActivityItemDTO));

        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198")).thenReturn(nthActivity);

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        when(mockGuidGeneratorUtil.getGuids("tab", 0)).thenReturn(Arrays.asList("value"));

        // Run the test
        nthActivityManagerUnderTest.saveOrUpdate(reqDTO);

        // Verify the results
        // Confirm IHsaNthActivityService.saveOrUpdate(...).
        final HsaNthActivity t = new HsaNthActivity();
        t.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setActivityCode("activityCode");
        t.setName("name");
        t.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setState(0);
        t.setDiscountRule("discountRule");
        t.setIsLimitPeriod(0);
        t.setLimitPeriodType(0);
        t.setLimitPeriodJson("equitiesTimeLimitedJson");
        t.setRelationRule(0);
        t.setApplyBusiness(0);
        t.setApplyBusinessJson("applyBusinessJson");
        t.setIsAllStore(0);
        t.setGroupType(0);
        t.setIsPublish(0);
        verify(mockNthActivityService).saveOrUpdate(t);
        verify(mockNthActivityStoreService).removeByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");

        // Confirm IHsaNthActivityStoreService.saveBatch(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> entityList = Arrays.asList(nthActivityStore);
        verify(mockNthActivityStoreService).saveBatch(entityList);
        verify(mockNthActivityItemService).removeByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");

        // Confirm IHsaNthActivityItemService.saveBatch(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> entityList1 = Arrays.asList(activityItem);
        verify(mockNthActivityItemService).saveBatch(entityList1);
    }

    @Test
    public void testSaveOrUpdate_GuidGeneratorUtilGetGuidsReturnsNoItems() {
        // Setup
        final NthActivityReqDTO reqDTO = new NthActivityReqDTO();
        reqDTO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        reqDTO.setState(0);
        reqDTO.setName("name");
        reqDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setIsLimitPeriod(0);
        reqDTO.setEquitiesTimeLimitedType(0);
        reqDTO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        reqDTO.setPerCount(0);
        reqDTO.setPerDiscount(new BigDecimal("0.00"));
        reqDTO.setRelationRule(0);
        reqDTO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        reqDTO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        reqDTO.setApplyBusiness(0);
        reqDTO.setApplyBusinessList(Arrays.asList("value"));
        reqDTO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        reqDTO.setItemDTOList(Arrays.asList(nthActivityItemDTO));

        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198")).thenReturn(nthActivity);

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        when(mockGuidGeneratorUtil.getGuids("tab", 0)).thenReturn(Collections.emptyList());

        // Run the test
        nthActivityManagerUnderTest.saveOrUpdate(reqDTO);

        // Verify the results
        // Confirm IHsaNthActivityService.saveOrUpdate(...).
        final HsaNthActivity t = new HsaNthActivity();
        t.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setActivityCode("activityCode");
        t.setName("name");
        t.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setState(0);
        t.setDiscountRule("discountRule");
        t.setIsLimitPeriod(0);
        t.setLimitPeriodType(0);
        t.setLimitPeriodJson("equitiesTimeLimitedJson");
        t.setRelationRule(0);
        t.setApplyBusiness(0);
        t.setApplyBusinessJson("applyBusinessJson");
        t.setIsAllStore(0);
        t.setGroupType(0);
        t.setIsPublish(0);
        verify(mockNthActivityService).saveOrUpdate(t);
        verify(mockNthActivityStoreService).removeByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");

        // Confirm IHsaNthActivityStoreService.saveBatch(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> entityList = Arrays.asList(nthActivityStore);
        verify(mockNthActivityStoreService).saveBatch(entityList);
        verify(mockNthActivityItemService).removeByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");

        // Confirm IHsaNthActivityItemService.saveBatch(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> entityList1 = Arrays.asList(activityItem);
        verify(mockNthActivityItemService).saveBatch(entityList1);
    }

    @Test
    public void testGet() {
        // Setup
        final NthActivityDetailsVO expectedResult = new NthActivityDetailsVO();
        expectedResult.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        expectedResult.setName("name");
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setIsLimitPeriod(0);
        expectedResult.setEquitiesTimeLimitedType(0);
        expectedResult.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        expectedResult.setPerCount(0);
        expectedResult.setPerDiscount(new BigDecimal("0.00"));
        expectedResult.setRelationRule(0);
        expectedResult.setApplyBusiness(0);
        expectedResult.setApplyBusinessList(Arrays.asList("value"));
        expectedResult.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        expectedResult.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        expectedResult.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        expectedResult.setItemDTOList(Arrays.asList(nthActivityItemDTO));

        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93")).thenReturn(nthActivity);

        // Configure IHsaNthActivityStoreService.listByActivityGuid(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> hsaNthActivityStores = Arrays.asList(nthActivityStore);
        when(mockNthActivityStoreService.listByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198"))
                .thenReturn(hsaNthActivityStores);

        // Configure IHsaNthActivityItemService.listByActivityGuid(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93"))
                .thenReturn(hsaNthActivityItems);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        final NthActivityDetailsVO result = nthActivityManagerUnderTest.get("d422bbaf-7d99-4fe5-8b9c-1773655b9d93");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGet_IHsaNthActivityStoreServiceReturnsNoItems() {
        // Setup
        final NthActivityDetailsVO expectedResult = new NthActivityDetailsVO();
        expectedResult.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        expectedResult.setName("name");
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setIsLimitPeriod(0);
        expectedResult.setEquitiesTimeLimitedType(0);
        expectedResult.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        expectedResult.setPerCount(0);
        expectedResult.setPerDiscount(new BigDecimal("0.00"));
        expectedResult.setRelationRule(0);
        expectedResult.setApplyBusiness(0);
        expectedResult.setApplyBusinessList(Arrays.asList("value"));
        expectedResult.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        expectedResult.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        expectedResult.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        expectedResult.setItemDTOList(Arrays.asList(nthActivityItemDTO));

        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93")).thenReturn(nthActivity);

        when(mockNthActivityStoreService.listByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198"))
                .thenReturn(Collections.emptyList());

        // Configure IHsaNthActivityItemService.listByActivityGuid(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93"))
                .thenReturn(hsaNthActivityItems);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        final NthActivityDetailsVO result = nthActivityManagerUnderTest.get("d422bbaf-7d99-4fe5-8b9c-1773655b9d93");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGet_IHsaNthActivityItemServiceReturnsNoItems() {
        // Setup
        final NthActivityDetailsVO expectedResult = new NthActivityDetailsVO();
        expectedResult.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        expectedResult.setName("name");
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setIsLimitPeriod(0);
        expectedResult.setEquitiesTimeLimitedType(0);
        expectedResult.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        expectedResult.setPerCount(0);
        expectedResult.setPerDiscount(new BigDecimal("0.00"));
        expectedResult.setRelationRule(0);
        expectedResult.setApplyBusiness(0);
        expectedResult.setApplyBusinessList(Arrays.asList("value"));
        expectedResult.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        expectedResult.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        expectedResult.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        expectedResult.setItemDTOList(Arrays.asList(nthActivityItemDTO));

        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93")).thenReturn(nthActivity);

        // Configure IHsaNthActivityStoreService.listByActivityGuid(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> hsaNthActivityStores = Arrays.asList(nthActivityStore);
        when(mockNthActivityStoreService.listByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198"))
                .thenReturn(hsaNthActivityStores);

        when(mockNthActivityItemService.listByActivityGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final NthActivityDetailsVO result = nthActivityManagerUnderTest.get("d422bbaf-7d99-4fe5-8b9c-1773655b9d93");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDelete() throws Exception {
        // Setup
        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("85c531b8-f064-433b-b81f-fa5f7b520e2e")).thenReturn(nthActivity);

        // Run the test
        nthActivityManagerUnderTest.delete("85c531b8-f064-433b-b81f-fa5f7b520e2e");

        // Verify the results
        verify(mockNthActivityService).removeByGuid("85c531b8-f064-433b-b81f-fa5f7b520e2e");
    }

    @Test
    public void testUpdateState() {
        // Setup
        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93")).thenReturn(nthActivity);

        // Configure IHsaNthActivityStoreService.listByActivityGuid(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> hsaNthActivityStores = Arrays.asList(nthActivityStore);
        when(mockNthActivityStoreService.listByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198"))
                .thenReturn(hsaNthActivityStores);

        // Configure IHsaNthActivityItemService.listByActivityGuid(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93"))
                .thenReturn(hsaNthActivityItems);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        nthActivityManagerUnderTest.updateState("d422bbaf-7d99-4fe5-8b9c-1773655b9d93", 0);

        // Verify the results
        // Confirm IHsaNthActivityService.updateByGuid(...).
        final HsaNthActivity t = new HsaNthActivity();
        t.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setActivityCode("activityCode");
        t.setName("name");
        t.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setState(0);
        t.setDiscountRule("discountRule");
        t.setIsLimitPeriod(0);
        t.setLimitPeriodType(0);
        t.setLimitPeriodJson("equitiesTimeLimitedJson");
        t.setRelationRule(0);
        t.setApplyBusiness(0);
        t.setApplyBusinessJson("applyBusinessJson");
        t.setIsAllStore(0);
        t.setGroupType(0);
        t.setIsPublish(0);
        verify(mockNthActivityService).updateByGuid(t);

        // Confirm CacheService.saveNthActivityCacheByOperSubjectGuid(...).
        final NthActivityDetailsVO nthActivityDetailsVO = new NthActivityDetailsVO();
        nthActivityDetailsVO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityDetailsVO.setName("name");
        nthActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setIsLimitPeriod(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedType(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        nthActivityDetailsVO.setPerCount(0);
        nthActivityDetailsVO.setPerDiscount(new BigDecimal("0.00"));
        nthActivityDetailsVO.setRelationRule(0);
        nthActivityDetailsVO.setApplyBusiness(0);
        nthActivityDetailsVO.setApplyBusinessList(Arrays.asList("value"));
        nthActivityDetailsVO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        nthActivityDetailsVO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        nthActivityDetailsVO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        nthActivityDetailsVO.setItemDTOList(Arrays.asList(nthActivityItemDTO));
        final List<NthActivityDetailsVO> activityDetailsVOList = Arrays.asList(nthActivityDetailsVO);
        verify(mockCacheService).saveNthActivityCacheByOperSubjectGuid("operSubjectGuid", activityDetailsVOList);
        verify(mockCacheService).deleteNthActivityCache("d422bbaf-7d99-4fe5-8b9c-1773655b9d93");
    }

    @Test
    public void testUpdateState_IHsaNthActivityStoreServiceReturnsNoItems() {
        // Setup
        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93")).thenReturn(nthActivity);

        when(mockNthActivityStoreService.listByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198"))
                .thenReturn(Collections.emptyList());

        // Configure IHsaNthActivityItemService.listByActivityGuid(...).
        final HsaNthActivityItem activityItem = new HsaNthActivityItem();
        activityItem.setGuid("05898cba-104f-4452-a36a-3f6fda6eb529");
        activityItem.setOperSubjectGuid("operSubjectGuid");
        activityItem.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        activityItem.setCommodityId("commodityId");
        activityItem.setCommodityCode("commodityCode");
        activityItem.setCommodityType(0);
        activityItem.setCommodityName("commodityName");
        activityItem.setChannel("channel");
        final List<HsaNthActivityItem> hsaNthActivityItems = Arrays.asList(activityItem);
        when(mockNthActivityItemService.listByActivityGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93"))
                .thenReturn(hsaNthActivityItems);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        nthActivityManagerUnderTest.updateState("d422bbaf-7d99-4fe5-8b9c-1773655b9d93", 0);

        // Verify the results
        // Confirm IHsaNthActivityService.updateByGuid(...).
        final HsaNthActivity t = new HsaNthActivity();
        t.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setActivityCode("activityCode");
        t.setName("name");
        t.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setState(0);
        t.setDiscountRule("discountRule");
        t.setIsLimitPeriod(0);
        t.setLimitPeriodType(0);
        t.setLimitPeriodJson("equitiesTimeLimitedJson");
        t.setRelationRule(0);
        t.setApplyBusiness(0);
        t.setApplyBusinessJson("applyBusinessJson");
        t.setIsAllStore(0);
        t.setGroupType(0);
        t.setIsPublish(0);
        verify(mockNthActivityService).updateByGuid(t);

        // Confirm CacheService.saveNthActivityCacheByOperSubjectGuid(...).
        final NthActivityDetailsVO nthActivityDetailsVO = new NthActivityDetailsVO();
        nthActivityDetailsVO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityDetailsVO.setName("name");
        nthActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setIsLimitPeriod(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedType(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        nthActivityDetailsVO.setPerCount(0);
        nthActivityDetailsVO.setPerDiscount(new BigDecimal("0.00"));
        nthActivityDetailsVO.setRelationRule(0);
        nthActivityDetailsVO.setApplyBusiness(0);
        nthActivityDetailsVO.setApplyBusinessList(Arrays.asList("value"));
        nthActivityDetailsVO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        nthActivityDetailsVO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        nthActivityDetailsVO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        nthActivityDetailsVO.setItemDTOList(Arrays.asList(nthActivityItemDTO));
        final List<NthActivityDetailsVO> activityDetailsVOList = Arrays.asList(nthActivityDetailsVO);
        verify(mockCacheService).saveNthActivityCacheByOperSubjectGuid("operSubjectGuid", activityDetailsVOList);
    }

    @Test
    public void testUpdateState_IHsaNthActivityItemServiceReturnsNoItems() {
        // Setup
        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93")).thenReturn(nthActivity);

        // Configure IHsaNthActivityStoreService.listByActivityGuid(...).
        final HsaNthActivityStore nthActivityStore = new HsaNthActivityStore();
        nthActivityStore.setGuid("3fde9cb4-0782-4729-8f93-b2e1fe3e50ef");
        nthActivityStore.setOperSubjectGuid("operSubjectGuid");
        nthActivityStore.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityStore.setStoreGuid("storeGuid");
        nthActivityStore.setStoreName("storeName");
        nthActivityStore.setStoreCode("storeCode");
        final List<HsaNthActivityStore> hsaNthActivityStores = Arrays.asList(nthActivityStore);
        when(mockNthActivityStoreService.listByActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198"))
                .thenReturn(hsaNthActivityStores);

        when(mockNthActivityItemService.listByActivityGuid("d422bbaf-7d99-4fe5-8b9c-1773655b9d93"))
                .thenReturn(Collections.emptyList());

        // Run the test
        nthActivityManagerUnderTest.updateState("d422bbaf-7d99-4fe5-8b9c-1773655b9d93", 0);

        // Verify the results
        // Confirm IHsaNthActivityService.updateByGuid(...).
        final HsaNthActivity t = new HsaNthActivity();
        t.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setActivityCode("activityCode");
        t.setName("name");
        t.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setState(0);
        t.setDiscountRule("discountRule");
        t.setIsLimitPeriod(0);
        t.setLimitPeriodType(0);
        t.setLimitPeriodJson("equitiesTimeLimitedJson");
        t.setRelationRule(0);
        t.setApplyBusiness(0);
        t.setApplyBusinessJson("applyBusinessJson");
        t.setIsAllStore(0);
        t.setGroupType(0);
        t.setIsPublish(0);
        verify(mockNthActivityService).updateByGuid(t);

        // Confirm CacheService.saveNthActivityCacheByOperSubjectGuid(...).
        final NthActivityDetailsVO nthActivityDetailsVO = new NthActivityDetailsVO();
        nthActivityDetailsVO.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityDetailsVO.setName("name");
        nthActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivityDetailsVO.setIsLimitPeriod(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedType(0);
        nthActivityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        nthActivityDetailsVO.setPerCount(0);
        nthActivityDetailsVO.setPerDiscount(new BigDecimal("0.00"));
        nthActivityDetailsVO.setRelationRule(0);
        nthActivityDetailsVO.setApplyBusiness(0);
        nthActivityDetailsVO.setApplyBusinessList(Arrays.asList("value"));
        nthActivityDetailsVO.setIsAllStore(0);
        final NthActivityStoreDTO nthActivityStoreDTO = new NthActivityStoreDTO();
        nthActivityStoreDTO.setStoreGuid("storeGuid");
        nthActivityStoreDTO.setStoreNumber("storeCode");
        nthActivityStoreDTO.setStoreName("storeName");
        nthActivityStoreDTO.setActivityGuid("activityGuid");
        nthActivityDetailsVO.setStoreDTOList(Arrays.asList(nthActivityStoreDTO));
        nthActivityDetailsVO.setGroupType(0);
        final NthActivityItemDTO nthActivityItemDTO = new NthActivityItemDTO();
        nthActivityItemDTO.setCommodityId("commodityId");
        nthActivityItemDTO.setActivityGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivityItemDTO.setCommodityCode("commodityCode");
        nthActivityItemDTO.setCommodityName("commodityName");
        nthActivityItemDTO.setCommodityType(0);
        nthActivityItemDTO.setChannel("channel");
        nthActivityItemDTO.setIsExist(0);
        nthActivityDetailsVO.setItemDTOList(Arrays.asList(nthActivityItemDTO));
        final List<NthActivityDetailsVO> activityDetailsVOList = Arrays.asList(nthActivityDetailsVO);
        verify(mockCacheService).saveNthActivityCacheByOperSubjectGuid("operSubjectGuid", activityDetailsVOList);
    }

    @Test
    public void testCheckActivityState() {
        // Setup
        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198")).thenReturn(nthActivity);

        // Run the test
        final Boolean result = nthActivityManagerUnderTest.checkActivityState("5e11d0f8-e3f6-419a-b5be-60cddac59198",
                0);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testPay() {
        // Setup
        final NthActivityBO biz = new NthActivityBO();
        final HsaNthActivityRecord nthActivityRecord = new HsaNthActivityRecord();
        nthActivityRecord.setGuid("guid");
        nthActivityRecord.setOperSubjectGuid("operSubjectGuid");
        nthActivityRecord.setActivityCode("activityCode");
        nthActivityRecord.setName("name");
        nthActivityRecord.setOrderGuid("orderGuid");
        nthActivityRecord.setOrderState(0);
        biz.setNthActivityRecord(nthActivityRecord);

        // Configure IHsaNthActivityService.queryByGuid(...).
        final HsaNthActivity nthActivity = new HsaNthActivity();
        nthActivity.setGuid("5e11d0f8-e3f6-419a-b5be-60cddac59198");
        nthActivity.setOperSubjectGuid("operSubjectGuid");
        nthActivity.setActivityCode("activityCode");
        nthActivity.setName("name");
        nthActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        nthActivity.setState(0);
        nthActivity.setDiscountRule("discountRule");
        nthActivity.setIsLimitPeriod(0);
        nthActivity.setLimitPeriodType(0);
        nthActivity.setLimitPeriodJson("equitiesTimeLimitedJson");
        nthActivity.setRelationRule(0);
        nthActivity.setApplyBusiness(0);
        nthActivity.setApplyBusinessJson("applyBusinessJson");
        nthActivity.setIsAllStore(0);
        nthActivity.setGroupType(0);
        nthActivity.setIsPublish(0);
        when(mockNthActivityService.queryByGuid("guid")).thenReturn(nthActivity);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Run the test
        nthActivityManagerUnderTest.pay(biz);

        // Verify the results
        // Confirm IHsaNthActivityRecordService.saveRecord(...).
        final HsaNthActivityRecord nthActivityRecord1 = new HsaNthActivityRecord();
        nthActivityRecord1.setGuid("guid");
        nthActivityRecord1.setOperSubjectGuid("operSubjectGuid");
        nthActivityRecord1.setActivityCode("activityCode");
        nthActivityRecord1.setName("name");
        nthActivityRecord1.setOrderGuid("orderGuid");
        nthActivityRecord1.setOrderState(0);
        verify(mockNthActivityRecordService).saveRecord(nthActivityRecord1);
    }

    @Test
    public void testRefund() {
        // Setup
        final NthActivityBO biz = new NthActivityBO();
        final HsaNthActivityRecord nthActivityRecord = new HsaNthActivityRecord();
        nthActivityRecord.setGuid("guid");
        nthActivityRecord.setOperSubjectGuid("operSubjectGuid");
        nthActivityRecord.setActivityCode("activityCode");
        nthActivityRecord.setName("name");
        nthActivityRecord.setOrderGuid("orderGuid");
        nthActivityRecord.setOrderState(0);
        biz.setNthActivityRecord(nthActivityRecord);

        // Run the test
        nthActivityManagerUnderTest.refund(biz);

        // Verify the results
        verify(mockNthActivityRecordService).updateRefundStateByOrderGuid("orderGuid");
    }
}
