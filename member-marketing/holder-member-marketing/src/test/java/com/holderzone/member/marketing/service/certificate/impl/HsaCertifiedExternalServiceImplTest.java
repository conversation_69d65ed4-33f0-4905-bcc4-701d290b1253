package com.holderzone.member.marketing.service.certificate.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.activity.VolumeInfoUpdateDTO;
import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.gift.QueryMerchantStoreDTO;
import com.holderzone.member.common.dto.label.MemberCertifiedLabel;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.feign.ZhuanCanFeign;
import com.holderzone.member.common.qo.activity.RequestLabelDTO;
import com.holderzone.member.common.qo.certificate.VolumeInfoQO;
import com.holderzone.member.common.qo.gift.QueryGiftStoreQO;
import com.holderzone.member.common.qo.member.RequestLabelSetting;
import com.holderzone.member.common.qo.member.RequestManualLabel;
import com.holderzone.member.common.qo.member.ResponseOperationMemberInfo;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.PlatFormUserDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;
import com.holderzone.member.common.vo.base.ResponseOperationLabelList;
import com.holderzone.member.common.vo.certificate.RequestMemberInfoVolumeSave;
import com.holderzone.member.common.vo.certificate.ResponseAppletsMemberInfo;
import com.holderzone.member.common.vo.certificate.SendVolumeVO;
import com.holderzone.member.common.vo.certificate.VolumeInfoVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.feign.MerchantModel;
import com.holderzone.member.common.vo.gift.ConsumptionGiftStoreVO;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedActivity;
import com.holderzone.member.marketing.entity.certificate.HsaCertifiedActivityRecord;
import com.holderzone.member.marketing.mapper.HsaCertifiedActivityMapper;
import com.holderzone.member.marketing.mapper.HsaCertifiedActivityRecordMapper;
import com.holderzone.member.marketing.service.certificate.HsaCertifiedActivityRecordService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaCertifiedExternalServiceImplTest {

    @Mock
    private ZhuanCanFeign mockZhuancanFeign;
    @Mock
    private SaasStoreFeign mockSaasStoreFeign;
    @Mock
    private HsaCertifiedActivityRecordMapper mockHsaCertifiedActivityRecordMapper;
    @Mock
    private HsaCertifiedActivityRecordService mockCertifiedActivityRecordService;
    @Mock
    private HsaCertifiedActivityMapper mockHsaCertifiedActivityMapper;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;
    @Mock
    private ExternalSupport mockExternalSupport;

    @InjectMocks
    private HsaCertifiedExternalServiceImpl hsaCertifiedExternalServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(hsaCertifiedExternalServiceImplUnderTest, "marketingThreadExecutor",
                MoreExecutors.directExecutor());
    }

    @Test
    public void testGetIslandCouponsList() {
        // Setup
        final IslandCouponDTO islandCouponDTO = new IslandCouponDTO();
        islandCouponDTO.setId("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO.setName("name");
        islandCouponDTO.setPlatFormId(0L);
        islandCouponDTO.setGuid("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO.setUpdateDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<IslandCouponDTO> expectedResult = Arrays.asList(islandCouponDTO);

        // Configure ZhuanCanFeign.getIslandCouponDetailsNew(...).
        final IslandCouponDTO islandCouponDTO1 = new IslandCouponDTO();
        islandCouponDTO1.setId("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO1.setName("name");
        islandCouponDTO1.setPlatFormId(0L);
        islandCouponDTO1.setGuid("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO1.setUpdateDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ResponseEntity<List<IslandCouponDTO>> listResponseEntity = new ResponseEntity<>(
                Arrays.asList(islandCouponDTO1), HttpStatus.OK);
        when(mockZhuancanFeign.getIslandCouponDetailsNew(Arrays.asList(0L))).thenReturn(listResponseEntity);

        // Configure ZhuanCanFeign.getPlatFormThirdNo(...).
        final PlatFormUserDTO platFormUserDTO = new PlatFormUserDTO();
        platFormUserDTO.setId(0L);
        platFormUserDTO.setUserName("userName");
        platFormUserDTO.setPhone("phone");
        platFormUserDTO.setImageUrl("imageUrl");
        platFormUserDTO.setAge(0);
        final ResponseEntity<PlatFormUserDTO> platFormUserDTOResponseEntity = new ResponseEntity<>(platFormUserDTO,
                HttpStatus.OK);
        when(mockZhuancanFeign.getPlatFormThirdNo("enterpriseGuid", "operSubjectGuid"))
                .thenReturn(platFormUserDTOResponseEntity);

        // Configure ZhuanCanFeign.getIslandCouponsList(...).
        final IslandCouponDTO islandCouponDTO2 = new IslandCouponDTO();
        islandCouponDTO2.setId("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO2.setName("name");
        islandCouponDTO2.setPlatFormId(0L);
        islandCouponDTO2.setGuid("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO2.setUpdateDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ResponseEntity<List<IslandCouponDTO>> listResponseEntity1 = new ResponseEntity<>(
                Arrays.asList(islandCouponDTO2), HttpStatus.OK);
        when(mockZhuancanFeign.getIslandCouponsList(NumberConstant.NUMBER_LONG_9999999, 0L, "useType", "keyword",
                Boolean.FALSE)).thenReturn(listResponseEntity1);

        // Run the test
        final List<IslandCouponDTO> result = hsaCertifiedExternalServiceImplUnderTest.getIslandCouponsList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetIslandCouponsList_ZhuanCanFeignGetIslandCouponDetailsNewReturnsNoItems() {
        // Setup
        // Configure ZhuanCanFeign.getIslandCouponDetailsNew(...).
        final ResponseEntity<List<IslandCouponDTO>> listResponseEntity = ResponseEntity.ok(Collections.emptyList());
        when(mockZhuancanFeign.getIslandCouponDetailsNew(Arrays.asList(0L))).thenReturn(listResponseEntity);

        // Run the test
        final List<IslandCouponDTO> result = hsaCertifiedExternalServiceImplUnderTest.getIslandCouponsList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetIslandCouponsList_ZhuanCanFeignGetIslandCouponsListReturnsNoItems() {
        // Setup
        // Configure ZhuanCanFeign.getPlatFormThirdNo(...).
        final PlatFormUserDTO platFormUserDTO = new PlatFormUserDTO();
        platFormUserDTO.setId(0L);
        platFormUserDTO.setUserName("userName");
        platFormUserDTO.setPhone("phone");
        platFormUserDTO.setImageUrl("imageUrl");
        platFormUserDTO.setAge(0);
        final ResponseEntity<PlatFormUserDTO> platFormUserDTOResponseEntity = new ResponseEntity<>(platFormUserDTO,
                HttpStatus.OK);
        when(mockZhuancanFeign.getPlatFormThirdNo("enterpriseGuid", "operSubjectGuid"))
                .thenReturn(platFormUserDTOResponseEntity);

        // Configure ZhuanCanFeign.getIslandCouponsList(...).
        final ResponseEntity<List<IslandCouponDTO>> listResponseEntity = ResponseEntity.ok(Collections.emptyList());
        when(mockZhuancanFeign.getIslandCouponsList(NumberConstant.NUMBER_LONG_9999999, 0L, "useType", "keyword",
                Boolean.FALSE)).thenReturn(listResponseEntity);

        // Run the test
        final List<IslandCouponDTO> result = hsaCertifiedExternalServiceImplUnderTest.getIslandCouponsList(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetLabelList() {
        // Setup
        final RequestLabelDTO requestLabelDTO = new RequestLabelDTO();
        requestLabelDTO.setLabelName("labelName");
        requestLabelDTO.setLabelType(0);
        requestLabelDTO.setOperSubjectGuid("operSubjectGuid");
        requestLabelDTO.setPageSize(0);
        requestLabelDTO.setPageIndex(0);

        final ResponseOperationLabel responseOperationLabel = new ResponseOperationLabel();
        responseOperationLabel.setLabelName("labelName");
        responseOperationLabel.setGuid("7c09a9df-01d3-460a-b0e6-86973f61e85d");
        responseOperationLabel.setLabelType(0);
        responseOperationLabel.setMemberNum(0);
        responseOperationLabel.setGmtCreate(LocalDate.of(2020, 1, 1));
        final List<ResponseOperationLabel> expectedResult = Arrays.asList(responseOperationLabel);

        // Configure SaasStoreFeign.getAutomaticLabelList(...).
        final FeignModel<Page<ResponseOperationLabelList>> pageFeignModel = FeignModel.success(new Page<>(0L, 0L, 0L));
        final RequestLabelDTO requestLabelDTO1 = new RequestLabelDTO();
        requestLabelDTO1.setLabelName("labelName");
        requestLabelDTO1.setLabelType(0);
        requestLabelDTO1.setOperSubjectGuid("operSubjectGuid");
        requestLabelDTO1.setPageSize(0);
        requestLabelDTO1.setPageIndex(0);
        when(mockSaasStoreFeign.getAutomaticLabelList(requestLabelDTO1)).thenReturn(pageFeignModel);

        // Run the test
        final List<ResponseOperationLabel> result = hsaCertifiedExternalServiceImplUnderTest.getLabelList(
                requestLabelDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSaveAutomaticLabel() {
        // Setup
        final RequestLabelSetting req = new RequestLabelSetting();
        req.setOperSubjectGuid("operSubjectGuid");
        req.setLabelType(0);
        req.setLabelName("labelName");
        req.setMemberNum(0);
        req.setConditionSet(0);

        when(mockExternalSupport.memberServer(0)).thenReturn(null);

        // Run the test
        final boolean result = hsaCertifiedExternalServiceImplUnderTest.saveAutomaticLabel(req);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSendLabel() {
        // Setup
        // Configure HsaCertifiedActivityMapper.queryByGuid(...).
        final HsaCertifiedActivity hsaCertifiedActivity = new HsaCertifiedActivity();
        hsaCertifiedActivity.setId(0L);
        hsaCertifiedActivity.setGuid("ab605db5-7106-44b0-9e03-66edc80b124f");
        hsaCertifiedActivity.setOperSubjectGuid("operSubjectGuid");
        hsaCertifiedActivity.setEnterpriseGuid("enterpriseGuid");
        hsaCertifiedActivity.setActivityLabelGuids("activityLabelGuids");
        when(mockHsaCertifiedActivityMapper.queryByGuid("activityGuid")).thenReturn(hsaCertifiedActivity);

        // Run the test
        hsaCertifiedExternalServiceImplUnderTest.sendLabel("t");

        // Verify the results
        verify(mockSaasStoreFeign).deleteLabelBatch("guid", Arrays.asList("value"));
    }

    @Test
    public void testMemberLabelRefresh() {
        // Setup
        final MemberCertifiedLabel memberCertifiedLabel = new MemberCertifiedLabel();
        memberCertifiedLabel.setCertifiedActivityGuid("activityGuid");
        memberCertifiedLabel.setStatus(0);
        memberCertifiedLabel.setPhoneNumber("phoneNumber");
        memberCertifiedLabel.setMemberInfoGuid("guid");
        memberCertifiedLabel.setCertificateValidity(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure HsaCertifiedActivityMapper.queryByGuid(...).
        final HsaCertifiedActivity hsaCertifiedActivity = new HsaCertifiedActivity();
        hsaCertifiedActivity.setId(0L);
        hsaCertifiedActivity.setGuid("ab605db5-7106-44b0-9e03-66edc80b124f");
        hsaCertifiedActivity.setOperSubjectGuid("operSubjectGuid");
        hsaCertifiedActivity.setEnterpriseGuid("enterpriseGuid");
        hsaCertifiedActivity.setActivityLabelGuids("activityLabelGuids");
        when(mockHsaCertifiedActivityMapper.queryByGuid("activityGuid")).thenReturn(hsaCertifiedActivity);

        // Configure SaasStoreFeign.getMemberInfoByPhone(...).
        final ResponseOperationMemberInfo responseOperationMemberInfo = new ResponseOperationMemberInfo();
        responseOperationMemberInfo.setId("id");
        responseOperationMemberInfo.setGuid("guid");
        responseOperationMemberInfo.setMemberAccount("memberAccount");
        responseOperationMemberInfo.setUserName("userName");
        responseOperationMemberInfo.setMemberInfoGradeGuid("memberInfoGradeGuid");
        final FeignModel<ResponseOperationMemberInfo> responseOperationMemberInfoFeignModel = FeignModel.success(
                responseOperationMemberInfo);
        when(mockSaasStoreFeign.getMemberInfoByPhone("phoneNumber", "operSubjectGuid"))
                .thenReturn(responseOperationMemberInfoFeignModel);

        // Run the test
        hsaCertifiedExternalServiceImplUnderTest.memberLabelRefresh(memberCertifiedLabel);

        // Verify the results
        // Confirm SaasStoreFeign.batchMemberManualLabel(...).
        final RequestManualLabel req = new RequestManualLabel();
        req.setMemberInfoGuidArray("guid");
        req.setLabelSettingGuid("labelSettingGuid");
        verify(mockSaasStoreFeign).batchMemberManualLabel(req);
        verify(mockSaasStoreFeign).deleteLabelBatch("guid", Arrays.asList("value"));
    }

    @Test
    public void testGetStoreInfoPage() {
        // Setup
        final QueryGiftStoreQO queryGiftStoreQO = new QueryGiftStoreQO();
        queryGiftStoreQO.setStoreName("storeName");
        queryGiftStoreQO.setStoreNumber("storeNumber");

        // Configure SaasStoreFeign.queryStoreByCondition(...).
        final MerchantModel<QueryMerchantStoreDTO> queryMerchantStoreDTOMerchantModel = new MerchantModel<>();
        queryMerchantStoreDTOMerchantModel.setCode(0);
        queryMerchantStoreDTOMerchantModel.setMessage("message");
        final QueryMerchantStoreDTO queryMerchantStoreDTO = new QueryMerchantStoreDTO();
        queryMerchantStoreDTO.setGuid("b5d2617f-18c4-4abd-a36a-4c50b19fdf95");
        queryMerchantStoreDTO.setCode("code");
        queryMerchantStoreDTOMerchantModel.setTData(queryMerchantStoreDTO);
        final QueryGiftStoreQO req = new QueryGiftStoreQO();
        req.setStoreName("storeName");
        req.setStoreNumber("storeNumber");
        when(mockSaasStoreFeign.queryStoreByCondition(req)).thenReturn(queryMerchantStoreDTOMerchantModel);

    }

    @Test
    public void testMemberCouponSend() {
        // Setup
        final SendCertifiedStampsDTO sendCertifiedStampsDTO = new SendCertifiedStampsDTO();
        sendCertifiedStampsDTO.setActivityGuid("activityGuid");
        sendCertifiedStampsDTO.setAuditStepGuid("auditStepGuid");
        sendCertifiedStampsDTO.setActivityName("activityName");
        sendCertifiedStampsDTO.setPhoneNumber("phoneNumber");
        sendCertifiedStampsDTO.setCertificateValidity(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure HsaCertifiedActivityMapper.queryByGuid(...).
        final HsaCertifiedActivity hsaCertifiedActivity = new HsaCertifiedActivity();
        hsaCertifiedActivity.setId(0L);
        hsaCertifiedActivity.setGuid("ab605db5-7106-44b0-9e03-66edc80b124f");
        hsaCertifiedActivity.setOperSubjectGuid("operSubjectGuid");
        hsaCertifiedActivity.setEnterpriseGuid("enterpriseGuid");
        hsaCertifiedActivity.setActivityLabelGuids("activityLabelGuids");
        when(mockHsaCertifiedActivityMapper.queryByGuid("activityGuid")).thenReturn(hsaCertifiedActivity);

        // Configure SaasStoreFeign.getMemberInfoByPhone(...).
        final ResponseOperationMemberInfo responseOperationMemberInfo = new ResponseOperationMemberInfo();
        responseOperationMemberInfo.setId("id");
        responseOperationMemberInfo.setGuid("guid");
        responseOperationMemberInfo.setMemberAccount("memberAccount");
        responseOperationMemberInfo.setUserName("userName");
        responseOperationMemberInfo.setMemberInfoGradeGuid("memberInfoGradeGuid");
        final FeignModel<ResponseOperationMemberInfo> responseOperationMemberInfoFeignModel = FeignModel.success(
                responseOperationMemberInfo);
        when(mockSaasStoreFeign.getMemberInfoByPhone("phoneNumber", "operSubjectGuid"))
                .thenReturn(responseOperationMemberInfoFeignModel);

        // Run the test
        hsaCertifiedExternalServiceImplUnderTest.memberCouponSend(sendCertifiedStampsDTO);

        // Verify the results
        // Confirm ZhuanCanFeign.certifiedActivityCouponSend(...).
        final SendCertifiedStampsDTO sendCertifiedStampsDTO1 = new SendCertifiedStampsDTO();
        sendCertifiedStampsDTO1.setActivityGuid("activityGuid");
        sendCertifiedStampsDTO1.setAuditStepGuid("auditStepGuid");
        sendCertifiedStampsDTO1.setActivityName("activityName");
        sendCertifiedStampsDTO1.setPhoneNumber("phoneNumber");
        sendCertifiedStampsDTO1.setCertificateValidity(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockZhuancanFeign).certifiedActivityCouponSend(sendCertifiedStampsDTO1);

        // Confirm SaasStoreFeign.batchMemberManualLabel(...).
        final RequestManualLabel req = new RequestManualLabel();
        req.setMemberInfoGuidArray("guid");
        req.setLabelSettingGuid("labelSettingGuid");
        verify(mockSaasStoreFeign).batchMemberManualLabel(req);
        verify(mockSaasStoreFeign).deleteLabelBatch("guid", Arrays.asList("value"));
    }

    @Test
    public void testQueryVolumeList() {
        // Setup
        final IslandCouponDTO islandCouponDTO = new IslandCouponDTO();
        islandCouponDTO.setId("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO.setName("name");
        islandCouponDTO.setPlatFormId(0L);
        islandCouponDTO.setGuid("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        islandCouponDTO.setUpdateDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<IslandCouponDTO> expectedResult = Arrays.asList(islandCouponDTO);

        // Configure SaasStoreFeign.queryUsableVolumeList(...).
        final VolumeInfoVO volumeInfoVO = new VolumeInfoVO();
        volumeInfoVO.setGuid("3358a073-e113-4cec-8bcc-2bbe51209b7d");
        volumeInfoVO.setVolumeType(0);
        volumeInfoVO.setVolumeTypeName("volumeTypeName");
        volumeInfoVO.setVolumeName("name");
        volumeInfoVO.setDenomination(new BigDecimal("0.00"));
        final FeignModel<List<VolumeInfoVO>> listFeignModel = FeignModel.success(Arrays.asList(volumeInfoVO));
        final VolumeInfoQO volumeInfoQO = new VolumeInfoQO();
        volumeInfoQO.setVolumeType(0);
        volumeInfoQO.setVolumeName("volumeName");
        volumeInfoQO.setVolumeState(0);
        volumeInfoQO.setEnterpriseGuid("enterpriseGuid");
        volumeInfoQO.setOperSubjectGuid("operSubjectGuid");
        when(mockSaasStoreFeign.queryUsableVolumeList(volumeInfoQO)).thenReturn(listFeignModel);

        // Run the test
        final List<IslandCouponDTO> result = hsaCertifiedExternalServiceImplUnderTest.queryVolumeList();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryVolumeList_SaasStoreFeignReturnsNoItems() {
        // Setup
        // Configure SaasStoreFeign.queryUsableVolumeList(...).
        final FeignModel<List<VolumeInfoVO>> listFeignModel = FeignModel.success(Collections.emptyList());
        final VolumeInfoQO volumeInfoQO = new VolumeInfoQO();
        volumeInfoQO.setVolumeType(0);
        volumeInfoQO.setVolumeName("volumeName");
        volumeInfoQO.setVolumeState(0);
        volumeInfoQO.setEnterpriseGuid("enterpriseGuid");
        volumeInfoQO.setOperSubjectGuid("operSubjectGuid");
        when(mockSaasStoreFeign.queryUsableVolumeList(volumeInfoQO)).thenReturn(listFeignModel);

        // Run the test
        final List<IslandCouponDTO> result = hsaCertifiedExternalServiceImplUnderTest.queryVolumeList();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testBatchSendVolume() {
        // Setup
        final SendVolumeVO sendVolumeVO = new SendVolumeVO();
        sendVolumeVO.setRecordGuidList(Arrays.asList("value"));

        // Configure HsaCertifiedActivityRecordMapper.queryReplenishSendRecord(...).
        final HsaCertifiedActivityRecord hsaCertifiedActivityRecord = new HsaCertifiedActivityRecord();
        hsaCertifiedActivityRecord.setId(0L);
        hsaCertifiedActivityRecord.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaCertifiedActivityRecord.setDiscount("discount");
        hsaCertifiedActivityRecord.setMemberGuid("memberGuid");
        hsaCertifiedActivityRecord.setIssuanceStatus(0);
        final List<HsaCertifiedActivityRecord> hsaCertifiedActivityRecords = Arrays.asList(hsaCertifiedActivityRecord);
        when(mockHsaCertifiedActivityRecordMapper.queryReplenishSendRecord(Arrays.asList("value")))
                .thenReturn(hsaCertifiedActivityRecords);

        // Configure SaasStoreFeign.getAppletsMemberInfoByUserId(...).
        final ResponseAppletsMemberInfo responseAppletsMemberInfo = new ResponseAppletsMemberInfo();
        responseAppletsMemberInfo.setCardList(Arrays.asList(new HashMap<>()));
        responseAppletsMemberInfo.setBirthday(LocalDate.of(2020, 1, 1));
        responseAppletsMemberInfo.setRegisterTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseAppletsMemberInfo.setLastConsumeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseAppletsMemberInfo.setMemberInfoGuid("memberInfoGuid");
        final FeignModel<ResponseAppletsMemberInfo> responseAppletsMemberInfoFeignModel = FeignModel.success(
                responseAppletsMemberInfo);
        when(mockSaasStoreFeign.getAppletsMemberInfoByUserId("memberGuid"))
                .thenReturn(responseAppletsMemberInfoFeignModel);

        // Configure SaasStoreFeign.batchSendVolume(...).
        final VolumeInfoUpdateDTO volumeInfoUpdateDTO = new VolumeInfoUpdateDTO();
        volumeInfoUpdateDTO.setPricePlanGuidArray("pricePlanGuidArray");
        volumeInfoUpdateDTO.setOperSubjectGuid("operSubjectGuid");
        volumeInfoUpdateDTO.setGuid("ab86f7a3-9a9a-493a-9af9-58caa2b6bcc4");
        volumeInfoUpdateDTO.setVolumeName("volumeName");
        volumeInfoUpdateDTO.setVolumeTypeName("volumeTypeName");
        final FeignModel<List<VolumeInfoUpdateDTO>> listFeignModel = FeignModel.success(
                Arrays.asList(volumeInfoUpdateDTO));
        final RequestMemberInfoVolumeSave request = new RequestMemberInfoVolumeSave();
        request.setMemberInfoGuid("memberInfoGuid");
        request.setVolumeInfoGuid("volumeInfoGuid");
        request.setVolumeNumber("volumeNumber");
        request.setSendEnterpriseGuid("sendEnterpriseGuid");
        request.setCollectionType(0);
        request.setVolumeSourceCode(0);
        when(mockSaasStoreFeign.batchSendVolume(request)).thenReturn(listFeignModel);

        // Run the test
        hsaCertifiedExternalServiceImplUnderTest.batchSendVolume(sendVolumeVO);

        // Verify the results
        // Confirm HsaCertifiedActivityRecordService.updateBatchByGuid(...).
        final HsaCertifiedActivityRecord hsaCertifiedActivityRecord1 = new HsaCertifiedActivityRecord();
        hsaCertifiedActivityRecord1.setId(0L);
        hsaCertifiedActivityRecord1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaCertifiedActivityRecord1.setDiscount("discount");
        hsaCertifiedActivityRecord1.setMemberGuid("memberGuid");
        hsaCertifiedActivityRecord1.setIssuanceStatus(0);
        final List<HsaCertifiedActivityRecord> updateList = Arrays.asList(hsaCertifiedActivityRecord1);
        verify(mockCertifiedActivityRecordService).updateBatchByGuid(updateList);
    }

    @Test
    public void testBatchSendVolume_HsaCertifiedActivityRecordMapperReturnsNoItems() {
        // Setup
        final SendVolumeVO sendVolumeVO = new SendVolumeVO();
        sendVolumeVO.setRecordGuidList(Arrays.asList("value"));

        when(mockHsaCertifiedActivityRecordMapper.queryReplenishSendRecord(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        hsaCertifiedExternalServiceImplUnderTest.batchSendVolume(sendVolumeVO);

        // Verify the results
    }

    @Test
    public void testBatchSendVolume_SaasStoreFeignBatchSendVolumeReturnsNoItems() {
        // Setup
        final SendVolumeVO sendVolumeVO = new SendVolumeVO();
        sendVolumeVO.setRecordGuidList(Arrays.asList("value"));

        // Configure HsaCertifiedActivityRecordMapper.queryReplenishSendRecord(...).
        final HsaCertifiedActivityRecord hsaCertifiedActivityRecord = new HsaCertifiedActivityRecord();
        hsaCertifiedActivityRecord.setId(0L);
        hsaCertifiedActivityRecord.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaCertifiedActivityRecord.setDiscount("discount");
        hsaCertifiedActivityRecord.setMemberGuid("memberGuid");
        hsaCertifiedActivityRecord.setIssuanceStatus(0);
        final List<HsaCertifiedActivityRecord> hsaCertifiedActivityRecords = Arrays.asList(hsaCertifiedActivityRecord);
        when(mockHsaCertifiedActivityRecordMapper.queryReplenishSendRecord(Arrays.asList("value")))
                .thenReturn(hsaCertifiedActivityRecords);

        // Configure SaasStoreFeign.getAppletsMemberInfoByUserId(...).
        final ResponseAppletsMemberInfo responseAppletsMemberInfo = new ResponseAppletsMemberInfo();
        responseAppletsMemberInfo.setCardList(Arrays.asList(new HashMap<>()));
        responseAppletsMemberInfo.setBirthday(LocalDate.of(2020, 1, 1));
        responseAppletsMemberInfo.setRegisterTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseAppletsMemberInfo.setLastConsumeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseAppletsMemberInfo.setMemberInfoGuid("memberInfoGuid");
        final FeignModel<ResponseAppletsMemberInfo> responseAppletsMemberInfoFeignModel = FeignModel.success(
                responseAppletsMemberInfo);
        when(mockSaasStoreFeign.getAppletsMemberInfoByUserId("memberGuid"))
                .thenReturn(responseAppletsMemberInfoFeignModel);

        // Configure SaasStoreFeign.batchSendVolume(...).
        final FeignModel<List<VolumeInfoUpdateDTO>> listFeignModel = FeignModel.success(Collections.emptyList());
        final RequestMemberInfoVolumeSave request = new RequestMemberInfoVolumeSave();
        request.setMemberInfoGuid("memberInfoGuid");
        request.setVolumeInfoGuid("volumeInfoGuid");
        request.setVolumeNumber("volumeNumber");
        request.setSendEnterpriseGuid("sendEnterpriseGuid");
        request.setCollectionType(0);
        request.setVolumeSourceCode(0);
        when(mockSaasStoreFeign.batchSendVolume(request)).thenReturn(listFeignModel);

        // Run the test
        hsaCertifiedExternalServiceImplUnderTest.batchSendVolume(sendVolumeVO);

        // Verify the results
        // Confirm HsaCertifiedActivityRecordService.updateBatchByGuid(...).
        final HsaCertifiedActivityRecord hsaCertifiedActivityRecord1 = new HsaCertifiedActivityRecord();
        hsaCertifiedActivityRecord1.setId(0L);
        hsaCertifiedActivityRecord1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaCertifiedActivityRecord1.setDiscount("discount");
        hsaCertifiedActivityRecord1.setMemberGuid("memberGuid");
        hsaCertifiedActivityRecord1.setIssuanceStatus(0);
        final List<HsaCertifiedActivityRecord> updateList = Arrays.asList(hsaCertifiedActivityRecord1);
        verify(mockCertifiedActivityRecordService).updateBatchByGuid(updateList);
    }
}
