package com.holderzone.member.queue.service.grade.context;

import com.holderzone.member.common.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.entity.grade.HsaMemberGradeInfo;
import com.holderzone.member.queue.entity.member.HsaOperationMemberInfo;
import com.holderzone.member.queue.entity.equities.HsaBusinessEquities;
import lombok.Data;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 会员等级变化处理上下文
 * 
 * 用于在整个等级变化处理过程中传递和共享数据，避免重复查询和计算
 * 
 * <AUTHOR>
 * @date 2025-09-18
 */
@Data
public class MemberGradeChangeContext {
    
    /**
     * 原始请求参数
     */
    private final MemberGradeChangeDTO gradeChangeDTO;
    
    /**
     * 等级体系是否启用
     */
    private boolean gradeSystemEnabled;
    
    /**
     * 当前生效的等级信息列表
     */
    private List<HsaMemberGradeInfo> currentGradeInfoList;
    
    /**
     * 历史等级信息映射 (gradeGuid -> gradeInfo)
     */
    private Map<String, HsaMemberGradeInfo> oldGradeInfoMap;
    
    /**
     * 等级权益映射 (gradeGuid -> equitiesList)
     */
    private Map<String, List<HsaBusinessEquities>> gradeEquitiesMap;
    
    /**
     * 等级变化记录 (memberGuid -> GradeChangeRecord)
     */
    private final Map<String, GradeChangeRecord> gradeChanges = new ConcurrentHashMap<>();
    
    /**
     * 需要更新的会员信息列表
     */
    private final List<HsaOperationMemberInfo> updatedMemberInfos = new ArrayList<>();
    
    /**
     * 需要发放的权益记录
     */
    private final List<RightsRecord> rightsRecords = new ArrayList<>();
    
    /**
     * 处理统计信息
     */
    private final ProcessStatistics statistics = new ProcessStatistics();

    public MemberGradeChangeContext(MemberGradeChangeDTO gradeChangeDTO) {
        this.gradeChangeDTO = gradeChangeDTO;
    }

    /**
     * 是否应该跳过处理
     */
    public boolean shouldSkip() {
        return !gradeSystemEnabled || 
               gradeChangeDTO.getHsaOperationMemberInfos() == null || 
               gradeChangeDTO.getHsaOperationMemberInfos().isEmpty();
    }

    /**
     * 获取操作主体GUID
     */
    public String getOperSubjectGuid() {
        return gradeChangeDTO.getOperSubjectGuid();
    }

    /**
     * 获取角色类型
     */
    public String getRoleType() {
        return gradeChangeDTO.getRoleType();
    }

    /**
     * 获取会员信息列表
     */
    public List<HsaOperationMemberInfo> getMemberInfos() {
        return gradeChangeDTO.getHsaOperationMemberInfos();
    }

    /**
     * 获取会员数量
     */
    public int getMemberCount() {
        return gradeChangeDTO.getHsaOperationMemberInfos() != null ? 
               gradeChangeDTO.getHsaOperationMemberInfos().size() : 0;
    }

    /**
     * 获取发行者类型
     */
    public Integer getIssuerType() {
        return gradeChangeDTO.getIssuerType();
    }

    /**
     * 添加等级变化记录
     */
    public void addGradeChange(HsaOperationMemberInfo memberInfo, 
                              HsaMemberGradeInfo oldGrade, 
                              HsaMemberGradeInfo newGrade) {
        GradeChangeRecord record = new GradeChangeRecord(memberInfo, oldGrade, newGrade);
        gradeChanges.put(memberInfo.getGuid(), record);
        statistics.incrementGradeChanges();
    }

    /**
     * 添加权益记录
     */
    public void addRightsRecord(String memberGuid, String rightsType, Object rightsData) {
        RightsRecord record = new RightsRecord(memberGuid, rightsType, rightsData);
        rightsRecords.add(record);
        statistics.incrementRightsRecords();
    }

    /**
     * 获取等级变化记录
     */
    public GradeChangeRecord getGradeChange(String memberGuid) {
        return gradeChanges.get(memberGuid);
    }

    /**
     * 是否有等级变化
     */
    public boolean hasGradeChanges() {
        return !gradeChanges.isEmpty();
    }

    /**
     * 等级变化记录
     */
    @Data
    public static class GradeChangeRecord {
        private final HsaOperationMemberInfo memberInfo;
        private final HsaMemberGradeInfo oldGrade;
        private final HsaMemberGradeInfo newGrade;
        private final long timestamp;

        public GradeChangeRecord(HsaOperationMemberInfo memberInfo, 
                               HsaMemberGradeInfo oldGrade, 
                               HsaMemberGradeInfo newGrade) {
            this.memberInfo = memberInfo;
            this.oldGrade = oldGrade;
            this.newGrade = newGrade;
            this.timestamp = System.currentTimeMillis();
        }

        /**
         * 是否为等级升级
         */
        public boolean isUpgrade() {
            if (oldGrade == null) return true;
            if (newGrade == null) return false;
            return newGrade.getVipGrade() > oldGrade.getVipGrade();
        }

        /**
         * 是否为等级降级
         */
        public boolean isDowngrade() {
            if (oldGrade == null || newGrade == null) return false;
            return newGrade.getVipGrade() < oldGrade.getVipGrade();
        }
    }

    /**
     * 权益记录
     */
    @Data
    public static class RightsRecord {
        private final String memberGuid;
        private final String rightsType;
        private final Object rightsData;
        private final long timestamp;

        public RightsRecord(String memberGuid, String rightsType, Object rightsData) {
            this.memberGuid = memberGuid;
            this.rightsType = rightsType;
            this.rightsData = rightsData;
            this.timestamp = System.currentTimeMillis();
        }
    }

    /**
     * 处理统计信息
     */
    @Data
    public static class ProcessStatistics {
        private int totalMembers;
        private int gradeChanges;
        private int rightsRecords;
        private int databaseQueries;
        private long startTime;
        private long endTime;

        public ProcessStatistics() {
            this.startTime = System.currentTimeMillis();
        }

        public void incrementGradeChanges() {
            this.gradeChanges++;
        }

        public void incrementRightsRecords() {
            this.rightsRecords++;
        }

        public void incrementDatabaseQueries() {
            this.databaseQueries++;
        }

        public void finish() {
            this.endTime = System.currentTimeMillis();
        }

        public long getProcessingTime() {
            return endTime > 0 ? endTime - startTime : System.currentTimeMillis() - startTime;
        }

        @Override
        public String toString() {
            return String.format(
                "ProcessStatistics{totalMembers=%d, gradeChanges=%d, rightsRecords=%d, " +
                "databaseQueries=%d, processingTime=%dms}",
                totalMembers, gradeChanges, rightsRecords, databaseQueries, getProcessingTime()
            );
        }
    }
}
