# 异常处理和日志优化详细说明

## 原有问题分析

### 1. 异常处理问题

#### 原代码问题
```java
try {
    // 业务逻辑
} catch (Exception e) {
    checkGradeRefresh(gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getIsRefresh(), gradeChangeDTO.getRoleType());
    e.printStackTrace(); // 不规范的异常处理
    log.info("等级刷新失败强制释放：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", e.getMessage());
    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
}
```

#### 存在的问题
1. **异常处理粗糙**：所有异常统一处理，缺乏分类
2. **日志不规范**：使用 `e.printStackTrace()` 而非专业日志
3. **异常信息丢失**：没有记录完整的异常上下文
4. **恢复机制缺失**：异常后缺乏有效的恢复策略

### 2. 日志问题

#### 原代码问题
```java
// 日志级别不当
log.info("会员数量：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", gradeChangeDTO.getHsaOperationMemberInfos().size());

// 日志格式不统一
log.info("获取主体下等级信息：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", currentMemberGradeInfoList);

// 可能导致OOM的日志
//不要打印会员了，5w以上 oom
log.info("会员数量：>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>{}", gradeChangeDTO.getHsaOperationMemberInfos().size());
```

#### 存在的问题
1. **日志级别混乱**：调试信息使用info级别
2. **日志格式不统一**：缺乏统一的日志格式规范
3. **性能影响**：大量对象序列化影响性能
4. **排查困难**：缺乏关键信息和上下文

## 优化方案

### 1. 分层异常处理体系

#### 异常分类定义
```java
/**
 * 会员等级异常体系
 */
public class MemberGradeExceptions {
    
    /**
     * 业务异常基类
     */
    public static abstract class MemberGradeBusinessException extends RuntimeException {
        private final String errorCode;
        private final Object[] args;
        
        public MemberGradeBusinessException(String errorCode, String message, Object... args) {
            super(message);
            this.errorCode = errorCode;
            this.args = args;
        }
        
        // getters
    }
    
    /**
     * 参数验证异常
     */
    public static class ParameterValidationException extends MemberGradeBusinessException {
        public ParameterValidationException(String field, Object value) {
            super("PARAM_INVALID", "参数验证失败: {} = {}", field, value);
        }
    }
    
    /**
     * 数据不一致异常
     */
    public static class DataInconsistencyException extends MemberGradeBusinessException {
        public DataInconsistencyException(String dataType, String reason) {
            super("DATA_INCONSISTENT", "数据不一致: {} - {}", dataType, reason);
        }
    }
    
    /**
     * 业务规则异常
     */
    public static class BusinessRuleException extends MemberGradeBusinessException {
        public BusinessRuleException(String rule, String reason) {
            super("BUSINESS_RULE_VIOLATION", "业务规则违反: {} - {}", rule, reason);
        }
    }
    
    /**
     * 系统资源异常
     */
    public static class SystemResourceException extends RuntimeException {
        private final String resourceType;
        
        public SystemResourceException(String resourceType, String message, Throwable cause) {
            super(message, cause);
            this.resourceType = resourceType;
        }
        
        // getters
    }
}
```

#### 异常处理器
```java
@Component
public class MemberGradeExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(MemberGradeExceptionHandler.class);
    
    /**
     * 处理参数验证异常
     */
    public void handleParameterValidation(ParameterValidationException e, MemberGradeChangeContext context) {
        log.warn("参数验证失败。operSubjectGuid: {}, error: {}", 
                context.getOperSubjectGuid(), e.getMessage());
        
        // 记录异常统计
        recordException("PARAMETER_VALIDATION", context);
        
        // 不需要回滚，直接返回错误
        throw new MemberGradeProcessException("参数验证失败", e);
    }
    
    /**
     * 处理数据不一致异常
     */
    public void handleDataInconsistency(DataInconsistencyException e, MemberGradeChangeContext context) {
        log.error("数据不一致异常。operSubjectGuid: {}, memberCount: {}, error: {}", 
                context.getOperSubjectGuid(), context.getMemberCount(), e.getMessage(), e);
        
        // 记录异常详情
        recordExceptionDetail(e, context);
        
        // 触发数据修复流程
        triggerDataRepair(context, e);
        
        throw new MemberGradeProcessException("数据不一致", e);
    }
    
    /**
     * 处理业务规则异常
     */
    public void handleBusinessRule(BusinessRuleException e, MemberGradeChangeContext context) {
        log.warn("业务规则异常。operSubjectGuid: {}, rule: {}, error: {}", 
                context.getOperSubjectGuid(), e.getErrorCode(), e.getMessage());
        
        // 记录业务异常
        recordBusinessException(e, context);
        
        // 业务异常通常不需要重试
        throw new MemberGradeProcessException("业务规则违反", e);
    }
    
    /**
     * 处理系统资源异常
     */
    public void handleSystemResource(SystemResourceException e, MemberGradeChangeContext context) {
        log.error("系统资源异常。operSubjectGuid: {}, resourceType: {}, error: {}", 
                context.getOperSubjectGuid(), e.getResourceType(), e.getMessage(), e);
        
        // 记录系统异常
        recordSystemException(e, context);
        
        // 系统异常可能需要重试
        if (isRetryableSystemException(e)) {
            scheduleRetry(context, e);
        }
        
        throw new MemberGradeProcessException("系统资源异常", e);
    }
    
    /**
     * 处理未知异常
     */
    public void handleUnknownException(Exception e, MemberGradeChangeContext context) {
        log.error("未知异常。operSubjectGuid: {}, memberCount: {}, error: {}", 
                context.getOperSubjectGuid(), context.getMemberCount(), e.getMessage(), e);
        
        // 记录完整异常信息
        recordUnknownException(e, context);
        
        // 发送告警
        sendAlert("UNKNOWN_EXCEPTION", context, e);
        
        throw new MemberGradeProcessException("未知异常", e);
    }
}
```

### 2. 结构化日志体系

#### 日志上下文管理
```java
@Component
public class MemberGradeLogContext {
    
    private static final String CONTEXT_KEY_OPER_SUBJECT = "operSubjectGuid";
    private static final String CONTEXT_KEY_ROLE_TYPE = "roleType";
    private static final String CONTEXT_KEY_MEMBER_COUNT = "memberCount";
    private static final String CONTEXT_KEY_TRACE_ID = "traceId";
    
    /**
     * 设置日志上下文
     */
    public void setContext(MemberGradeChangeContext context) {
        MDC.put(CONTEXT_KEY_OPER_SUBJECT, context.getOperSubjectGuid());
        MDC.put(CONTEXT_KEY_ROLE_TYPE, context.getRoleType());
        MDC.put(CONTEXT_KEY_MEMBER_COUNT, String.valueOf(context.getMemberCount()));
        MDC.put(CONTEXT_KEY_TRACE_ID, generateTraceId());
    }
    
    /**
     * 清理日志上下文
     */
    public void clearContext() {
        MDC.clear();
    }
    
    /**
     * 生成追踪ID
     */
    private String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}
```

#### 结构化日志记录器
```java
@Component
public class MemberGradeLogger {
    
    private static final Logger log = LoggerFactory.getLogger(MemberGradeLogger.class);
    private final ObjectMapper objectMapper;
    
    /**
     * 记录业务开始
     */
    public void logBusinessStart(MemberGradeChangeContext context) {
        log.info("会员等级变化开始处理。memberCount: {}, operSubjectGuid: {}, roleType: {}", 
                context.getMemberCount(), 
                context.getOperSubjectGuid(), 
                context.getRoleType());
    }
    
    /**
     * 记录业务完成
     */
    public void logBusinessComplete(MemberGradeChangeContext context) {
        ProcessStatistics stats = context.getStatistics();
        log.info("会员等级变化处理完成。统计信息: processingTime={}ms, gradeChanges={}, rightsRecords={}, dbQueries={}", 
                stats.getProcessingTime(),
                stats.getGradeChanges(),
                stats.getRightsRecords(),
                stats.getDatabaseQueries());
    }
    
    /**
     * 记录阶段处理
     */
    public void logPhaseProcessing(String phaseName, int processedCount, long elapsedTime) {
        log.info("阶段处理完成。phase: {}, processedCount: {}, elapsedTime: {}ms", 
                phaseName, processedCount, elapsedTime);
    }
    
    /**
     * 记录性能指标
     */
    public void logPerformanceMetrics(String operation, long duration, int count) {
        log.info("性能指标。operation: {}, duration: {}ms, count: {}, avgTime: {}ms", 
                operation, duration, count, count > 0 ? duration / count : 0);
    }
    
    /**
     * 记录数据变化（调试级别）
     */
    public void logDataChange(String dataType, String operation, Object before, Object after) {
        if (log.isDebugEnabled()) {
            log.debug("数据变化。dataType: {}, operation: {}, before: {}, after: {}", 
                    dataType, operation, 
                    safeToString(before), 
                    safeToString(after));
        }
    }
    
    /**
     * 安全的对象转字符串（避免OOM）
     */
    private String safeToString(Object obj) {
        if (obj == null) {
            return "null";
        }
        
        try {
            String str = objectMapper.writeValueAsString(obj);
            // 限制日志长度，避免过长的日志
            return str.length() > 1000 ? str.substring(0, 1000) + "..." : str;
        } catch (Exception e) {
            return obj.getClass().getSimpleName() + "@" + Integer.toHexString(obj.hashCode());
        }
    }
}
```

### 3. 异常恢复机制

#### 自动重试机制
```java
@Component
public class MemberGradeRetryManager {
    
    private final RetryTemplate retryTemplate;
    
    public MemberGradeRetryManager() {
        this.retryTemplate = RetryTemplate.builder()
                .maxAttempts(3)
                .exponentialBackoff(1000, 2, 10000)
                .retryOn(SystemResourceException.class)
                .retryOn(TransientDataException.class)
                .build();
    }
    
    /**
     * 执行带重试的操作
     */
    public <T> T executeWithRetry(String operation, Supplier<T> supplier, MemberGradeChangeContext context) {
        return retryTemplate.execute(retryContext -> {
            try {
                log.debug("执行操作。operation: {}, attempt: {}", operation, retryContext.getRetryCount() + 1);
                return supplier.get();
                
            } catch (Exception e) {
                log.warn("操作执行失败。operation: {}, attempt: {}, error: {}", 
                        operation, retryContext.getRetryCount() + 1, e.getMessage());
                
                // 记录重试统计
                recordRetryAttempt(operation, retryContext.getRetryCount() + 1, e);
                
                throw e;
            }
        });
    }
    
    /**
     * 异步重试机制
     */
    @Async
    public CompletableFuture<Void> scheduleAsyncRetry(String operation, 
                                                     Runnable task, 
                                                     MemberGradeChangeContext context,
                                                     int delaySeconds) {
        return CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(delaySeconds * 1000);
                task.run();
                log.info("异步重试成功。operation: {}", operation);
                
            } catch (Exception e) {
                log.error("异步重试失败。operation: {}", operation, e);
                // 记录重试失败，可能需要人工介入
                recordAsyncRetryFailure(operation, context, e);
            }
        });
    }
}
```

### 4. 监控和告警集成

#### 异常监控
```java
@Component
public class MemberGradeMonitoring {
    
    private final MeterRegistry meterRegistry;
    private final AlertManager alertManager;
    
    /**
     * 记录异常指标
     */
    public void recordException(String exceptionType, String phase, MemberGradeChangeContext context) {
        Counter.builder("member.grade.exception")
                .tag("type", exceptionType)
                .tag("phase", phase)
                .tag("operSubject", context.getOperSubjectGuid())
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * 记录处理时间
     */
    public void recordProcessingTime(String phase, long duration) {
        Timer.builder("member.grade.processing.time")
                .tag("phase", phase)
                .register(meterRegistry)
                .record(duration, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 发送告警
     */
    public void sendAlert(String alertType, MemberGradeChangeContext context, Exception e) {
        Alert alert = Alert.builder()
                .type(alertType)
                .severity(determineSeverity(e))
                .message(buildAlertMessage(context, e))
                .context(buildAlertContext(context))
                .build();
                
        alertManager.sendAlert(alert);
    }
    
    private AlertSeverity determineSeverity(Exception e) {
        if (e instanceof SystemResourceException) {
            return AlertSeverity.HIGH;
        } else if (e instanceof DataInconsistencyException) {
            return AlertSeverity.CRITICAL;
        } else if (e instanceof BusinessRuleException) {
            return AlertSeverity.MEDIUM;
        } else {
            return AlertSeverity.HIGH;
        }
    }
}
```

### 5. 日志配置优化

#### Logback配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/member-grade.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/member-grade.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>
    
    <!-- 异常专用日志 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/member-grade-error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/member-grade-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>
    
    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
    
    <!-- 特定包的日志级别 -->
    <logger name="com.holderzone.member.queue.service.grade" level="DEBUG"/>
    <logger name="org.springframework.transaction" level="DEBUG"/>
</configuration>
```

## 效果预期

### 1. 异常处理改进
- **异常分类准确率**：95%以上
- **异常恢复成功率**：80%以上
- **问题定位时间**：减少70%

### 2. 日志质量提升
- **日志结构化程度**：100%
- **关键信息覆盖率**：95%以上
- **日志存储空间**：优化30%

### 3. 运维效率提升
- **问题排查效率**：提升60%
- **告警准确率**：提升80%
- **自动恢复率**：提升50%

通过完善的异常处理和日志优化，系统的可观测性和可维护性将得到显著提升，大大提高问题排查和解决的效率。
