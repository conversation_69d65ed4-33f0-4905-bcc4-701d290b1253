package com.holderzone.member.queue.service.grade.idempotency;

import cn.hutool.core.util.StrUtil;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.common.service.CacheService;
import com.holderzone.member.queue.entity.member.HsaOperationMemberInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 会员等级变化幂等性控制器
 * 
 * 负责确保会员等级变化操作的幂等性，防止重复执行导致的数据不一致问题
 * 
 * <AUTHOR>
 * @date 2025-09-18
 */
@Slf4j
@Component
public class MemberGradeIdempotencyController {

    private static final String IDEMPOTENCY_KEY_PREFIX = "MEMBER_GRADE_CHANGE_IDEMPOTENCY";
    private static final int DEFAULT_EXPIRE_MINUTES = 30;
    private static final int PROCESSING_EXPIRE_MINUTES = 10;

    @Resource
    private CacheService cacheService;

    /**
     * 检查并设置幂等性标记
     * 
     * @param gradeChangeDTO 等级变化参数
     * @return 幂等性检查结果
     */
    public IdempotencyCheckResult checkAndSetIdempotency(MemberGradeChangeDTO gradeChangeDTO) {
        String idempotencyKey = generateIdempotencyKey(gradeChangeDTO);
        
        try {
            // 检查是否已经处理过
            String existingStatus = cacheService.get(idempotencyKey);
            
            if (StrUtil.isNotBlank(existingStatus)) {
                IdempotencyStatus status = IdempotencyStatus.valueOf(existingStatus);
                
                switch (status) {
                    case PROCESSING:
                        log.warn("等级变化正在处理中，请勿重复提交。key: {}", idempotencyKey);
                        return IdempotencyCheckResult.processing(idempotencyKey);
                        
                    case COMPLETED:
                        log.info("等级变化已处理完成，跳过重复处理。key: {}", idempotencyKey);
                        return IdempotencyCheckResult.completed(idempotencyKey);
                        
                    case FAILED:
                        log.info("等级变化之前处理失败，允许重新处理。key: {}", idempotencyKey);
                        break;
                        
                    default:
                        log.warn("未知的幂等性状态: {}, key: {}", status, idempotencyKey);
                        break;
                }
            }
            
            // 设置处理中状态
            boolean setSuccess = cacheService.setIfAbsent(idempotencyKey, 
                    IdempotencyStatus.PROCESSING.name(), 
                    PROCESSING_EXPIRE_MINUTES, 
                    TimeUnit.MINUTES);
            
            if (!setSuccess) {
                log.warn("设置幂等性标记失败，可能存在并发处理。key: {}", idempotencyKey);
                return IdempotencyCheckResult.processing(idempotencyKey);
            }
            
            log.debug("幂等性检查通过，开始处理。key: {}", idempotencyKey);
            return IdempotencyCheckResult.allowed(idempotencyKey);
            
        } catch (Exception e) {
            log.error("幂等性检查异常。key: {}", idempotencyKey, e);
            // 异常情况下允许处理，但记录日志
            return IdempotencyCheckResult.allowed(idempotencyKey);
        }
    }

    /**
     * 标记处理完成
     * 
     * @param idempotencyKey 幂等性键
     */
    public void markCompleted(String idempotencyKey) {
        try {
            cacheService.set(idempotencyKey, 
                    IdempotencyStatus.COMPLETED.name(), 
                    DEFAULT_EXPIRE_MINUTES, 
                    TimeUnit.MINUTES);
            
            log.debug("标记处理完成。key: {}", idempotencyKey);
            
        } catch (Exception e) {
            log.error("标记处理完成失败。key: {}", idempotencyKey, e);
        }
    }

    /**
     * 标记处理失败
     * 
     * @param idempotencyKey 幂等性键
     */
    public void markFailed(String idempotencyKey) {
        try {
            cacheService.set(idempotencyKey, 
                    IdempotencyStatus.FAILED.name(), 
                    DEFAULT_EXPIRE_MINUTES, 
                    TimeUnit.MINUTES);
            
            log.debug("标记处理失败。key: {}", idempotencyKey);
            
        } catch (Exception e) {
            log.error("标记处理失败异常。key: {}", idempotencyKey, e);
        }
    }

    /**
     * 清理幂等性标记
     * 
     * @param idempotencyKey 幂等性键
     */
    public void clearIdempotency(String idempotencyKey) {
        try {
            cacheService.delete(idempotencyKey);
            log.debug("清理幂等性标记。key: {}", idempotencyKey);
            
        } catch (Exception e) {
            log.error("清理幂等性标记失败。key: {}", idempotencyKey, e);
        }
    }

    /**
     * 生成幂等性键
     * 
     * 基于操作主体、角色类型、会员列表、操作时间等生成唯一键
     */
    private String generateIdempotencyKey(MemberGradeChangeDTO gradeChangeDTO) {
        StringBuilder keyBuilder = new StringBuilder();
        
        // 基础信息
        keyBuilder.append(gradeChangeDTO.getOperSubjectGuid()).append(":");
        keyBuilder.append(gradeChangeDTO.getRoleType()).append(":");
        
        // 会员信息摘要
        String memberDigest = generateMemberDigest(gradeChangeDTO.getHsaOperationMemberInfos());
        keyBuilder.append(memberDigest).append(":");
        
        // 操作类型
        if (gradeChangeDTO.getIssuerType() != null) {
            keyBuilder.append("issuer:").append(gradeChangeDTO.getIssuerType()).append(":");
        }
        
        if (gradeChangeDTO.getSourceType() != null) {
            keyBuilder.append("source:").append(gradeChangeDTO.getSourceType()).append(":");
        }
        
        // 时间窗口（精确到分钟，避免秒级重复）
        String timeWindow = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        keyBuilder.append("time:").append(timeWindow);
        
        // 生成MD5摘要
        String rawKey = keyBuilder.toString();
        String md5Key = DigestUtils.md5DigestAsHex(rawKey.getBytes(StandardCharsets.UTF_8));
        
        return String.join(StringConstant.COLON, IDEMPOTENCY_KEY_PREFIX, md5Key);
    }

    /**
     * 生成会员信息摘要
     */
    private String generateMemberDigest(List<HsaOperationMemberInfo> memberInfos) {
        if (memberInfos == null || memberInfos.isEmpty()) {
            return "empty";
        }
        
        // 按GUID排序后生成摘要，确保相同会员列表生成相同摘要
        String memberGuids = memberInfos.stream()
                .map(HsaOperationMemberInfo::getGuid)
                .sorted()
                .collect(Collectors.joining(","));
        
        return DigestUtils.md5DigestAsHex(memberGuids.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 幂等性状态枚举
     */
    public enum IdempotencyStatus {
        PROCESSING,  // 处理中
        COMPLETED,   // 已完成
        FAILED       // 已失败
    }

    /**
     * 幂等性检查结果
     */
    public static class IdempotencyCheckResult {
        private final boolean allowed;
        private final IdempotencyStatus status;
        private final String idempotencyKey;
        private final String message;

        private IdempotencyCheckResult(boolean allowed, IdempotencyStatus status, 
                                     String idempotencyKey, String message) {
            this.allowed = allowed;
            this.status = status;
            this.idempotencyKey = idempotencyKey;
            this.message = message;
        }

        public static IdempotencyCheckResult allowed(String idempotencyKey) {
            return new IdempotencyCheckResult(true, null, idempotencyKey, "允许处理");
        }

        public static IdempotencyCheckResult processing(String idempotencyKey) {
            return new IdempotencyCheckResult(false, IdempotencyStatus.PROCESSING, 
                    idempotencyKey, "正在处理中");
        }

        public static IdempotencyCheckResult completed(String idempotencyKey) {
            return new IdempotencyCheckResult(false, IdempotencyStatus.COMPLETED, 
                    idempotencyKey, "已处理完成");
        }

        // Getters
        public boolean isAllowed() { return allowed; }
        public IdempotencyStatus getStatus() { return status; }
        public String getIdempotencyKey() { return idempotencyKey; }
        public String getMessage() { return message; }

        /**
         * 是否应该跳过处理
         */
        public boolean shouldSkip() {
            return !allowed;
        }

        /**
         * 是否正在处理中
         */
        public boolean isProcessing() {
            return status == IdempotencyStatus.PROCESSING;
        }

        /**
         * 是否已完成
         */
        public boolean isCompleted() {
            return status == IdempotencyStatus.COMPLETED;
        }

        @Override
        public String toString() {
            return String.format("IdempotencyCheckResult{allowed=%s, status=%s, key=%s, message='%s'}", 
                    allowed, status, idempotencyKey, message);
        }
    }
}
