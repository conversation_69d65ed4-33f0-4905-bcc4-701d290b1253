package com.holderzone.member.queue.service.grade.transaction;

import com.holderzone.member.queue.service.grade.context.MemberGradeChangeContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Supplier;

/**
 * 会员等级变化事务管理器
 * 
 * 负责管理等级变化过程中的事务边界，实现细粒度的事务控制
 * 
 * <AUTHOR>
 * @date 2025-09-18
 */
@Slf4j
@Component
public class MemberGradeTransactionManager {

    @Resource
    private TransactionTemplate transactionTemplate;
    
    @Resource
    private ThreadPoolExecutor memberQueueThreadExecutor;

    /**
     * 执行等级变化核心事务
     * 
     * 这是最关键的事务，包含等级计算和基础数据更新
     * 
     * @param context 处理上下文
     * @param processor 处理器
     */
    @Transactional(rollbackFor = Exception.class, timeout = 30)
    public void executeGradeChangeTransaction(MemberGradeChangeContext context, 
                                            Runnable processor) {
        try {
            log.debug("开始执行等级变化核心事务。memberCount: {}", context.getMemberCount());
            
            processor.run();
            
            log.debug("等级变化核心事务执行完成");
            
        } catch (Exception e) {
            log.error("等级变化核心事务执行失败", e);
            throw new RuntimeException("等级变化核心事务失败", e);
        }
    }

    /**
     * 执行权益发放事务
     * 
     * 权益发放相对独立，可以单独事务处理
     * 
     * @param context 处理上下文
     * @param processor 处理器
     */
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public void executeRightsTransaction(MemberGradeChangeContext context, 
                                       Runnable processor) {
        try {
            log.debug("开始执行权益发放事务");
            
            processor.run();
            
            log.debug("权益发放事务执行完成");
            
        } catch (Exception e) {
            log.error("权益发放事务执行失败", e);
            // 权益发放失败不应该影响等级变化的核心逻辑
            // 可以考虑记录失败信息，后续补偿处理
            handleRightsTransactionFailure(context, e);
        }
    }

    /**
     * 执行批量更新事务
     * 
     * 批量更新会员信息，使用较短的事务时间
     * 
     * @param processor 处理器
     */
    @Transactional(rollbackFor = Exception.class, timeout = 20)
    public void executeBatchUpdateTransaction(Runnable processor) {
        try {
            log.debug("开始执行批量更新事务");
            
            processor.run();
            
            log.debug("批量更新事务执行完成");
            
        } catch (Exception e) {
            log.error("批量更新事务执行失败", e);
            throw new RuntimeException("批量更新事务失败", e);
        }
    }

    /**
     * 执行新事务
     * 
     * 在新的事务中执行操作，不受当前事务影响
     * 
     * @param supplier 操作供应商
     * @return 操作结果
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public <T> T executeInNewTransaction(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("新事务执行失败", e);
            throw e;
        }
    }

    /**
     * 异步执行非事务操作
     * 
     * 对于不需要事务保证的操作，异步执行提高性能
     * 
     * @param processor 处理器
     * @return CompletableFuture
     */
    public CompletableFuture<Void> executeAsync(Runnable processor) {
        return CompletableFuture.runAsync(() -> {
            try {
                processor.run();
            } catch (Exception e) {
                log.error("异步操作执行失败", e);
                // 异步操作失败不抛出异常，避免影响主流程
            }
        }, memberQueueThreadExecutor);
    }

    /**
     * 执行补偿事务
     * 
     * 当某些操作失败时，执行补偿逻辑
     * 
     * @param context 处理上下文
     * @param compensationProcessor 补偿处理器
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void executeCompensationTransaction(MemberGradeChangeContext context,
                                             Runnable compensationProcessor) {
        try {
            log.info("开始执行补偿事务。operSubjectGuid: {}", context.getOperSubjectGuid());
            
            compensationProcessor.run();
            
            log.info("补偿事务执行完成");
            
        } catch (Exception e) {
            log.error("补偿事务执行失败", e);
            // 补偿失败需要人工介入
            notifyCompensationFailure(context, e);
        }
    }

    /**
     * 分阶段执行事务
     * 
     * 将复杂的业务逻辑分解为多个小事务，提高成功率
     * 
     * @param context 处理上下文
     * @param phases 事务阶段列表
     */
    public void executeInPhases(MemberGradeChangeContext context, 
                               TransactionPhase... phases) {
        
        for (int i = 0; i < phases.length; i++) {
            TransactionPhase phase = phases[i];
            
            try {
                log.debug("执行事务阶段 {}: {}", i + 1, phase.getName());
                
                transactionTemplate.execute(status -> {
                    phase.execute(context);
                    return null;
                });
                
                log.debug("事务阶段 {} 执行完成", i + 1);
                
            } catch (Exception e) {
                log.error("事务阶段 {} 执行失败: {}", i + 1, phase.getName(), e);
                
                // 根据阶段的重要性决定是否继续
                if (phase.isCritical()) {
                    // 关键阶段失败，执行回滚逻辑
                    executeRollbackPhases(context, i, phases);
                    throw new RuntimeException("关键事务阶段失败: " + phase.getName(), e);
                } else {
                    // 非关键阶段失败，记录错误但继续执行
                    log.warn("非关键事务阶段失败，继续执行后续阶段: {}", phase.getName());
                    context.getStatistics().incrementErrors();
                }
            }
        }
    }

    /**
     * 执行回滚阶段
     */
    private void executeRollbackPhases(MemberGradeChangeContext context, 
                                     int failedPhaseIndex, 
                                     TransactionPhase[] phases) {
        
        log.info("开始执行回滚操作，失败阶段索引: {}", failedPhaseIndex);
        
        // 从失败阶段开始，逆序执行回滚
        for (int i = failedPhaseIndex; i >= 0; i--) {
            TransactionPhase phase = phases[i];
            
            if (phase.hasRollback()) {
                try {
                    executeCompensationTransaction(context, () -> phase.rollback(context));
                    log.debug("阶段 {} 回滚完成", i + 1);
                    
                } catch (Exception e) {
                    log.error("阶段 {} 回滚失败", i + 1, e);
                    // 回滚失败需要人工处理
                }
            }
        }
    }

    /**
     * 处理权益事务失败
     */
    private void handleRightsTransactionFailure(MemberGradeChangeContext context, Exception e) {
        log.error("权益发放事务失败，记录失败信息用于后续补偿。operSubjectGuid: {}", 
                context.getOperSubjectGuid(), e);
        
        // 异步记录失败信息
        executeAsync(() -> recordRightsFailure(context, e));
    }

    /**
     * 记录权益发放失败信息
     */
    private void recordRightsFailure(MemberGradeChangeContext context, Exception e) {
        try {
            // 这里可以将失败信息记录到数据库或消息队列
            // 用于后续的补偿处理
            log.info("记录权益发放失败信息完成");
            
        } catch (Exception recordException) {
            log.error("记录权益发放失败信息异常", recordException);
        }
    }

    /**
     * 通知补偿失败
     */
    private void notifyCompensationFailure(MemberGradeChangeContext context, Exception e) {
        // 发送告警通知，需要人工介入处理
        log.error("补偿事务失败，需要人工介入。operSubjectGuid: {}", 
                context.getOperSubjectGuid(), e);
    }

    /**
     * 事务阶段接口
     */
    public interface TransactionPhase {
        /**
         * 阶段名称
         */
        String getName();
        
        /**
         * 是否为关键阶段
         */
        boolean isCritical();
        
        /**
         * 执行阶段逻辑
         */
        void execute(MemberGradeChangeContext context);
        
        /**
         * 是否有回滚逻辑
         */
        default boolean hasRollback() {
            return false;
        }
        
        /**
         * 回滚逻辑
         */
        default void rollback(MemberGradeChangeContext context) {
            // 默认空实现
        }
    }
}
